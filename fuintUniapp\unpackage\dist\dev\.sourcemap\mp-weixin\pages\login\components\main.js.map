{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/main.vue?e4e2", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/main.vue?9234", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/main.vue?9683", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/main.vue?a93d", "uni-app:///pages/login/components/main.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/main.vue?54ce", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/main.vue?14d9"], "names": ["props", "isParty", "type", "default", "partyData", "data", "accountTitle", "isRegister", "loginType", "isLoading", "<PERSON><PERSON>a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "smsState", "times", "mobile", "account", "password", "password1", "captchaCode", "smsCode", "created", "methods", "switchLoginType", "toRegister", "getCaptcha", "Login<PERSON><PERSON>", "then", "app", "handelSmsCaptcha", "formValidation", "validteMobile", "validteAccount", "validtePassword", "validtePassword1", "validteCaptchaCode", "validteSmsCode", "sendSmsCaptcha", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "uuid", "finally", "timer", "clearInterval", "handleSubmit", "handleCancel", "submitRegister", "store", "submitLogin", "verifyCode", "isNeedAuth", "onNavigateBack", "uni", "delta"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,uoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACmF5qB;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AAAA,gBAEA;EACAA;IACA;IACAC;MACAC;MACAC;QAAA;MAAA;IACA;IACA;IACAC;MACAF;IACA;EACA;EAEAG;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACAC,mBACAC;QACAC;QACAA;QACAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAD;MACA;IACA;IAEA;IACAE;MACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;MACA;QACA;UACA;QACA;QACA;UACA;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAT;MACAF;QACAY;QACAnB;QACAJ;QACAwB;MACA,GACAZ;QACA;QACA;UACAC;UACA;UACAA;QACA;UACAA;QACA;MACA,GACAY;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACAb;MACA;QACAA;QACA;UACAA;UACAA;UACAc;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;UACAf;QACA;MACA;;MAEA;MACA;QACA;UACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;QACA;MACA;MAEA;IACA;IACA;IACAgB;MACA;IACA;IACA;IACAC;MACA;MACAjB;MACAkB;QACA9B;QACAC;QACAC;QACAoB;QACAnB;QACAoB;MACA,GACAZ;QACA;QACAC;QACA;UACA;UACAA;QACA;UACAA;UACAA;QACA;MACA,GACAY;QAAA;MAAA;IACA;IAEA;IACAO;MACA;MACAnB;MACAkB;QACAE;QACAjC;QACAC;QACAC;QACAhB;QACAG;QACAe;QACAoB;MACA,GACAZ;QACA;QACAC;QACA;UACA;UACAA;QACA;UACAA;UACAA;QACA;MACA,GACAY;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAS;MACA;IACA;IAEA;AACA;AACA;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACjbA;AAAA;AAAA;AAAA;AAAuwC,CAAgB,kqCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/components/main.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./main.vue?vue&type=template&id=248061c3&scoped=true&\"\nvar renderjs\nimport script from \"./main.vue?vue&type=script&lang=js&\"\nexport * from \"./main.vue?vue&type=script&lang=js&\"\nimport style0 from \"./main.vue?vue&type=style&index=0&id=248061c3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"248061c3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/components/main.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=template&id=248061c3&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">      \n    <!-- 页面头部 -->\n    <view class=\"header\">\n      <view class=\"title\">\n           <view class=\"item\" @click=\"switchLoginType('account')\"><text :class=\"loginType === 'account' ? 'active' : ''\">{{ accountTitle }}</text></view>\n           <view class=\"item\" @click=\"switchLoginType('sms')\"><text :class=\"loginType === 'sms' ? 'active' : ''\">短信登录</text></view>\n      </view>\n    </view>\n    \n    <!-- 账号登录表单 start-->\n    <view class=\"login-form\" v-if=\"loginType === 'account'\">\n      <!-- 手机号 -->\n      <view class=\"form-item\">\r\n        <text class=\"iconfont icon-sy-yh\"></text>\n        <input class=\"form-item--input uni-input\" type=\"text\" v-model=\"account\" maxlength=\"30\" clearable=\"true\" placeholder=\"请输入您的用户名\" />\n      </view>\n      <!-- 密码 -->\n      <view class=\"form-item\">\r\n        <text class=\"iconfont icon-suo\"></text>\n        <input class=\"form-item--input\" type=\"password\" autocomplete=\"off\" v-model=\"password\" maxlength=\"30\" minlength=\"1\" value=\"\" placeholder=\"请输入您的密码\" />\n      </view>\n      <!-- 确认密码 -->\n      <view class=\"form-item\" v-if=\"isRegister\">\r\n        <text class=\"iconfont icon-suo\"></text>\n        <input class=\"form-item--input\" type=\"password\" autocomplete=\"off\" v-model=\"password1\" maxlength=\"30\" value=\"\" placeholder=\"请再次输入密码\" />\n      </view>\n      <!-- 图形验证码 -->\n      <view class=\"form-item\">\r\n        <text class=\"iconfont icon-tuxingyanzhengma\"></text>\n        <input class=\"form-item--input\" type=\"text\" v-model=\"captchaCode\" maxlength=\"5\" placeholder=\"请输入图形验证码\" />\n        <view class=\"form-item--parts\">\n          <view class=\"captcha\" @click=\"getCaptcha()\">\n            <image class=\"image\" :src=\"captcha\"></image>\n          </view>\n        </view>\n      </view>\n      <!-- 按钮 -->\n      <view class=\"login-button\" v-if=\"!isRegister\" @click=\"handleSubmit\"><text>立即登录</text></view>\n      <view class=\"login-button\" v-if=\"isRegister\" @click=\"handleSubmit\"><text>立即注册</text></view>\r\n      <view class=\"cancel-button\" @click=\"handleCancel\"><text>取消</text></view>\n      <view class=\"register\" v-if=\"!isRegister\" @click=\"toRegister()\">还没有账号？去注册</view>\n      <view class=\"register\" v-if=\"isRegister\" @click=\"toRegister()\">已有账号？立即登录</view>\n    </view>\n    <!-- 账号登录表单 end-->\n    \n    <!-- 短信登录表单 start-->\n    <view class=\"login-form\" v-if=\"loginType === 'sms'\">\n      <!-- 手机号 -->\n      <view class=\"form-item\">\r\n          <text class=\"iconfont icon-shoujihao\"></text>\n          <text class=\"pre-mobile\">+86</text>\n          <input class=\"form-item--input\" style=\"padding-left: 12rpx;\" type=\"number\" v-model=\"mobile\" maxlength=\"11\" placeholder=\"请输入手机号码\" />\n      </view>\n      <!-- 图形验证码 -->\n      <view class=\"form-item\">\r\n        <text class=\"iconfont icon-tuxingyanzhengma\"></text>\n        <input class=\"form-item--input\" type=\"text\" v-model=\"captchaCode\" maxlength=\"5\" placeholder=\"请输入图形验证码\" />\n        <view class=\"form-item--parts\">\n          <view class=\"captcha\" @click=\"getCaptcha()\">\n            <image class=\"image\" :src=\"captcha\"></image>\n          </view>\n        </view>\n      </view>\n      <!-- 短信验证码 -->\n      <view class=\"form-item\">\r\n        <text class=\"iconfont icon-yanzhengma\"></text>\n        <input class=\"form-item--input\" type=\"number\" v-model=\"smsCode\" maxlength=\"6\" placeholder=\"请输入短信验证码\" />\n        <view class=\"form-item--parts\">\n          <view class=\"captcha-sms\" @click=\"handelSmsCaptcha()\">\n            <text v-if=\"!smsState\" class=\"activate\">获取验证码</text>\n            <text v-else class=\"un-activate\">重新发送({{ times }})秒</text>\n          </view>\n        </view>\n      </view>\n      <!-- 登录按钮 -->\n      <view class=\"login-button\" @click=\"handleSubmit\"><text>验证码登录</text></view>\n    </view>\n    <!-- 短信登录表单 end-->\n  </view>\n</template>\n\n<script>\n  import store from '@/store'\n  import * as LoginApi from '@/api/login'\n  import { throttle, debounce } from '@/utils/util'\n  import * as Verify from '@/utils/verify'\n  import { checkLogin, isWechat } from '@/utils/app'\n\n  // 倒计时时长(秒)\n  const times = 60\n\n  // 表单验证场景\n  const GET_CAPTCHA = 10\n  const SUBMIT_LOGIN = 20\n  const SUBMIT_LOGIN_ACCOUNT = 30\n  const SUBMIT_REGISTER = 40\n\n  export default {\n    props: {\n      // 是否存在第三方用户信息\n      isParty: {\n        type: Boolean,\n        default: () => false\n      },\n      // 第三方用户信息数据\n      partyData: {\n        type: Object\n      }\n    },\n\n    data() {\n      return {\n        // 账号标题\n        accountTitle : '账号登录',\n        // 是否注册新账号\n        isRegister: false,\n        // 登录方式\n        loginType: 'account',\n        // 正在加载\n        isLoading: false,\n        // 图形验证码信息\n        captcha: \"\",\n        // 图形验证码uuid\n        captchaUuid: \"\",\n        // 账号图形验证码信息\n        captchaForAccount: \"\",\n        // 短信验证码发送状态\n        smsState: false,\n        // 倒计时\n        times,\n        // 手机号\n        mobile: '',\n        // 账号\n        account: '',\n        // 密码\n        password: '',\n        // 确认密码\n        password1: '',\n        // 图形验证码\n        captchaCode: '',\n        // 短信验证码\n        smsCode: ''\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    created() {\n      // 获取图形验证码\n      this.getCaptcha();\n    },\n\n    methods: {\n      // 切换登录方式\n      switchLoginType(loginType) {\n        this.loginType = loginType;\r\n        this.mobile = \"\";\r\n        this.account = \"\";\r\n        this.password = \"\";\r\n        this.password1 = \"\";\r\n        this.smsCode = \"\";\r\n        this.getCaptcha();\n        if (loginType === 'sms') {\n            this.isRegister = false;\n            this.accountTitle = '账号登录';\n        }\n      },\n      // 注册新用户\n      toRegister() {\n         if (!this.isRegister) {\n             this.accountTitle = '注册新账号';\n             this.isRegister = true;\n         } else {\n             this.accountTitle = '账号登录';\n             this.isRegister = false;\n         }\n      },\n      // 获取图形验证码\n      getCaptcha() {\n        const app = this\n        LoginApi.captcha()\n          .then(result => {\n            app.captcha = result.data.captcha;\n            app.captchaUuid = result.data.uuid;\r\n            app.captchaCode = \"\";\n        })\n      },\n\n      // 点击发送短信验证码\n      handelSmsCaptcha() {\n        const app = this\n        if (!app.isLoading && !app.smsState && app.formValidation(GET_CAPTCHA)) {\n            app.sendSmsCaptcha();\n        }\n      },\n\n      // 表单验证\n      formValidation(scene) {\n        const app = this\n        // 验证获取短信验证码\n        if (scene === GET_CAPTCHA) {\n            if (!app.validteMobile(app.mobile) || !app.validteCaptchaCode(app.captchaCode)) {\n                return false\n            }\n        }\n        // 验证提交登录\n        if (scene === SUBMIT_LOGIN) {\n            if (!app.validteMobile(app.mobile) || !app.validteSmsCode(app.smsCode)) {\n                return false\n            }\n        }\n        // 验证账号登录\n        if (scene === SUBMIT_LOGIN_ACCOUNT) {\n            if (!app.validteAccount(app.account) || !app.validtePassword(app.password) || !app.validteCaptchaCode(app.captchaCode)) {\n                return false\n            }\n        }\n        // 验证提交注册\n        if (scene === SUBMIT_REGISTER) {\n            if (!app.validteAccount(app.account) || !app.validtePassword(app.password) || !app.validtePassword1(app.password1) || !app.validteCaptchaCode(app.captchaCode)) {\n                return false\n            }\n            if (app.password !== app.password1) {\n                this.$toast('两次输入的密码不一致');\n                return false;\n            }\n        }\n        return true;\n      },\n\n      // 验证手机号\n      validteMobile(str) {\n        if (Verify.isEmpty(str)) {\n          this.$toast('请先输入手机号')\n          return false\n        }\n        if (!Verify.isMobile(str)) {\n          this.$toast('请输入正确格式的手机号')\n          return false\n        }\n        return true\n      },\n      \n      // 验证账号\n      validteAccount(str) {\n        if (Verify.isEmpty(str)) {\n            this.$toast('请先输入您的用户名')\n            return false\n        }\n        if (str.length < 5) {\n            this.$toast('用户名不能少于5位')\n            return false\n        }\n        return true\n      },\n      // 验证密码\n      validtePassword(str) {\n        if (Verify.isEmpty(str)) {\n            this.$toast('请先输入您的密码')\n            return false\n        }\n        if (str.length < 6) {\n            this.$toast('密码不能少于6位')\n            return false\n        }\n        return true\n      },\n      // 验证密码\n      validtePassword1(str) {\n        if (Verify.isEmpty(str)) {\n            this.$toast('请再次输入您的密码')\n            return false\n        }\n        return true\n      },\n\n      // 验证图形验证码\n      validteCaptchaCode(str) {\n        if (Verify.isEmpty(str)) {\n            this.$toast('请先输入图形验证码')\n            return false\n        }\n        return true\n      },\n\n      // 验证短信验证码\n      validteSmsCode(str) {\n        if (Verify.isEmpty(str)) {\n          this.$toast('请先输入短信验证码')\n          return false\n        }\n        return true\n      },\n\n      // 请求发送短信验证码接口\n      sendSmsCaptcha() {\n        const app = this\n        app.isLoading = true\n        LoginApi.sendSmsCaptcha({\n              captchaKey: app.captcha.key,\n              captchaCode: app.captchaCode,\n              mobile: app.mobile,\n              uuid: app.captchaUuid\n          })\n          .then(result => {\n            // 显示发送成功\n            if (result.data) {\n               app.$toast(result.message)\n               // 执行定时器\n               app.timer()\n            } else {\n                app.$error(result.message)\n            }\n          })\n          .finally(() => app.isLoading = false)\n      },\n\n      // 执行定时器\n      timer() {\n        const app = this\n        app.smsState = true\n        const inter = setInterval(() => {\n          app.times = app.times - 1\n          if (app.times <= 0) {\n              app.smsState = false;\n              app.times = times;\n              clearInterval(inter);\n          }\n        }, 1000)\n      },\n\n      // 点击提交\n      handleSubmit() {\n        const app = this\n        // 短信验证码登录\n        if (!app.isLoading && !app.isRegister && app.loginType === 'sms') {\n            if (app.formValidation(SUBMIT_LOGIN)) {\n                app.submitLogin();\n            }\n        }\n        \n        // 账号登录\n        if (!app.isLoading && !app.isRegister && app.loginType === 'account') {\n            if (app.formValidation(SUBMIT_LOGIN_ACCOUNT)) {\n                app.submitLogin();\n            }\n        }\n        \n        // 注册新账号\n        if (!app.isLoading && app.isRegister) {\n            if (app.formValidation(SUBMIT_REGISTER)) {\n                app.submitRegister();\n            }\n        }\n        \n        return true\n      },\r\n      // 取消返回\r\n      handleCancel() {\r\n          this.$navTo('pages/user/index');\r\n      },\n      // 确认注册\n      submitRegister() {\n        const app = this\n        app.isLoading = true\n        store.dispatch('Register', {\n            account: app.account,\n            password: app.password,\n            password1: app.password1,\n            captchaKey: app.captcha.key,\n            captchaCode: app.captchaCode,\n            uuid: app.captchaUuid\n          })\n          .then(result => {\n              // 显示登录信息\n              app.$toast(result.message)\n              if (result.code === 200) {\n                  // 注册成功，去认证\n                  app.isNeedAuth(result.data);\n              } else {\r\n                  app.$error(result.message);\n                  app.getCaptcha();\n              }\n          })\n          .finally(() => app.isLoading = false)\n      },\n\n      // 确认登录\n      submitLogin() {\n        const app = this\n        app.isLoading = true\n        store.dispatch('Login', {\n            verifyCode: app.smsCode,\n            mobile: app.mobile,\n            account: app.account,\n            password: app.password,\n            isParty: app.isParty,\n            partyData: app.partyData,\n            captchaCode: app.captchaCode,\n            uuid: app.captchaUuid\n          })\n          .then(result => {\n            // 显示登录信息\n            app.$toast(result.message)\n            if (result.code === 200) {\n                // 登录成功，去认证\r\n                app.isNeedAuth(result.data);\n            } else {\n                app.$error(result.message);\n                app.getCaptcha();\n            }\n          })\n          .finally(() => app.isLoading = false)\n      },\r\n      \r\n      /**\r\n       * 去授权认证\r\n       * */\r\n      isNeedAuth(loginInfo) {\r\n         this.onNavigateBack(1);\r\n      },\n\n      /**\n       * 登录成功-跳转回原页面\n       */\n      onNavigateBack(delta) {\n        uni.navigateBack({\n          delta: Number(delta || 1)\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    padding: 160rpx 60rpx 100rpx 60rpx;\n    min-height: 100vh;\n    background-color: #fff;\n    .fast-icon {\n        margin-bottom: 80rpx;\n        font-size: 50rpx;\n        cursor: pointer;\n    }\n  }\n\n  // 页面头部\n  .header {\n    margin-bottom: 50rpx;\n    .title {\n       color: #191919;\n       font-size: 33rpx;\n       height: 88rpx;\n       padding: 10rpx;\n       cursor: pointer;\n       .item {\n           width: 50%;\n           height: 88rpx;\n           float: left;\n           text-align: center;\r\n           font-weight: bold;\n       }\n       .active {\n           border-bottom: #ff3800 10rpx solid;\n           padding-bottom: 10rpx;\n           text-align: center;\n       }\n    }\n  }\n\n  // 输入框元素\n  .form-item {\n    display: flex;\n    padding: 18rpx;\n    border-bottom: 2rpx solid #cccccc;\n    margin-bottom: 25rpx;\n    height: 110rpx;\r\n    align-items: center;\r\n    justify-content: center;\n    .pre-mobile {\n        line-height: 75rpx;\n        color: #888888;\n    }\n\n    &--input {\n      font-size: 26rpx;\n      letter-spacing: 1rpx;\n      flex: 1;\n      height: 100%;\r\n      padding-left: 5rpx;\n    }\n\n    &--parts {\n      min-width: 100rpx;\n      height: 100%;\n    }\n\n    // 图形验证码\n    .captcha {\n      height: 100%;\n\n      .image {\n        display: block;\n        width: 192rpx;\n        height: 80rpx;\n      }\n    }\n\n    // 短信验证码\n    .captcha-sms {\n      font-size: 22rpx;\n      line-height: 50rpx;\n      padding-right: 20rpx;\n\n      .activate {\n        color: #cea26a;\n        border: #ccc solid 1px;\n        padding: 18rpx;\n        border-radius: 8rpx;\n      }\n\n      .un-activate {\n        color: #9e9e9e;\n      }\n    }\n  }\n\n\n  // 登录按钮\n  .login-button {\n    width: 96%;\n    height: 86rpx;\n    margin: 0 auto;\r\n    margin-top: 60rpx;\n    background: $fuint-theme;\n    text-align: center;\n    line-height: 86rpx;\n    color: #fff;\n    border-radius: 80rpx;\n    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.1);\n    letter-spacing: 5rpx;\r\n    cursor: pointer;\n  }\r\n  \r\n  // 取消按钮\r\n  .cancel-button {\r\n    width: 96%;\r\n    height: 86rpx;\r\n    margin: 0 auto;\r\n    margin-top: 20rpx;\r\n    background: #dfdfdf;\r\n    color: #fff;\r\n    text-align: center;\r\n    line-height: 86rpx;\r\n    border-radius: 80rpx;\r\n    box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.1);\r\n    letter-spacing: 5rpx;\r\n    cursor: pointer;\r\n  }\n  \n  // 去注册\n  .register {\n      margin-top: 40rpx;\n      text-align: right;\n  }\n\n  // 微信授权登录\n  .wechat-auth {\n    display: flex;\n    justify-content: center;\n    margin-top: 40rpx;\n\n    .icon {\n      width: 38rpx;\n      height: 38rpx;\n      margin-right: 15rpx;\n    }\n\n    .title {\n      font-size: 28rpx;\n      color: #666666;\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=style&index=0&id=248061c3&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./main.vue?vue&type=style&index=0&id=248061c3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425476\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}