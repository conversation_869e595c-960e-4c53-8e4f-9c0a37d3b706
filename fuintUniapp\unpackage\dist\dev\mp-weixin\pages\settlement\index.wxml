<view class="container p-bottom data-v-60591ae5"><view class="m-top20 data-v-60591ae5"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="checkout_list data-v-60591ae5"><view class="flow-shopList dis-flex data-v-60591ae5"><view class="flow-list-right flex-box data-v-60591ae5"><text class="goods-name twolist-hidden data-v-60591ae5">{{"预存￥"+item.$orig.store+" 到账 ￥"+item.$orig.upStore}}</text><view class="flow-list-cont dis-flex flex-x-between flex-y-center data-v-60591ae5"><text class="small data-v-60591ae5">{{"X"+item.$orig.num+''}}</text><text class="flow-cont data-v-60591ae5">{{"￥"+item.g0}}</text></view></view></view></view></block><view class="flow-num-box b-f data-v-60591ae5"><text class="data-v-60591ae5">{{"共"+totalNum+"张，合计："}}</text><text class="flow-money col-m data-v-60591ae5">{{"￥"+$root.g1}}</text></view></view><view class="pay-method flow-all-money b-f m-top20 data-v-60591ae5"><view class="flow-all-list dis-flex data-v-60591ae5"><text class="flex-five data-v-60591ae5">支付方式</text></view><view data-event-opts="{{[['tap',[['handleSelectPayType',['$0'],['PayTypeEnum.WECHAT.value']]]]]}}" class="pay-item dis-flex flex-x-between data-v-60591ae5" bindtap="__e"><view class="item-left dis-flex flex-y-center data-v-60591ae5"><view class="item-left_icon wechat data-v-60591ae5"><text class="iconfont icon-weixinzhifu data-v-60591ae5"></text></view><view class="item-left_text data-v-60591ae5"><text class="data-v-60591ae5">{{PayTypeEnum.WECHAT.name}}</text></view></view><block wx:if="{{curPayType==PayTypeEnum.WECHAT.value}}"><view class="item-right col-m data-v-60591ae5"><text class="iconfont icon-duihao data-v-60591ae5"></text></view></block></view></view><view class="flow-all-money b-f m-top20 data-v-60591ae5"><view class="ipt-wrapper data-v-60591ae5"><textarea rows="3" maxlength="100" placeholder="买家留言 (选填,100字以内)" type="text" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e" class="data-v-60591ae5"></textarea></view></view><view class="flow-fixed-footer b-f m-top10 data-v-60591ae5"><view class="dis-flex chackout-box data-v-60591ae5"><view class="chackout-left pl-12 data-v-60591ae5"><view class="col-amount-do data-v-60591ae5">{{"应付金额：￥"+$root.g2}}</view><view class="col-amount-view data-v-60591ae5">{{"实得金额：￥"+$root.g3}}</view></view><view data-event-opts="{{[['tap',[['onSubmitOrder']]]]}}" class="chackout-right data-v-60591ae5" bindtap="__e"><view class="{{['flow-btn','f-32','data-v-60591ae5',(disabled)?'disabled':'']}}">提交订单</view></view></view></view></view>