






























































































































































































































































































/* 在这里引入自定义样式 */

/* 链接和图片效果 */
._a {
    display: inline;
    padding: 1.5px 0 1.5px 0;
    color: #366092;
    word-break: break-all;
}
._hover {
    text-decoration: underline;
    opacity: 0.7;
}
._img {
    /* display: inline-block; */
display: block;
    max-width: 100%;
    overflow: hidden;
}
:host {
    display: inline;
}
.interlayer {
    display: inherit;
    flex-direction: inherit;
    flex-wrap: inherit;
    align-content: inherit;
    align-items: inherit;
    justify-content: inherit;
    width: 100%;
    white-space: inherit;
}
._b,
._strong {
    font-weight: bold;
}
._blockquote,
._div,
._p,
._ol,
._ul,
._li {
    display: block;
}
._code {
    font-family: monospace;
}
._del {
    text-decoration: line-through;
}
._em,
._i {
    font-style: italic;
}
._h1 {
    font-size: 2em;
}
._h2 {
    font-size: 1.5em;
}
._h3 {
    font-size: 1.17em;
}
._h5 {
    font-size: 0.83em;
}
._h6 {
    font-size: 0.67em;
}
._h1,
._h2,
._h3,
._h4,
._h5,
._h6 {
    display: block;
    font-weight: bold;
}
._image {
    display: block;
    width: 100%;
    height: 360px;
    margin-top: -360px;
    opacity: 0;
}
._ins {
    text-decoration: underline;
}
._li {
    flex: 1;
    width: 0;
}
._ol-bef {
    width: 36px;
    margin-right: 5px;
    text-align: right;
}
._ul-bef {
    display: block;
    margin: 0 12px 0 23px;
    line-height: normal;
}
._ol-bef,
._ul-bef {
    flex: none;
    -webkit-user-select: none;
            user-select: none;
}
._ul-p1 {
    display: inline-block;
    width: 0.3em;
    height: 0.3em;
    overflow: hidden;
    line-height: 0.3em;
}
._ul-p2 {
    display: inline-block;
    width: 0.23em;
    height: 0.23em;
    border: 0.05em solid black;
    border-radius: 50%;
}
._q::before {
    content: '"';
}
._q::after {
    content: '"';
}
._sub {
    font-size: smaller;
    vertical-align: sub;
}
._sup {
    font-size: smaller;
    vertical-align: super;
}
.__bdo,
.__bdi,
.__ruby,
.__rt {
    display: inline-block;
}
._video {
    position: relative;
    display: inline-block;
    width: 300px;
    height: 225px;
    background-color: black;
}
._video::after {
    position: absolute;
    top: 50%;
    left: 50%;
    margin: -15px 0 0 -15px;
    content: '';
    border-color: transparent transparent transparent white;
    border-style: solid;
    border-width: 15px 0 15px 30px;
}

