{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/goods.vue?9181", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/goods.vue?b299", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/goods.vue?da2a", "uni-app:///pages/settlement/goods.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/goods.vue?de64", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/goods.vue?1e63"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "selectSwitch", "data", "defaultOrderMode", "PayTypeEnum", "options", "curPayType", "hafanBalance", "remark", "disabled", "onLoad", "onShow", "app", "savedOrderMode", "userInfo", "console", "methods", "getUserInfo", "UserApi", "result", "getCartList", "orderMode", "CartApi", "then", "resolve", "catch", "getDefaultAddress", "AddressApi", "handleShowPoints", "handleShowPopup", "handleSelectCoupon", "handleNotUseCoupon", "checkCanUseCoupon", "onSelectAddress", "from", "switchTakeMode", "switchMode", "getStoreInfo", "<PERSON><PERSON><PERSON>", "onSubmitOrder", "doSubmitOrder", "uni", "tmplIds", "success", "fail", "complete", "SettlementApi", "onSubmitCallback", "navToOrderResult", "message", "onPullDownRefresh", "setTimeout", "handleReservationChange", "initDateTimePicker", "tomorrow", "label", "value", "defaultHour", "defaultDateIndex", "defaultMinuteIndex", "formatDate", "formatDateValue", "onPickerChange", "cancelReservation", "confirmReservation"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mNAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACsU9pB;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACAC;EACA;EACAC;IAAA;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IAAA,mDAEA,yDACA,uDAEA,uDACA,oDACA,oDACA,uDACA,yDACA,mEAEA,wDACA,sDACA,4DACA,6DACA,mDACA,oDACA,0DACA,0DAEA,yDAEA,8DAEA,qDAEA,yDACA,2DAEA,uDAEA,4DAEA,6DAEA,+DACA,iEACA,2DACA,4DACA,qDACA,uDACA;EAEA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cACA;gBACAA;cACA;;cAEA;cACAC;cACA;gBACA;gBACAD;gBACAA;cACA;;cAEA;cAAA;cAAA,OACAA;YAAA;cAAAE;cACAC;cACA;gBACAH;cACA;;cAEA;cACAA;cACA;cACAA;cACA;cACAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EAEAI;IAEA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBAAA,kCAKAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;QACAR;QACAA;MACA;MACA;QACA;QACA;QACA;UACAS;QACA;QACAC,8IACAC;UACAX;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;UACAA;UACAA;UACAY;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;MACAC,qBACAJ;QACAX;MACA;IACA;IAEA;IACAgB;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;QACAlB;QACA;MACA;MACAA;MACA;MACAA;MACA;MACAA;IACA;IAEA;IACAmB;MACA;MACAnB;MACA;MACAA;MACA;MACAA;IACA;IAGA;IACAoB;MACA;IACA;IAEA;IACAC;MACA;QAAAC;MAAA;IACA;IAEAC;MACApB;MACA;IACA;IAEA;IACAqB;MACA;MACAxB;MACA;QACAA;MACA;MACAA;IACA;IAEA;IACAyB;MACA;MACA;QACAC,yBACAf;UACAX;QACA;MACA;IACA;IAEA;IACA2B;MACA;MACA;QACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;QACA3B;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACA;QACAS;MACA;;MAEA;MACAT;IACA;IAEA;IACA4B;MACA;;MAEA;MACA5B;;MAEA;MACA;QACA;QACA;UACAA;UACA;QACA;MACA;MAEA;QACAA;QACA;MACA;MAEA;QACAA;QACAA;QACA;MACA;;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;MACA;QACAS;MACA;;MAEA;MACAT;MACA;;MAEA;MACA;MACA;;MAEA;MACA6B;QACAC;QACAC;QACAC;QACAC;UACA;UACAC,qOACAvB;YACAX;UACA,GACAa;YACA;cACA;cACA;gBACAb;gBACA;cACA;YACA;YACAA;UACA;QACA;MACA;IACA;IAEA;IACAmC;MACAhC;MACA;MACA;QACA;UACAH;QACA;UACAA;QACA;QACAA;QACA;MACA;MAEA;MACA;QACAA;QACA;MACA;;MAEA;MACA;QAIA;UACAA;QACA;UACAA;QACA;UACAA;UACAA;QACA;MACA;;MAEA;MACA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;UACAA;QACA;QACA;MACA;;MAEA;MACA;QACAA;QACAA;MACA;IACA;IAEA;IACAoC;MACA;QACAC;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACAC;QACA;QACAvC;QACA;QACAA;QACA;QACAA;QACA6B;MACA;IACA;IAEA;IACAW;MACA;QACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACAC;MAEA;QACAC;QACAC;MACA;MACA;QACAD;QACAC;MACA;;MAEA;MACA;MACA;QACA;QACA;UACAD;UACAC;QACA;MACA;;MAEA;MACA;MACA;QACA;QACA;UACAD;UACAC;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACAC;QACAC;MACA;;MAEA;MACA;MACA;QACAC;QACAF;QACA;UACAA;UACAC;QACA;MACA;MAEA;IACA;IAEA;IACAE;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MAEA;MACA;MACA;MAEA;;MAEA;MACA;MACA;MACA;MACA;MACA;MAEA;QACA;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC13BA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/settlement/goods.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/settlement/goods.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./goods.vue?vue&type=template&id=38cab12e&scoped=true&\"\nvar renderjs\nimport script from \"./goods.vue?vue&type=script&lang=js&\"\nexport * from \"./goods.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goods.vue?vue&type=style&index=0&id=38cab12e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"38cab12e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/settlement/goods.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=template&id=38cab12e&scoped=true&\"", "var components\ntry {\n  components = {\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-switch/u-switch\" */ \"@/uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-modal/u-modal\" */ \"@/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !(_vm.curPayType === \"HAFAN\") ? _vm.couponList.length : null\n  var g1 =\n    !(_vm.curPayType === \"HAFAN\") && g0 > 0 && !_vm.useCouponInfo\n      ? _vm.couponList.length\n      : null\n  var g2 = _vm.payPrice ? _vm.payPrice.toFixed(2) : null\n  var g3 =\n    _vm.hafanBalance !== null ? (_vm.hafanBalance / 100).toFixed(2) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showRemarkInput = true\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <!-- 订单模式选择：到店自提/配送到家 -->\r\n    <!-- <view class=\"flow-mode\">\r\n      <selectSwitch :switchList=\"orderModeList\" checked_bj_color=\"#232e5d\" :defaultSwitch=\"defaultOrderMode\" @change=\"switchMode\"/>\r\n    </view>\r\n     -->\r\n    <!-- 堂食/外带选择 -->\r\n    <view v-if=\"orderMode == 1\" class=\"flow-mode\">\r\n      <view class=\"flex-row justify-between items-center\">\r\n        <!-- <view class=\"flex-row items-center\">\r\n          <text class=\"store-name\">{{ storeInfo ? storeInfo.name : '门店信息' }}</text>\r\n        </view> -->\r\n        <!-- <view class=\"section_3\">\r\n          <view class=\"text-wrapper_2\" :class=\"takeMode ? 'active-left' : 'active-right'\">\r\n            <text class=\"text_3\">{{ takeMode ? '堂食' : '外带' }}</text>\r\n          </view>\r\n          <text class=\"text_4\" @click=\"switchTakeMode(!takeMode)\">{{ takeMode ? '外带' : '堂食' }}</text>\r\n        </view> -->\r\n      </view>\r\n      \r\n      <!-- 使用原来的selectSwitch组件 -->\r\n      <view style=\"margin-top: 20rpx; text-align: center;\">\r\n        <selectSwitch :switchList=\"['堂食','外带']\" checked_bj_color=\"#232e5d\" @change=\"switchTakeMode\"/>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 快递配送：配送地址 -->\r\n    <view @click=\"onSelectAddress\" v-if=\"orderMode == false\" class=\"flow-delivery\">\r\n      <view class=\"flow-delivery__detail\">\r\n        <view class=\"detail-location\">\r\n          <text class=\"iconfont icon-dingwei\"></text>\r\n        </view>\r\n        <view class=\"detail-content\">\r\n          <block v-if=\"address\">\r\n            <view class=\"detail-content__title\">\r\n              <text>{{ address.name }}</text>\r\n              <text class=\"detail-content__title-phone\">{{ address.mobile }}</text>\r\n            </view>\r\n            <view class=\"address-info detail-content__describe\">\r\n              <text class=\"region\">{{ address.provinceName }}{{ address.cityName }}{{ address.regionName }}</text>\r\n              <text class=\"detail\">{{ address.detail }}</text>\r\n              <text class=\"icon\"> » </text>\r\n            </view>\r\n          </block>\r\n          <block v-else>\r\n            <view class=\"detail-content__describe select-address\">\r\n              <text>请选择配送地址 <text class=\"icon\"> > </text></text>\r\n            </view>\r\n          </block>\r\n        </view>\r\n        <view class=\"detail-arrow\">\r\n          <text class=\"iconfont icon-arrow-right\"></text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 门店自提：自提地址 -->\r\n    <view v-if=\"orderMode == true\" class=\"flow-delivery\">\r\n      <view class=\"flow-delivery__detail\">\r\n        <view class=\"detail-location\">\r\n           <text class=\"iconfont icon-dingwei\"></text>\r\n        </view>\r\n        <view class=\"detail-content\">\r\n          <view class=\"store\">\r\n            <view class=\"store-name\">{{ storeInfo ? storeInfo.name : '' }}</view>\r\n            <view class=\"store-phone\">{{ storeInfo ? storeInfo.phone : '' }}</view>\r\n            <text class=\"store-address\">{{ storeInfo ? storeInfo.address : '' }}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"detail-arrow\">\r\n          <text class=\"iconfont icon-arrow-right\"></text>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 商品详情 -->\r\n    <view class=\"goods-detail-header\">\r\n      <view class=\"section-title\">商品详情</view>\r\n      <view class=\"section-subtitle\">注意选对温度和甜度</view>\r\n    </view>\r\n    \r\n    <!-- 商品列表 -->\r\n    <view class=\"checkout_list\">\r\n      <view v-for=\"(item, index) in goodsCart\" :key=\"index\" class=\"flow-shopList\">\r\n        <!-- 商品图片 -->\r\n        <view class=\"flow-list-left\">\r\n          <image mode=\"scaleToFill\" :src=\"item.goodsInfo.logo\"></image>\r\n        </view>\r\n        <view class=\"flow-list-right\">\r\n          <!-- 商品名称 -->\r\n          <text class=\"goods-name\">{{ item.goodsInfo.name }}</text>\r\n          \r\n         <!-- 普通商品规格 -->\r\n         <view v-if=\"!item.isPackage || item.isPackage !== 'Y'\" class=\"goods-props\">\r\n           <view class=\"goods-props-item\" v-for=\"(props, idx) in item.specList\" :key=\"idx\">\r\n             <text class=\"group-name\">{{ props.specName }}: </text>\r\n             <text>{{ props.specValue }}；</text>\r\n           </view>\r\n         </view>\r\n         <!-- 套餐商品信息 -->\r\n         <view v-if=\"item.isPackage === 'Y' && item.packageGroups\" class=\"goods-props\">\r\n           <view class=\"goods-props-item\" v-for=\"(group, groupIndex) in item.packageGroups\" :key=\"groupIndex\">\r\n             <text class=\"group-name\">{{ group.groupName }}: </text>\r\n             <view class=\"group-items\">\r\n               <view class=\"goods-props-item\" v-for=\"(packageItem, itemIndex) in group.items\" :key=\"itemIndex\">\r\n                 <text class=\"group-name\">{{ packageItem.itemName }}</text>\r\n                 <text class=\"item-spec\" v-if=\"packageItem.selectedSkuText\">{{ packageItem.selectedSkuText }}</text>\r\n                 <text class=\"item-quantity\">x{{ packageItem.quantity }}</text>\r\n               </view>\r\n             </view>\r\n           </view>\r\n         </view>\r\n          \r\n         <!-- 商品数量和单价 -->\r\n         <view class=\"flow-list-cont\">\r\n           <text class=\"small\">x{{ item.num }}</text>\r\n           <text class=\"flow-cont\">￥{{ item.goodsInfo.price }}</text>\r\n         </view>\r\n        </view>\r\n      </view>\r\n      \r\n      <!-- 商品统计 -->\r\n      <view class=\"flow-num-box\">\r\n        <text>共{{ totalNum }}件商品，</text>\r\n        <text>合计</text>\r\n        <text class=\"flow-money\">￥{{ totalPrice }}</text>\r\n      </view>\r\n    </view>\r\n    \r\n\r\n    <!-- 费用明细 -->\r\n    <view class=\"flow-all-money\">\r\n      <view class=\"detail-title\">费用明细</view>\r\n      <!-- 卡券 -->\r\n      <view class=\"flow-all-list\">\r\n        <text>优惠券</text>\r\n        <view>\r\n          <view v-if=\"curPayType === 'HAFAN'\">\r\n            <text class=\"col-gray\">Hafan支付不可使用卡券</text>\r\n          </view>\r\n          <view v-else-if=\"couponList.length > 0\" @click=\"handleShowPopup()\">\r\n            <text class=\"col-m\" v-if=\"useCouponInfo\">-￥{{ useCouponInfo.amount }}</text>\r\n            <text v-else>共有卡券{{ couponList.length }}张</text>\r\n            <text class=\"right-arrow iconfont icon-arrow-right\"></text>\r\n          </view>\r\n          <text v-else>暂无可用</text>\r\n        </view>\r\n      </view>\r\n      <!-- 积分抵扣 -->\r\n      <view class=\"flow-all-list\" v-if=\"usePoint > 0\">\r\n        <view @click=\"handleShowPoints()\">\r\n          <text>使用{{ usePoint }}积分抵扣</text>\r\n          <text class=\"iconfont icon-help\"></text>\r\n        </view>\r\n        <view>\r\n          <text class=\"col-m\">-￥{{ usePointAmount }}</text>\r\n          <u-switch v-model=\"isUsePoints\" size=\"48\" active-color=\"#3f51b5\" @change=\"getCartList()\"></u-switch>\r\n        </view>\r\n      </view>\r\n      <!-- 备注 -->\r\n      <view class=\"flow-all-list\">\r\n        <text>备注</text>\r\n        <view @click=\"showRemarkInput = true\">\r\n          <text v-if=\"remark\">{{ remark }}</text>\r\n          <text v-else class=\"col-gray\">口味、包装等要求</text>\r\n          <text class=\"right-arrow iconfont icon-arrow-right\"></text>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 预约取餐 -->\r\n      <view class=\"flow-all-list\" v-if=\"orderMode == true\">\r\n        <text>预约取餐</text>\r\n        <view>\r\n          <text v-if=\"isReservation && reservationTime\" class=\"reservation-time\">{{ reservationTime }}</text>\r\n          <text v-else class=\"col-gray\">立即制作</text>\r\n          <u-switch v-model=\"isReservation\" size=\"48\" active-color=\"#3f51b5\" @change=\"handleReservationChange\"></u-switch>\r\n        </view>\r\n      </view>\r\n      <!-- 会员折扣 -->\r\n      <view class=\"flow-all-list\">\r\n        <text>会员支付折扣</text>\r\n        <text>{{ (memberDiscount > 0) ? '￥' + memberDiscount : '无折扣' }}</text>\r\n      </view>\r\n      <!-- 运费 -->\r\n      <view class=\"flow-all-list\" v-if=\"deliveryFee > 0 && orderMode == false\">\r\n        <text>配送费用</text>\r\n        <text class=\"col-m\">￥{{ deliveryFee }}</text>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 买家留言 -->\r\n    <view class=\"flow-all-money\" v-if=\"showRemarkInput || remark\">\r\n      <view class=\"ipt-wrapper\">\r\n        <textarea v-model=\"remark\" rows=\"3\" maxlength=\"100\" placeholder=\"买家留言 (选填,100字以内)\"></textarea>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 提交订单 -->\r\n    <view class=\"flow-fixed-footer\">\r\n      <view class=\"chackout-box\">\r\n        <view class=\"chackout-left\">\r\n          <view class=\"col-amount-do\">支付金额<text class=\"pay-amount\">￥{{ payPrice ? payPrice.toFixed(2) : '0.00' }}</text>\r\n          </view>\r\n        </view>\r\n        <view class=\"chackout-right\" @click=\"onSubmitOrder()\">\r\n          <view class=\"flow-btn\" :class=\"{ disabled }\">支付</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \r\n     <!-- 积分说明弹窗 -->\r\n        <u-modal v-model=\"showPoints\" :title=\"`积分说明`\">\r\n          <scroll-view class=\"points-content\" :scroll-y=\"true\">\r\n            <text>积分兑换金额</text>\r\n          </scroll-view>\r\n        </u-modal>\r\n    \r\n        <!-- 卡券弹出框 -->\r\n        <u-popup v-model=\"showPopup\" mode=\"bottom\">\r\n          <view class=\"popup__coupon\">\r\n            <view class=\"coupon__title f-30\">选择卡券</view>\r\n            <!-- 卡券列表 -->\r\n            <view class=\"coupon-list\">\r\n              <scroll-view :scroll-y=\"true\" style=\"height: 565rpx;\">\r\n                <view class=\"coupon-item\" v-for=\"(item, index) in couponList\" :key=\"index\">\r\n                  <view class=\"item-wrapper\"\r\n                    :class=\"[item.status == 'A' ? 'color-default': 'color-gray']\"\r\n                    @click=\"handleSelectCoupon(index)\">\r\n                    <view class=\"coupon-type\">{{ item.type }}</view>\r\n                    <view class=\"tip dis-flex flex-dir-column flex-x-center\">\r\n                      <text class=\"money\">￥{{ item.amount }}</text>\r\n                      <text class=\"pay-line\">{{ item.description }}</text>\r\n                    </view>\r\n                    <view class=\"split-line\"></view>\r\n                    <view class=\"content dis-flex flex-dir-column flex-x-between\">\r\n                      <view class=\"title\">{{ item.name }}</view>\r\n                      <view class=\"bottom dis-flex flex-y-center\">\r\n                        <view class=\"time flex-box\">\r\n                          <block>{{ item.effectiveDate }}</block>\r\n                        </view>\r\n                      </view>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </scroll-view>\r\n            </view>\r\n            <!-- 不使用卡券 -->\r\n            <view class=\"coupon__do_not dis-flex flex-y-center flex-x-center\">\r\n              <view class=\"control dis-flex flex-y-center flex-x-center\" @click=\"handleNotUseCoupon()\">\r\n                <text class=\"f-26\">不使用卡券</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </u-popup>\r\n        \r\n        <!-- 支付方式弹窗 -->\r\n        <u-popup v-model=\"showPayPopup\" mode=\"bottom\" :closeable=\"true\">\r\n          <view class=\"pay-type-popup\">\r\n            <view class=\"title\">请选择支付方式</view>\r\n            <view class=\"pop-content\">\r\n              <!-- 微信支付 -->\r\n              <view class=\"pay-item dis-flex flex-x-between\" @click=\"doSubmitOrder(PayTypeEnum.WECHAT.value)\">\r\n                <view class=\"item-left dis-flex flex-y-center\">\r\n                  <view class=\"item-left_icon wechat\">\r\n                    <text class=\"iconfont icon-weixinzhifu\"></text>\r\n                  </view>\r\n                  <view class=\"item-left_text\">\r\n                    <text>{{ PayTypeEnum.WECHAT.name }}</text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              <!-- Hafan支付 -->\r\n              <view class=\"pay-item dis-flex flex-x-between\" @click=\"doSubmitOrder('HAFAN')\">\r\n                <view class=\"item-left dis-flex flex-y-center\">\r\n                  <view class=\"item-left_icon balance\">\r\n                    <text class=\"iconfont icon-qiandai\"></text>\r\n                  </view>\r\n                  <view class=\"item-left_text\">\r\n                    <text>火炬币</text>\r\n                    <text class=\"balance-amount\" v-if=\"hafanBalance !== null\">\r\n                      (可用: ¥{{ (hafanBalance / 100).toFixed(2) }})\r\n                    </text>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </u-popup>\r\n\r\n        <!-- 预约时间选择弹窗 -->\r\n        <u-popup v-model=\"showReservationPopup\" mode=\"bottom\" :closeable=\"true\">\r\n          <view class=\"reservation-popup\">\r\n            <view class=\"popup-header\">\r\n              <text class=\"popup-title\">选择预约时间</text>\r\n              <text class=\"popup-subtitle\">请选择24小时内的取餐时间</text>\r\n            </view>\r\n            <view class=\"datetime-picker\">\r\n              <picker-view :value=\"pickerValue\" @change=\"onPickerChange\" class=\"picker-view\">\r\n                <picker-view-column>\r\n                  <view v-for=\"(item, index) in dateList\" :key=\"index\" class=\"picker-item\">\r\n                    {{ item.label }}\r\n                  </view>\r\n                </picker-view-column>\r\n                <picker-view-column>\r\n                  <view v-for=\"(item, index) in hourList\" :key=\"index\" class=\"picker-item\">\r\n                    {{ item.label }}\r\n                  </view>\r\n                </picker-view-column>\r\n                <picker-view-column>\r\n                  <view v-for=\"(item, index) in minuteList\" :key=\"index\" class=\"picker-item\">\r\n                    {{ item.label }}\r\n                  </view>\r\n                </picker-view-column>\r\n              </picker-view>\r\n            </view>\r\n            <view class=\"popup-buttons\">\r\n              <view class=\"btn-cancel\" @click=\"cancelReservation\">取消</view>\r\n              <view class=\"btn-confirm\" @click=\"confirmReservation\">确定</view>\r\n            </view>\r\n          </view>\r\n        </u-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import * as Verify from '@/utils/verify'\r\n  import * as CartApi from '@/api/cart'\r\n  import * as SettlementApi from '@/api/settlement'\r\n  import DeliveryTypeEnum from '@/common/enum/order/DeliveryType'\r\n  import PayTypeEnum from '@/common/enum/order/PayType'\r\n  import { wxPayment } from '@/utils/app'\r\n  import selectSwitch from \"@/components/xuan-switch/xuan-switch.vue\"\r\n  import * as AddressApi from '@/api/address'\r\n  import * as settingApi from '@/api/setting'\r\n  import storage from '@/utils/storage'\r\n  import * as UserApi from '@/api/user'\r\n\r\n  export default {\r\n    components: {\r\n      selectSwitch\r\n    },\r\n    data() {\r\n      return {\r\n        // 默认配送方式\r\n        defaultOrderMode: true,\r\n        // 枚举类\r\n        PayTypeEnum,\r\n        // 当前页面参数\r\n        options: {},\r\n        // 当前选中的支付方式\r\n        curPayType: PayTypeEnum.WECHAT.value,\r\n        // Hafan钱包余额\r\n        hafanBalance: null,\r\n        // 买家留言\r\n        remark: '',\r\n        // 禁用submit按钮\r\n        disabled: false,\r\n        // 按钮禁用\r\n        disabled: false,\r\n        goodsCart: [],\r\n        // 优惠券列表\r\n        couponList: [],\r\n        totalPrice: 0,\r\n        payPrice: 0,\r\n        totalNum: 0,\r\n        deliveryFee: 0,\r\n        orderModeList: ['到店自提', '配送到家'],\r\n\t\t// orderModeList: ['到店自提'],\r\n\t\ttakeMode: true,\r\n        orderMode: true,\r\n        address: null,\r\n        useCouponInfo: null,\r\n        selectCouponId: 0,\r\n        myPoint: 0,\r\n        usePoint: 0,\r\n        usePointAmount: 0.00,\r\n        // 是否使用积分抵扣\r\n        isUsePoints: true,\r\n        // 是否显示积分说明\r\n        showPoints: false,\r\n        // 会员折扣\r\n        memberDiscount: 0,\r\n        // 是否显示卡券弹窗\r\n        showPopup: false,\r\n        storeInfo: null,\r\n        // 支付方式弹窗\r\n        showPayPopup: false,\r\n        // 订单ID\r\n        orderId: \"\",\r\n        // 显示备注输入框\r\n        showRemarkInput: false,\r\n        // 预约取餐相关\r\n        isReservation: false,\r\n        reservationTime: '',\r\n        showReservationPopup: false,\r\n        pickerValue: [0, 0, 0],\r\n        dateList: [],\r\n        hourList: [],\r\n        minuteList: []\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 生命周期函数--监听页面加载\r\n     */\r\n    onLoad(options) {\r\n      this.options = options;\r\n    },\r\n\r\n    /**\r\n     * 生命周期函数--监听页面显示\r\n     */\r\n    async onShow() {\r\n       const app = this;\r\n       if (app.orderId) {\r\n           app.navToOrderResult(app.orderId);\r\n       }\r\n\r\n       // 从storage中获取订单模式\r\n       const savedOrderMode = storage.get('current_order_mode')\r\n       if (savedOrderMode) {\r\n           // 设置默认的订单模式和开关状态\r\n           app.defaultOrderMode = savedOrderMode === 'oneself'\r\n           app.orderMode = app.defaultOrderMode\r\n       }\r\n\r\n       // 获取火炬币\r\n       const userInfo = await app.getUserInfo();\r\n       console.log('userInfo :>> ', userInfo);\r\n       if (userInfo && userInfo.hafanInfo && userInfo.hafanInfo.wallet) {\r\n           app.hafanBalance = userInfo.hafanInfo.wallet.balance;\r\n       }\r\n\r\n       // 获取购物车信息\r\n       app.getCartList();\r\n       // 获取默认收货地址\r\n       app.getDefaultAddress();\r\n       // 获取店铺信息\r\n       app.getStoreInfo();\r\n    },\r\n\r\n    methods: {\r\n      \r\n      // 获取当前用户信息\r\n      async getUserInfo() { \r\n        const result = await UserApi.info(); \r\n        // app.hafanInfo = result.data.hafanInfo; \r\n        // app.memberGrade = result.data.memberGrade;\r\n        // app.gradeEndTime = result.data.gradeEndTime;\r\n        // app.isMerchant = result.data.isMerchant;   \r\n        return result.data;\r\n      },\r\n      // 获取购物车信息\r\n      getCartList() {\r\n        const app = this\r\n        if (!app.isUsePoints) {\r\n            app.usePoint = 0;\r\n            app.usePointAmount = 0;\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n          // 配送或自取\r\n          let orderMode = \"oneself\";\r\n          if (!app.orderMode) {\r\n              orderMode = \"express\";\r\n          }\r\n          CartApi.list(app.options.cartIds, app.options.goodsId, app.options.skuId, app.options.buyNum, app.selectCouponId, app.isUsePoints, orderMode)\r\n            .then(result => {\r\n              app.goodsCart = result.data.list;\r\n              app.totalNum = result.data.totalNum;\r\n              app.totalPrice = result.data.totalPrice;\r\n              app.payPrice = result.data.payPrice;\r\n              app.couponList = result.data.couponList;\r\n              app.useCouponInfo = result.data.useCouponInfo;\r\n              app.usePoint = result.data.usePoint;\r\n              app.myPoint = result.data.myPoint;\r\n              app.deliveryFee = result.data.deliveryFee;\r\n              if (app.usePoint < 1) {\r\n                  app.isUsePoints = false;\r\n              }\r\n              app.usePointAmount = result.data.usePointAmount;\r\n              app.memberDiscount = result.data.memberDiscount ? result.data.memberDiscount : 0;\r\n              resolve(result);\r\n            })\r\n            .catch(err => reject(err))\r\n        })\r\n      },\r\n      \r\n      // 获取默认收货地址\r\n      getDefaultAddress() {\r\n        const app = this\r\n        AddressApi.detail(0)\r\n          .then(result => {\r\n              app.address = result.data.address ? result.data.address : null;\r\n          })\r\n      },\r\n      \r\n      // 显示积分说明\r\n      handleShowPoints() {\r\n          this.showPoints = true;\r\n      },\r\n\r\n      // 显示卡券弹窗\r\n      handleShowPopup() {\r\n          this.showPopup = true;\r\n      },\r\n\r\n      // 选择卡券\r\n      handleSelectCoupon(index) {\r\n          const app = this;\r\n          // 当前选择的卡券\r\n          const couponItem = app.couponList[index];\r\n          // 记录选中的卡券id\r\n          if (couponItem.status != 'A') {\r\n              app.$error('该卡券不可用');\r\n              return false;\r\n          }\r\n          app.selectCouponId = couponItem.userCouponId;\r\n          // 重新获取购物车信息\r\n          app.getCartList();\r\n          // 隐藏卡券弹层\r\n          app.showPopup = false;\r\n      },\r\n\r\n      // 不使用卡券\r\n      handleNotUseCoupon() {\r\n          const app = this;\r\n          app.selectCouponId = 0;\r\n          // 重新获取购物车信息\r\n          app.getCartList();\r\n          // 隐藏卡券弹层\r\n          app.showPopup = false;\r\n      },\r\n      \r\n\r\n      // 检查是否可以使用优惠券\r\n      checkCanUseCoupon() {\r\n        return this.curPayType !== 'HAFAN';\r\n      },\r\n      \r\n      // 快递配送：选择收货地址\r\n      onSelectAddress() {\r\n         this.$navTo('pages/address/index', { from: 'checkout' })\r\n      },\r\n\t  \r\n\t  switchTakeMode(mode) {\r\n\t\t  console.log(mode);\r\n\t     this.takeMode = mode;\r\n\t  },\r\n      \r\n      // 切换配送模式\r\n      switchMode(mode) {\r\n         const app = this;\r\n         app.orderMode = mode;\r\n         if (mode && !app.storeInfo) {\r\n             app.getStoreInfo();\r\n         }\r\n         app.getCartList();\r\n      },\r\n      \r\n      // 获取店铺详情\r\n      getStoreInfo() {\r\n         const app = this;\r\n         if (!app.storeInfo) {\r\n             settingApi.storeDetail()\r\n             .then(result => {\r\n                 app.storeInfo = result.data.storeInfo;\r\n             })\r\n         }\r\n      },\r\n      \r\n      // 弹出支付方式\r\n      onSubmitOrder() {\r\n          const app = this\r\n          if (app.disabled) {\r\n              return false\r\n          }\r\n          \r\n          // const tableId = uni.getStorageSync(\"tableId\") ? uni.getStorageSync(\"tableId\") : 0;\r\n          // if (tableId > 0) {\r\n          //     return app.doSubmitOrder(PayTypeEnum.WECHAT.value);\r\n          // }\r\n          \r\n          if (app.totalPrice < 0 || app.goodsCart.length < 1) {\r\n              app.disabled = true\r\n              return false\r\n          }\r\n          \r\n          // 表单验证\r\n          if (!app.orderMode && app.address == undefined) {\r\n              app.$toast('请先选择配送地址哦')\r\n              return false\r\n          }\r\n          \r\n          // 配送或自取\r\n          let orderMode = \"oneself\";\r\n          if (!app.orderMode) {\r\n              orderMode = \"express\";\r\n          }\r\n\r\n          // 弹出支付方式选择弹窗\r\n          app.showPayPopup = true;\r\n      },\r\n\r\n      // 订单提交\r\n      doSubmitOrder(payType) {\r\n        const app = this;\r\n        \r\n        // 关闭支付方式弹窗\r\n        app.showPayPopup = false;\r\n        \r\n        // 如果是Hafan支付，检查余额是否足够\r\n        if (payType === 'HAFAN') {\r\n          const balance = app.hafanBalance / 100;\r\n          if (balance < app.payPrice) {\r\n            app.$toast('火炬币不足，请选择其他支付方式');\r\n            return false;\r\n          }\r\n        }\r\n        \r\n        if (app.disabled) {\r\n            app.$toast('请勿重复提交订单');\r\n            return false;\r\n        }\r\n        \r\n        if (app.totalPrice < 0 || app.goodsCart.length < 1) {\r\n            app.$toast('提交订单商品有误');\r\n            app.disabled = true;\r\n            return false;\r\n        }\r\n        \r\n        // 表单验证\r\n        if (!app.orderMode && app.address == undefined) {\r\n            app.$toast('请先选择配送地址哦');\r\n            return false;\r\n        }\r\n        \r\n        // 配送或自取\r\n        let orderMode = \"oneself\";\r\n        if (!app.orderMode) {\r\n            orderMode = \"express\";\r\n        }\r\n        \r\n        // 按钮禁用\r\n        app.disabled = true;\r\n        const takeMode = app.takeMode? '堂食' : '外带';\r\n\r\n        // 预约参数\r\n        const isReservation = app.isReservation ? 'Y' : 'N';\r\n        const reservationTime = app.isReservation ? app.reservationTime : '';\r\n\r\n        // 请求订阅消息\r\n        uni.requestSubscribeMessage({\r\n          tmplIds: ['iN-nhCWdmmaP2gAS69lFbjHCHbSHU3F4en1vVWGFLTs','eTqcnyNUQAm2fkMddmEU3RwuJozteKz7A2Acdzh_6y8','kmdYCkoClvvaFts0hFACpeda7-FQT1TjBbWwGZLpY5s'],\r\n          success (res) { },\r\n          fail  (res) { },\r\n          complete(){\r\n            // 请求api\r\n            SettlementApi.submit(0, \"\", \"goods\", app.remark, 0, app.usePoint, app.selectCouponId, app.options.cartIds, app.options.goodsId, app.options.skuId, app.options.buyNum, orderMode, payType, takeMode, isReservation, reservationTime)\r\n              .then(result => {\r\n                  app.onSubmitCallback(result);\r\n              })\r\n              .catch(err => {\r\n                if (err.result) {\r\n                    const errData = err.result.data;\r\n                    if (errData.isCreated) {\r\n                        app.navToOrderResult(errData.orderInfo.id);\r\n                        return false;\r\n                    }\r\n                }\r\n                app.disabled = false;\r\n              })\r\n          }\r\n        })\r\n      },\r\n\r\n      // 订单提交成功后回调\r\n      onSubmitCallback(result) {\r\n        console.log('result :>> ', result);\r\n        const app = this;\r\n        if (result.code != '200' && !result.data) {\r\n            if (result.message) {\r\n                app.$error(result.message);\r\n            } else {\r\n                app.$error('订单提交失败');\r\n            }\r\n            app.disabled = false;\r\n            return false;\r\n        }\r\n        \r\n        const tableId = uni.getStorageSync(\"tableId\") ? uni.getStorageSync(\"tableId\") : 0;\r\n        if (tableId > 0) {\r\n            app.navToOrderResult(result.data.orderInfo.id, '订单提交成功');\r\n            return false;\r\n        }\r\n        \r\n        // 发起微信支付\r\n        if (result.data.payType == PayTypeEnum.WECHAT.value) {\r\n            // #ifdef H5\r\n            app.orderId = result.data.orderInfo.id;\r\n            // #endif\r\n            wxPayment(result.data.payment).then(() => {\r\n                app.$success('支付成功');\r\n            }).catch(err => {\r\n                app.$error('支付失败');\r\n            }).finally(() => {\r\n                app.disabled = false;\r\n                app.navToOrderResult(result.data.orderInfo.id, '');\r\n            })\r\n        }\r\n        \r\n        // Hafan支付\r\n        if (result.data.payType === 'HAFAN') {\r\n            if (result.code === 200) {\r\n                app.$success('Hafan支付成功');\r\n                app.disabled = false;\r\n                app.navToOrderResult(result.data.orderInfo.id, '支付成功');\r\n            } else {\r\n                app.$error(result.message || 'Hafan支付失败');\r\n                app.disabled = false;\r\n            }\r\n            return;\r\n        }\r\n        \r\n        // 余额支付\r\n        if (result.data.payType == PayTypeEnum.BALANCE.value) {\r\n            app.disabled = false;\r\n            app.navToOrderResult(result.data.orderInfo.id, result.message);\r\n        }\r\n      },\r\n\r\n      // 跳转到订单结果页\r\n      navToOrderResult(orderId, message) {\r\n        if (!message || message == undefined) {\r\n            message = \"\";\r\n        }\r\n        this.$navTo('pages/order/result?orderId='+orderId+'&message=' + message);\r\n      },\r\n      \r\n      /**\r\n       * 下拉刷新\r\n       */\r\n      onPullDownRefresh() {\r\n          const app = this;\r\n          setTimeout(() => {\r\n            // 获取购物车信息\r\n            app.getCartList();\r\n            // 获取默认收货地址\r\n            app.getDefaultAddress();\r\n            // 获取店铺信息\r\n            app.getStoreInfo();\r\n            uni.stopPullDownRefresh();\r\n          }, 1000)\r\n      },\r\n\r\n      // 预约取餐开关变化\r\n      handleReservationChange(value) {\r\n        if (value) {\r\n          this.initDateTimePicker();\r\n          this.showReservationPopup = true;\r\n        } else {\r\n          this.reservationTime = '';\r\n        }\r\n      },\r\n\r\n      // 初始化日期时间选择器\r\n      initDateTimePicker() {\r\n        const now = new Date();\r\n        const currentHour = now.getHours();\r\n        const currentMinute = now.getMinutes();\r\n\r\n        // 生成日期列表（今天和明天）\r\n        this.dateList = [];\r\n        const today = new Date();\r\n        const tomorrow = new Date(today);\r\n        tomorrow.setDate(today.getDate() + 1);\r\n\r\n        this.dateList.push({\r\n          label: '今天 ' + this.formatDate(today),\r\n          value: this.formatDateValue(today)\r\n        });\r\n        this.dateList.push({\r\n          label: '明天 ' + this.formatDate(tomorrow),\r\n          value: this.formatDateValue(tomorrow)\r\n        });\r\n\r\n        // 生成小时列表\r\n        this.hourList = [];\r\n        for (let i = 0; i < 24; i++) {\r\n          const hour = i.toString().padStart(2, '0');\r\n          this.hourList.push({\r\n            label: hour + '时',\r\n            value: hour\r\n          });\r\n        }\r\n\r\n        // 生成分钟列表（每10分钟一个选项）\r\n        this.minuteList = [];\r\n        for (let i = 0; i < 60; i += 10) {\r\n          const minute = i.toString().padStart(2, '0');\r\n          this.minuteList.push({\r\n            label: minute + '分',\r\n            value: minute\r\n          });\r\n        }\r\n\r\n        // 设置默认选中值（当前时间后1小时）\r\n        let defaultHour = currentHour + 1;\r\n        let defaultDateIndex = 0;\r\n\r\n        if (defaultHour >= 24) {\r\n          defaultHour = defaultHour - 24;\r\n          defaultDateIndex = 1;\r\n        }\r\n\r\n        // 找到最接近的15分钟间隔\r\n        let defaultMinuteIndex = Math.ceil(currentMinute / 15);\r\n        if (defaultMinuteIndex >= 4) {\r\n          defaultMinuteIndex = 0;\r\n          defaultHour += 1;\r\n          if (defaultHour >= 24) {\r\n            defaultHour = 0;\r\n            defaultDateIndex = 1;\r\n          }\r\n        }\r\n\r\n        this.pickerValue = [defaultDateIndex, defaultHour, defaultMinuteIndex];\r\n      },\r\n\r\n      // 格式化日期显示\r\n      formatDate(date) {\r\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n        const day = date.getDate().toString().padStart(2, '0');\r\n        return `${month}-${day}`;\r\n      },\r\n\r\n      // 格式化日期值\r\n      formatDateValue(date) {\r\n        const year = date.getFullYear();\r\n        const month = (date.getMonth() + 1).toString().padStart(2, '0');\r\n        const day = date.getDate().toString().padStart(2, '0');\r\n        return `${year}-${month}-${day}`;\r\n      },\r\n\r\n      // 选择器值变化\r\n      onPickerChange(e) {\r\n        this.pickerValue = e.detail.value;\r\n      },\r\n\r\n      // 取消预约\r\n      cancelReservation() {\r\n        this.showReservationPopup = false;\r\n        this.isReservation = false;\r\n        this.reservationTime = '';\r\n      },\r\n\r\n      // 确认预约时间\r\n      confirmReservation() {\r\n        const dateIndex = this.pickerValue[0];\r\n        const hourIndex = this.pickerValue[1];\r\n        const minuteIndex = this.pickerValue[2];\r\n\r\n        const selectedDate = this.dateList[dateIndex].value;\r\n        const selectedHour = this.hourList[hourIndex].value;\r\n        const selectedMinute = this.minuteList[minuteIndex].value;\r\n\r\n        const reservationDateTime = `${selectedDate} ${selectedHour}:${selectedMinute}:00`;\r\n\r\n        // 验证预约时间是否在24小时内且不早于当前时间\r\n        const now = new Date();\r\n        const reservationDate = new Date(reservationDateTime);\r\n        const timeDiff = reservationDate.getTime() - now.getTime();\r\n        const minutesDiff = timeDiff / (1000 * 60);\r\n        const hoursDiff = timeDiff / (1000 * 60 * 60);\r\n\r\n        if (minutesDiff < 5) {\r\n          this.$toast('预约时间不能早于当前时间5分钟');\r\n          return;\r\n        }\r\n\r\n        if (hoursDiff > 24) {\r\n          this.$toast('预约时间不能超过24小时');\r\n          return;\r\n        }\r\n\r\n        this.reservationTime = reservationDateTime;\r\n        this.showReservationPopup = false;\r\n        this.$toast('预约时间设置成功');\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  @import \"./style.scss\";\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=style&index=0&id=38cab12e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./goods.vue?vue&type=style&index=0&id=38cab12e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751892058608\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}