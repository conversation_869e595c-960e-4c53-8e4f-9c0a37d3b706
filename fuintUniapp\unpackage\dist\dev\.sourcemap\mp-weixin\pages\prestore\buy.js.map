{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?586e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?13e3", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?27ea", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?5674", "uni-app:///pages/prestore/buy.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?eff4", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?1d01", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?3f47", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/buy.vue?e896"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Shortcut", "Popup", "data", "isLoading", "couponId", "couponInfo", "storeRule", "showPopup", "onLoad", "methods", "onRefreshPage", "app", "Promise", "finally", "getCouponDetail", "CouponApi", "then", "ruleItem", "resolve", "catch", "onShowPopup", "onTargetHome", "onShareAppMessage", "title", "path", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACuD;AACL;AACa;AACyB;;;AAGxF;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAwoB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgG5pB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAKA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC;MACAC,qCACAC;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACAC,+BACAC;UACAL;UACA;UACAM;YACA;YACAN;cAAA;cAAA;YAAA;UACA;UACAO;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EAEA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAlB;IACA;IACA;MACAmB;MACAC;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACArB;IACA;IACA;MACAmB;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC7MA;AAAA;AAAA;AAAA;AAA26B,CAAgB,q4BAAG,EAAC,C;;;;;;;;;;;ACA/7B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA2uC,CAAgB,iqCAAG,EAAC,C;;;;;;;;;;;ACA/vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/prestore/buy.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/prestore/buy.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./buy.vue?vue&type=template&id=dcaad8e4&scoped=true&\"\nvar renderjs\nimport script from \"./buy.vue?vue&type=script&lang=js&\"\nexport * from \"./buy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./buy.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./buy.vue?vue&type=style&index=1&id=dcaad8e4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dcaad8e4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/prestore/buy.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=template&id=dcaad8e4&scoped=true&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-show=\"!isLoading\" class=\"container\">\n    <!-- 卡券信息 -->\n    <view v-if=\"!isLoading\" class=\"coupon-info m-top20\">\n      <!-- 标题、分享 -->\n      <view class=\"info-item info-item__name dis-flex flex-y-center\">\n        <view class=\"coupon-name flex-box\">\n          <text class=\"twolist-hidden\">{{ couponInfo.name }}</text>\n        </view>\n        <!-- #ifdef MP-WEIXIN -->\n        <view class=\"coupon-share__line\"></view>\n        <view class=\"coupon-share\">\n          <button class=\"share-btn dis-flex flex-dir-column\" open-type=\"share\">\n            <text class=\"share__icon iconfont icon-fenxiang-post\"></text>\n            <text class=\"f-24\">分享</text>\n          </button>\n        </view>\n        <!-- #endif -->\n      </view>\n      <!-- 卡券预存规则 -->\n      <view class=\"store-rule\">\n          <view class=\"title\">预存规则：</view>\n          <view v-for=\"(item, index) in storeRule\" :key=\"index\" class=\"item\">\n            <text>预存￥{{ item.store }} 到账 ￥{{ item.upStore }}</text>\n          </view>\n      </view>\n      <view class=\"info-item\">\n          <text>已有<text class=\"number\">{{ couponInfo.gotNum }}</text>人预存，剩余<text class=\"number\">{{ couponInfo.limitNum }}</text>名额</text>\n      </view>\n      <view class=\"info-item\">\n          <text>有效期：{{ couponInfo.effectiveDate }}</text>\n      </view>\n    </view>\n\n    <!-- 选择卡券规格 -->\n    <view class=\"coupon-choice m-top20 b-f\" @click=\"onShowPopup()\">\n      <view class=\"spec-list\">\n        <view class=\"flex-box\">\n          <text class=\"col-8\">选择：</text>\n          <text class=\"spec-name\" key=\"index\">预存金额、数量</text>\n        </view>\n        <view class=\"f-26 col-9 t-r\">\n          <text class=\"iconfont icon-xiangyoujiantou\"></text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 预存选项弹窗 -->\n    <Popup v-if=\"!isLoading\" v-model=\"showPopup\" :couponInfo=\"couponInfo\" :storeRule=\"storeRule\"/>\n\n    <!-- 卡券描述 -->\n    <view v-if=\"!isLoading\" class=\"coupon-content m-top20\">\n      <view class=\"item-title b-f\">\n        <text>卡券描述</text>\n      </view>\n      <block v-if=\"couponInfo.description != ''\">\n        <view class=\"coupon-content-detail b-f\">\n          <jyf-parser :html=\"couponInfo.description\"></jyf-parser>\n        </view>\n      </block>\n      <empty v-else tips=\"亲，暂无卡券描述\" />\n    </view>\n\n    <!-- 底部选项卡 -->\n    <view class=\"footer-fixed\">\n      <view class=\"footer-container\">\n        <!-- 导航图标 -->\n        <view class=\"foo-item-fast\">\n          <!-- 客服 (仅微信小程序端显示) -->\n          <!-- #ifdef MP-WEIXIN -->\n          <button class=\"btn-normal\" open-type=\"contact\">\n            <view class=\"fast-item\">\n              <view class=\"fast-icon\">\n                <text class=\"iconfont icon-kefu1\"></text>\n              </view>\n              <view class=\"fast-text\">\n                <text>客服</text>\n              </view>\n            </view>\n          </button>\n          <!-- #endif -->\n        </view>\n        <!-- 操作按钮 -->\n        <view class=\"foo-item-btn\">\n          <view class=\"btn-wrapper\">\n            <view class=\"btn-item btn-item-main\" @click=\"onShowPopup()\">\n              <text>立即预存</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as CouponApi from '@/api/coupon'\n  import jyfParser from '@/components/jyf-parser/jyf-parser'\n  import Shortcut from '@/components/shortcut'\n  import Popup from './components/Popup'\n\n  export default {\n    components: {\n      jyfParser,\n      Shortcut,\n      Popup\n    },\n    data() {\n      return {\n        // 正在加载\n        isLoading: true,\n        // 当前卡券ID\n        couponId: null,\n        // 卡券详情\n        couponInfo: null,\n        // 预存规则\n        storeRule: [],\n        // 显示/隐藏弹窗\n        showPopup: false\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 卡券ID\n      this.couponId = parseInt(options.couponId)\n      // 加载页面数据\n      this.onRefreshPage()\n    },\n\n    methods: {\n\n      // 刷新页面数据\n      onRefreshPage() {\n        const app = this\n        app.isLoading = true\n        Promise.all([app.getCouponDetail()])\n          .finally(() => app.isLoading = false)\n      },\n\n      // 获取卡券信息\n      getCouponDetail() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          CouponApi.detail(app.couponId)\n            .then(result => {\n              app.couponInfo = result.data\n              let ruleItem = app.couponInfo.inRule.split(\",\");\n              ruleItem.forEach(function(item) {\n                  let rule = item.split(\"_\")\n                  app.storeRule.push({\"store\": rule[0], \"upStore\": rule[1]})\n              })\n              resolve(result)\n            })\n            .catch(err => reject(err))\n        })\n      },\n\n      /**\n       * 显示/隐藏预存弹窗\n       */\n      onShowPopup() {\n        this.showPopup = !this.showPopup\n      },\n\n      // 跳转到首页\n      onTargetHome(e) {\n        this.$navTo('pages/index/index')\n      }\n      \n    },\n\n    /**\n     * 分享当前页面\n     */\n    onShareAppMessage() {\n      const app = this\n      // 构建页面参数\n      const params = app.$getShareUrlParams({\n        couponId: app.couponId\n      })\n      return {\n        title: app.couponInfo.name,\n        path: `/pages/prestore/buy?${params}`\n      }\n    },\n\n    /**\n     * 分享到朋友圈\n     * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)\n     * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html\n     */\n    onShareTimeline() {\n      const app = this\n      // 构建页面参数\n      const params = app.$getShareUrlParams({\n        couponId: app.couponId,\n      })\n      return {\n        title: app.couponInfo.name,\n        path: `/pages/prestore/buy?${params}`\n      }\n    }\n  }\n</script>\n\n<style>\n  page {\n    background: #fafafa;\n  }\n</style>\n<style lang=\"scss\" scoped>\n  @import \"./buy.scss\";\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424214\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=1&id=dcaad8e4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./buy.vue?vue&type=style&index=1&id=dcaad8e4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425919\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}