<view class="container data-v-3a20e7fa"><mescroll-body vue-id="7b027563-1" sticky="{{true}}" down="{{({native:true})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" class="data-v-3a20e7fa vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('7b027563-2')+','+('7b027563-1')}}" list="{{tabs}}" is-scroll="{{false}}" current="{{curTab}}" active-color="#FA2209" duration="{{0.2}}" data-event-opts="{{[['^change',[['onChangeTab']]]]}}" bind:change="__e" class="data-v-3a20e7fa" bind:__l="__l"></u-tabs><view class="order-list data-v-3a20e7fa"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="order-item data-v-3a20e7fa"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="item-top data-v-3a20e7fa" bindtap="__e"><view class="item-top-left data-v-3a20e7fa"><text class="order-type data-v-3a20e7fa">{{item.typeName}}</text></view><view class="item-top-right data-v-3a20e7fa"><text class="{{['data-v-3a20e7fa',item.status]}}">{{item.statusText}}</text></view></view><block wx:if="{{item.goods}}"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="goods-list data-v-3a20e7fa" bindtap="__e"><block wx:for="{{item.goods}}" wx:for-item="goods" wx:for-index="idx" wx:key="idx"><view class="goods-item data-v-3a20e7fa"><view class="goods-image data-v-3a20e7fa"><image class="image data-v-3a20e7fa" src="{{goods.image}}"></image></view><view class="goods-content data-v-3a20e7fa"><view class="goods-title twolist-hidden data-v-3a20e7fa"><text class="data-v-3a20e7fa">{{goods.name}}</text></view><view class="goods-props clearfix data-v-3a20e7fa"><block wx:for="{{goods.specList}}" wx:for-item="props" wx:for-index="idx" wx:key="idx"><view class="goods-props-item data-v-3a20e7fa"><text class="data-v-3a20e7fa">{{props.specValue}}</text></view></block></view></view><view class="goods-trade data-v-3a20e7fa"><view class="goods-price data-v-3a20e7fa"><text class="unit data-v-3a20e7fa">￥</text><text class="value data-v-3a20e7fa">{{goods.price}}</text></view><view class="goods-num data-v-3a20e7fa"><text class="data-v-3a20e7fa">{{"×"+goods.num}}</text></view></view></view></block></view></block><block wx:if="{{item.remark}}"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="remark data-v-3a20e7fa" bindtap="__e"><text class="data-v-3a20e7fa">备注：</text><text class="data-v-3a20e7fa">{{item.remark?item.remark:'--'}}</text></view></block><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="order-total data-v-3a20e7fa" bindtap="__e"><text class="data-v-3a20e7fa">总金额</text><text class="unit data-v-3a20e7fa">￥</text><text class="money data-v-3a20e7fa">{{item.amount}}</text></view><view class="order-handle data-v-3a20e7fa"><view class="order-time data-v-3a20e7fa"><text class="time data-v-3a20e7fa">{{item.createTime}}</text></view><view class="btn-group data-v-3a20e7fa"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="btn-item data-v-3a20e7fa" bindtap="__e">详情</view></view></view></view></block></view></mescroll-body></view>