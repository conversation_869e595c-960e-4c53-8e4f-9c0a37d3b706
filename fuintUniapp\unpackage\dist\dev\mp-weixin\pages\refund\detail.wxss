@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.detail-header.data-v-70953b1f {
  position: relative;
  width: 100%;
  height: 140rpx;
}
.detail-header .header-backdrop.data-v-70953b1f {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
}
.detail-header .header-backdrop .image.data-v-70953b1f {
  display: block;
  width: 750rpx;
  height: 140rpx;
}
.header-state.data-v-70953b1f {
  padding: 0rpx 130rpx;
  font-size: 60rpx;
  font-weight: bold;
}
/* 商品详情 */
.detail-goods.data-v-70953b1f {
  padding: 24rpx 20rpx;
}
.detail-goods .left .goods-image.data-v-70953b1f {
  display: block;
  width: 150rpx;
  height: 150rpx;
  border-radius: 6rpx;
}
.detail-goods .right.data-v-70953b1f {
  padding-left: 20rpx;
}
.detail-goods .goods-props.data-v-70953b1f {
  margin-top: 14rpx;
  color: #ababab;
  font-size: 24rpx;
  overflow: hidden;
}
.detail-goods .goods-props .goods-props-item.data-v-70953b1f {
  display: inline-block;
  margin-right: 14rpx;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background-color: #F5F5F5;
  width: auto;
}
.detail-order.data-v-70953b1f {
  padding: 15rpx 20rpx;
  font-size: 26rpx;
}
.detail-order .item.data-v-70953b1f {
  margin-bottom: 10rpx;
}
.detail-order .item.data-v-70953b1f:last-child {
  margin-bottom: 0;
}
/* 售后详情 */
.detail-refund.data-v-70953b1f {
  padding: 15rpx 20rpx;
}
.detail-refund__row.data-v-70953b1f {
  margin: 20rpx 0;
}
/* 申请凭证 */
.image-list.data-v-70953b1f {
  margin-bottom: -15rpx;
}
.image-list .image-preview.data-v-70953b1f {
  margin: 0 15rpx 15rpx 0;
  float: left;
}
.image-list .image-preview .image.data-v-70953b1f {
  display: block;
  width: 180rpx;
  height: 180rpx;
}
.image-list .image-preview.data-v-70953b1f:nth-child(3n+0) {
  margin-right: 0;
}
/* 商家收货地址 */
.detail-address.data-v-70953b1f {
  padding: 20rpx 30rpx;
}
.address-details.data-v-70953b1f {
  padding: 5rpx 0;
  border-bottom: 1px solid #eee;
}
.address-details .address-details__row.data-v-70953b1f {
  margin: 10rpx 0;
}
.address-tips.data-v-70953b1f {
  line-height: 46rpx;
}
.detail-address__row.data-v-70953b1f {
  margin: 15rpx 0;
}
/* 填写物流信息 */
.detail-express.data-v-70953b1f {
  padding: 10rpx 30rpx;
}
.form-group.data-v-70953b1f {
  height: 60rpx;
  margin: 14rpx 0;
}
.form-group .input.data-v-70953b1f {
  height: 100%;
  font-size: 28rpx;
}
/* 底部操作栏 */
.footer.data-v-70953b1f {
  margin-top: 60rpx;
}
.footer .btn-wrapper.data-v-70953b1f {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.footer .btn-item.data-v-70953b1f {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
}
.footer .btn-item-main.data-v-70953b1f {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.footer .btn-item-main.disabled.data-v-70953b1f {
  background: #ff9779;
}
