<view class="container data-v-52e26b05"><view class="info-list data-v-52e26b05"><view class="info-item data-v-52e26b05"><view class="contacts avatar-warp data-v-52e26b05"><text class="name data-v-52e26b05">头像</text><image class="avatar data-v-52e26b05" src="{{avatar}}" data-event-opts="{{[['tap',[['chooseImage']]]]}}" bindtap="__e"></image></view></view><view class="info-item data-v-52e26b05"><view class="contacts data-v-52e26b05"><text class="name data-v-52e26b05">称呼</text><input class="weui-input value data-v-52e26b05" type="nickname" placeholder="请输入称呼" data-event-opts="{{[['blur',[['getnickname',['$event']]]],['input',[['__set_model',['','nickname','$event',[]]]]]]}}" value="{{nickname}}" bindblur="__e" bindinput="__e"/></view></view><view class="info-item data-v-52e26b05"><view class="contacts data-v-52e26b05"><text class="name data-v-52e26b05">手机</text><button class="button btn-normal value data-v-52e26b05" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e"><block wx:if="{{userInfo.mobile}}"><text class="data-v-52e26b05">{{userInfo.mobile}}</text></block><text style="color:#f9211c;margin-left:2px;" class="data-v-52e26b05">更换</text></button></view></view><view class="info-item data-v-52e26b05"><view class="contacts data-v-52e26b05"><text class="name data-v-52e26b05">性别</text><view class="value data-v-52e26b05"><radio-group data-event-opts="{{[['change',[['genderChange',['$event']]]]]}}" bindchange="__e" class="data-v-52e26b05"><label class="radio data-v-52e26b05"><radio value="1" color="#3f51b5" checked="{{userInfo.sex=='1'?true:false}}" class="data-v-52e26b05"></radio>男</label><label class="radio second data-v-52e26b05"><radio value="0" color="#3f51b5" checked="{{userInfo.sex=='0'?true:false}}" class="data-v-52e26b05"></radio>女</label></radio-group></view></view></view><view class="info-item data-v-52e26b05"><view class="contacts data-v-52e26b05"><text class="name data-v-52e26b05">生日</text><picker class="value data-v-52e26b05" mode="date" value="{{userInfo.birthday}}" start="1900-01-01" data-event-opts="{{[['change',[['bindDateChange',['$event']]]]]}}" bindchange="__e"><view class="picker data-v-52e26b05">{{userInfo.birthday?userInfo.birthday:'选择生日'}}</view></picker></view></view></view><block wx:if="{{userInfo.id}}"><view class="footer-fixed data-v-52e26b05"><view class="btn-wrapper data-v-52e26b05"><view data-event-opts="{{[['tap',[['save']]]]}}" class="btn-item btn-item-main data-v-52e26b05" bindtap="__e">保存信息</view></view><view class="btn-wrapper data-v-52e26b05"><view data-event-opts="{{[['tap',[['logout']]]]}}" class="btn-item btn-item-out data-v-52e26b05" bindtap="__e">退出登录</view></view></view></block></view>