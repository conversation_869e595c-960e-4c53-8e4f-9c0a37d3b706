<view class="container data-v-304432f4"><mescroll-body vue-id="4f7aa6dc-1" sticky="{{true}}" down="{{({native:true})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" class="data-v-304432f4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('4f7aa6dc-2')+','+('4f7aa6dc-1')}}" list="{{tabs}}" is-scroll="{{false}}" current="{{curTab}}" active-color="#FA2209" duration="{{0.2}}" data-event-opts="{{[['^change',[['onChangeTab']]]]}}" bind:change="__e" class="data-v-304432f4" bind:__l="__l"></u-tabs><view class="widget-list data-v-304432f4"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="widget-detail data-v-304432f4"><view class="row-block dis-flex flex-y-center data-v-304432f4"><view class="flex-box data-v-304432f4">{{item.createTime}}</view><view class="flex-box t-r data-v-304432f4"><text class="col-m data-v-304432f4">{{item.statusText}}</text></view></view><block wx:for="{{item.orderInfo.goods}}" wx:for-item="goodsInfo" wx:for-index="key" wx:key="key"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="detail-goods row-block dis-flex data-v-304432f4" catchtap="__e"><view class="goods-image data-v-304432f4"><image class="image data-v-304432f4" src="{{goodsInfo.image}}" mode="aspectFit"></image></view><view class="goods-right flex-box data-v-304432f4"><view class="goods-name data-v-304432f4"><text class="twolist-hidden data-v-304432f4">{{goodsInfo.name}}</text></view><view class="goods-props clearfix data-v-304432f4"><block wx:for="{{goodsInfo.specList}}" wx:for-item="props" wx:for-index="idx" wx:key="idx"><view class="goods-props-item data-v-304432f4"><text class="data-v-304432f4">{{props.specName}}</text></view></block></view><view class="goods-num t-r data-v-304432f4"><text class="f-26 col-8 data-v-304432f4">{{"×"+goodsInfo.num}}</text></view></view></view></block><view class="detail-order row-block data-v-304432f4"><view class="item dis-flex flex-x-end flex-y-center data-v-304432f4"><text class="data-v-304432f4">售后金额：</text><text class="col-m data-v-304432f4">{{"￥"+item.amount}}</text></view></view><view class="detail-operate row-block dis-flex flex-x-end flex-y-center data-v-304432f4"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="detail-btn btn-detail data-v-304432f4" catchtap="__e">查看详情</view></view></view></block></view></mescroll-body></view>