@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 顶部选项卡 */
.container.data-v-6043f55b {
  min-height: 100vh;
}
.tabs-wrapper.data-v-6043f55b {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  display: flex;
  width: 100%;
  height: 88rpx;
  color: #333;
  font-size: 28rpx;
  background: #fff;
  border-bottom: 1rpx solid #e4e4e4;
  z-index: 100;
  overflow: hidden;
  white-space: nowrap;
}
.tab-item.data-v-6043f55b {
  display: inline-block;
  padding: 0 15rpx;
  text-align: center;
  min-width: 20%;
  height: 87rpx;
  line-height: 88rpx;
  box-sizing: border-box;
}
.tab-item .value.data-v-6043f55b {
  height: 100%;
}
.tab-item.active .value.data-v-6043f55b {
  color: #fd4a5f;
  border-bottom: 4rpx solid #fd4a5f;
}
/* 会员列表 */
.member-list.data-v-6043f55b {
  padding-top: 20rpx;
  line-height: 1;
  background: #f7f7f7;
}
.member-item.data-v-6043f55b {
  margin-bottom: 10rpx;
  padding: 30rpx;
  background: #fff;
  border: #f5f5f5 solid 1rpx;
  height: 188rpx;
}
.member-item.data-v-6043f55b:last-child {
  margin-bottom: 0;
}
.member-item .left.data-v-6043f55b {
  width: 320rxp;
  float: left;
}
.member-item .left .image.data-v-6043f55b {
  display: block;
  width: 120rpx;
  height: 120rpx;
  border-radius: 120rpx;
  border: solid 1rpx #cccccc;
}
.member-item .right.data-v-6043f55b {
  margin-left: 140rpx;
  height: 180rpx;
}
.member-item .right .base .name.data-v-6043f55b {
  font-weight: bold;
  max-height: 80rpx;
  font-size: 30rpx;
  color: #333;
}
.member-item .right .base .grade.data-v-6043f55b {
  margin-left: 20rpx;
  float: right;
}
.member-item .right .amount.data-v-6043f55b {
  margin-top: 10rpx;
}
.member-item .right .amount .balance.data-v-6043f55b {
  margin-top: 15rpx;
}
.member-item .right .amount .point.data-v-6043f55b {
  margin-top: 10rpx;
}
.member-item .right .footer.data-v-6043f55b {
  margin-top: 20rpx;
}
.member-item .right .footer .member-views.data-v-6043f55b {
  float: right;
}
