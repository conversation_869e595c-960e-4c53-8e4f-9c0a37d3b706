@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.com-user.data-v-0c9bd25d {
  width: 100%;
  height: auto;
  padding: 0 20rpx 20rpx;
  margin-top: -60rpx;
  position: relative;
  z-index: 2;
}
.com-user .user-main.data-v-0c9bd25d {
  width: 100%;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  border: #cccccc solid 1rpx;
  display: flex;
  align-items: center;
}
.com-user .user-main .avatar.data-v-0c9bd25d {
  width: 130rpx;
  height: 130rpx;
  border-radius: 50%;
}
.com-user .user-main .uc.data-v-0c9bd25d {
  flex: 1;
  padding-left: 10rpx;
}
.com-user .user-main .uc .name.data-v-0c9bd25d {
  font-size: 32rpx;
  font-weight: 500;
  color: #000;
}
.com-user .user-main .uc .tip.data-v-0c9bd25d {
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}
.com-user .user-main .ur.data-v-0c9bd25d {
  width: 140rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  border-radius: 60rpx;
  justify-content: center;
  color: #fff;
  font-size: 26rpx;
  background-color: #3f51b5;
}
.com-user .user-main .qr.data-v-0c9bd25d {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  border-radius: 6rpx;
  justify-content: center;
  text-align: center;
  color: #3f51b5;
  font-size: 68rpx;
  background-color: #fff;
  border: solid 1rpx #3f51b5;
  padding: 2rpx;
}
