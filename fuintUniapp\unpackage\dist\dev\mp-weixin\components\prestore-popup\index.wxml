<view class="{{['pre-store-popup','popup','data-v-9768638a',value&&complete?'show':'none']}}" catchtouchmove="__e" data-event-opts="{{[['touchmove',[['moveHandle',['$event']]]]]}}"><view data-event-opts="{{[['tap',[['close',['mask']]]]]}}" class="mask data-v-9768638a" bindtap="__e"></view><view class="layer attr-content data-v-9768638a" style="{{('border-radius: 10rpx 10rpx 0 0;')}}"><view class="specification-wrapper data-v-9768638a"><scroll-view class="specification-wrapper-content data-v-9768638a" scroll-y="true"><view class="specification-header data-v-9768638a"><view class="specification-name data-v-9768638a">{{couponInfo.name}}</view></view><view class="specification-content data-v-9768638a"><block wx:for="{{storeRule}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="store-item data-v-9768638a"><view style="flex:4;" class="data-v-9768638a"><view class="item-rule data-v-9768638a">{{"预存￥"+item.store+" 到账 ￥"+item.upStore}}</view></view><view style="flex:1;text-align:right;" class="data-v-9768638a"><number-box bind:input="__e" vue-id="{{'55a30baa-1-'+index}}" min="{{minBuyNum}}" max="{{maxBuyNum}}" step="{{stepBuyNum}}" positive-integer="{{true}}" value="{{selectNum[index]}}" data-event-opts="{{[['^input',[['__set_model',['$0',index,'$event',[]],['selectNum']]]]]}}" class="data-v-9768638a" bind:__l="__l"></number-box></view></view></block></view></scroll-view><block wx:if="{{showClose}}"><view data-event-opts="{{[['tap',[['close',['close']]]]]}}" class="close data-v-9768638a" bindtap="__e"><image class="close-item data-v-9768638a" src="{{closeImage}}"></image></view></block></view><block wx:if="{{noStock}}"><view class="btn-wrapper data-v-9768638a"><view class="sure data-v-9768638a" style="color:#ffffff;background-color:#cccccc;">库存没有了</view></view></block><view class="btn-wrapper data-v-9768638a"><view data-event-opts="{{[['tap',[['buyNow',['$event']]]]]}}" class="sure data-v-9768638a" bindtap="__e">立即预存</view></view></view></view>