@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-482e85b8 {
  padding: 20rpx;
  min-height: 100vh;
  background: #f7f7f7;
}
.search-wrapper.data-v-482e85b8 {
  display: flex;
  height: 78rpx;
}
.search-input.data-v-482e85b8 {
  width: 80%;
  background: #fff;
  border-radius: 50rpx 0 0 50rpx;
  box-sizing: border-box;
  overflow: hidden;
  border: solid 1px #cccccc;
}
.search-input .search-input-wrapper.data-v-482e85b8 {
  display: flex;
}
.search-input .search-input-wrapper .left.data-v-482e85b8 {
  display: flex;
  width: 60rpx;
  justify-content: center;
  align-items: center;
}
.search-input .search-input-wrapper .left .search-icon.data-v-482e85b8 {
  display: block;
  color: #b4b4b4;
  font-size: 30rpx;
  font-weight: bold;
}
.search-input .search-input-wrapper .right.data-v-482e85b8 {
  flex: 1;
}
.search-input .search-input-wrapper .right input.data-v-482e85b8 {
  font-size: 28rpx;
  height: 78rpx;
  line-height: 78rpx;
}
.search-input .search-input-wrapper .right input .input-placeholder.data-v-482e85b8 {
  color: #aba9a9;
}
.search-button.data-v-482e85b8 {
  width: 20%;
  box-sizing: border-box;
}
.search-button .button.data-v-482e85b8 {
  line-height: 78rpx;
  height: 78rpx;
  font-size: 28rpx;
  border-radius: 0 20px 20px 0;
  background: #3f51b5;
}
.history .his-head.data-v-482e85b8 {
  font-size: 28rpx;
  padding: 50rpx 0 0 0;
  color: #777;
}
.history .his-head .icon.data-v-482e85b8 {
  float: right;
}
.history .his-list.data-v-482e85b8 {
  padding: 10px 0;
  overflow: hidden;
}
.history .his-list .his-item.data-v-482e85b8 {
  width: 33.3%;
  float: left;
  padding: 10rpx;
  box-sizing: border-box;
}
.history .his-list .his-item .history-button.data-v-482e85b8 {
  text-align: center;
  padding: 14rpx;
  line-height: 30rpx;
  border-radius: 100rpx;
  background: #fff;
  font-size: 26rpx;
  border: 1px solid #efefef;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
