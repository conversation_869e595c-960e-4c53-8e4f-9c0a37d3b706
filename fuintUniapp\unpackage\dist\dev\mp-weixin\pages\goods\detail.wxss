
page {
  background: #fafafa;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-f0a9f5ba {
  padding-bottom: 112rpx;
}
/* 商品信息 */
.goods-info.data-v-f0a9f5ba {
  background: #fff;
  padding: 25rpx 30rpx;
}
.info-item__top.data-v-f0a9f5ba {
  min-height: 40rpx;
  margin-bottom: 20rpx;
}
.info-item__top .active-tag.data-v-f0a9f5ba {
  width: 108rpx;
  color: #fff;
  background: #fe293f;
  padding: 3rpx 5rpx;
  border-radius: 15rpx;
  font-size: 24rpx;
  text-align: center;
  margin-right: 15rpx;
}
.floor-price__samll.data-v-f0a9f5ba {
  font-size: 26rpx;
  line-height: 1;
  color: #FA2209;
}
/* 商品价 */
.floor-price.data-v-f0a9f5ba {
  color: #FA2209;
  margin-right: 15rpx;
  font-size: 38rpx;
  line-height: 1;
  margin-bottom: -2rpx;
}
.original-price.data-v-f0a9f5ba {
  font-size: 24rpx;
  line-height: 1;
  text-decoration: line-through;
  color: #959595;
}
.goods-sales.data-v-f0a9f5ba {
  font-size: 24rpx;
  color: #959595;
}
.info-item__name .goods-name.data-v-f0a9f5ba {
  font-size: 28rpx;
}
/* 商品分享 */
.goods-share__line.data-v-f0a9f5ba {
  border-left: 1rpx solid #f4f4f4;
  height: 60rpx;
  margin: 0 30rpx;
}
.goods-share .share-btn.data-v-f0a9f5ba {
  line-height: normal;
  padding: 0;
  background: none;
  border-radius: 0;
  box-shadow: none;
  font-size: 8pt;
  border: none;
  color: #191919;
}
.goods-share .share-btn.data-v-f0a9f5ba::after {
  border: none;
}
.goods-share .share__icon.data-v-f0a9f5ba {
  font-size: 40rpx;
  margin-bottom: 5rpx;
}
/* 商品卖点 */
.info-item_selling-point.data-v-f0a9f5ba {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #808080;
}
.goods-choice.data-v-f0a9f5ba {
  padding: 26rpx 30rpx;
  font-size: 28rpx;
}
.goods-choice .spec-list.data-v-f0a9f5ba {
  display: flex;
  align-items: center;
}
.goods-choice .spec-list .spec-name.data-v-f0a9f5ba {
  margin-right: 10rpx;
}
/* 商品详情 */
.goods-content .item-title.data-v-f0a9f5ba {
  padding: 20rpx;
  font-size: 30rpx;
}
.goods-content .goods-content-detail.data-v-f0a9f5ba {
  padding: 20rpx;
  min-height: 400rpx;
}
/* 底部操作栏 */
.footer-fixed.data-v-f0a9f5ba {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  display: flex;
  height: 180rpx;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);
  background: #fff;
}
.footer-container.data-v-f0a9f5ba {
  width: 100%;
  display: flex;
  margin-bottom: 40rpx;
}
.foo-item-fast.data-v-f0a9f5ba {
  box-sizing: border-box;
  width: 256rpx;
  line-height: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}
.foo-item-fast .fast-item.data-v-f0a9f5ba {
  position: relative;
  padding: 4rpx 10rpx;
  line-height: 1;
}
.foo-item-fast .fast-item .fast-icon.data-v-f0a9f5ba {
  margin-bottom: 6rpx;
}
.foo-item-fast .fast-item--home.data-v-f0a9f5ba {
  margin-right: 30rpx;
}
.foo-item-fast .fast-item--cart .fast-icon.data-v-f0a9f5ba {
  padding-left: 3px;
}
.foo-item-fast .fast-item .fast-badge.data-v-f0a9f5ba {
  display: inline-block;
  box-sizing: border-box;
  min-width: 46rpx;
  padding: 5rpx;
  color: #fff;
  font-weight: 500;
  font-size: 22rpx;
  font-family: -apple-system-font, Helvetica Neue, Arial, sans-serif;
  line-height: 36rpx;
  text-align: center;
  background-color: #fa5151;
  border: 1px solid #fff;
  border-radius: 999px;
}
.foo-item-fast .fast-item .fast-badge--fixed.data-v-f0a9f5ba {
  position: absolute;
  top: 0;
  right: 0;
  -webkit-transform-origin: 100%;
          transform-origin: 100%;
}
.foo-item-fast .fast-item .fast-icon.data-v-f0a9f5ba {
  font-size: 46rpx;
}
.foo-item-fast .fast-item .fast-text.data-v-f0a9f5ba {
  font-size: 24rpx;
}
.foo-item-btn.data-v-f0a9f5ba {
  flex: 1;
}
.foo-item-btn .btn-wrapper.data-v-f0a9f5ba {
  height: 100%;
  display: flex;
  align-items: center;
}
.foo-item-btn .btn-item.data-v-f0a9f5ba {
  flex: 1;
  font-size: 28rpx;
  height: 90rpx;
  line-height: 90rpx;
  margin-right: 5rpx;
  text-align: center;
  color: #fff;
  border-radius: 8rpx;
}
.foo-item-btn .btn-item-main.data-v-f0a9f5ba {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.foo-item-btn .btn-item-deputy.data-v-f0a9f5ba {
  background: linear-gradient(to right, #3f51b5, #3f51b5);
}
