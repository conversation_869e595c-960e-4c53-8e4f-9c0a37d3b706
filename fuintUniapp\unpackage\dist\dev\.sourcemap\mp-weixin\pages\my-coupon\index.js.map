{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/my-coupon/index.vue?fd75", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/my-coupon/index.vue?6f90", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/my-coupon/index.vue?fbe1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/my-coupon/index.vue?e407", "uni-app:///pages/my-coupon/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/my-coupon/index.vue?cf10", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/my-coupon/index.vue?4d7a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "value", "components", "MescrollBody", "Empty", "mixins", "data", "CouponTypeEnum", "color", "tabs", "curTab", "type", "list", "isLoading", "upOption", "auto", "page", "size", "noMoreSize", "empty", "tip", "onLoad", "uni", "title", "onShow", "methods", "upCallback", "app", "then", "catch", "onDetail", "userCouponId", "getCouponList", "MyCouponApi", "status", "load", "resolve", "getTabValue", "onChangeTab", "onRefreshList", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACgD9pB;AACA;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAGA;AACA;AACA;EACAC;EACAC;AACA;EACAD;EACAC;AACA;EACAD;EACAC;AACA;AAAA,eAEA;EACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;QACA;QACAC;UACAC;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IAEAC;MACA;MACA;MACAC,4BACAC;QACA;QACA;QACAD;MACA,GACAE;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QACA;UAAAC;QAAA;MACA;QACA;UAAAA;QAAA;MACA;QACA;UAAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;UAAAtB;UAAAuB;UAAAlB;QAAA;UAAAmB;QAAA,GACAP;UACA;UACA;UACAD;UACAS;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAX;MACA;MACAA;IACA;IAEA;IACAY;MAAA;MACA;MACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxLA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my-coupon/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my-coupon/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=271a43c1&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=271a43c1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"271a43c1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my-coupon/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=271a43c1&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-tabs/u-tabs\" */ \"@/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ use: true }\" @down=\"downCallback\" :up=\"upOption\"\n      @up=\"upCallback\">\n\n      <!-- tab栏 -->\n      <u-tabs :list=\"tabs\" :is-scroll=\"false\" :current=\"curTab\" active-color=\"#FA2209\" :duration=\"0.2\" @change=\"onChangeTab\" />\n\n      <!-- 卡券列表 -->\n      <view class=\"goods-list\">\n          <view class=\"goods-item\" v-for=\"(item, index) in list.content\" :key=\"index\">\n            <!-- 单列卡券 -->\n            <view class=\"dis-flex\" @click=\"onDetail(item.id, item.type)\">\n                <!-- 卡券图片 -->\n                <view class=\"goods-item_left\">\n                  <image class=\"image\" :src=\"item.image\"></image>\n                </view>\n                <view class=\"goods-item_right\">\n                  <!-- 卡券名称 -->\n                  <view class=\"goods-name twolist-hidden\">\n                    <text>{{ item.name }}</text>\n                  </view>\n                  <view class=\"goods-item_desc\">\n                    <!-- 卡券卖点 -->\n                    <view class=\"desc-selling_point dis-flex\">\n                      <text class=\"onelist-hidden\">{{ item.tips }}</text>\n                    </view>\n                    <view class=\"coupon-attr\">\n                          <view class=\"attr-l\">\n                              <view class=\"desc-goods_sales dis-flex\">\n                                <text>{{ item.effectiveDate }}</text>\n                              </view>\n                              <view v-if=\"item.amount > 0\" class=\"desc_footer\">\n                                <text class=\"price_x\">¥{{ item.amount }}</text>\n                              </view>\n                          </view>\n                    </view>\n                  </view>\n                </view>\n              </view>\n          </view>\n      </view>\n    </mescroll-body>\n  </view>\n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n  import * as MyCouponApi from '@/api/myCoupon'\n  import { CouponTypeEnum } from '@/common/enum/coupon'\n  import Empty from '@/components/empty'\n\n  const color = ['red', 'blue', 'violet', 'yellow']\n  const pageSize = 15\n  const tabs = [{\n    name: `未使用`,\n    value: 'A'\n  }, {\n    name: `已使用`,\n    value: 'B'\n  }, {\n    name: `已过期`,\n    value: 'C'\n  }]\n\n  export default {\n    components: {\n      MescrollBody,\n      Empty\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        // 枚举类\n        CouponTypeEnum,\n        // 颜色组\n        color,\n        // 标签栏数据\n        tabs,\n        // 当前标签索引\n        curTab: 0,\n        // 卡券类型\n        type: \"\",\n        // 优惠券列表数据\n        list: getEmptyPaginateObj(),\n        // 正在加载\n        isLoading: false,\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于4条才显示无更多数据\n          noMoreSize: 4,\n          // 空布局\n          empty: {\n            tip: '亲，暂无卡券'\n          }\n        }\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n       let type = options.type !== undefined ? options.type : '';\n       this.type = type;\n       uni.setNavigationBarTitle({\n           title: \"我的\" + CouponTypeEnum[type].name\n       })\n    },\n    \n    onShow() {\n      // 获取页面数据\n      this.getCouponList(1);\n    },\n\n    methods: {\n        \n      upCallback(page) {\n        const app = this\n        // 设置列表数据\n        app.getCouponList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length\n            const totalSize = list.totalElements\n            app.mescroll.endBySize(curPageLen, totalSize)\n          })\n          .catch(() => app.mescroll.endErr())\n      },\n      \n      // 卡券详情\n      onDetail(userCouponId, type) {\n          if (type === 'C') {\n              this.$navTo(`pages/coupon/detail`, { userCouponId });\n          } else if(type === 'T') {\n              this.$navTo(`pages/timer/detail`, { userCouponId });\n          } else if(type === 'P') {\n              this.$navTo(`pages/prestore/detail`, { userCouponId });\n          }\n      },\n      \n      /**\n       * 获取卡券列表\n       */\n      getCouponList(pageNo = 1) {\n        const app = this;\n        return new Promise((resolve, reject) => {\n          MyCouponApi.list({ type: app.type, status: app.getTabValue(), page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data;\n              app.list.content = getMoreListData(newList, app.list, pageNo);\n              resolve(newList);\n            })\n        })\n      },\n\n      // 类型\n      getTabValue() {\n        return this.tabs[this.curTab].value;\n      },\n\n      // 切换标签项\n      onChangeTab(index) {\n        const app = this;\n        // 设置当前选中的标签\n        app.curTab = index;\n        // 刷新优惠券列表\n        app.onRefreshList();\n      },\n\n      // 刷新优惠券列表\n      onRefreshList() {\n        this.list = getEmptyPaginateObj();\n        setTimeout(() => {\n          this.mescroll.resetUpScroll();\n        }, 120)\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n.goods-list {\n  padding: 4rpx;\n  box-sizing: border-box;\n\n  .goods-item {\n    box-sizing: border-box;\n    padding: 10rpx;\n    height: 260rpx;\n    \n  .goods-item_left {\n    display: flex;\n    width: 35%;\n    align-items: center;\n    background: #fff;\n    padding: 20rpx;\n    height: 244rpx;\n    \n    .image {\n      display: block;\n      border-radius: 5rpx;\n      width: 200rpx;\n      height: 158rpx;\r\n      border: solid 1rpx #cccccc;\n    }\n  }\n  \n  .goods-item_right {\n    position: relative;\n    width: 65%;\n    background: #fff;\n  \n    .goods-name {\n      margin-top: 45rpx;\n      height: 45rpx;\n      white-space: normal;\n      color: #484848;\r\n      font-weight: bold;\n      font-size: 30rpx;\n    }\n  }\n  \n  .goods-item_desc {\n    margin-top: 0rpx;\n    .coupon-attr {\n       .attr-l {\n           float: left;\n           width: 100%;\n       }\n       .attr-r {\n           margin-top: 5rpx;\n           float:left;\n       }\n    }\n  }\n  \n  .desc-selling_point {\n    width: 400rpx;\n    font-size: 24rpx;\n    color: #e49a3d;\n  }  \n  .desc-goods_sales {\n    color: #999;\n    font-size: 24rpx;\n  }\n  \n  .desc_footer {\n    font-size: 24rpx;\n  \n    .price_x {\n      margin-right: 16rpx;\n      color: #f03c3c;\n      font-size: 30rpx;\n    }\n  \n    .price_y {\n      text-decoration: line-through;\n    }\n  }\n }\n // 空数据按钮\n .empty-ipt {\n   width: 220rpx;\n   margin: 10rpx auto;\n   font-size: 28rpx;\n   height: 64rpx;\n   line-height: 64rpx;\n   text-align: center;\n   color: #fff;\n   border-radius: 5rpx;\n   background: linear-gradient(to right, $fuint-theme, $fuint-theme);\n }\n}\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=271a43c1&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=271a43c1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420684\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}