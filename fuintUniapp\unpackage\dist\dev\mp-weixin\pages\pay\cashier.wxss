
.placeholder {
    color: #ccc;
    font-size: 28rpx;
    font-weight: normal;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
page.data-v-b4d6bb1a,
.container.data-v-b4d6bb1a {
  background: #fff;
}
.container.data-v-b4d6bb1a {
  padding-bottom: 70rpx;
}
/* 账户面板 */
.account-panel.data-v-b4d6bb1a {
  width: 650rpx;
  height: 180rpx;
  margin: 50rpx auto;
  padding: 0 60rpx;
  box-sizing: border-box;
  border-radius: 8rpx;
  color: #fff;
  background: #3f51b5;
  box-shadow: 0 5px 22px 0 rgba(0, 0, 0, 0.26);
}
.panel-lable.data-v-b4d6bb1a {
  font-size: 32rpx;
}
.recharge-label.data-v-b4d6bb1a {
  color: #333333;
  font-size: 32rpx;
  margin-bottom: 25rpx;
}
.panel-balance.data-v-b4d6bb1a {
  text-align: right;
  font-size: 46rpx;
}
.recharge-panel.data-v-b4d6bb1a {
  margin-top: 60rpx;
  padding: 0 60rpx;
}
.recharge-input.data-v-b4d6bb1a {
  margin-top: 25rpx;
}
.recharge-input .label.data-v-b4d6bb1a {
  margin-bottom: 10rpx;
}
.recharge-input .uni.data-v-b4d6bb1a {
  font-weight: bold;
  font-size: 32rpx;
}
.recharge-input input.data-v-b4d6bb1a {
  border: 1rpx solid #e4e4e4;
  border-radius: 6rpx;
  padding: 20rpx;
  font-weight: bold;
  font-size: 58rpx;
}
.remark-input.data-v-b4d6bb1a {
  margin-top: 50rpx;
}
.remark-input .label.data-v-b4d6bb1a {
  margin-bottom: 10rpx;
}
.remark-input input.data-v-b4d6bb1a {
  border: 1rpx solid #e4e4e4;
  border-radius: 6rpx;
  padding: 20rpx;
  font-size: 26rpx;
}
/* 立即充值 */
.recharge-submit.data-v-b4d6bb1a {
  margin-top: 70rpx;
}
.btn-submit .button.data-v-b4d6bb1a {
  font-size: 30rpx;
  background: linear-gradient(to right, #f9211c, #ff6335);
  border: none;
  color: white;
  border-radius: 40rpx;
  padding: 0 120rpx;
  line-height: 3;
}
.btn-submit .button[disabled].data-v-b4d6bb1a {
  background: #ff6335;
  border-color: #ff6335;
  color: white;
}
