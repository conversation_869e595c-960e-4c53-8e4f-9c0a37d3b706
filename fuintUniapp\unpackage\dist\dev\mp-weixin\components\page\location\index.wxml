<block wx:if="{{storeInfo}}"><view class="shop-header data-v-5152a28d"><view class="shop-info data-v-5152a28d"><view data-event-opts="{{[['tap',[['onTargetLocation',['$event']]]]]}}" class="shop-details data-v-5152a28d" bindtap="__e"><view class="shop-name-row data-v-5152a28d"><image class="location-icon data-v-5152a28d" src="/static/location-icon.png"></image><text class="shop-name data-v-5152a28d">{{storeInfo.name+''}}</text><image class="arrow-icon data-v-5152a28d" src="/static/nav-arrow.png"></image></view></view><view class="delivery-switch data-v-5152a28d"><view data-event-opts="{{[['tap',[['onSwitchDelivery',['oneself']]]]]}}" class="{{['switch-item','data-v-5152a28d',(currentDeliveryMode==='oneself')?'active':'']}}" bindtap="__e"><text class="switch-text data-v-5152a28d">自取</text></view><view data-event-opts="{{[['tap',[['onSwitchDelivery',['express']]]]]}}" class="{{['switch-item','data-v-5152a28d',(currentDeliveryMode==='express')?'active':'',(storeInfo.deliverySupported!=='Y')?'disabled':'']}}" bindtap="__e"><text class="switch-text data-v-5152a28d">外送</text></view></view></view></view></block>