<block wx:if="{{!isLoading}}"><view class="container b-f p-b data-v-040499d4"><view class="base data-v-040499d4"><view class="coupon-main data-v-040499d4"><view class="left data-v-040499d4"><image class="image data-v-040499d4" src="{{detail.image}}"></image></view><view class="right data-v-040499d4"><view class="item data-v-040499d4"><view class="name data-v-040499d4">{{detail.name?detail.name:''}}</view></view><block wx:if="{{detail.useRule>0}}"><view class="item data-v-040499d4"><view class="amount data-v-040499d4"><text class="num data-v-040499d4">{{detail.useRule}}</text>次卡</view></view></block></view></view><view class="item data-v-040499d4"><view class="label data-v-040499d4">有效期至：</view><view class="data-v-040499d4">{{detail.effectiveDate}}</view></view><view class="item data-v-040499d4"><view class="label data-v-040499d4">适用门店：</view><view class="data-v-040499d4">{{detail.storeNames?detail.storeNames:'全部'}}</view></view></view><block wx:if="{{detail.qrCode}}"><view class="coupon-qr data-v-040499d4"><view class="data-v-040499d4"><image class="image data-v-040499d4" src="{{detail.qrCode}}"></image></view><view class="qr-code data-v-040499d4"><view class="code _p data-v-040499d4">{{"核销码："+detail.code}}</view><view class="tips _p data-v-040499d4">请出示以上券码给核销人员</view></view></view></block><block wx:if="{{$root.g0>0}}"><view class="coupon-timer data-v-040499d4"><view class="tips data-v-040499d4">{{"核销记录("+detail.confirmCount+"/"+detail.useRule+")"}}</view><block wx:for="{{$root.l1}}" wx:for-item="row" wx:for-index="__i0__" wx:key="id"><uni-row class="time-row data-v-040499d4" vue-id="{{'7c8f21b2-1-'+__i0__}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{row.l0}}" wx:for-item="item" wx:for-index="__i1__" wx:key="id"><uni-col class="time-item data-v-040499d4" vue-id="{{('7c8f21b2-2-'+__i0__+'-'+__i1__)+','+('7c8f21b2-1-'+__i0__)}}" span="{{rowCount}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{item.$orig.isActive==true}}"><view class="data-v-040499d4"><view class="time active data-v-040499d4"></view><view class="show-time data-v-040499d4">{{item.$orig.showTime?item.m0:''}}</view></view></block><block wx:else><view class="time data-v-040499d4"></view></block></uni-col></block></uni-row></block></view></block><view class="coupon-content m-top20 data-v-040499d4"><view class="title data-v-040499d4">使用须知</view><view class="content data-v-040499d4"><jyf-parser vue-id="7c8f21b2-3" html="{{detail.description?detail.description:'暂无...'}}" class="data-v-040499d4" bind:__l="__l"></jyf-parser></view></view><shortcut vue-id="7c8f21b2-4" class="data-v-040499d4" bind:__l="__l"></shortcut><view class="receive-pop data-v-040499d4"><uni-popup vue-id="7c8f21b2-5" type="dialog" data-ref="receiveCodePopup" class="data-v-040499d4 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('7c8f21b2-6')+','+('7c8f21b2-5')}}" mode="input" focus="false" title="领取码" type="info" placeholder="请输入领取码" before-close="{{true}}" value="{{receiveCode}}" data-event-opts="{{[['^close',[['cancelReceive']]],['^confirm',[['doReceive']]],['^input',[['__set_model',['','receiveCode','$event',[]]]]]]}}" bind:close="__e" bind:confirm="__e" bind:input="__e" class="data-v-040499d4" bind:__l="__l"></uni-popup-dialog></uni-popup></view><block wx:if="{{!detail.code&&!detail.isReceive}}"><view class="footer-fixed data-v-040499d4"><view class="footer-container data-v-040499d4"><view class="foo-item-btn data-v-040499d4"><view class="btn-wrapper data-v-040499d4"><view data-event-opts="{{[['tap',[['receive',['$0'],['detail.id']]]]]}}" class="btn-item btn-item-main data-v-040499d4" bindtap="__e"><text class="data-v-040499d4">领取次卡</text></view></view></view></view></view></block></view></block>