@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.goods-list.data-v-271a43c1 {
  padding: 4rpx;
  box-sizing: border-box;
}
.goods-list .goods-item.data-v-271a43c1 {
  box-sizing: border-box;
  padding: 10rpx;
  height: 260rpx;
}
.goods-list .goods-item .goods-item_left.data-v-271a43c1 {
  display: flex;
  width: 35%;
  align-items: center;
  background: #fff;
  padding: 20rpx;
  height: 244rpx;
}
.goods-list .goods-item .goods-item_left .image.data-v-271a43c1 {
  display: block;
  border-radius: 5rpx;
  width: 200rpx;
  height: 158rpx;
  border: solid 1rpx #cccccc;
}
.goods-list .goods-item .goods-item_right.data-v-271a43c1 {
  position: relative;
  width: 65%;
  background: #fff;
}
.goods-list .goods-item .goods-item_right .goods-name.data-v-271a43c1 {
  margin-top: 45rpx;
  height: 45rpx;
  white-space: normal;
  color: #484848;
  font-weight: bold;
  font-size: 30rpx;
}
.goods-list .goods-item .goods-item_desc.data-v-271a43c1 {
  margin-top: 0rpx;
}
.goods-list .goods-item .goods-item_desc .coupon-attr .attr-l.data-v-271a43c1 {
  float: left;
  width: 100%;
}
.goods-list .goods-item .goods-item_desc .coupon-attr .attr-r.data-v-271a43c1 {
  margin-top: 5rpx;
  float: left;
}
.goods-list .goods-item .desc-selling_point.data-v-271a43c1 {
  width: 400rpx;
  font-size: 24rpx;
  color: #e49a3d;
}
.goods-list .goods-item .desc-goods_sales.data-v-271a43c1 {
  color: #999;
  font-size: 24rpx;
}
.goods-list .goods-item .desc_footer.data-v-271a43c1 {
  font-size: 24rpx;
}
.goods-list .goods-item .desc_footer .price_x.data-v-271a43c1 {
  margin-right: 16rpx;
  color: #f03c3c;
  font-size: 30rpx;
}
.goods-list .goods-item .desc_footer .price_y.data-v-271a43c1 {
  text-decoration: line-through;
}
.goods-list .empty-ipt.data-v-271a43c1 {
  width: 220rpx;
  margin: 10rpx auto;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  color: #fff;
  border-radius: 5rpx;
  background: linear-gradient(to right, #3f51b5, #3f51b5);
}
