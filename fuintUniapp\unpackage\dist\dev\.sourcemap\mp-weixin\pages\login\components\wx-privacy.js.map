{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/wx-privacy.vue?0512", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/wx-privacy.vue?82d8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/wx-privacy.vue?a3cd", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/wx-privacy.vue?2612", "uni-app:///pages/login/components/wx-privacy.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/wx-privacy.vue?4e28", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/wx-privacy.vue?df85"], "names": ["name", "emits", "props", "title", "type", "default", "protocol", "enableAutoProtocol", "subDesc", "disagreeEnabled", "disagreePromptText", "disagreeText", "agreeText", "data", "showPrivacy", "privacyContractName", "methods", "openPrivacy", "closePrivacy", "handleDisagree", "handleAgree"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,6oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4BlrB;EACAA;EACAC;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;;IACA;IACAG;MACAJ;MACAC;IACA;IACA;AACA;AACA;AACA;AACA;AACA;IACAI;MACAL;MACAC;IACA;;IACA;AACA;AACA;IACAK;MACAN;MACAC;IACA;;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3GA;AAAA;AAAA;AAAA;AAA6wC,CAAgB,wqCAAG,EAAC,C;;;;;;;;;;;ACAjyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/components/wx-privacy.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./wx-privacy.vue?vue&type=template&id=ee2dccb4&scoped=true&\"\nvar renderjs\nimport script from \"./wx-privacy.vue?vue&type=script&lang=js&\"\nexport * from \"./wx-privacy.vue?vue&type=script&lang=js&\"\nimport style0 from \"./wx-privacy.vue?vue&type=style&index=0&id=ee2dccb4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ee2dccb4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/components/wx-privacy.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-privacy.vue?vue&type=template&id=ee2dccb4&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-privacy.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-privacy.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"privacy\">\r\n        <u-popup id=\"privacy\" mode=\"bottom\" :border-radius=\"26\" v-model=\"showPrivacy\" :closeable=\"true\">\r\n            <view class=\"ws-privacy-popup\">\r\n                <view class=\"ws-privacy-popup__header\">\r\n                    <!--标题-->\r\n                    <view class=\"ws-picker__title\">{{ title }}</view>\r\n                </view>\r\n                 <scroll-view class=\"content-scroll\" :scroll-y=\"true\">\r\n                    <view class=\"ws-privacy-popup__container\">\r\n                        <text>{{ desc }}</text>\r\n                        <text>{{ subDesc }}</text>\r\n                    </view>\r\n                </scroll-view>\r\n                <view class=\"ws-privacy-popup__footer\">\r\n                    <button class=\"is-agree\" id=\"agree-btn\" open-type=\"agreePrivacyAuthorization\"\r\n                        @agreeprivacyauthorization=\"handleAgree\">\r\n                        {{agreeText}}\r\n                    </button>\r\n                    <button class=\"is-disagree\" id=\"disagree-btn\" @click=\"handleDisagree\">\r\n                        {{disagreeText}}\r\n                    </button>\r\n                </view>\r\n            </view>\r\n        </u-popup>\r\n    </view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tname: 'wxPrivacy',\r\n\t\temits: ['disagree', 'agree'],\r\n\t\tprops: {\r\n\t\t\t// 标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '用户隐私保护提示'\r\n\t\t\t},\r\n\t\t\t// 自定义隐私保护指引名称\r\n\t\t\tprotocol: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '《用户隐私保护指引》'\r\n\t\t\t},\r\n\t\t\t// 是否自动获取隐私保护指引名称（开启后调用getPrivacySetting获取名称）\r\n\t\t\tenableAutoProtocol: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false, // 默认为使用自定义隐私指引名称\r\n\t\t\t},\r\n\t\t\t// 子描述\r\n\t\t\tsubDesc: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '当您点击同意并开始使用产品服务时，即表示你已理解并同意该条款内容，该条款将对您产生法律约束力。如您拒绝，将无法使用相应服务。'\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 控制是否可以点击不同意按钮并显示提示。\r\n\t\t\t * 如果设置为 true，用户可以点击不同意按钮执行后续逻辑。\r\n\t\t\t * 如果设置为 false，点击不同意按钮会显示提示信息，但不会执行后续逻辑。\r\n\t\t\t * 默认为 true\r\n\t\t\t */\r\n\t\t\tdisagreeEnabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true, // 默认为可以点击\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 配置不同意按钮的提示消息内容。\r\n\t\t\t */\r\n\t\t\tdisagreePromptText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '请先仔细阅读并同意隐私协议', // 默认提示消息\r\n\t\t\t},\r\n\t\t\t// 拒绝按钮文字\r\n\t\t\tdisagreeText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '不同意'\r\n\t\t\t},\r\n\t\t\t// 同意按钮文字\r\n\t\t\tagreeText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '同意并继续'\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n              showPrivacy: false,\r\n\t\t\t  privacyContractName: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n            // 打开弹窗\r\n            openPrivacy() {\r\n              this.showPrivacy = true;\r\n            },\r\n            \r\n            // 关闭弹窗\r\n            closePrivacy() {\r\n              this.showPrivacy = false;\r\n            },\r\n            \r\n\t\t\t// 拒绝隐私协议\r\n\t\t\thandleDisagree() {\r\n              this.$emit('disagree')\r\n\t\t\t},\r\n\r\n\t\t\t// 同意隐私协议\r\n\t\t\thandleAgree() {\r\n\t\t\t  this.$emit('agree')\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.ws-privacy-popup {\r\n\t\tpadding: 24rpx;\r\n\t\tbox-sizing: border-box;\r\n\t\toverflow: hidden;\r\n\t\twidth: 100%;\r\n\t\tbackground: #fff;\r\n\t\tborder-radius: 10rpx;\r\n        margin-top: 50rpx;\r\n        \r\n        .content-scroll {\r\n          min-height: 400rpx;\r\n          max-height: 750rpx;\r\n        }\r\n\r\n\t\t&__header {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 52rpx;\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\tfont-family: PingFangSC-Medium, PingFang SC;\r\n\t\t\tfont-weight: 550;\r\n\t\t\tcolor: #1a1a1a;\r\n\t\t\tline-height: 52rpx;\r\n\t\t\tmargin-bottom: 48rpx;\r\n\t\t}\r\n\r\n\t\t&__container {\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tfont-family: PingFangSC-Regular, PingFang SC;\r\n\t\t\tfont-weight: 400;\r\n\t\t\tcolor: #333333;\r\n\t\t\tline-height: 48rpx;\r\n\t\t\tmargin-bottom: 48rpx;\r\n\r\n\t\t\t&-protocol {\r\n\t\t\t\tfont-weight: 550;\r\n\t\t\t\tcolor: $fuint-theme;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__footer {\r\n\t\t\tdisplay: flex;\r\n\t\t\tflex-direction: column;\r\n            margin-top: 30rpx;\r\n\t\t\t.is-disagree,\r\n\t\t\t.is-agree {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\theight: 88rpx;\r\n                text-align: center;\r\n\t\t\t\tbackground: #ffffff;\r\n\t\t\t\tborder-radius: 44rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n                display: flex;\r\n                justify-content: center;\r\n                align-items: center;\r\n\t\t\t\tfont-family: PingFangSC-Regular, PingFang SC;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tcolor: #666666;\r\n\t\t\t}\r\n\r\n\t\t\t.is-agree {\r\n\t\t\t\tbackground: $fuint-theme;\r\n\t\t\t\tcolor: #ffffff;\r\n\t\t\t\tmargin-bottom: 18rpx;\r\n\t\t\t}\r\n\r\n\t\t\tbutton {\r\n\t\t\t\tborder: none;\r\n\t\t\t\toutline: none;\r\n\r\n\t\t\t\t&::after {\r\n\t\t\t\t\tborder: none;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-privacy.vue?vue&type=style&index=0&id=ee2dccb4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./wx-privacy.vue?vue&type=style&index=0&id=ee2dccb4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426019\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}