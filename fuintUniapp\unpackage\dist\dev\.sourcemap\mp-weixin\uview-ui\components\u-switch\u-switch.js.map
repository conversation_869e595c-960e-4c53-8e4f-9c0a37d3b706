{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-switch/u-switch.vue?e837", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-switch/u-switch.vue?b09c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-switch/u-switch.vue?2575", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-switch/u-switch.vue?768f", "uni-app:///uview-ui/components/u-switch/u-switch.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-switch/u-switch.vue?e0e1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-switch/u-switch.vue?85d3"], "names": ["name", "props", "loading", "type", "default", "disabled", "size", "activeColor", "inactiveColor", "value", "vibrateShort", "activeValue", "inactiveValue", "data", "computed", "switchStyle", "style", "loadingColor", "methods", "onClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACahrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAcA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACA,QAEA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzGA;AAAA;AAAA;AAAA;AAA2wC,CAAgB,sqCAAG,EAAC,C;;;;;;;;;;;ACA/xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-switch/u-switch.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-switch.vue?vue&type=template&id=7cafa8c0&scoped=true&\"\nvar renderjs\nimport script from \"./u-switch.vue?vue&type=script&lang=js&\"\nexport * from \"./u-switch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-switch.vue?vue&type=style&index=0&id=7cafa8c0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7cafa8c0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-switch/u-switch.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=template&id=7cafa8c0&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-loading/u-loading\" */ \"@/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.switchStyle])\n  var g0 = _vm.$u.addUnit(this.size)\n  var g1 = _vm.$u.addUnit(this.size)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"u-switch\" :class=\"[value == true ? 'u-switch--on' : '', disabled ? 'u-switch--disabled' : '']\" @tap=\"onClick\"\n     :style=\"[switchStyle]\">\n        <view class=\"u-switch__node node-class\" :style=\"{\n            width: $u.addUnit(this.size),\n            height: $u.addUnit(this.size)\n        }\">\n            <u-loading :show=\"loading\" class=\"u-switch__loading\" :size=\"size * 0.6\" :color=\"loadingColor\" />\n        </view>\n    </view>\n</template>\n\n<script>\n    /**\n     * switch 开关选择器\n     * @description 选择开关一般用于只有两个选择，且只能选其一的场景。\n     * @tutorial https://www.uviewui.com/components/switch.html\n     * @property {Boolean} loading 是否处于加载中（默认false）\n     * @property {Boolean} disabled 是否禁用（默认false）\n     * @property {String Number} size 开关尺寸，单位rpx（默认50）\n     * @property {String} active-color 打开时的背景色（默认#2979ff）\n     * @property {Boolean} inactive-color 关闭时的背景色（默认#ffffff）\n     * @property {Boolean | Number | String} active-value 打开选择器时通过change事件发出的值（默认true）\n     * @property {Boolean | Number | String} inactive-value 关闭选择器时通过change事件发出的值（默认false）\n     * @event {Function} change 在switch打开或关闭时触发\n     * @example <u-switch v-model=\"checked\" active-color=\"red\" inactive-color=\"#eee\"></u-switch>\n     */\n    export default {\n        name: \"u-switch\",\n        props: {\n            // 是否为加载中状态\n            loading: {\n                type: Boolean,\n                default: false\n            },\n            // 是否为禁用装填\n            disabled: {\n                type: Boolean,\n                default: false\n            },\n            // 开关尺寸，单位rpx\n            size: {\n                type: [Number, String],\n                default: 50\n            },\n            // 打开时的背景颜色\n            activeColor: {\n                type: String,\n                default: '#2979ff'\n            },\n            // 关闭时的背景颜色\n            inactiveColor: {\n                type: String,\n                default: '#ffffff'\n            },\n            // 通过v-model双向绑定的值\n            value: {\n                type: Boolean,\n                default: false\n            },\n            // 是否使手机发生短促震动，目前只在iOS的微信小程序有效(2020-05-06)\n            vibrateShort: {\n                type: Boolean,\n                default: false\n            },\n            // 打开选择器时的值\n            activeValue: {\n                type: [Number, String, Boolean],\n                default: true\n            },\n            // 关闭选择器时的值\n            inactiveValue: {\n                type: [Number, String, Boolean],\n                default: false\n            },\n        },\n        data() {\n            return {\n\n            }\n        },\n        computed: {\n            switchStyle() {\n                let style = {};\n                style.fontSize = this.size + 'rpx';\n                style.backgroundColor = this.value ? this.activeColor : this.inactiveColor;\n                return style;\n            },\n            loadingColor() {\n                return this.value ? this.activeColor : null;\n            }\n        },\n        methods: {\n            onClick() {\n                if (!this.disabled && !this.loading) {\n                    // 使手机产生短促震动，微信小程序有效，APP(HX 2.6.8)和H5无效\n                    if(this.vibrateShort) uni.vibrateShort();\n                    this.$emit('input', !this.value);\n                    // 放到下一个生命周期，因为双向绑定的value修改父组件状态需要时间，且是异步的\n                    this.$nextTick(() => {\n                        this.$emit('change', this.value ? this.activeValue : this.inactiveValue);\n                    })\n                }\n            }\n        }\n    };\n</script>\n\n<style lang=\"scss\" scoped>\n    @import \"../../libs/css/style.components.scss\";\n    \n    .u-switch {\n        position: relative;\n        /* #ifndef APP-NVUE */\n        display: inline-block;\n        /* #endif */\n        box-sizing: initial;\n        width: 2em;\n        height: 1em;\n        background-color: #fff;\n        border: 1px solid rgba(0, 0, 0, 0.1);\n        border-radius: 1em;\n        transition: background-color 0.3s;\n        font-size: 50rpx;\n    }\n\n    .u-switch__node {\n        @include vue-flex;\n        align-items: center;\n        justify-content: center;\n        position: absolute;\n        top: 0;\n        left: 0;\n        border-radius: 100%;\n        z-index: 1;\n        background-color: #fff;\n        background-color: #fff;\n        box-shadow: 0 3px 1px 0 rgba(0, 0, 0, 0.05), 0 2px 2px 0 rgba(0, 0, 0, 0.1), 0 3px 3px 0 rgba(0, 0, 0, 0.05);\n        box-shadow: 0 3px 1px 0 rgba(0, 0, 0, 0.05), 0 2px 2px 0 rgba(0, 0, 0, 0.1), 0 3px 3px 0 rgba(0, 0, 0, 0.05);\n        transition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05);\n        transition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05), -webkit-transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05);\n        transition: transform cubic-bezier(0.3, 1.05, 0.4, 1.05);\n        transition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05)\n    }\n\n    .u-switch__loading {\n        @include vue-flex;\n        align-items: center;\n        justify-content: center;\n    }\n\n    .u-switch--on {\n        background-color: #1989fa;\n    }\n\n    .u-switch--on .u-switch__node {\n        transform: translateX(100%);\n    }\n\n    .u-switch--disabled {\n        opacity: 0.4;\n    }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=style&index=0&id=7cafa8c0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-switch.vue?vue&type=style&index=0&id=7cafa8c0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425101\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}