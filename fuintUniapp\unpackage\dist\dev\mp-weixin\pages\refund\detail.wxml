<block wx:if="{{!isLoading}}"><view class="container p-bottom data-v-70953b1f"><view class="detail-header dis-flex flex-y-center data-v-70953b1f"><view class="header-backdrop data-v-70953b1f"><image class="image data-v-70953b1f" src="/static/order/refund-bg.png"></image></view><view class="header-state data-v-70953b1f"><block wx:if="{{detail.status==RefundStatusEnum.A.key}}"><text class="f-32 col-f data-v-70953b1f">{{RefundStatusEnum.A.name}}</text></block><block wx:if="{{detail.status==RefundStatusEnum.B.key}}"><text class="f-32 col-f data-v-70953b1f">{{RefundStatusEnum.B.name}}</text></block><block wx:if="{{detail.status==RefundStatusEnum.C.key}}"><text class="f-32 col-f data-v-70953b1f">{{RefundStatusEnum.C.name}}</text></block><block wx:if="{{detail.status==RefundStatusEnum.D.key}}"><text class="f-32 col-f data-v-70953b1f">{{RefundStatusEnum.D.name}}</text></block><block wx:if="{{detail.status==RefundStatusEnum.E.key}}"><text class="f-32 col-f data-v-70953b1f">{{RefundStatusEnum.E.name}}</text></block></view></view><block wx:for="{{detail.orderInfo.goods}}" wx:for-item="goods" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onGoodsDetail',['$0'],[[['detail.orderInfo.goods','',index,'goodsId']]]]]]]}}" class="detail-goods b-f m-top20 dis-flex flex-dir-row data-v-70953b1f" bindtap="__e"><view class="left data-v-70953b1f"><image class="goods-image data-v-70953b1f" src="{{goods.image}}"></image></view><view class="right dis-flex flex-box flex-dir-column flex-x-around data-v-70953b1f"><view class="goods-name data-v-70953b1f"><text class="twolist-hidden data-v-70953b1f">{{goods.name}}</text></view><view class="dis-flex col-9 f-24 data-v-70953b1f"><view class="flex-box data-v-70953b1f"><view class="goods-props clearfix data-v-70953b1f"><block wx:for="{{goods.specList}}" wx:for-item="props" wx:for-index="idx" wx:key="idx"><view class="goods-props-item data-v-70953b1f"><text class="data-v-70953b1f">{{props.specName}}</text></view></block></view></view><text class="t-r data-v-70953b1f">{{"×"+goods.num}}</text></view></view></view></block><view class="detail-order b-f row-block data-v-70953b1f"><view class="item dis-flex flex-x-end flex-y-center data-v-70953b1f"><text class="data-v-70953b1f">订单金额：</text><text class="col-m data-v-70953b1f">{{"￥"+detail.orderInfo.amount}}</text></view></view><block wx:if="{{detail.status=='B'&&detail.type=='A'}}"><view class="detail-order b-f row-block dis-flex flex-x-end flex-y-center data-v-70953b1f"><text class="data-v-70953b1f">已退款金额：</text><text class="col-m data-v-70953b1f">{{"￥"+detail.refundMoney}}</text></view></block><view class="detail-refund b-f m-top20 data-v-70953b1f"><view class="detail-refund__row dis-flex data-v-70953b1f"><view class="text data-v-70953b1f"><text class="data-v-70953b1f">售后类型：</text></view><view class="flex-box data-v-70953b1f"><block wx:if="{{detail.type==RefundTypeEnum.RETURN.value}}"><text class="data-v-70953b1f">{{RefundTypeEnum.RETURN.name}}</text></block><block wx:if="{{detail.type==RefundTypeEnum.EXCHANGE.value}}"><text class="data-v-70953b1f">{{RefundTypeEnum.EXCHANGE.name}}</text></block></view></view><view class="detail-refund__row dis-flex data-v-70953b1f"><view class="text data-v-70953b1f"><text class="data-v-70953b1f">申请原因：</text></view><view class="flex-box data-v-70953b1f"><text class="data-v-70953b1f">{{detail.remark}}</text></view></view><block wx:if="{{$root.g0}}"><view class="detail-refund__row dis-flex data-v-70953b1f"><view class="text data-v-70953b1f"><text class="data-v-70953b1f">申请凭证：</text></view><view class="image-list flex-box data-v-70953b1f"><block wx:for="{{detail.imageList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="image-preview data-v-70953b1f"><image class="image data-v-70953b1f" mode="aspectFill" src="{{item}}" data-event-opts="{{[['tap',[['handlePreviewImages',[index]]]]]}}" bindtap="__e"></image></view></block></view></view></block></view><block wx:if="{{detail.status==RefundStatusEnum.C.key}}"><view class="detail-refund b-f m-top20 data-v-70953b1f"><view class="detail-refund__row dis-flex data-v-70953b1f"><view class="text data-v-70953b1f"><text class="col-m data-v-70953b1f">拒绝原因：</text></view><view class="flex-box data-v-70953b1f"><text class="data-v-70953b1f">{{detail.remark}}</text></view></view></view></block><block wx:if="{{detail.status==RefundStatusEnum.B.key}}"><view class="detail-address b-f m-top20 data-v-70953b1f"><view class="detail-address__row address-title data-v-70953b1f"><text class="col-m data-v-70953b1f">退货地址</text></view><view class="detail-address__row address-details data-v-70953b1f"><view class="address-details__row data-v-70953b1f"><text class="data-v-70953b1f">{{"收货人名："+(detail.address.name?detail.address.name:'-')}}</text></view><view class="address-details__row data-v-70953b1f"><text class="data-v-70953b1f">{{"联系电话："+(detail.address.mobile?detail.address.mobile:'-')}}</text></view><view class="address-details__row dis-flex data-v-70953b1f"><view class="text data-v-70953b1f"><text class="data-v-70953b1f">详细地址：</text></view><view class="address flex-box data-v-70953b1f"><block wx:for="{{detail.address.region}}" wx:for-item="region" wx:for-index="idx" wx:key="idx"><text class="region data-v-70953b1f">{{region}}</text></block><text class="detail data-v-70953b1f">{{detail.address.detail?detail.address.detail:'-'}}</text></view></view></view><view class="detail-address__row address-tips data-v-70953b1f"><view class="f-26 col-9 data-v-70953b1f"><text class="data-v-70953b1f">· 未与卖家协商一致情况下，请勿寄到付或平邮</text></view><view class="f-26 col-9 data-v-70953b1f"><text class="data-v-70953b1f">· 请填写真实有效物流信息</text></view></view></view></block><block wx:if="{{detail.type==RefundTypeEnum.RETURN.value&&detail.status==RefundStatusEnum.B.value&&!detail.is_user_send}}"><form data-event-opts="{{[['submit',[['onSubmit']]]]}}" bindsubmit="__e" class="data-v-70953b1f"><view class="detail-express b-f m-top20 data-v-70953b1f"><view class="form-group dis-flex flex-y-center data-v-70953b1f"><view class="field data-v-70953b1f">物流公司：</view><view class="flex-box data-v-70953b1f"><input class="input data-v-70953b1f" placeholder="请填写物流公司名称" data-event-opts="{{[['input',[['__set_model',['','expressName','$event',[]]]]]]}}" value="{{expressName}}" bindinput="__e"/></view></view><view class="form-group dis-flex flex-y-center data-v-70953b1f"><view class="field data-v-70953b1f">物流单号：</view><view class="flex-box data-v-70953b1f"><input class="input data-v-70953b1f" placeholder="请填写物流单号" data-event-opts="{{[['input',[['__set_model',['','expressNo','$event',[]]]]]]}}" value="{{expressNo}}" bindinput="__e"/></view></view></view><view class="footer data-v-70953b1f"><view class="btn-wrapper data-v-70953b1f"><button class="{{['btn-item','btn-item-main','btn-normal','data-v-70953b1f',(disabled)?'disabled':'']}}" formType="submit">确认发货</button></view></view></form></block></view></block>