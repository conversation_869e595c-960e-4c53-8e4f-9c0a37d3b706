@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.content.data-v-52bf62ce {
  padding-bottom: 50rpx;
}
.content .top-v.data-v-52bf62ce {
  margin: 20rpx;
}
.content .top-v .storeName.data-v-52bf62ce {
  font-weight: bold;
  font-size: 32rpx;
}
.content .top-v .moreStore.data-v-52bf62ce {
  float: right;
  color: #3f51b5;
  border: 1rpx solid #3f51b5;
  padding: 6rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.getInfo-v.data-v-52bf62ce {
  background-color: #fff;
  padding: 50rpx 30rpx;
  border-radius: 20rpx;
  width: 600rpx;
}
.getInfo-v .getInfo-btn.data-v-52bf62ce {
  background-color: #3f51b5;
  color: #fff;
  padding: 20rpx;
  border-radius: 10rpx;
  margin-top: 30rpx;
  text-align: center;
}
.btn.data-v-52bf62ce {
  margin: 20rpx auto;
  background-color: #3f51b5;
  padding: 20rpx;
  border-radius: 40rpx;
  text-align: center;
  color: #fff;
  width: 680rpx;
  font-size: 30rpx;
  margin-top: 50rpx;
}
.info-v.data-v-52bf62ce {
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}
.info-v .title.data-v-52bf62ce {
  font-weight: bold;
  color: #3f51b5;
}
.info-v .list-v.data-v-52bf62ce {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
}
.info-v .list-v .item-v.data-v-52bf62ce {
  border-radius: 12rpx;
  font-size: 30rpx;
  margin-top: 10rpx;
  margin-left: 10rpx;
  font-weight: bold;
  width: 30%;
  border: 1rpx solid #ccc;
  text-align: center;
  padding: 20rpx;
}
.info-v .list-v .activeItem.data-v-52bf62ce {
  font-size: 30rpx;
  border-radius: 12rpx;
  margin-top: 10rpx;
  margin-left: 10rpx;
  width: 30%;
  font-weight: bold;
  background-color: #3f51b5;
  border: 1rpx solid #ccc;
  color: #fff;
  text-align: center;
  padding: 20rpx;
}
.info-v .list-v .disable.data-v-52bf62ce {
  border-radius: 12rpx;
  font-size: 30rpx;
  margin-top: 10rpx;
  margin-left: 10rpx;
  font-weight: bold;
  width: 30%;
  border: 1rpx solid #ccc;
  text-align: center;
  color: white !important;
  background-color: #bcbcbc !important;
  padding: 20rpx;
}
