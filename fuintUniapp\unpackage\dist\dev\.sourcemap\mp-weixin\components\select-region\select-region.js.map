{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/select-region/select-region.vue?116c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/select-region/select-region.vue?f35c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/select-region/select-region.vue?c736", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/select-region/select-region.vue?e51a", "uni-app:///components/select-region/select-region.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/select-region/select-region.vue?dcab", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/select-region/select-region.vue?b476"], "names": ["keys", "findOptionsKey", "name", "mixins", "model", "prop", "event", "props", "value", "type", "default", "placeholder", "data", "isLoading", "show", "defaultValue", "valueText", "options", "watch", "created", "methods", "handleSelect", "getTreeData", "app", "RegionModel", "then", "finally", "onConfirm", "setDefaultValue", "getOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "label", "optionItem"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0NAEN;AACP,KAAK;AACL;AACA,aAAa,oNAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAkpB,CAAgB,gpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACatqB;AACA;AACA;;;;;;;;;;;;;;AAEA;AACA;AACA;EAAA;EAAA;EACA;IAAA;EAAA;EACA;IACAA;IACA;MACAC;IACA;EACA;EACA;AACA;AAAA,gBAEA;EACAC;EACAC;EACAC;IACAC;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAV;MACA;MACA;QAAA;MAAA;MACA;MACA;MACA;IACA;EACA;EACAW;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAC;MACAC,8BACAC;QACA;QACA;MACA,GACAC;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;QAAA;MAAA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QAAAC;MACA;MACA;QACA;QACA;QACA;UACAtB;UACAuB;QACA;QACA;UACAC;QACA;QACAf;MACA;MAEA;IACA;IAEA;IACAa;MACA;QACA;MACA;MACA;QACA;MACA;MACA;IACA;EAEA;AACA;AAAA,4B;;;;;;;;;;;;ACxJA;AAAA;AAAA;AAAA;AAAqvC,CAAgB,2qCAAG,EAAC,C;;;;;;;;;;;ACAzwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/select-region/select-region.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./select-region.vue?vue&type=template&id=1ed35bd0&scoped=true&\"\nvar renderjs\nimport script from \"./select-region.vue?vue&type=script&lang=js&\"\nexport * from \"./select-region.vue?vue&type=script&lang=js&\"\nimport style0 from \"./select-region.vue?vue&type=style&index=0&id=1ed35bd0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1ed35bd0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/select-region/select-region.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./select-region.vue?vue&type=template&id=1ed35bd0&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-loading/u-loading\" */ \"@/uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n    uSelect: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-select/u-select\" */ \"@/uview-ui/components/u-select/u-select.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./select-region.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./select-region.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view v-if=\"isLoading\" class=\"loading\">\n      <u-loading mode=\"circle\"></u-loading>\n    </view>\n    <view v-else class=\"field-body\" @click=\"handleSelect()\">\n      <view class=\"field-value onelist-hidden\">{{ valueText ? valueText: placeholder }}</view>\n    </view>\n    <u-select v-model=\"show\" mode=\"mutil-column-auto\" :list=\"options\" :default-value=\"defaultValue\" @confirm=\"onConfirm\"></u-select>\n  </view>\n</template>\n\n<script>\n  import Emitter from '@/uview-ui/libs/util/emitter'\n  import { isEmpty } from '@/utils/util'\n  import RegionModel from '@/common/model/Region'\n\n  // 根据选中的value集获取索引集keys\n  // 用于设置默认选中\n  const findOptionsKey = (data, searchValue, deep = 1, keys = []) => {\n    const index = data.findIndex(item => item.value === searchValue[deep - 1])\n    if (index > -1) {\n      keys.push(index)\n      if (data[index].children) {\n        findOptionsKey(data[index].children, searchValue, ++deep, keys)\n      }\n    }\n    return keys\n  }\n\n  export default {\n    name: 'SelectRegion',\n    mixins: [Emitter],\n    model: {\n      prop: 'value',\n      event: 'change'\n    },\n    props: {\n      // v-model 指定选中项\n      value: {\n        type: Array,\n        default: () => {\n          return []\n        }\n      },\n      // 未选中时的提示文字\n      placeholder: {\n        type: String,\n        default: '请选择省/市/区'\n      }\n    },\n    data() {\n      return {\n        // 正在加载\n        isLoading: true,\n        // 是否显示\n        show: false,\n        // 默认选中的值\n        defaultValue: [],\n        // 选中项内容(文本展示)\n        valueText: '',\n        // 级联选择器数据\n        options: []\n      }\n    },\n    watch: {\n      // 监听v-model\n      value(val) {\n        // 设置默认选中的值\n        this.valueText = val.map(item => item.label).join('/')\n        this.setDefaultValue(val)\n        // 将当前的值发送到 u-form-item 进行校验\n        this.dispatch('u-form-item', 'on-form-change', val)\n      },\n    },\n    created() {\n      // 获取地区数据\n      this.getTreeData()\n    },\n\n    methods: {\n\n      // 打开选择器\n      handleSelect() {\n        this.show = true\n      },\n\n      // 获取地区数据\n      getTreeData() {\n        const app = this\n        app.isLoading = true\n        RegionModel.getTreeData()\n          .then(regions => {\n            // 格式化级联选择器数据\n            this.options = this.getOptions(regions)\n          })\n          .finally(() => app.isLoading = false)\n      },\n\n      // 确认选择后的回调\n      onConfirm(value) {\n        // 绑定到v-model执行的值\n        this.$emit('input', value)\n        this.$emit('change', value)\n      },\n\n      /**\n       * 设置默认选中的值\n       * 该操作是为了每次打开选择器时聚焦到上次选择\n       * @param {Object} value\n       */\n      setDefaultValue(value) {\n        const values = value.map(item => item.value)\n        const options = this.options\n        this.defaultValue = findOptionsKey(options, values)\n      },\n\n      /**\n       * 格式化级联选择器数据\n       * @param {*} regions 地区数据\n       */\n      getOptions(regions) {\n        const { getOptions, getChildren } = this\n        const options = []\n        for (const index in regions) {\n          const item = regions[index]\n          const children = getChildren(item)\n          const optionItem = {\n            value: item.id,\n            label: item.name\n          }\n          if (children !== false) {\n            optionItem.children = getOptions(children)\n          }\n          options.push(optionItem)\n        }\n        \n        return options\n      },\n\n      // 获取子集地区\n      getChildren(item) {\n        if (item.city) {\n          return item.city\n        }\n        if (item.region) {\n          return item.region\n        }\n        return false\n      }\n\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    width: 100%;\n  }\n\n  .loading {\n    padding-left: 10rpx;\n    // text-align: center;\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./select-region.vue?vue&type=style&index=0&id=1ed35bd0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./select-region.vue?vue&type=style&index=0&id=1ed35bd0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420523\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}