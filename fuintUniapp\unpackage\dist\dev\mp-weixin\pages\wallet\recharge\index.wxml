<block wx:if="{{userInfo.id}}"><view class="container data-v-dfd304d2"><view class="account-panel dis-flex flex-y-center data-v-dfd304d2"><view class="panel-lable data-v-dfd304d2"><text class="data-v-dfd304d2">账户余额</text></view><view class="panel-balance flex-box data-v-dfd304d2"><text class="data-v-dfd304d2">{{"￥"+userInfo.balance}}</text></view></view><view class="recharge-panel data-v-dfd304d2"><view class="recharge-label data-v-dfd304d2"><text class="data-v-dfd304d2">充值金额</text></view><block wx:if="{{$root.g0>0}}"><view class="recharge-plan clearfix data-v-dfd304d2"><block wx:for="{{setting.planList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-dfd304d2"><view data-event-opts="{{[['tap',[['onSelectPlan',['$0'],[[['setting.planList','',index,'rechargeAmount']]]]]]]}}" class="{{['recharge-plan_item','data-v-dfd304d2',(rechargeAmount==item.rechargeAmount)?'active':'']}}" bindtap="__e"><view class="plan_money data-v-dfd304d2"><text class="data-v-dfd304d2">{{item.rechargeAmount}}</text></view><block wx:if="{{item.giveAmount>0}}"><view class="plan_gift data-v-dfd304d2"><text class="data-v-dfd304d2">{{"送"+item.giveAmount}}</text></view></block></view></block></block></view></block><block wx:if="{{$root.g1}}"><view class="recharge-input data-v-dfd304d2"><input type="digit" placeholder="请输入充值金额" data-event-opts="{{[['input',[['__set_model',['','inputValue','$event',[]]],['onChangeMoney',['$event']]]]]}}" value="{{inputValue}}" bindinput="__e" class="data-v-dfd304d2"/></view></block><view class="recharge-submit btn-submit data-v-dfd304d2"><form data-event-opts="{{[['submit',[['onSubmit',['$event']]]]]}}" bindsubmit="__e" class="data-v-dfd304d2"><button class="button data-v-dfd304d2" formType="submit" disabled="{{disabled}}">立即充值</button></form></view></view><block wx:if="{{$root.g2>0}}"><view class="recharge-describe data-v-dfd304d2"><view class="recharge-label data-v-dfd304d2"><text class="data-v-dfd304d2">充值说明</text></view><view class="content data-v-dfd304d2"><text space="ensp" class="data-v-dfd304d2">{{setting.remark}}</text></view></view></block></view></block>