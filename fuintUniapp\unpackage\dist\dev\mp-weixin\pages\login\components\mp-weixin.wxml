<view class="container data-v-3aaf109c"><view class="wechatapp data-v-3aaf109c"><view class="header data-v-3aaf109c"><open-data class="avatar data-v-3aaf109c" type="userAvatarUrl"></open-data></view></view><view class="auth-title data-v-3aaf109c">申请获取以下权限</view><block wx:if="{{isProfile}}"><view class="data-v-3aaf109c"><view class="auth-subtitle data-v-3aaf109c">获得您微信绑定的手机号码</view><view class="login-btn data-v-3aaf109c"><button class="button-mobile btn-primary data-v-3aaf109c" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['getPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">授权手机号</button></view></view></block><block wx:if="{{!isProfile}}"><view class="data-v-3aaf109c"><view class="auth-subtitle data-v-3aaf109c">获得你的公开信息（昵称、头像等）</view><view class="login-btn data-v-3aaf109c"><button data-event-opts="{{[['tap',[['getUserProfile',['$event']]]]]}}" class="button btn-normal data-v-3aaf109c" catchtap="__e">授权登录</button></view></view></block><view class="no-login-btn data-v-3aaf109c"><button data-event-opts="{{[['tap',[['cancelLogin',['$event']]]]]}}" class="button btn-normal data-v-3aaf109c" bindtap="__e">暂不登录</button></view><view class="privacy data-v-3aaf109c"><label data-event-opts="{{[['tap',[['privacyChange',['$event']]]]]}}" bindtap="__e" class="data-v-3aaf109c"><checkbox style="transform:scale(0.7);" checked="{{agreePrivacy}}" value="agree" color="#fa2209" class="data-v-3aaf109c"></checkbox>已阅读并同意</label><text data-event-opts="{{[['tap',[['openPrivacy',[1]]]]]}}" class="privacy-ptl data-v-3aaf109c" bindtap="__e">《隐私协议》</text><text data-event-opts="{{[['tap',[['openPrivacy',[2]]]]]}}" class="member-ptl data-v-3aaf109c" bindtap="__e">《用户协议》</text><weixin-privacy vue-id="7266ff94-1" title="{{protocolTitle}}" subDesc="{{protocolSubDesc}}" data-ref="popPrivacy" data-event-opts="{{[['^agree',[['handleAgree']]],['^disagree',[['handleDisagree']]]]}}" bind:agree="__e" bind:disagree="__e" class="data-v-3aaf109c vue-ref" bind:__l="__l"></weixin-privacy></view></view>