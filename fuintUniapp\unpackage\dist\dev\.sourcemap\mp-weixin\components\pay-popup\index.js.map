{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/pay-popup/index.vue?9e26", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/pay-popup/index.vue?ff8b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/pay-popup/index.vue?559e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/pay-popup/index.vue?d760", "uni-app:///components/pay-popup/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/pay-popup/index.vue?bc5d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/pay-popup/index.vue?7a22"], "names": ["name", "props", "value", "Type", "default", "payInfo", "maskCloseAble", "showClose", "closeImage", "data", "complete", "usePoint", "showPayPopup", "PayTypeEnum", "mounted", "that", "methods", "init", "open", "close", "moveHandle", "toPay", "payNow", "couponId", "SettlementApi", "then", "catch", "onGetLogin", "uni", "url", "onSubmitCallback", "app", "amount", "point", "finally", "modifyPoint", "modifyCoupon", "doUsePoint", "doUseCoupon", "closeDialog", "toast", "title", "icon", "watch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,gWAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpHA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACwH9pB;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AAAA,eACA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;EACA;EACAK;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;IACAC;MACA;IAAA,CACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;gBACAA;gBACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAI;MACA;QACAJ;QACAA;MACA;QACA;UACAA;UACAA;QACA;MACA;IACA;IAEAK;MACA;IAAA,CACA;IACA;IACAC;MACA;QACAN;MACA;QACAA;MACA;IACA;IACA;IACAO;MACA;MACA;MACA;MACA;QACAC;MACA;MACAC,+KACAC;QAAA;MAAA,GACAC;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACAC;QACA;UACAA;QACA;QACA;MACA;;MAEA;MACA;QACA,yCACAN;UACAM;YAAAC;YAAAC;UAAA;QACA,GACAP;UAAA,OACAK;QAAA,GACAG;UACA;QAAA,CACA;MACA;;MAEA;MACA;QACA;UACAH;YAAAC;YAAAC;UAAA;QACA;UACA;YACAF;UACA;YACAA;UACA;QACA;MACA;IACA;IACAI;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA1B;MACA;MACA;QACA;QACA;MACA;MAEA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;MAEA;MAEA;MACA;IACA;IACA2B;MACA;QACA;MACA;QACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACAZ;QACAa;QACAC;MACA;IACA;EACA;EACAC;IACAzC;MACA;QACAa;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClUA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/pay-popup/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=65b5fac2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=65b5fac2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"65b5fac2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/pay-popup/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=65b5fac2&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniPopupDialog: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog\" */ \"@/uni_modules/uni-popup/components/uni-popup-dialog/uni-popup-dialog.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = !_vm.showPayPopup\n    ? _vm.payInfo.isLogin &&\n      parseFloat(_vm.payInfo.totalAmount) -\n        parseFloat(_vm.payInfo.totalAmount) *\n          parseFloat(_vm.payInfo.payDiscount) >\n        0\n    : null\n  var g0 =\n    !_vm.showPayPopup && m0 && _vm.payInfo.payDiscount < 1\n      ? parseFloat(_vm.payInfo.payDiscount * 10).toFixed(2)\n      : null\n  var g1 =\n    !_vm.showPayPopup && m0 && _vm.payInfo.payDiscount < 1\n      ? (\n          parseFloat(_vm.payInfo.totalAmount) -\n          parseFloat(_vm.payInfo.totalAmount) *\n            parseFloat(_vm.payInfo.payDiscount)\n        ).toFixed(2)\n      : null\n  var g2 =\n    !_vm.showPayPopup &&\n    _vm.payInfo.isLogin &&\n    _vm.payInfo.pointAmount >= 0.01 &&\n    _vm.payInfo.canUsedAsMoney == \"true\" &&\n    _vm.payInfo.usePoint > 0\n      ? _vm.payInfo.usePoint.toFixed(0)\n      : null\n  var g3 =\n    !_vm.showPayPopup &&\n    _vm.payInfo.isLogin &&\n    _vm.payInfo.pointAmount >= 0.01 &&\n    _vm.payInfo.canUsedAsMoney == \"true\" &&\n    _vm.payInfo.usePoint > 0\n      ? parseFloat(_vm.payInfo.pointAmount).toFixed(2)\n      : null\n  var g4 =\n    !_vm.showPayPopup &&\n    _vm.payInfo.isLogin &&\n    _vm.payInfo.couponInfo !== null &&\n    _vm.payInfo.couponAmount > 0\n      ? parseFloat(_vm.payInfo.couponAmount).toFixed(2)\n      : null\n  var g5 = !_vm.showPayPopup\n    ? parseFloat(_vm.payInfo.payAmount).toFixed(2)\n    : null\n  var g6 = !_vm.showPayPopup ? _vm.payInfo.usePoint.toFixed(0) : null\n  var g7 = !_vm.showPayPopup ? _vm.payInfo.usePoint.toFixed(0) : null\n  var g8 =\n    !_vm.showPayPopup && _vm.payInfo.couponAmount > 0\n      ? _vm.payInfo.usePoint.toFixed(0)\n      : null\n  var g9 =\n    !_vm.showPayPopup && _vm.payInfo.couponAmount > 0\n      ? _vm.payInfo.usePoint.toFixed(0)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"pay-popup popup\" catchtouchmove=\"true\" :class=\"(value && complete) ? 'show' : 'none'\"\n    @touchmove.stop.prevent=\"moveHandle\">\n    <!-- 页面内容开始 -->\n    <view class=\"mask\" @click=\"close('mask')\"></view>\n    <!-- 页面开始 -->\n    <view class=\"confirm\" v-if=\"!showPayPopup\">\n        <view class=\"layer attr-content\" :style=\"'border-radius: 10rpx 10rpx 0 0;'\">\n          <view class=\"specification-wrapper\">\n            <scroll-view class=\"specification-wrapper-content\" scroll-y=\"true\">\n              <view class=\"specification-header\">\n                <view class=\"specification-name\">支付确认</view>\n              </view>\n              <view class=\"specification-content\">\r\n                <view v-if=\"payInfo.isLogin && ((parseFloat(payInfo.totalAmount) - (parseFloat(payInfo.totalAmount) * parseFloat(payInfo.payDiscount))) > 0)\" class=\"pay-item\">\r\n                   <view class=\"item-point\">\r\n                      <view class=\"title\">\r\n                         <text class=\"iconfont icon-success\"></text>\r\n                         <text v-if=\"payInfo.payDiscount < 1\" class=\"point-amount\">会员{{parseFloat(payInfo.payDiscount * 10).toFixed(2)}}折优惠<text class=\"amount\">￥{{(parseFloat(payInfo.totalAmount) - ((parseFloat(payInfo.totalAmount) * parseFloat(payInfo.payDiscount)))).toFixed(2)}}</text></text>\r\n                      </view>\r\n                   </view>\r\n                </view>\n                <view v-if=\"payInfo.isLogin && payInfo.pointAmount >= 0.01 && payInfo.canUsedAsMoney == 'true'\" class=\"pay-item\">\n                    <view class=\"item-point\">\n                        <view class=\"title\">\n                           <text class=\"iconfont icon-success\"></text>\n                           <text v-if=\"payInfo.usePoint > 0\" class=\"point-amount\">使用{{ payInfo.usePoint.toFixed(0) }}积分抵扣</text>\n                           <text v-if=\"payInfo.usePoint > 0\" class=\"amount\">￥{{ parseFloat(payInfo.pointAmount).toFixed(2) }}</text>\n                           <text v-if=\"payInfo.usePoint < 1\" class=\"point-amount\">不使用积分抵扣</text>\n                           <text v-if=\"payInfo.maxPoint > 0\" class=\"modify\" @click=\"modifyPoint\">修改>></text>\n                        </view>\n                    </view>\n                </view>\n                <view v-if=\"!payInfo.isLogin && payInfo.maxPoint < 1\" class=\"pay-item\">\n                    <view class=\"item-point\">\n                        <view class=\"title\">\n                           <text class=\"iconfont icon-success\"></text>\n                           <text>会员可使用积分进行抵扣哦~</text>\n                           <text class=\"modify\" @click=\"onGetLogin\">去登录>></text>\n                        </view>\n                    </view>\n                </view>\n                <view v-if=\"payInfo.isLogin && payInfo.couponInfo !== null\" class=\"pay-item\">\n                    <view class=\"item-point\">\n                        <view class=\"title\">\n                           <text class=\"iconfont icon-success\"></text>\n                           <text v-if=\"payInfo.couponAmount > 0\" class=\"point-amount\">使用卡券抵扣</text>\n                           <text v-if=\"payInfo.couponAmount > 0\" class=\"amount\">￥{{ parseFloat(payInfo.couponAmount).toFixed(2) }}</text>\n                           <text v-if=\"payInfo.couponAmount <= 0 && payInfo.couponInfo.amount\" class=\"point-amount\">不使用卡券抵扣？</text>\n                           <text v-if=\"payInfo.couponInfo.amount\" class=\"modify\" @click=\"modifyCoupon\">修改>></text>\n                        </view>\n                    </view>\n                </view>\n                <view class=\"pay-item\">\n                    <view class=\"item-amount\">\n                        <view class=\"title\">\n                            <text class=\"iconfont icon-success\"></text>\n                            实付金额：<text class=\"amount\">￥{{ (parseFloat(payInfo.payAmount)).toFixed(2) }}</text>\n                        </view>\n                    </view>\n                </view>\n              </view>\n            </scroll-view>\n            <view class=\"close\" @click=\"close('close')\" v-if=\"showClose\">\n              <image class=\"close-item\" :src=\"closeImage\"></image>\n            </view>\n          </view>\n          <view class=\"btn-wrapper\">\n            <view class=\"sure\" @click=\"toPay\">确认支付</view>\n          </view>\n          <!-- 页面结束 -->\n        </view>\n        <view class=\"point-popup\">\n           <uni-popup ref=\"pointPopup\" type=\"dialog\">\n              <uni-popup-dialog mode=\"input\" focus=\"false\" v-model=\"payInfo.usePoint.toFixed(0)\" title=\"修改积分数量\"  type=\"info\" placeholder=\"请输入积分数量\" :before-close=\"true\" @close=\"closeDialog\" @confirm=\"doUsePoint\"></uni-popup-dialog>\n           </uni-popup>\n        </view>\n        <view class=\"coupon-popup\">\n           <uni-popup ref=\"couponPopup\" type=\"dialog\">\n              <uni-popup-dialog focus=\"false\" v-if=\"payInfo.couponAmount > 0\" v-model=\"payInfo.usePoint.toFixed(0)\" title=\"确认信息\" content=\"不使用卡券进行抵扣？\" type=\"info\" :before-close=\"true\" @close=\"closeDialog\" @confirm=\"doUseCoupon\"></uni-popup-dialog>\n              <uni-popup-dialog focus=\"false\" v-if=\"payInfo.couponAmount <= 0 && payInfo.couponInfo !== null\" title=\"确认信息\" :content=\"'使用卡券最多可抵扣￥'+ payInfo.couponInfo.amount\" type=\"info\" :before-close=\"true\" @close=\"closeDialog\" @confirm=\"doUseCoupon\"></uni-popup-dialog>\n           </uni-popup>\n        </view>\n    </view>\n    \n    <!-- 支付方式弹窗 -->\n    <u-popup v-model=\"showPayPopup\" mode=\"bottom\" :closeable=\"true\">\n      <view class=\"pay-type-popup\">\n        <view class=\"title\">请选择支付方式</view>\n        <view class=\"pop-content\">\n          <!-- 微信支付 -->\n          <view class=\"pay-item dis-flex flex-x-between\" @click=\"payNow(PayTypeEnum.WECHAT.value)\">\n            <view class=\"item-left dis-flex flex-y-center\">\n              <view class=\"item-left_icon wechat\">\n                <text class=\"iconfont icon-weixinzhifu\"></text>\n              </view>\n              <view class=\"item-left_text\">\n                <text>{{ PayTypeEnum.WECHAT.name }}</text>\n              </view>\n            </view>\n          </view>\n          <!-- 余额支付 -->\n         <!-- <view class=\"pay-item dis-flex flex-x-between\" @click=\"payNow(PayTypeEnum.BALANCE.value)\">\n            <view class=\"item-left dis-flex flex-y-center\">\n              <view class=\"item-left_icon balance\">\n                <text class=\"iconfont icon-qiandai\"></text>\n              </view>\n              <view class=\"item-left_text\">\n                <text>{{ PayTypeEnum.BALANCE.name }}</text>\n              </view>\n            </view>\n          </view> -->\n        </view>\n      </view>\n    </u-popup>\n    <!-- 页面内容结束 -->\n  </view>\n</template>\n\n<script>\n  import * as SettlementApi from '@/api/settlement'\n  import PayTypeEnum from '@/common/enum/order/PayType'\n  import { wxPayment } from '@/utils/app'\n  \n  var that; // 当前页面对象\n  var vk; // 自定义函数集\n  export default {\n    name: 'PayPopup',\n    props: {\n      // true 组件显示 false 组件隐藏\n      value: {\n        Type: Boolean,\n        default: false\n      },\n      // vk云函数路由模式参数开始-----------------------------------------------------------\n      // 支付信息\n      payInfo: {\n        Type: Object,\n        default: {}\n      },\n      // vk云函数路由模式参数结束-----------------------------------------------------------\n      // 点击遮罩是否关闭组件 true 关闭 false 不关闭 默认true\n      maskCloseAble: {\n        Type: Boolean,\n        default: true\n      },\n      // 是否显示右上角关闭按钮\n      showClose: {\n        Type: Boolean,\n        default: true\n      },\n      // 关闭按钮的图片地址\n      closeImage: {\n        Type: String,\n        default: \"https://img.alicdn.com/imgextra/i1/121022687/O1CN01ImN0O11VigqwzpLiK_!!121022687.png\"\n      }\n    },\n    data() {\n      return {\n        complete: false, // 组件是否加载完成\n        usePoint: '',\n        showPayPopup: false,\n        PayTypeEnum\n      };\n    },\n    mounted() {\n      that = this;\n    },\n    methods: {\n      // 初始化\n      init() {\n         //empty\n      },\n      async open() {\n        that.complete = true;\n        that.$emit(\"open\", true);\n        that.$emit(\"input\", true);\n      },\n      // 监听 - 弹出层收起\n      close(s) {\n        if (s == \"close\") {\n            that.$emit(\"input\", false);\n            that.$emit(\"close\", \"close\");\n        } else if (s == \"mask\") {\n          if (that.maskCloseAble) {\n              that.$emit(\"input\", false);\n              that.$emit(\"close\", \"mask\");\n          }\n        }\n      },\n      \n      moveHandle() {\n        // 禁止父元素滑动\n      },\r\n      // 确认支付\n      toPay() {\n        if (parseFloat(that.payInfo.payAmount) <= 0) {\r\n            that.payNow(PayTypeEnum.BALANCE.value);\r\n        } else {\r\n            that.showPayPopup = true;\r\n        }\n      },\n      // 立即支付\n      payNow(payType) {\n        const app = this\n        // 请求api\n        let couponId = 0\n        if (app.payInfo.couponAmount > 0) {\n            couponId = app.payInfo.couponInfo.userCouponId;\n        }\n        SettlementApi.submit(0, \"\", \"payment\", app.payInfo.remark, parseFloat(app.payInfo.totalAmount).toFixed(2), parseInt(app.payInfo.usePoint), couponId, \"\", 0, 0, 0, \"\", payType)\n          .then(result => app.onSubmitCallback(result))\n          .catch(err => {\n            if (err.result) {\n                const errData = err.result.data;\n                if (errData) {\n                    return false;\n                }\n            }\n        })\n      },\n      // 去登录\n      onGetLogin() {\n        uni.navigateTo({\n          url: \"/pages/login/index\"\n        })\n      },\n      // 订单提交成功后回调\n      onSubmitCallback(result) {\n        const app = this\n        if (result.code != '200') {\n            if (result.message) {\n                app.$error(result.message);\n            } else {\n                app.$error('支付失败');\n            }\n            return false\n        }\n        \n        // 微信支付\n        if (result.data.payType == PayTypeEnum.WECHAT.value) {\n            wxPayment(result.data.payment)\n           .then(() => {\n                  app.$navTo(`pages/pay/result`, { amount: parseFloat(result.data.orderInfo.amount).toFixed(2), point: parseInt(result.data.orderInfo.usePoint)})\n              })\n            .catch(err => \n                app.$error('订单未支付'))\n            .finally(() => {\n               //empty\n            })\n        }\n          \n          // 余额支付\n        if (result.data.payType == PayTypeEnum.BALANCE.value) {\n            if (result.data.orderInfo.payStatus == 'B') {\n                app.$navTo(`pages/pay/result`, { amount: parseFloat(result.data.orderInfo.amount).toFixed(2), point: parseInt(result.data.orderInfo.usePoint)})\n            } else {\n                if (result.message) {\n                    app.$error(result.message);\n                } else {\n                    app.$error('支付失败');\n                }\n            }\n        }\n      },\n      modifyPoint() {\n          this.$refs.pointPopup.open('top')\n      },\n      modifyCoupon() {\n          this.$refs.couponPopup.open('top')\n      },\n      doUsePoint(usePoint) {\n        if (usePoint.length < 1) {\n            usePoint = 0\n        }\n        if (!(/(^[0-9]\\d*$)/.test(usePoint))) {\n            this.$error('请输入正整数')\n        　　return false\n        }\n        \n        if (usePoint > this.payInfo.maxPoint) {\n            if (this.payInfo.maxPoint > 0) {\n                this.$error('最多使用' + this.payInfo.maxPoint + '积分')\n            } else {\n                this.$error('您暂无可用积分')\n            }\n            return false\n        }\n        \n        this.payInfo.usePoint = usePoint\n        \n        this.$emit('modifyChoice', this.payInfo)\n          this.$refs.pointPopup.close()\n      },\n      doUseCoupon() {\n          if (this.payInfo.couponAmount > 0) {\n              this.payInfo.couponAmount = 0\n          } else {\n              this.payInfo.couponAmount = this.payInfo.couponInfo.amount\n          }\n          this.$emit('modifyChoice', this.payInfo)\n          this.$refs.couponPopup.close()\n      },\n      closeDialog() {\n            this.$refs.pointPopup.close()\n          this.$refs.couponPopup.close()\n      },\n      // 弹窗\n      toast(title, icon) {\n        uni.showToast({\n          title: title,\n          icon: icon\n        });\n      }\n    },\n    watch: {\n      value: function(val) {\n        if (val) {\n          that.open();\n        }\n      },\n    }\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n   // 弹出层-支付方式\n   .pay-type-popup {\n     padding: 25rpx 25rpx 70rpx 25rpx;\n     .title {\n       font-size: 30rpx;\n       margin-bottom: 50rpx;\n       font-weight: bold;\n       text-align: center;\n     }\n   \n     .pop-content {\n       min-height: 140rpx;\n       padding: 0 20rpx;\n   \n       .pay-item {\n         padding: 30rpx;\n         font-size: 30rpx;\n         background: #fff;\n         border: 1rpx solid $fuint-theme;\n         border-radius: 8rpx;\n         color: #888;\n         margin-bottom: 12rpx;\n         text-align: center;\n   \n         .item-left_icon {\n           margin-right: 20rpx;\n           font-size: 48rpx;\n   \n           &.wechat {\n             color: #00c800;\n           }\n   \n           &.balance {\n             color: $fuint-theme;\n           }\n         }\n       }\n     }\n   }\n  \n  .pay-popup {\n    position: fixed;\n    left: 0;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 21;\n    overflow: hidden;\n\n    &.show {\n      display: block;\n\n      .mask {\n        animation: showPopup 0.2s linear both;\n      }\n\n      .layer {\n        animation: showLayer 0.2s linear both;\n      }\n    }\n\n    &.hide {\n      .mask {\n        animation: hidePopup 0.2s linear both;\n      }\n\n      .layer {\n        animation: hideLayer 0.2s linear both;\n      }\n    }\n\n    &.none {\n      display: none;\n    }\n\n    .mask {\n      position: fixed;\n      top: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 1;\n      background-color: rgba(0, 0, 0, 0.65);\n    }\n\n    .layer {\n      display: flex;\n      width: 100%;\n      flex-direction: column;\n      position: fixed;\n      z-index: 99;\n      bottom: 0;\n      border-radius: 10rpx 10rpx 0 0;\n      background-color: #fff;\n\n      .specification-wrapper {\n        width: 100%;\n        padding: 30rpx 25rpx 10rpx 25rpx;\n        box-sizing: border-box;\n        background: #ffffff;\n\n        .specification-wrapper-content {\n          width: 100%;\n          max-height: 900rpx;\n          min-height: 300rpx;\n\n          &::-webkit-scrollbar {\n            /*隐藏滚轮*/\n            display: none;\n          }\n\n          .specification-header {\n            width: 100%;\n            display: flex;\n            flex-direction: row;\n            position: relative;\n            margin-bottom: 40rpx;\n            text-align: center;\n            .specification-name {\n                font-weight: bold;\n                width: 100%;\n                font-size: 30rpx;\n                padding: 10rpx;\n            }\n          }\n\n          .specification-content {\n            text-align: left;\n            .pay-item {\n                padding: 35rpx 30rpx 30rpx 100rpx;\n                cursor: pointer;\n                margin-bottom: 8rpx;\n                border: solid 3rpx #cccccc;\n                border-radius: 10rpx;\n                .iconfont {\n                    margin-right: 10rpx;\n                    color: $fuint-theme\n                }\n                .item-point {\n                    .amount {\n                        color: #f9211c;\n                    }\n                    .modify {\n                        margin-left: 30rpx;\n                        color: $fuint-theme;\n                    }\n                }\n                .item-amount {\n                    font-size: 30rpx;\n                    .amount {\n                        color: #f9211c;\n                        font-size: 35rpx;\n                        font-weight: bold;\n                    }\n                }\n            }\n          }\n        }\n\n        .close {\n          position: absolute;\n          top: 30rpx;\n          right: 25rpx;\n          width: 50rpx;\n          height: 50rpx;\n          text-align: center;\n          line-height: 50rpx;\n\n          .close-item {\n            width: 40rpx;\n            height: 40rpx;\n          }\n        }\n      }\n\n      .btn-wrapper {\n        display: flex;\n        width: 100%;\n        height: 120rpx;\n        flex: 0 0 120rpx;\n        align-items: center;\n        justify-content: space-between;\n        padding: 0 26rpx;\n        box-sizing: border-box;\n        margin-bottom: 60rpx;\n        .layer-btn {\n          width: 335rpx;\n          height: 76rpx;\n          border-radius: 38rpx;\n          color: #fff;\n          line-height: 76rpx;\n          text-align: center;\n          font-weight: 500;\n          font-size: 28rpx;\n\n          &.add-cart {\n            background: #ffbe46;\n          }\n\n          &.buy {\n            background: #fe560a;\n          }\n        }\n\n        .sure {\n          width: 698rpx;\n          height: 80rpx;\n          border-radius: 40rpx;\n          color: #fff;\n          line-height: 80rpx;\n          text-align: center;\n          font-weight: 500;\n          font-size: 28rpx;\n          background:linear-gradient(to right, #f9211c, #ff6335)\n        }\n\n        .sure.add-cart {\n          background: #ff9402;\n        }\n      }\n    }\n\n    @keyframes showPopup {\n      0% {\n        opacity: 0;\n      }\n\n      100% {\n        opacity: 1;\n      }\n    }\n\n    @keyframes hidePopup {\n      0% {\n        opacity: 1;\n      }\n\n      100% {\n        opacity: 0;\n      }\n    }\n\n    @keyframes showLayer {\n      0% {\n        transform: translateY(120%);\n      }\n\n      100% {\n        transform: translateY(0%);\n      }\n    }\n\n    @keyframes hideLayer {\n      0% {\n        transform: translateY(0);\n      }\n\n      100% {\n        transform: translateY(120%);\n      }\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=65b5fac2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=65b5fac2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425468\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}