@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.uni-popup-dialog.data-v-6f54520a {
  width: 300px;
  border-radius: 15px;
  margin: 25% auto;
  background-color: #fff;
}
.uni-dialog-title.data-v-6f54520a {
  display: flex;
  flex-direction: row;
  justify-content: center;
  padding-top: 15px;
  padding-bottom: 5px;
}
.uni-dialog-title-text.data-v-6f54520a {
  font-size: 16px;
  font-weight: 500;
}
.uni-dialog-content.data-v-6f54520a {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 5px 15px 15px 15px;
}
.uni-dialog-content-text.data-v-6f54520a {
  font-size: 14px;
  color: #6e6e6e;
}
.uni-dialog-button-group.data-v-6f54520a {
  display: flex;
  flex-direction: row;
  border-top-color: #f5f5f5;
  border-top-style: solid;
  border-top-width: 1px;
}
.uni-dialog-button.data-v-6f54520a {
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 45px;
}
.uni-border-left.data-v-6f54520a {
  border-left-color: #f0f0f0;
  border-left-style: solid;
  border-left-width: 1px;
}
.uni-dialog-button-text.data-v-6f54520a {
  font-size: 14px;
}
.uni-button-color.data-v-6f54520a {
  color: #007aff;
}
.uni-dialog-input.data-v-6f54520a {
  flex: 1;
  font-size: 14px;
  border: 1px #eee solid;
  height: 40px;
  padding: 0 10px;
  border-radius: 5px;
  color: #555;
}
.uni-popup__success.data-v-6f54520a {
  color: #4cd964;
}
.uni-popup__warn.data-v-6f54520a {
  color: #f0ad4e;
}
.uni-popup__error.data-v-6f54520a {
  color: #dd524d;
}
.uni-popup__info.data-v-6f54520a {
  color: #909399;
}
