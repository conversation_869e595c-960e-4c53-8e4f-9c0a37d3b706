<view class="container data-v-ae3d83da"><mescroll-body vue-id="73470553-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-ae3d83da vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="log-list data-v-ae3d83da"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="log-item data-v-ae3d83da"><view class="item-left flex-box data-v-ae3d83da"><view class="rec-status data-v-ae3d83da"><text class="data-v-ae3d83da">充值成功</text></view><view class="rec-time data-v-ae3d83da"><text class="data-v-ae3d83da">{{item.createTime}}</text></view></view><view class="item-right data-v-ae3d83da"><text class="data-v-ae3d83da">{{"+"+item.amount+"元"}}</text></view></view></block></view></mescroll-body></view>