{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?e07c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?bf17", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?0074", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?6d1a", "uni-app:///pages/points/gift.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?cc5d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?5fdf", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?51f7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/gift.vue?9e69"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "amount", "required", "message", "trigger", "mobile", "validator", "components", "data", "userInfo", "point", "form", "remark", "rules", "disabled", "onShow", "onReady", "methods", "handleSubmit", "app", "PointApi", "then", "uni", "finally", "getUserInfo", "UserApi", "resolve", "catch", "reject", "useAllPoint"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACa;AACyB;;;AAGzF;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAyoB,CAAgB,uoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgC7pB;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAH;IACAC;IACAC;EACA;IACA;IACAE;MACA;MACA;IACA;IACAH;IACA;IACAC;EACA;AACA;AAAA,eAEA;EACAG;EACAC;IACA;MACAC;QAAAC;MAAA;MACAC;QAAAN;QAAAJ;QAAAW;MAAA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEAC;IAEA;IACAC;MAAA;MACA;MACA;QACA;MACA;MACAC;QACA;UACAA;UACAC;YAAAnB;YAAAI;YAAAO;UAAA,GACAS;YACA;cACAF;cACAA;cACAA;cACAG;YACA;cACAH;YACA;UACA,GACAI;YAAA;UAAA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC,eACAJ;UACAF;UACAO;QACA,GACAC;UACA;YACAR;YACAO;UACA;YACAE;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAA46B,CAAgB,s4BAAG,EAAC,C;;;;;;;;;;;ACAh8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA4uC,CAAgB,kqCAAG,EAAC,C;;;;;;;;;;;ACAhwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/points/gift.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/points/gift.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./gift.vue?vue&type=template&id=445924f3&scoped=true&\"\nvar renderjs\nimport script from \"./gift.vue?vue&type=script&lang=js&\"\nexport * from \"./gift.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gift.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./gift.vue?vue&type=style&index=1&id=445924f3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"445924f3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/points/gift.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gift.vue?vue&type=template&id=445924f3&scoped=true&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-form/u-form\" */ \"@/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-form-item/u-form-item\" */ \"@/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gift.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gift.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 标题 -->\n    <view class=\"page-title\">把积分转赠给好友</view>\n    <!-- 表单组件 -->\n    <view class=\"form-wrapper\">\n      <u-form :model=\"form\" ref=\"uForm\" label-width=\"160rpx\">\n        <u-form-item label=\"手机号码\" prop=\"mobile\">\n          <u-input v-model=\"form.mobile\" placeholder=\"请输入好友手机号\" />\n        </u-form-item>\n        <u-form-item label=\"转赠数量\" prop=\"amount\">\n          <u-input v-model=\"form.amount\" placeholder=\"请输入转赠数量\" />\n        </u-form-item>\r\n        <view class=\"my-amount\">可用积分：\r\n             <text class=\"amount\">{{ userInfo.point ? userInfo.point : 0 }}</text>\r\n             <text class=\"all\" @click=\"useAllPoint()\">全部</text>\r\n        </view>\n        <u-form-item label=\"转赠留言\" prop=\"remark\" :border-bottom=\"false\">\n          <u-input v-model=\"form.remark\" placeholder=\"请输入转赠留言\" />\n        </u-form-item>\n      </u-form>\n    </view>\n    <!-- 操作按钮 -->\n    <view class=\"footer\">\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" :class=\"{ disabled }\" @click=\"handleSubmit()\">确定转赠</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import { isMobile } from '@/utils/verify'\n  import * as PointApi from '@/api/points/log'\r\n  import * as UserApi from '@/api/user'\n\n  // 表单验证规则\n  const rules = {\n    amount: [{\n      required: true,\n      message: '请输入转赠数量',\n      trigger: ['blur', 'change']\n    }],\n    mobile: [{\n      required: true,\n      message: '请输入好友手机号',\n      trigger: ['blur', 'change']\n    }, {\n      // 自定义验证函数\n      validator: (rule, value, callback) => {\n        // 返回true表示校验通过，返回false表示不通过\n        return isMobile(value)\n      },\n      message: '好友手机号码不正确',\n      // 触发器可以同时用blur和change\n      trigger: ['blur'],\n    }]\n  }\n\n  export default {\r\n    components: {},\n    data() {\n      return {\r\n        userInfo: { point: 0 },\n        form: { mobile: \"\", amount: \"\", remark: \"\" },\n        rules,\n        // 按钮禁用\n        disabled: false\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onShow(options) {\r\n        this.getUserInfo()\r\n    },\n\n    // 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕\n    onReady() {\n      this.$refs.uForm.setRules(this.rules)\n    },\n\n    methods: {\n\n      // 表单提交\n      handleSubmit() {\n        const app = this\n        if (app.disabled) {\n          return false\n        }\n        app.$refs.uForm.validate(valid => {\n          if (valid) {\n            app.disabled = true\n            PointApi.gift({ amount: this.form.amount, mobile: this.form.mobile, remark: this.form.remark })\n              .then(result => {\r\n                  if (result.data) {\r\n                     app.form.mobile = \"\"\r\n                     app.form.amount = \"\"\r\n                     app.form.remark = \"\"\r\n                     uni.navigateBack() \r\n                  } else {\r\n                      app.$toast(result.message)\r\n                  }\n              })\n              .finally(() => app.disabled = false)\n          }\n        })\n      },\r\n      // 获取当前用户信息\r\n      getUserInfo() {\r\n        const app = this\r\n        return new Promise((resolve, reject) => {\r\n            UserApi.info()\r\n            .then(result => {\r\n                  app.userInfo = result.data.userInfo\r\n                  resolve(app.userInfo)\r\n            })\r\n            .catch(err => {\r\n              if (err.result && err.result.status == 1001) {\r\n                  app.isLogin = false\r\n                  resolve(null)\r\n              } else {\r\n                  reject(err)\r\n              }\r\n            })\r\n        })\r\n      },\r\n      useAllPoint() {\r\n          const amount = this.userInfo.point ? this.userInfo.point : 0\r\n          this.form.amount = amount+\"\"\r\n      }\n    }\n  }\n</script>\n\n<style>\n  page {\n    background: #f7f8fa;\n  }\n</style>\n<style lang=\"scss\" scoped>\n  .page-title {\n    width: 94%;\n    margin: 0 auto;\n    padding-top: 40rpx;\n    font-size: 28rpx;\n    color: rgba(69, 90, 100, 0.6);\n  }\n\n  .form-wrapper {\n    margin: 20rpx auto 20rpx auto;\n    padding: 0 40rpx;\n    width: 94%;\n    box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);\n    border-radius: 16rpx;\n    background: #fff;\r\n    .my-amount {\r\n        height: 60rpx;\r\n        margin-left: 160rpx;\r\n        margin-top: 10rpx;\r\n        color: #888888;\r\n        .amount{\r\n            color: #f9211c;\r\n        }\r\n        .all {\r\n            margin-left: 30rpx;\r\n            color: #888888;\r\n            background: #f5f5f5;\r\n            padding: 5rpx 10rpx 5rpx 10rpx;\r\n            border-radius: 10rpx;\r\n        }\r\n    }\n  }\n\n  /* 底部操作栏 */\n\n  .footer {\n    margin-top: 60rpx;\n\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      padding: 0 20rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n\n      // 禁用按钮\n      &.disabled {\n        background: #ff9779;\n      }\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gift.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gift.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420206\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gift.vue?vue&type=style&index=1&id=445924f3&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gift.vue?vue&type=style&index=1&id=445924f3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420658\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}