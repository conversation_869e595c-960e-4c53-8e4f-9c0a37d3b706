{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/neoceansoft-keyboard/neoceansoft-keyboard.vue?dd7f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/neoceansoft-keyboard/neoceansoft-keyboard.vue?0841", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/neoceansoft-keyboard/neoceansoft-keyboard.vue?1fd8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/neoceansoft-keyboard/neoceansoft-keyboard.vue?5460", "uni-app:///components/neoceansoft-keyboard/neoceansoft-keyboard.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/neoceansoft-keyboard/neoceansoft-keyboard.vue?a67c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/neoceansoft-keyboard/neoceansoft-keyboard.vue?8a28"], "names": ["data", "numBtnWidth", "numBtnHeight", "behaviorBtnHeight", "popupHeight", "payboardWidth", "payboardHeight", "numWidht", "numOpWidht", "opBtnWidth", "opBtnHeight1", "opBtnHeight2", "nums", "isShowPay", "bl", "num1", "num2", "num3", "num4", "name", "type", "props", "keyboardType", "default", "behaviorName", "behaviorTextColor", "behaviorBgColor", "created", "uni", "success", "console", "width", "that", "height", "methods", "paymentClick", "btnClick", "inputNum", "putNum", "del"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuK;AACvK;AACwE;AACL;AACqC;;;AAGxG;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,qIAAM;AACR,EAAE,8IAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yIAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,upBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoG7qB;EACAA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;IAGA;EACA;EACAC;IACAC;MACAF;MACAG;IACA;;IACAC;MACAJ;MACAG;IACA;IACAE;MACAL;MACAG;IACA;IACAG;MACAN;MACAG;IACA;EACA;EAGAI;IAGA;IACAC;MACAC;QACAC;QACA;QACA;UACAC;QACA;UACAA;QACA;QACA;QACA;UACAC;UACAC;QACA;UACAA;QACA;QAEAD;QACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MAEA;IACA;IACA;MACA;MACA;IACA;MACA;MACA;IACA;MACA;MACA;MACA;MACA;IAEA;EAEA;EACAE;IACAC;MACA;IACA;IAGA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UAEA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;MAAA;IAGA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;IAIA;EAKA;AACA;AAAA,2B;;;;;;;;;;;;;ACtSA;AAAA;AAAA;AAAA;AAAo9B,CAAgB,86BAAG,EAAC,C;;;;;;;;;;;ACAx+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/neoceansoft-keyboard/neoceansoft-keyboard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./neoceansoft-keyboard.vue?vue&type=template&id=c193ff38&scoped=true&name=neoceansoft-keyboard&\"\nvar renderjs\nimport script from \"./neoceansoft-keyboard.vue?vue&type=script&lang=js&\"\nexport * from \"./neoceansoft-keyboard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./neoceansoft-keyboard.vue?vue&type=style&index=0&id=c193ff38&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c193ff38\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/neoceansoft-keyboard/neoceansoft-keyboard.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./neoceansoft-keyboard.vue?vue&type=template&id=c193ff38&scoped=true&name=neoceansoft-keyboard&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./neoceansoft-keyboard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./neoceansoft-keyboard.vue?vue&type=script&lang=js&\"", "<template name=\"neoceansoft-keyboard\">\n\n    <view  :style=\"{'height':popupHeight}\" class=\"numView\">\n\n        <view style=\"display: flex;\">\n\n\n            <view :style=\"{'width':numWidht}\">\n\n                <view style=\"width: 100%; height: 2rpx;background-color: #efefef;\" />\n                <view style=\"display: flex;\">\n                    <view v-for=\"(item,index) in num1\" :key=\"index\" :style=\"{'height':numBtnHeight,'width':numBtnWidth+'px'}\"\n                        class=\"numLayout\">\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                        <button plain=\"true\" hover-class=\"numClickCss\"\n                            :style=\"{'height':numBtnHeight,'width':numBtnWidth+'px'}\" class=\"numBtn\"\n                            @click=\"btnClick(item)\">\n                            {{item}}\n                        </button>\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                    </view>\n                </view>\n\n\n                <view style=\"width: 100%; height: 2rpx;background-color: #efefef;\" />\n                <view style=\"display: flex;\">\n                    <view v-for=\"(item,index) in num2\" :key=\"index\" :style=\"{'height':numBtnHeight,'width':numBtnWidth+'px'}\"\n                        class=\"numLayout\">\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                        <button plain=\"true\" hover-class=\"numClickCss\"\n                            :style=\"{'height':numBtnHeight,'width':numBtnWidth+'px'}\" class=\"numBtn\"\n                            @click=\"btnClick(item)\">\n                            {{item}}\n                        </button>\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                    </view>\n                </view>\n\n\n                <view style=\"width: 100%; height: 2rpx;background-color: #efefef;\" />\n                <view style=\"display: flex;\">\n                    <view v-for=\"(item,index) in num3\" :key=\"index\" :style=\"{'height':numBtnHeight,'width':numBtnWidth+'px'}\"\n                        class=\"numLayout\">\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                        <button plain=\"true\" hover-class=\"numClickCss\"\n                            :style=\"{'height':numBtnHeight,'width':numBtnWidth+'px'}\" class=\"numBtn\"\n                            @click=\"btnClick(item)\">\n                            {{item}}\n                        </button>\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                    </view>\n                </view>\n\n\n                <view style=\"width: 100%; height: 2rpx;background-color: #efefef;\" />\n                <view style=\"display: flex;\">\n                    <view v-for=\"(item,index) in num4\" :key=\"index\" class=\"numLayout\">\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                        <button plain=\"true\" v-if=\"index==2||(keyboardType == 'password'&&index==1)\"\n                            :hover-class=\"keyboardType=='payment'?'':'numClickCss'\"\n                            :style=\"{'height':numBtnHeight,'width':numBtnWidth+'px'}\" class=\"numBtn\"\n                            @click=\"btnClick(item.type)\">\n                            <view v-if=\"keyboardType!='payment'\" class=\"iconfont icon-deletenumber\"></view>\n\n                        </button>\n                        <button v-else plain=\"true\" hover-class=\"numClickCss\"\n                            :style=\"{'height':numBtnHeight,'width':keyboardType=='password'||(keyboardType=='payment'&&index==0)?((numBtnWidth*2)-1)+'px':(numBtnWidth-1)+'px'}\"\n                            class=\"numBtn\" @click=\"btnClick(item.type)\">\n                            {{item.name}}\n                        </button>\n                        <view v-if=\"index==1\" style=\"width: 2rpx; height: 100%;background-color: #efefef;\"></view>\n                    </view>\n                </view>\n\n            </view>\n            <view v-if=\"keyboardType=='payment'\"\n                :style=\"{'width':payboardWidth,'height':payboardHeight,'display':'flex'}\">\n                <view style=\"width: 2rpx;height: 100%;background-color: #efefef;\"></view>\n                <view :style=\"{'display':'flex','width':'100%','height':'100%'}\">\n\n                    <view style=\"width: 100%;height: 100%;\">\n                        <view style=\"width: 100%;height: 2rpx;background-color: #efefef;\"></view>\n                        <button plain=\"true\" hover-class=\"numClickCss\"\n                            :style=\"{'height':numBtnHeight,'width':(numBtnWidth-1)+'px'}\" class=\"numBtn\" @click=\"btnClick(-2)\">\n                            <view class=\"iconfont icon-deletenumber\"></view>\n\n                        </button>\n                        <button plain=\"true\" hover-class=\"behaviorCommonCss\" :style=\"{'height':behaviorBtnHeight,'width':(numBtnWidth-1),'color':behaviorTextColor,\n                            'background':behaviorBgColor}\" class=\"behaviorCss\" @click=\"paymentClick()\">\n                            {{behaviorName}}\n\n                        </button>\n                    </view>\n                </view>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\n    export default {\n        data() {\n            return {\n                numBtnWidth: 0,\n                numBtnHeight: '',\n                behaviorBtnHeight: '',\n                popupHeight: '',\n                payboardWidth: '',\n                payboardHeight: '',\n                numWidht: '',\n                numOpWidht: '',\n                opBtnWidth: '',\n                opBtnHeight1: '',\n                opBtnHeight2: '',\n                nums: [],\n                isShowPay: true,\n                bl: 0.45,\n                num1: [1, 2, 3],\n                num2: [4, 5, 6],\n                num3: [7, 8, 9],\n                num4: [{\n                    name: '.',\n                    type: '.'\n                }, {\n                    name: '0',\n                    type: 0\n                }, {\n                    name: '删除',\n                    type: -2\n                }],\n\n\n            }\n        },\n        props: {\n            keyboardType: {\n                type: String,\n                default: \"number\" // number idcard,payment,password\n            },\n            behaviorName: {\n                type: String,\n                default: '付款'\n            },\n            behaviorTextColor: {\n                type: String,\n                default: '#ffffff'\n            },\n            behaviorBgColor: {\n                type: String,\n                default: '#41ae3c'\n            }\n        },\n\n\n        created() {\n\n\n            var that = this\n            uni.getSystemInfo({\n                success(res) {\n                    console.log(\"页面信息：\" + JSON.stringify(res))\n                    var width = 0\n                    if (that.keyboardType == 'payment') {\n                        width = (res.screenWidth / 4) * 3\n                    } else {\n                        width = (res.screenWidth / 3) * 3\n                    }\n                    var height = ''\n                    if (that.keyboardType == 'payment') {\n                        that.bl = 0.58\n                        height = width / 3 * that.bl\n                    } else {\n                        height = width / 3 * that.bl\n                    }\n\n                    that.opBtnWidth = (res.screenWidth / 4) * 1 + 'px'\n                    // that.opBtnHeight1 = (height * 4) * (3 / 5) + 'px'\n                    // that.opBtnHeight2 = (height * 4) * (2 / 5) + 'px'\n                    that.opBtnHeight1 = ((height * 2) + 2.5) + 'px'\n                    that.opBtnHeight2 = ((height * 2) + 2) + 'px'\n                    that.numWidht = width + 'px'\n                    that.payboardWidth = (res.screenWidth - width) + 'px'\n                    that.payboardHeight = height * 3\n                    that.numBtnWidth = width / 3\n                    that.numBtnHeight = height + 'px'\n                    that.behaviorBtnHeight = height * 3 + 'px'\n                    that.popupHeight = (height * 4) + 'px'\n\n                }\n            })\n            if (this.keyboardType == 'password') {\n                //密码键盘，不存在小数点问题\n                this.num4.splice(0, 1)\n            } else if (this.keyboardType == 'idcard') {\n                this.num4[0].name = 'X'\n                this.num4[0].type = 'X'\n            } else if (this.keyboardType == 'payment') {\n                this.num4.splice(2, 1)\n                var bean = this.num4[0]\n                this.num4[0] = this.num4[1]\n                this.num4[1] = bean\n\n            }\n\n        },\n        methods: {\n            paymentClick(){\n                this.$emit('paymentClick')\n            },\n\n\n            // 数字点击事件回调\n            btnClick(item) {\n                // this.$emit('click',item)\n                this.$emit('keyclick', item)\n                this.inputNum(item)\n            },\n            // 输入处理事件回调\n            inputNum(item) {\n                switch (item) {\n                    case 0:\n                    //todo 验证待处理\n                        this.putNum('0')\n                        break\n                    case 1:\n                        //todo 验证待处理\n                        this.putNum('1')\n\n                        break\n                    case 2:\n                        //todo 验证待处理\n                        this.putNum('2')\n                        break\n                    case 3:\n                        //todo 验证待处理\n                        this.putNum('3')\n                        break\n                    case 4:\n                        //todo 验证待处理\n                        this.putNum('4')\n                        break\n                    case 5:\n                        //todo 验证待处理\n                        this.putNum('5')\n                        break\n                    case 6:\n                        //todo 验证待处理\n                        this.putNum('6')\n                        break\n                    case 7:\n                        //todo 验证待处理\n                        this.putNum('7')\n                        break\n                    case 8:\n                        //todo 验证待处理\n                        this.putNum('8')\n                        break\n                    case 9:\n                        //todo 验证待处理\n                        this.putNum('9')\n                        break\n                    case '.':\n                        this.putNum('.')\n                        break\n                    case 'X':\n                        this.putNum('X')\n                        break\n                    case -2:\n                        this.del()\n                        break\n\n                }\n            },\n            putNum(data) {\n                this.nums.push(data)\n                var result = this.nums.join('')\n                this.$emit('result', result)\n            },\n            del() {\n                if (this.nums.length == 0) {\n                    return\n                }\n                this.nums.splice(this.nums.length - 1, 1)\n                var result = this.nums.join('')\n                this.$emit('result', result)\n\n\n\n            },\n\n\n\n\n        }\n    }\n</script>\n\n<style scoped>\n    @font-face {\n        font-family: 'iconfont';\n\n        src: url('https://at.alicdn.com/t/font_2180051_21huv31g6dq.woff2?t=1625469481487') format('woff2'),\n            url('https://at.alicdn.com/t/font_2180051_21huv31g6dq.woff?t=1625469481487') format('woff'),\n            url('https://at.alicdn.com/t/font_2180051_21huv31g6dq.ttf?t=1625469481487') format('truetype');\n\n    }\n\n\n    .iconfont {\n        font-family: \"iconfont\" !important;\n        font-size: 14px;\n        font-style: normal;\n        -webkit-font-smoothing: antialiased;\n        -moz-osx-font-smoothing: grayscale;\n    }\n\n    .icon-deletenumber:before {\n        content: \"\\e666\";\n    }\n\n\n\n\n    .numView {\n        position: fixed;\n        bottom: 0rpx;\n        width: 100%;\n    }\n\n    .numBtn {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        justify-items: center;\n        border-radius: 20rpx;\n        border: 0rpx solid #ffffff;\n\n\n    }\n\n    .behaviorCss {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        justify-items: center;\n        border-radius: 0rpx;\n        border: 0rpx solid #E0E0E0;\n        color: #ffffff;\n        font-weight: 500;\n        font-size: 30rpx;\n\n    }\n\n    .behaviorCommonCss {\n        background-color: #efefef !important;\n        transform: translate(1rpx, 1rpx);\n\n    }\n\n    .numLayout {\n        display: flex;\n    }\n\n    .numLayout2 {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        justify-content: center;\n\n    }\n\n    .boderSy {\n        width: 2rpx;\n        background-color: #efefef;\n    }\n\n    .opBtn1 {\n        background-color: #f29100;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        justify-items: center;\n        align-content: center;\n        color: #FFFFFF;\n        font-weight: 400;\n        font-size: 30rpx;\n        text-align: center;\n\n\n    }\n\n    .numClickCss {\n        background-color: #efefef;\n        color: #FFFFFF;\n    }\n\n\n    .opBtn1x {\n        background-color: #ffd28b;\n        display: flex;\n        color: #9fa0a0;\n        align-items: center;\n        justify-content: center;\n        justify-items: center;\n        align-content: center;\n        font-weight: 400;\n        font-size: 30rpx;\n        text-align: center;\n\n    }\n\n\n    .opBtn2 {\n        background-color: #0090ff;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        justify-items: center;\n        align-content: center;\n        color: #FFFFFF;\n        font-weight: 400;\n        font-size: 30rpx;\n        text-align: center;\n\n    }\n\n    .opBtn2x {\n        background-color: #7fcff4;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        justify-items: center;\n        align-content: center;\n        font-weight: 400;\n        font-size: 30rpx;\n        text-align: center;\n        color: #9fa0a0;\n\n    }\n\n    .customStyle {\n        color: 'red'\n    }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./neoceansoft-keyboard.vue?vue&type=style&index=0&id=c193ff38&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./neoceansoft-keyboard.vue?vue&type=style&index=0&id=c193ff38&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891419750\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}