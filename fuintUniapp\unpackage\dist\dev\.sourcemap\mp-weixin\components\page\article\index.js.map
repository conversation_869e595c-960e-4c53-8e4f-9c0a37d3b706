{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/article/index.vue?9829", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/article/index.vue?6d4c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/article/index.vue?1bad", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/article/index.vue?3a99", "uni-app:///components/page/article/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/article/index.vue?d16d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/article/index.vue?a39f"], "names": ["name", "props", "itemIndex", "params", "dataList", "methods", "onTargetDetail", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoC7qB;EACAA;EACA;AACA;AACA;AACA;EACAC;IACAC;IACAC;IACAC;EACA;EAEA;AACA;AACA;AACA;EACAC;IAEA;AACA;AACA;IACAC;MACAC;QACAC;MACA;IACA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjEA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/page/article/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=e7e36a18&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=e7e36a18&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e7e36a18\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/page/article/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=e7e36a18&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <!-- 文章组 -->\n  <view class=\"diy-article\">\n    <view class=\"article-item\" :class=\"[`show-type__${item.show_type}`]\" v-for=\"(item, index) in dataList\" :key=\"index\"\n      @click=\"onTargetDetail(item.article_id)\">\n      <!-- 小图模式 -->\n      <block v-if=\"item.show_type == 10\">\n        <view class=\"article-item__left flex-box\">\n          <view class=\"article-item__title twolist-hidden\">\n            <text>{{ item.title }}</text>\n          </view>\n          <view class=\"article-item__footer m-top10\">\n            <text class=\"article-views f-24 col-8\">{{ item.show_views }}次浏览</text>\n          </view>\n        </view>\n        <view class=\"article-item__image\">\n          <image class=\"image\" mode=\"widthFix\" :src=\"item.image_url\"></image>\n        </view>\n      </block>\n      <!-- 大图模式 -->\n      <block v-if=\"item.show_type == 20\">\n        <view class=\"article-item__title twolist-hidden\">\n          <text>{{ item.title }}</text>\n        </view>\n        <view class=\"article-item__image m-top20\">\n          <image class=\"image\" mode=\"widthFix\" :src=\"item.image_url\"></image>\n        </view>\n        <view class=\"article-item__footer m-top10\">\n          <text class=\"article-views f-24 col-8\">{{ item.show_views }}次浏览</text>\n        </view>\n      </block>\n    </view>\n  </view>\n</template>\n\n<script>\n  export default {\n    name: \"Article\",\n    /**\n     * 组件的属性列表\n     * 用于组件自定义设置\n     */\n    props: {\n      itemIndex: String,\n      params: Object,\n      dataList: Array\n    },\n\n    /**\n     * 组件的方法列表\n     * 更新属性和数据的方法与更新页面数据的方法类似\n     */\n    methods: {\n\n      /**\n       * 跳转文章详情页\n       */\n      onTargetDetail(id) {\n        uni.navigateTo({\n          url: '/pages/article/detail?articleId=' + id\n        })\n      }\n\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .diy-article {\n    background: #f7f7f7;\n\n    .article-item {\n      margin-bottom: 20rpx;\n      padding: 30rpx;\n      background: #fff;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n\n      .article-item__title {\n        max-height: 80rpx;\n        font-size: 30rpx;\n        color: #333;\n      }\n\n      .article-item__image .image {\n        display: block;\n      }\n\n    }\n\n\n\n  }\n\n  /* 小图模式 */\n\n  .show-type__10 {\n    display: flex;\n\n    .article-item__left {\n      padding-right: 20rpx;\n    }\n\n    .article-item__title {\n      // min-height: 72rpx;\n    }\n\n    .article-item__image .image {\n      width: 240rpx;\n    }\n\n  }\n\n  /* 大图模式 */\n\n  .show-type__20 .article-item__image .image {\n    width: 100%;\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=e7e36a18&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=e7e36a18&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426759\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}