{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeBanner.vue?2875", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeBanner.vue?3512", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeBanner.vue?bf19", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeBanner.vue?872b", "uni-app:///pages/index/components/HomeBanner.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeBanner.vue?db32", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeBanner.vue?219d"], "names": ["props", "banners", "type", "default", "data", "current", "methods", "goUrl", "_bindChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,6oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiBlrB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzCA;AAAA;AAAA;AAAA;AAA6wC,CAAgB,wqCAAG,EAAC,C;;;;;;;;;;;ACAjyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/components/HomeBanner.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./HomeBanner.vue?vue&type=template&id=10fd5284&scoped=true&\"\nvar renderjs\nimport script from \"./HomeBanner.vue?vue&type=script&lang=js&\"\nexport * from \"./HomeBanner.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HomeBanner.vue?vue&type=style&index=0&id=10fd5284&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"10fd5284\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/HomeBanner.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeBanner.vue?vue&type=template&id=10fd5284&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeBanner.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeBanner.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"banner\">\r\n        <swiper class=\"bg\" circular :indicator-dots=\"false\" :autoplay=\"true\" :interval=\"5000\" :duration=\"200\" @change=\"_bindChange\">\r\n            <swiper-item v-for=\"(item, index) in banners\" :key=\"index\">\r\n                <view class=\"swiper\">\r\n                    <image :src=\"item.image\" @click.stop=\"goUrl(item.url)\"></image>\r\n                </view>\r\n            </swiper-item>\r\n        </swiper>\r\n        <!-- 指示点 -->\r\n        <view class=\"indicator-dots round\">\r\n          <view class=\"dots-item\" :class=\"{ active: current == index }\" v-for=\"(dataItem, index) in banners\" :key=\"index\"></view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        props: {\r\n            banners: {\r\n                type: Array,\r\n                default: []\r\n            }\r\n        },\r\n        data() {\r\n          return {\r\n            current: 1 // 轮播图指针\r\n          }\r\n        },\r\n        methods: {\r\n            goUrl(url) {\r\n              this.$navTo(url);\r\n            },\r\n            \r\n            /**\r\n             * 记录当前指针\r\n             */\r\n            _bindChange(e) {\r\n              this.current = e.detail.current;\r\n            }\r\n        },\r\n    }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.banner {\r\n    position: relative;\r\n    width: 100%;\r\n    height: 600rpx;\r\n    .bg {\r\n        width: 100%;\r\n        height: 600rpx;\r\n    }\r\n}\r\n.banner,\r\n.bg,\r\n.swiper {\r\n    width: 100%;\r\n    height: 600rpx;\r\n    image {\r\n        width: 100%;\r\n        height: 100%;\r\n    }\r\n}\r\n/* 指示点 */\r\n.indicator-dots {\r\n  width: 100%;\r\n  height: 28rpx;\r\n  padding: 10rpx;\r\n  position: absolute;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 70rpx;\r\n  opacity: 0.8;\r\n  display: flex;\r\n  justify-content: center;\r\n  .dots-item {\r\n    width: 16rpx;\r\n    height: 16rpx;\r\n    margin-right: 8rpx;\r\n    background-color: #333;\r\n    border: solid 5rpx #fff;\r\n    &:last-child {\r\n      margin-right: 0;\r\n    }\r\n\r\n    &.active {\r\n      background-color: #fff !important;\r\n    }\r\n  }\r\n\r\n  // 圆形\r\n  &.round .dots-item {\r\n    width: 18rpx;\r\n    height: 18rpx;\r\n    border-radius: 18rpx;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeBanner.vue?vue&type=style&index=0&id=10fd5284&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeBanner.vue?vue&type=style&index=0&id=10fd5284&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425270\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}