@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 快捷导航 */
.shortcut.data-v-7dec4178 {
  position: fixed;
  right: 24rpx;
  bottom: 250rpx;
  width: 76rpx;
  line-height: 1;
  z-index: 5;
  border-radius: 50%;
}
/* 导航菜单元素 */
.nav-item.data-v-7dec4178 {
  position: absolute;
  bottom: 0;
  padding: 0;
  width: 76rpx;
  height: 76rpx;
  line-height: 76rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  text-align: center;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  opacity: 0;
}
.nav-item .iconfont.data-v-7dec4178 {
  font-size: 40rpx;
}
/* 导航开关 */
.nav-item__switch.data-v-7dec4178 {
  opacity: 1;
}
.shortcut_click_show.data-v-7dec4178 {
  margin-bottom: 0;
  background: #ff5454;
}
/* 显示动画 */
.show_80.data-v-7dec4178 {
  bottom: 384rpx;
  -webkit-animation: show_80-data-v-7dec4178 0.3s forwards;
          animation: show_80-data-v-7dec4178 0.3s forwards;
}
.show_60.data-v-7dec4178 {
  bottom: 288rpx;
  -webkit-animation: show_60-data-v-7dec4178 0.3s forwards;
          animation: show_60-data-v-7dec4178 0.3s forwards;
}
.show_40.data-v-7dec4178 {
  bottom: 192rpx;
  -webkit-animation: show_40-data-v-7dec4178 0.3s forwards;
          animation: show_40-data-v-7dec4178 0.3s forwards;
}
.show_20.data-v-7dec4178 {
  bottom: 96rpx;
  -webkit-animation: show_20-data-v-7dec4178 0.3s forwards;
          animation: show_20-data-v-7dec4178 0.3s forwards;
}
@-webkit-keyframes show_20-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 96rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
@keyframes show_20-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 96rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
@-webkit-keyframes show_40-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 192rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
@keyframes show_40-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 192rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
@-webkit-keyframes show_60-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 288rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
@keyframes show_60-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 288rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
@-webkit-keyframes show_80-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 384rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
@keyframes show_80-data-v-7dec4178 {
from {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
to {
    bottom: 384rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
}
/* 隐藏动画 */
.hide_80.data-v-7dec4178 {
  bottom: 0;
  -webkit-animation: hide_80-data-v-7dec4178 0.3s;
          animation: hide_80-data-v-7dec4178 0.3s;
  opacity: 0;
}
.hide_60.data-v-7dec4178 {
  bottom: 0;
  -webkit-animation: hide_60-data-v-7dec4178 0.3s;
          animation: hide_60-data-v-7dec4178 0.3s;
  opacity: 0;
}
.hide_40.data-v-7dec4178 {
  bottom: 0;
  -webkit-animation: hide_40-data-v-7dec4178 0.3s;
          animation: hide_40-data-v-7dec4178 0.3s;
  opacity: 0;
}
.hide_20.data-v-7dec4178 {
  bottom: 0;
  -webkit-animation: hide_20-data-v-7dec4178 0.3s;
          animation: hide_20-data-v-7dec4178 0.3s;
  opacity: 0;
}
@-webkit-keyframes hide_20-data-v-7dec4178 {
from {
    bottom: 96rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
@keyframes hide_20-data-v-7dec4178 {
from {
    bottom: 96rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
@-webkit-keyframes hide_40-data-v-7dec4178 {
from {
    bottom: 192rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
@keyframes hide_40-data-v-7dec4178 {
from {
    bottom: 192rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
@-webkit-keyframes hide_60-data-v-7dec4178 {
from {
    bottom: 288rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
@keyframes hide_60-data-v-7dec4178 {
from {
    bottom: 288rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
@-webkit-keyframes hide_80-data-v-7dec4178 {
from {
    bottom: 384rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
@keyframes hide_80-data-v-7dec4178 {
from {
    bottom: 384rpx;
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
    opacity: 1;
}
to {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
    opacity: 0;
}
}
