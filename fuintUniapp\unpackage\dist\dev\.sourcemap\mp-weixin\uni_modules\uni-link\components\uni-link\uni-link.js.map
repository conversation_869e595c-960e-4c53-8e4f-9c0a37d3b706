{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-link/components/uni-link/uni-link.vue?0552", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-link/components/uni-link/uni-link.vue?21b6", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-link/components/uni-link/uni-link.vue?04bd", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-link/components/uni-link/uni-link.vue?166f", "uni-app:///uni_modules/uni-link/components/uni-link/uni-link.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-link/components/uni-link/uni-link.vue?ff55", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-link/components/uni-link/uni-link.vue?a0ee"], "names": ["name", "props", "href", "type", "default", "text", "download", "showUnderLine", "copyTips", "color", "fontSize", "computed", "isShowA", "created", "methods", "isMail", "isTel", "openURL", "uni", "data", "content", "showCancel", "makePhoneCall", "phoneNumber"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqH;AACrH;AAC4D;AACL;AACa;;;AAGpE;AACiL;AACjL,gBAAgB,kLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,mFAAM;AACR,EAAE,4FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2qB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqB/rB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,eAaA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;EACA;EACAO;IACAC;MAIA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAYAC;QACAC;MACA;MACAD;QACAE;QACAC;MACA;IAEA;IACAC;MACAJ;QACAK;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAA09B,CAAgB,04BAAG,EAAC,C;;;;;;;;;;;ACA9+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-link/components/uni-link/uni-link.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-link.vue?vue&type=template&id=6c93f7f9&\"\nvar renderjs\nimport script from \"./uni-link.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-link.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-link.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-link/components/uni-link/uni-link.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=template&id=6c93f7f9&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<a v-if=\"isShowA\" class=\"uni-link\" :href=\"href\"\r\n\t\t:class=\"{'uni-link--withline':showUnderLine===true||showUnderLine==='true'}\"\r\n\t\t:style=\"{color,fontSize:fontSize+'px'}\" :download=\"download\">\r\n\t\t<slot>{{text}}</slot>\r\n\t</a>\r\n\t<!-- #ifndef APP-NVUE -->\r\n\t<text v-else class=\"uni-link\" :class=\"{'uni-link--withline':showUnderLine===true||showUnderLine==='true'}\"\r\n\t\t:style=\"{color,fontSize:fontSize+'px'}\" @click=\"openURL\">\r\n\t\t<slot>{{text}}</slot>\r\n\t</text>\r\n\t<!-- #endif -->\r\n\t<!-- #ifdef APP-NVUE -->\r\n\t<text v-else class=\"uni-link\" :class=\"{'uni-link--withline':showUnderLine===true||showUnderLine==='true'}\"\r\n\t\t:style=\"{color,fontSize:fontSize+'px'}\" @click=\"openURL\">\r\n\t\t{{text}}\r\n\t</text>\r\n\t<!-- #endif -->\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * Link 外部网页超链接组件\r\n\t * @description uni-link是一个外部网页超链接组件，在小程序内复制url，在app内打开外部浏览器，在h5端打开新网页\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=1182\r\n\t * @property {String} href 点击后打开的外部网页url\r\n\t * @property {String} text 显示的文字\r\n\t * @property {String} downlaod H5平台下载文件名\r\n\t * @property {Boolean} showUnderLine 是否显示下划线\r\n\t * @property {String} copyTips 在小程序端复制链接时显示的提示语\r\n\t * @property {String} color 链接文字颜色\r\n\t * @property {String} fontSize 链接文字大小\r\n\t * @example * <uni-link href=\"https://ext.dcloud.net.cn\" text=\"https://ext.dcloud.net.cn\"></uni-link>\r\n\t */\r\n\texport default {\r\n\t\tname: 'uniLink',\r\n\t\tprops: {\r\n\t\t\thref: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\ttext: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tdownload: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tshowUnderLine: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tcopyTips: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '已自动复制网址，请在手机浏览器里粘贴该网址'\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#999999'\r\n\t\t\t},\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 14\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisShowA() {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tthis._isH5 = true;\r\n\t\t\t\t// #endif\r\n\t\t\t\tif ((this.isMail() || this.isTel()) && this._isH5 === true) {\r\n\t\t\t\t\treturn true;\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis._isH5 = null;\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisMail() {\r\n\t\t\t\treturn this.href.startsWith('mailto:');\r\n\t\t\t},\r\n\t\t\tisTel() {\r\n\t\t\t\treturn this.href.startsWith('tel:');\r\n\t\t\t},\r\n\t\t\topenURL() {\r\n\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\tif (this.isTel()) {\r\n\t\t\t\t\tthis.makePhoneCall(this.href.replace('tel:', ''));\r\n\t\t\t\t} else {\r\n\t\t\t\t\tplus.runtime.openURL(this.href);\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\twindow.open(this.href)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef MP\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: this.href\r\n\t\t\t\t});\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t\tcontent: this.copyTips,\r\n\t\t\t\t\tshowCancel: false\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tmakePhoneCall(phoneNumber) {\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* #ifndef APP-NVUE */\r\n\t.uni-link {\r\n\t\tcursor: pointer;\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.uni-link--withline {\r\n\t\ttext-decoration: underline;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-link.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891421468\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}