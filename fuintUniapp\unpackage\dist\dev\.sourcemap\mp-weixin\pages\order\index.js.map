{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/index.vue?be79", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/index.vue?34df", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/index.vue?3aac", "uni-app:///pages/order/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/index.vue?e758", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/index.vue?fd08"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "value", "components", "MescrollBody", "mixins", "data", "DeliveryStatusEnum", "DeliveryTypeEnum", "OrderStatusEnum", "PayStatusEnum", "PayTypeEnum", "ReceiptStatusEnum", "options", "dataType", "tabs", "curTab", "list", "isLoading", "upOption", "auto", "page", "size", "noMoreSize", "empty", "tip", "showPayPopup", "statusText", "onLoad", "onShow", "methods", "initCurTab", "console", "app", "upCallback", "then", "catch", "getOrderList", "OrderApi", "load", "resolve", "onTargetIndex", "getTabValue", "onChangeTab", "onRefreshList", "setTimeout", "handleTargetDetail", "orderId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC0E9pB;AASA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;;AAEA;AACA;EACAC;EACAC;AACA;EACAD;EACAC;AACA;EACAD;EACAC;AACA;EACAD;EACAC;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QAAAC;MAAA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;QACA;QACAC;UACAC;QACA;MACA;MACA;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACA;QACAC;QACA;UAAA;QAAA;QACAC;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAD,2BACAE;QACA;QACA;QACAF;MACA,GACAG;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;UAAAxB;UAAAO;QAAA;UAAAkB;QAAA,GACAJ;UACA;UACA;UACAF;UACAO;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAV;MACA;MACAA;IACA;IAEA;IACAW;MAAA;MACA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QAAAC;MAAA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;ACjPA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0ca91b30&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0ca91b30&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ca91b30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=0ca91b30&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-tabs/u-tabs\" */ \"@/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ native: true }\" @down=\"downCallback\"\n      :up=\"upOption\" @up=\"upCallback\">\n\n      <!-- tab栏 -->\n      <view class=\"tab-section\">\n        <u-tabs :list=\"tabs\" :is-scroll=\"false\" :current=\"curTab\" active-color=\"#2b387e\" :duration=\"0.2\" @change=\"onChangeTab\" />\n      </view>\n\n      <!-- 订单列表 -->\n      <view class=\"order-list\">\n        <view class=\"order-card\" v-for=\"(item, index) in list.content\" :key=\"index\">\n          <!-- 订单头部 -->\n          <view class=\"order-header\" @click=\"handleTargetDetail(item.id)\">\n            <view class=\"flex-row items-center\">\n              <view class=\"order-type-tag\">\n                <text class=\"tag-text\">{{ item.typeName }}</text>\n              </view>\n              <!-- 预约标识 -->\n              <view v-if=\"item.isReservation === 'Y'\" class=\"reservation-tag\">\n                <text class=\"reservation-icon\">🕐</text>\n                <text class=\"reservation-text\">预约</text>\n              </view>\n              <!-- <text class=\"store-name\">{{ item.storeName || '默认门店' }}</text> -->\n            </view>\n            <text class=\"status-text\">{{ item.statusText }}</text>\n          </view>\n          \n          <!-- 订单时间 -->\n          <text class=\"order-time\">{{ item.createTime }}</text>\n          \n          <!-- 商品列表 -->\n          <view class=\"goods-section\" v-if=\"item.goods\" @click=\"handleTargetDetail(item.id)\">\n            <view class=\"goods-item\" v-for=\"(goods, idx) in item.goods\" :key=\"idx\">\n              <image class=\"goods-image\" :src=\"goods.image\"></image>\n              <view class=\"goods-info\">\n                <view class=\"goods-content\">\n                  <view class=\"goods-title\">{{ goods.name }}</view>\n                  <view class=\"goods-specs\">\n                    <view class=\"spec-item\" v-for=\"(props, idx) in goods.specList\" :key=\"idx\">\n                      {{ props.specValue }}\n                    </view>\n                  </view>\n                </view>\n                <view class=\"goods-price-section\">\n                  <view class=\"price-group\">\n                    <text class=\"price-symbol\">￥</text>\n                    <text class=\"price-value\">{{ goods.price }}</text>\n                  </view>\n                  <text class=\"goods-count\">共{{ goods.num }}件</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          \n          <!-- 备注信息 -->\n          <view v-if=\"item.remark\" class=\"remark-section\" @click=\"handleTargetDetail(item.id)\">\n            <text class=\"remark-label\">备注：</text>\n            <text class=\"remark-text\">{{ item.remark || '--' }}</text>\n          </view>\n\n          <!-- 预约信息 -->\n          <view v-if=\"item.isReservation === 'Y'\" class=\"reservation-section\" @click=\"handleTargetDetail(item.id)\">\n            <text class=\"reservation-label\">预约取餐：</text>\n            <text class=\"reservation-time-text\">{{ item.reservationTime }}</text>\n          </view>\n        </view>\n      </view>\n    </mescroll-body>\n  </view>\n</template>\n\n<script>\n  import {\n    DeliveryStatusEnum,\n    DeliveryTypeEnum,\n    OrderStatusEnum,\n    PayStatusEnum,\n    PayTypeEnum,\n    ReceiptStatusEnum\n  } from '@/common/enum/order'\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n  import * as OrderApi from '@/api/order'\n  import { wxPayment } from '@/utils/app'\n\n  // 每页记录数量\n  const pageSize = 15\n\n  // tab栏数据\n  const tabs = [{\n    name: `全部`,\n    value: 'all'\n  }, {\n    name: `待支付`,\n    value: 'toPay'\n  }, {\n    name: `已支付`,\n    value: 'paid'\n  }, {\n    name: `已取消`,\n    value: 'cancel'\n  }]\n\n  export default {\n    components: {\n      MescrollBody\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        // 枚举类\n        DeliveryStatusEnum,\n        DeliveryTypeEnum,\n        OrderStatusEnum,\n        PayStatusEnum,\n        PayTypeEnum,\n        ReceiptStatusEnum,\n\n        // 当前页面参数\n        options: { dataType: 'all' },\n        // tab栏数据\n        tabs,\n        // 当前标签索引\n        curTab: 0,\n        // 订单列表数据\n        list: getEmptyPaginateObj(),\n        // 正在加载\n        isLoading: false,\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于12条才显示无更多数据\n          noMoreSize: 12,\n          // 空布局\n          empty: {\n            tip: '亲，暂无订单记录'\n          }\n        },\n        // 支付方式弹窗\n        showPayPopup: false,\n        statusText: \"payStatus\"\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 初始化当前选中的标签\n      this.initCurTab(options)\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n      this.onRefreshList();\n    },\n\n    methods: {\n\n      // 初始化当前选中的标签\n      initCurTab(options) {\n        const app = this\n        if (options.dataType) {\n            console.log(\"options === \", options);\n            const index = app.tabs.findIndex(item => item.value == options.dataType)\n            app.curTab = index > -1 ? index : 0\n        }\n      },\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this\n        // 设置列表数据\n        app.getOrderList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length;\n            const totalSize = list.totalElements;\n            app.mescroll.endBySize(curPageLen, totalSize);\n          })\n          .catch(() => app.mescroll.endErr())\n      },\n\n      // 获取订单列表\n      getOrderList(pageNo = 1) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          OrderApi.list({ dataType: app.getTabValue(), page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data;\n              app.list.content = getMoreListData(newList, app.list, pageNo);\n              resolve(newList);\n            })\n        })\n      },\n      \n      // 点击跳转到首页\n      onTargetIndex() {\n        this.$navTo('pages/index/index')\n      },\n\n      // 获取当前标签项的值\n      getTabValue() {\n        return this.tabs[this.curTab].value\n      },\n\n      // 切换标签项\n      onChangeTab(index) {\n        const app = this\n        // 设置当前选中的标签\n        app.curTab = index\n        // 刷新订单列表\n        app.onRefreshList()\n      },\n\n      // 刷新订单列表\n      onRefreshList() {\n        this.list = getEmptyPaginateObj()\n        setTimeout(() => {\n            this.mescroll.resetUpScroll()\n        }, 120)\n      },\n\n      // 跳转到订单详情页\n      handleTargetDetail(orderId) {\n        this.$navTo('pages/order/detail', { orderId })\n      }\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  // 通用布局类\n  .flex-row {\n    display: flex;\n    flex-direction: row;\n  }\n  \n  .flex-col {\n    display: flex;\n    flex-direction: column;\n  }\n  \n  .items-center {\n    align-items: center;\n  }\n  \n  .justify-between {\n    justify-content: space-between;\n  }\n  \n  .self-stretch {\n    align-self: stretch;\n  }\n\n  // 页面容器\n  .page {\n    background-color: #f2f2f2;\n    min-height: 100vh;\n  }\n\n  // 标签页区域\n  .tab-section {\n    background-color: #fff;\n    padding: 28rpx 92rpx 32rpx;\n    border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);\n  }\n\n  // 订单列表\n  .order-list {\n    padding: 32rpx 28rpx;\n  }\n\n  // 订单卡片\n  .order-card {\n    margin-bottom: 32rpx;\n    padding: 40rpx 32rpx;\n    background-color: #fff;\n    border-radius: 18rpx;\n    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);\n  }\n\n  // 订单头部\n  .order-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20rpx;\n\n    .order-type-tag {\n      padding: 8rpx 16rpx;\n      background-color: #fff;\n      border-radius: 18rpx;\n      min-width: 72rpx;\n      height: 36rpx;\n      border: 1rpx solid #515881;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 10rpx;\n\n      .tag-text {\n        color: #454d78;\n        font-size: 20rpx;\n        font-weight: 600;\n        line-height: 19rpx;\n        letter-spacing: 4rpx;\n        white-space: nowrap;\n      }\n    }\n\n    .store-name {\n      color: #000;\n      font-size: 28rpx;\n      font-weight: 400;\n      line-height: 26rpx;\n      letter-spacing: 5rpx;\n    }\n\n    .status-text {\n      color: #454d78;\n      font-size: 26rpx;\n      font-weight: 400;\n      line-height: 24rpx;\n      text-shadow: 0 0 #454d78;\n    }\n  }\n\n  // 订单时间\n  .order-time {\n    color: #b7b7b7;\n    font-size: 20rpx;\n    font-weight: 300;\n    line-height: 16rpx;\n    margin-bottom: 44rpx;\n    text-shadow: 0 0 #b7b7b7;\n  }\n\n  // 商品区域\n  .goods-section {\n    margin-bottom: 20rpx;\n  }\n\n  // 商品项\n  .goods-item {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24rpx;\n\n    .goods-image {\n      width: 124rpx;\n      height: 124rpx;\n      border-radius: 6rpx;\n      margin-right: 20rpx;\n      flex-shrink: 0;\n    }\n\n    .goods-info {\n      flex: 1;\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n    }\n\n    .goods-content {\n      flex: 1;\n\n      .goods-title {\n        color: #000;\n        font-size: 28rpx;\n        font-weight: 400;\n        line-height: 28rpx;\n        margin-bottom: 8rpx;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }\n\n      .goods-specs {\n        display: flex;\n        flex-wrap: wrap;\n        margin-top: 8rpx;\n\n        .spec-item {\n          color: #999;\n          font-size: 24rpx;\n          font-weight: 300;\n          line-height: 22rpx;\n          margin-right: 16rpx;\n          padding: 4rpx 12rpx;\n          background-color: #f5f5f5;\n          border-radius: 8rpx;\n        }\n      }\n    }\n\n    .goods-price-section {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-align: center;\n\n      .price-group {\n        line-height: 28rpx;\n        margin-bottom: 18rpx;\n\n        .price-symbol {\n          color: #000;\n          font-size: 20rpx;\n          font-weight: 600;\n          line-height: 16rpx;\n        }\n\n        .price-value {\n          color: #000;\n          font-size: 34rpx;\n          font-weight: 600;\n          line-height: 28rpx;\n        }\n      }\n\n      .goods-count {\n        color: #999;\n        font-size: 20rpx;\n        font-weight: 300;\n        line-height: 18rpx;\n      }\n    }\n  }\n\n  // 备注区域\n  .remark-section {\n    padding: 24rpx 0;\n    border-top: 1rpx solid rgba(0, 0, 0, 0.1);\n    margin-top: 20rpx;\n\n    .remark-label {\n      color: #666;\n      font-size: 26rpx;\n      font-weight: 400;\n    }\n\n    .remark-text {\n      color: #333;\n      font-size: 26rpx;\n      font-weight: 400;\n    }\n  }\n\n  // 预约标识\n  .reservation-tag {\n    display: flex;\n    align-items: center;\n    background-color: #fff3cd;\n    border: 1rpx solid #ffeaa7;\n    border-radius: 12rpx;\n    padding: 4rpx 8rpx;\n    margin-right: 12rpx;\n\n    .reservation-icon {\n      font-size: 20rpx;\n      margin-right: 4rpx;\n    }\n\n    .reservation-text {\n      color: #856404;\n      font-size: 20rpx;\n      font-weight: 500;\n    }\n  }\n\n  // 预约信息区域\n  .reservation-section {\n    padding: 24rpx 0;\n    border-top: 1rpx solid rgba(0, 0, 0, 0.1);\n    margin-top: 20rpx;\n\n    .reservation-label {\n      color: #666;\n      font-size: 26rpx;\n      font-weight: 400;\n    }\n\n    .reservation-time-text {\n      color: #ff6600;\n      font-size: 26rpx;\n      font-weight: 600;\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0ca91b30&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=0ca91b30&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891675358\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}