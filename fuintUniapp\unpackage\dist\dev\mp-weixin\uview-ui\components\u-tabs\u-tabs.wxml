<view class="u-tabs data-v-3b2b1a80" style="{{'background:'+(bgColor)+';'}}"><view id="{{id}}" class="data-v-3b2b1a80"><scroll-view class="u-scroll-view data-v-3b2b1a80" scroll-x="{{true}}" scroll-left="{{scrollLeft}}" scroll-with-animation="{{true}}"><view class="{{['u-scroll-box','data-v-3b2b1a80',(!isScroll)?'u-tabs-scorll-flex':'']}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="u-tab-item u-line-1 data-v-3b2b1a80" style="{{item.s0}}" id="{{'u-tab-item-'+index}}" data-event-opts="{{[['tap',[['clickTab',[index]]]]]}}" bindtap="__e"><u-badge vue-id="{{'6ae46646-1-'+index}}" count="{{item.$orig[count]||item.$orig['count']||0}}" offset="{{offset}}" size="mini" class="data-v-3b2b1a80" bind:__l="__l"></u-badge>{{''+(item.$orig[name]||item.$orig['name'])+''}}</view></block><block wx:if="{{showBar}}"><view class="u-tab-bar data-v-3b2b1a80" style="{{$root.s1}}"></view></block></view></scroll-view></view></view>