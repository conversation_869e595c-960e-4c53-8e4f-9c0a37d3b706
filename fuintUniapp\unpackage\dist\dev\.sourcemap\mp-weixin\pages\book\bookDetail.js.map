{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/jyf-parser.vue?eb3a", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/jyf-parser.vue?4188", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/jyf-parser.vue?aa04", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/jyf-parser.vue?9b13", "uni-app:///components/jyf-parser/jyf-parser.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/jyf-parser.vue?bb3a", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/jyf-parser.vue?d60d", "uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/bookDetail.vue?b474", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/bookDetail.vue?7761", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/bookDetail.vue?68ff", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/bookDetail.vue?e657", "uni-app:///pages/book/bookDetail.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/bookDetail.vue?3fdd", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/bookDetail.vue?1e12"], "names": ["fs", "<PERSON><PERSON><PERSON>", "val", "name", "data", "showAm", "nodes", "components", "trees", "props", "html", "autopause", "type", "default", "autoscroll", "autosetTitle", "compress", "loadingImg", "useCache", "domain", "lazyLoad", "selectable", "tagStyle", "showWithAnimation", "useAnchor", "watch", "created", "newSrc", "info", "filePath", "encoding", "success", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "<PERSON><PERSON><PERSON><PERSON>", "cache", "uni", "title", "cs", "f", "select", "height", "getText", "replace", "in", "navigateTo", "d", "selector", "scrollTop", "duration", "obj", "getVideoContext", "wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "myBookId", "isLoading", "detail", "qrCode", "onLoad", "getBookDetail", "BookApi", "then", "app", "finally", "onCancel", "content"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuH;AACvH;AAC8D;AACL;AACa;;;AAGtE;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,qFAAM;AACR,EAAE,8FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,6oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoBnqB;AAAA;EAAA;IAAA;EAAA;AAAA;AAGA;EAEAA;EAEAC;AACA;AACA;AACA;EACA;IACAC;EAAA;EACA;AACA;;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAC;EACAC;IACA;MAQAC;MAEAC;IACA;EACA;EAEAC;IACAC;EACA;EAEAC;IACAC;IACAC;MACAC;MACAC;IACA;IACAC;IACAC;MACAH;MACAC;IACA;IAEAG;IACAC;IACAC;IAEAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAf;MACA;IACA;EACA;EACAgB;IACA;IACA;IACA;MACA;QACA;MAAA;IACA;IACA;MAAA;MACA;;MAEA;MACA;QACA;QACA;UACA;UACAC;QACA;QACAA;QACA;MACA;MAEA;MACA;MACA;QACA;UAAAC;QACA;QAEAC;QACA7B;UACA6B;UACAzB;UACA0B;UACAC;YAAA;UAAA;QACA;MAYA;IACA;EACA;EACAC;IAAA;IAKA;IAEA;MAAA;IAAA;IAKA;EAIA;EACAC;IAIA;MASA,iDACAjC;QACA6B;MACA;IAEA;IACAK;EACA;EACAC;IACA;IACAC;MAAA;MA+LA;MACA;MACA;MACA;MACA;QACA;QACA,oBACA9B,4BACA;UACAA;UACA+B;QACA;MACA;MACA;MACA,uDACA;MACA,sDACAC;QACAC;MACA;MACA;MACA;MACA;QACA;UACA;YACA;cACAC;cACAA;cACAC;YACA;UACA;QACA;QACA;MACA;MAEA;MACAP;MACA;QAKAI,qCACAI;UACA;UACA;UAEA;YACA;YACAR;UACA;UACAS;QAEA;MAEA;MACA;IAEA;IACA;IACAC;MAAA;MACA;MAQA;QACA,2GACAC,4BACA,qCACA;UACA;UACA,oHACA;UACA;UACA;UACA,2DACA;QACA;MACA;MAEA;IACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MAUA;MAEAC;MAEA,oHACA;MACA,oHACAC;MACAA;QACA;QACA;QACA,uEACAX;UACAY;UACAC;QACA;QACAC;MACA;IAEA;IACA;IACAC;MAEA,wCAEA;QACA;MAAA;IAEA;EA4FA;AACA;AAAA,2B;;;;;;;;;;;;;AC9mBA;AAAA;AAAA;AAAA;AAAk7B,CAAgB,44BAAG,EAAC,C;;;;;;;;;;;ACAt8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAC,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wHAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA+oB,CAAgB,6oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACoDnqB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAtD;IACA;MACA;MACAuD;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEA5B;IACA;IACA6B;MACA;MACAC,mCACAC;QACAC;MACA,GACAC;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA/B;QACAC;QACA+B;QACAvC;UACA;YACAkC,6BACAC;cACA;cACAC;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1GA;AAAA;AAAA;AAAA;AAAkvC,CAAgB,wqCAAG,EAAC,C;;;;;;;;;;;ACAtwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/book/bookDetail.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./jyf-parser.vue?vue&type=template&id=eab15eb8&\"\nvar renderjs\nimport script from \"./jyf-parser.vue?vue&type=script&lang=js&\"\nexport * from \"./jyf-parser.vue?vue&type=script&lang=js&\"\nimport style0 from \"./jyf-parser.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/jyf-parser/jyf-parser.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=template&id=eab15eb8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.nodes.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=script&lang=js&\"", "<template>\n    <view>\n        <slot v-if=\"!nodes.length\" />\n        <!--#ifdef APP-PLUS-NVUE-->\n        <web-view id=\"_top\" ref=\"web\" :style=\"'margin-top:-2px;height:'+height+'px'\" @onPostMessage=\"_message\" />\n        <!--#endif-->\n        <!--#ifndef APP-PLUS-NVUE-->\n        <view id=\"_top\" :style=\"showAm+(selectable?';user-select:text;-webkit-user-select:text':'')\">\n            <!--#ifdef H5 || MP-360-->\n            <div :id=\"'rtf'+uid\"></div>\n            <!--#endif-->\n            <!--#ifndef H5 || MP-360-->\n            <trees :nodes=\"nodes\" :lazyLoad=\"lazyLoad\" :loading=\"loadingImg\" />\n            <!--#endif-->\n        </view>\n        <!--#endif-->\n    </view>\n</template>\n\n<script>\n    var search;\n    // #ifndef H5 || APP-PLUS-NVUE || MP-360\n    import trees from './libs/trees';\n    var cache = {},\n        // #ifdef MP-WEIXIN || MP-TOUTIAO\n        fs = uni.getFileSystemManager ? uni.getFileSystemManager() : null,\n        // #endif\n        Parser = require('./libs/MpHtmlParser.js');\n    var dom;\n    // 计算 cache 的 key\n    function hash(str) {\n        for (var i = str.length, val = 5381; i--;)\n            val += (val << 5) + str.charCodeAt(i);\n        return val;\n    }\n    // #endif\n    // #ifdef H5 || APP-PLUS-NVUE || MP-360\n    var {\n        windowWidth,\n        platform\n    } = uni.getSystemInfoSync(),\n        cfg = require('./libs/config.js');\n    // #endif\n    // #ifdef APP-PLUS-NVUE\n    var weexDom = weex.requireModule('dom');\n    // #endif\n    /**\n     * Parser 富文本组件\n     * @tutorial https://github.com/jin-yufeng/Parser\n     * @property {String} html 富文本数据\n     * @property {Boolean} autopause 是否在播放一个视频时自动暂停其他视频\n     * @property {Boolean} autoscroll 是否自动给所有表格添加一个滚动层\n     * @property {Boolean} autosetTitle 是否自动将 title 标签中的内容设置到页面标题\n     * @property {Number} compress 压缩等级\n     * @property {String} domain 图片、视频等链接的主域名\n     * @property {Boolean} lazyLoad 是否开启图片懒加载\n     * @property {String} loadingImg 图片加载完成前的占位图\n     * @property {Boolean} selectable 是否开启长按复制\n     * @property {Object} tagStyle 标签的默认样式\n     * @property {Boolean} showWithAnimation 是否使用渐显动画\n     * @property {Boolean} useAnchor 是否使用锚点\n     * @property {Boolean} useCache 是否缓存解析结果\n     * @event {Function} parse 解析完成事件\n     * @event {Function} load dom 加载完成事件\n     * @event {Function} ready 所有图片加载完毕事件\n     * @event {Function} error 错误事件\n     * @event {Function} imgtap 图片点击事件\n     * @event {Function} linkpress 链接点击事件\n     * <AUTHOR>\n     * @version 20201029\n     * @listens MIT\n     */\n    export default {\n        name: 'parser',\n        data() {\n            return {\n                // #ifdef H5 || MP-360\n                uid: this._uid,\n                // #endif\n                // #ifdef APP-PLUS-NVUE\n                height: 1,\n                // #endif\n                // #ifndef APP-PLUS-NVUE\n                showAm: '',\n                // #endif\n                nodes: []\n            }\n        },\n        // #ifndef H5 || APP-PLUS-NVUE || MP-360\n        components: {\n            trees\n        },\n        // #endif\n        props: {\n            html: String,\n            autopause: {\n                type: Boolean,\n                default: true\n            },\n            autoscroll: Boolean,\n            autosetTitle: {\n                type: Boolean,\n                default: true\n            },\n            // #ifndef H5 || APP-PLUS-NVUE || MP-360\n            compress: Number,\n            loadingImg: String,\n            useCache: Boolean,\n            // #endif\n            domain: String,\n            lazyLoad: Boolean,\n            selectable: Boolean,\n            tagStyle: Object,\n            showWithAnimation: Boolean,\n            useAnchor: Boolean\n        },\n        watch: {\n            html(html) {\n                this.setContent(html);\n            }\n        },\n        created() {\n            // 图片数组\n            this.imgList = [];\n            this.imgList.each = function(f) {\n                for (var i = 0, len = this.length; i < len; i++)\n                    this.setItem(i, f(this[i], i, this));\n            }\n            this.imgList.setItem = function(i, src) {\n                if (i == void 0 || !src) return;\n                // #ifndef MP-ALIPAY || APP-PLUS\n                // 去重\n                if (src.indexOf('http') == 0 && this.includes(src)) {\n                    var newSrc = src.split('://')[0];\n                    for (var j = newSrc.length, c; c = src[j]; j++) {\n                        if (c == '/' && src[j - 1] != '/' && src[j + 1] != '/') break;\n                        newSrc += Math.random() > 0.5 ? c.toUpperCase() : c;\n                    }\n                    newSrc += src.substr(j);\n                    return this[i] = newSrc;\n                }\n                // #endif\n                this[i] = src;\n                // 暂存 data src\n                if (src.includes('data:image')) {\n                    var filePath, info = src.match(/data:image\\/(\\S+?);(\\S+?),(.+)/);\n                    if (!info) return;\n                    // #ifdef MP-WEIXIN || MP-TOUTIAO\n                    filePath = `${wx.env.USER_DATA_PATH}/${Date.now()}.${info[1]}`;\n                    fs && fs.writeFile({\n                        filePath,\n                        data: info[3],\n                        encoding: info[2],\n                        success: () => this[i] = filePath\n                    })\n                    // #endif\n                    // #ifdef APP-PLUS\n                    filePath = `_doc/parser_tmp/${Date.now()}.${info[1]}`;\n                    var bitmap = new plus.nativeObj.Bitmap();\n                    bitmap.loadBase64Data(src, () => {\n                        bitmap.save(filePath, {}, () => {\n                            bitmap.clear()\n                            this[i] = filePath;\n                        })\n                    })\n                    // #endif\n                }\n            }\n        },\n        mounted() {\n            // #ifdef H5 || MP-360\n            this.document = document.getElementById('rtf' + this._uid);\n            // #endif\n            // #ifndef H5 || APP-PLUS-NVUE || MP-360\n            if (dom) this.document = new dom(this);\n            // #endif\n            if (search) this.search = args => search(this, args);\n            // #ifdef APP-PLUS-NVUE\n            this.document = this.$refs.web;\n            setTimeout(() => {\n                // #endif\n                if (this.html) this.setContent(this.html);\n                // #ifdef APP-PLUS-NVUE\n            }, 30)\n            // #endif\n        },\n        beforeDestroy() {\n            // #ifdef H5 || MP-360\n            if (this._observer) this._observer.disconnect();\n            // #endif\n            this.imgList.each(src => {\n                // #ifdef APP-PLUS\n                if (src && src.includes('_doc')) {\n                    plus.io.resolveLocalFileSystemURL(src, entry => {\n                        entry.remove();\n                    });\n                }\n                // #endif\n                // #ifdef MP-WEIXIN || MP-TOUTIAO\n                if (src && src.includes(uni.env.USER_DATA_PATH))\n                    fs && fs.unlink({\n                        filePath: src\n                    })\n                // #endif\n            })\n            clearInterval(this._timer);\n        },\n        methods: {\n            // 设置富文本内容\n            setContent(html, append) {\n                // #ifdef APP-PLUS-NVUE\n                if (!html)\n                    return this.height = 1;\n                if (append)\n                    this.$refs.web.evalJs(\"var b=document.createElement('div');b.innerHTML='\" + html.replace(/'/g, \"\\\\'\") +\n                        \"';document.getElementById('parser').appendChild(b)\");\n                else {\n                    html =\n                        '<meta charset=\"utf-8\" /><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no\"><style>html,body{width:100%;height:100%;overflow:hidden}body{margin:0}</style><base href=\"' +\n                        this.domain + '\"><div id=\"parser\"' + (this.selectable ? '>' : ' style=\"user-select:none\">') + this._handleHtml(html).replace(/\\n/g, '\\\\n') +\n                        '</div><script>\"use strict\";function e(e){if(window.__dcloud_weex_postMessage||window.__dcloud_weex_){var t={data:[e]};window.__dcloud_weex_postMessage?window.__dcloud_weex_postMessage(t):window.__dcloud_weex_.postMessage(JSON.stringify(t))}}document.body.onclick=function(){e({action:\"click\"})},' +\n                        (this.showWithAnimation ? 'document.body.style.animation=\"_show .5s\",' : '') +\n                        'setTimeout(function(){e({action:\"load\",text:document.body.innerText,height:document.getElementById(\"parser\").scrollHeight})},50);\\x3c/script>';\n                    if (platform == 'android') html = html.replace(/%/g, '%25');\n                    this.$refs.web.evalJs(\"document.write('\" + html.replace(/'/g, \"\\\\'\") + \"');document.close()\");\n                }\n                this.$refs.web.evalJs(\n                    'var t=document.getElementsByTagName(\"title\");t.length&&e({action:\"getTitle\",title:t[0].innerText});for(var o,n=document.getElementsByTagName(\"style\"),r=1;o=n[r++];)o.innerHTML=o.innerHTML.replace(/body/g,\"#parser\");for(var a,c=document.getElementsByTagName(\"img\"),s=[],i=0==c.length,d=0,l=0,g=0;a=c[l];l++)parseInt(a.style.width||a.getAttribute(\"width\"))>' +\n                    windowWidth + '&&(a.style.height=\"auto\"),a.onload=function(){++d==c.length&&(i=!0)},a.onerror=function(){++d==c.length&&(i=!0),' + (cfg.errorImg ? 'this.src=\"' + cfg.errorImg + '\",' : '') +\n                    'e({action:\"error\",source:\"img\",target:this})},a.hasAttribute(\"ignore\")||\"A\"==a.parentElement.nodeName||(a.i=g++,s.push(a.getAttribute(\"original-src\")||a.src||a.getAttribute(\"data-src\")),a.onclick=function(t){t.stopPropagation(),e({action:\"preview\",img:{i:this.i,src:this.src}})});e({action:\"getImgList\",imgList:s});for(var u,m=document.getElementsByTagName(\"a\"),f=0;u=m[f];f++)u.onclick=function(m){m.stopPropagation();var t,o=this.getAttribute(\"href\");if(\"#\"==o[0]){var n=document.getElementById(o.substr(1));n&&(t=n.offsetTop)}return e({action:\"linkpress\",href:o,offset:t}),!1};for(var h,y=document.getElementsByTagName(\"video\"),v=0;h=y[v];v++)h.style.maxWidth=\"100%\",h.onerror=function(){e({action:\"error\",source:\"video\",target:this})}' +\n                    (this.autopause ? ',h.onplay=function(){for(var e,t=0;e=y[t];t++)e!=this&&e.pause()}' : '') +\n                    ';for(var _,p=document.getElementsByTagName(\"audio\"),w=0;_=p[w];w++)_.onerror=function(){e({action:\"error\",source:\"audio\",target:this})};' +\n                    (this.autoscroll ? 'for(var T,E=document.getElementsByTagName(\"table\"),B=0;T=E[B];B++){var N=document.createElement(\"div\");N.style.overflow=\"scroll\",T.parentNode.replaceChild(N,T),N.appendChild(T)}' : '') +\n                    'var x=document.getElementById(\"parser\");clearInterval(window.timer),window.timer=setInterval(function(){i&&clearInterval(window.timer),e({action:\"ready\",ready:i,height:x.scrollHeight})},350)'\n                )\n                this.nodes = [1];\n                // #endif\n                // #ifdef H5 || MP-360\n                if (!html) {\n                    if (this.rtf && !append) this.rtf.parentNode.removeChild(this.rtf);\n                    return;\n                }\n                var div = document.createElement('div');\n                if (!append) {\n                    if (this.rtf) this.rtf.parentNode.removeChild(this.rtf);\n                    this.rtf = div;\n                } else {\n                    if (!this.rtf) this.rtf = div;\n                    else this.rtf.appendChild(div);\n                }\n                div.innerHTML = this._handleHtml(html, append);\n                for (var styles = this.rtf.getElementsByTagName('style'), i = 0, style; style = styles[i++];) {\n                    style.innerHTML = style.innerHTML.replace(/body/g, '#rtf' + this._uid);\n                    style.setAttribute('scoped', 'true');\n                }\n                // 懒加载\n                if (!this._observer && this.lazyLoad && IntersectionObserver) {\n                    this._observer = new IntersectionObserver(changes => {\n                        for (let item, i = 0; item = changes[i++];) {\n                            if (item.isIntersecting) {\n                                item.target.src = item.target.getAttribute('data-src');\n                                item.target.removeAttribute('data-src');\n                                this._observer.unobserve(item.target);\n                            }\n                        }\n                    }, {\n                        rootMargin: '500px 0px 500px 0px'\n                    })\n                }\n                var _ts = this;\n                // 获取标题\n                var title = this.rtf.getElementsByTagName('title');\n                if (title.length && this.autosetTitle)\n                    uni.setNavigationBarTitle({\n                        title: title[0].innerText\n                    })\n                // 填充 domain\n                var fill = target => {\n                    var src = target.getAttribute('src');\n                    if (this.domain && src) {\n                        if (src[0] == '/') {\n                            if (src[1] == '/')\n                                target.src = (this.domain.includes('://') ? this.domain.split('://')[0] : '') + ':' + src;\n                            else target.src = this.domain + src;\n                        } else if (!src.includes('://') && src.indexOf('data:') != 0) target.src = this.domain + '/' + src;\n                    }\n                }\n                // 图片处理\n                this.imgList.length = 0;\n                var imgs = this.rtf.getElementsByTagName('img');\n                for (let i = 0, j = 0, img; img = imgs[i]; i++) {\n                    if (parseInt(img.style.width || img.getAttribute('width')) > windowWidth)\n                        img.style.height = 'auto';\n                    fill(img);\n                    if (!img.hasAttribute('ignore') && img.parentElement.nodeName != 'A') {\n                        img.i = j++;\n                        _ts.imgList.push(img.getAttribute('original-src') || img.src || img.getAttribute('data-src'));\n                        img.onclick = function(e) {\n                            e.stopPropagation();\n                            var preview = true;\n                            this.ignore = () => preview = false;\n                            _ts.$emit('imgtap', this);\n                            if (preview) {\n                                // uni.previewImage({\n                                //     current: this.i,\n                                //     urls: _ts.imgList\n                                // });\n                            }\n                        }\n                    }\n                    img.onerror = function() {\n                        if (cfg.errorImg)\n                            _ts.imgList[this.i] = this.src = cfg.errorImg;\n                        _ts.$emit('error', {\n                            source: 'img',\n                            target: this\n                        });\n                    }\n                    if (_ts.lazyLoad && this._observer && img.src && img.i != 0) {\n                        img.setAttribute('data-src', img.src);\n                        img.removeAttribute('src');\n                        this._observer.observe(img);\n                    }\n                }\n                // 链接处理\n                var links = this.rtf.getElementsByTagName('a');\n                for (var link of links) {\n                    link.onclick = function(e) {\n                        e.stopPropagation();\n                        var jump = true,\n                            href = this.getAttribute('href');\n                        _ts.$emit('linkpress', {\n                            href,\n                            ignore: () => jump = false\n                        });\n                        if (jump && href) {\n                            if (href[0] == '#') {\n                                if (_ts.useAnchor) {\n                                    _ts.navigateTo({\n                                        id: href.substr(1)\n                                    })\n                                }\n                            } else if (href.indexOf('http') == 0 || href.indexOf('//') == 0)\n                                return true;\n                            else\n                                uni.navigateTo({\n                                    url: href\n                                })\n                        }\n                        return false;\n                    }\n                }\n                // 视频处理\n                var videos = this.rtf.getElementsByTagName('video');\n                _ts.videoContexts = videos;\n                for (let video, i = 0; video = videos[i++];) {\n                    fill(video);\n                    video.style.maxWidth = '100%';\n                    video.onerror = function() {\n                        _ts.$emit('error', {\n                            source: 'video',\n                            target: this\n                        });\n                    }\n                    video.onplay = function() {\n                        if (_ts.autopause)\n                            for (let item, i = 0; item = _ts.videoContexts[i++];)\n                                if (item != this) item.pause();\n                    }\n                }\n                // 音频处理\n                var audios = this.rtf.getElementsByTagName('audio');\n                for (var audio of audios) {\n                    fill(audio);\n                    audio.onerror = function() {\n                        _ts.$emit('error', {\n                            source: 'audio',\n                            target: this\n                        });\n                    }\n                }\n                // 表格处理\n                if (this.autoscroll) {\n                    var tables = this.rtf.getElementsByTagName('table');\n                    for (var table of tables) {\n                        let div = document.createElement('div');\n                        div.style.overflow = 'scroll';\n                        table.parentNode.replaceChild(div, table);\n                        div.appendChild(table);\n                    }\n                }\n                if (!append) this.document.appendChild(this.rtf);\n                this.$nextTick(() => {\n                    this.nodes = [1];\n                    this.$emit('load');\n                });\n                setTimeout(() => this.showAm = '', 500);\n                // #endif\n                // #ifndef APP-PLUS-NVUE\n                // #ifndef H5 || MP-360\n                var nodes;\n                if (!html) return this.nodes = [];\n                var parser = new Parser(html, this);\n                // 缓存读取\n                if (this.useCache) {\n                    var hashVal = hash(html);\n                    if (cache[hashVal])\n                        nodes = cache[hashVal];\n                    else {\n                        nodes = parser.parse();\n                        cache[hashVal] = nodes;\n                    }\n                } else nodes = parser.parse();\n                this.$emit('parse', nodes);\n                if (append) this.nodes = this.nodes.concat(nodes);\n                else this.nodes = nodes;\n                if (nodes.length && nodes.title && this.autosetTitle)\n                    uni.setNavigationBarTitle({\n                        title: nodes.title\n                    })\n                if (this.imgList) this.imgList.length = 0;\n                this.videoContexts = [];\n                this.$nextTick(() => {\n                    (function f(cs) {\n                        for (var i = cs.length; i--;) {\n                            if (cs[i].top) {\n                                cs[i].controls = [];\n                                cs[i].init();\n                                f(cs[i].$children);\n                            }\n                        }\n                    })(this.$children)\n                    this.$emit('load');\n                })\n                // #endif\n                var height;\n                clearInterval(this._timer);\n                this._timer = setInterval(() => {\n                    // #ifdef H5 || MP-360\n                    this.rect = this.rtf.getBoundingClientRect();\n                    // #endif\n                    // #ifndef H5 || MP-360\n                    uni.createSelectorQuery().in(this)\n                        .select('#_top').boundingClientRect().exec(res => {\n                            if (!res) return;\n                            this.rect = res[0];\n                            // #endif\n                            if (this.rect.height == height) {\n                                this.$emit('ready', this.rect)\n                                clearInterval(this._timer);\n                            }\n                            height = this.rect.height;\n                            // #ifndef H5 || MP-360\n                        });\n                    // #endif\n                }, 350);\n                if (this.showWithAnimation && !append) this.showAm = 'animation:_show .5s';\n                // #endif\n            },\n            // 获取文本内容\n            getText(ns = this.nodes) {\n                var txt = '';\n                // #ifdef APP-PLUS-NVUE\n                txt = this._text;\n                // #endif\n                // #ifdef H5 || MP-360\n                txt = this.rtf.innerText;\n                // #endif\n                // #ifndef H5 || APP-PLUS-NVUE || MP-360\n                for (var i = 0, n; n = ns[i++];) {\n                    if (n.type == 'text') txt += n.text.replace(/&nbsp;/g, '\\u00A0').replace(/&lt;/g, '<').replace(/&gt;/g, '>')\n                        .replace(/&amp;/g, '&');\n                    else if (n.type == 'br') txt += '\\n';\n                    else {\n                        // 块级标签前后加换行\n                        var block = n.name == 'p' || n.name == 'div' || n.name == 'tr' || n.name == 'li' || (n.name[0] == 'h' && n.name[1] >\n                            '0' && n.name[1] < '7');\n                        if (block && txt && txt[txt.length - 1] != '\\n') txt += '\\n';\n                        if (n.children) txt += this.getText(n.children);\n                        if (block && txt[txt.length - 1] != '\\n') txt += '\\n';\n                        else if (n.name == 'td' || n.name == 'th') txt += '\\t';\n                    }\n                }\n                // #endif\n                return txt;\n            },\n            // 锚点跳转\n            in (obj) {\n                if (obj.page && obj.selector && obj.scrollTop) this._in = obj;\n            },\n            navigateTo(obj) {\n                if (!this.useAnchor) return obj.fail && obj.fail('Anchor is disabled');\n                // #ifdef APP-PLUS-NVUE\n                if (!obj.id)\n                    weexDom.scrollToElement(this.$refs.web);\n                else\n                    this.$refs.web.evalJs('var pos=document.getElementById(\"' + obj.id +\n                        '\");if(pos)post({action:\"linkpress\",href:\"#\",offset:pos.offsetTop+' + (obj.offset || 0) + '})');\n                obj.success && obj.success();\n                // #endif\n                // #ifndef APP-PLUS-NVUE\n                var d = ' ';\n                // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\n                d = '>>>';\n                // #endif\n                var selector = uni.createSelectorQuery().in(this._in ? this._in.page : this).select((this._in ? this._in.selector :\n                    '#_top') + (obj.id ? `${d}#${obj.id},${this._in?this._in.selector:'#_top'}${d}.${obj.id}` : '')).boundingClientRect();\n                if (this._in) selector.select(this._in.selector).scrollOffset().select(this._in.selector).boundingClientRect();\n                else selector.selectViewport().scrollOffset();\n                selector.exec(res => {\n                    if (!res[0]) return obj.fail && obj.fail('Label not found')\n                    var scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + (obj.offset || 0);\n                    if (this._in) this._in.page[this._in.scrollTop] = scrollTop;\n                    else uni.pageScrollTo({\n                        scrollTop,\n                        duration: 300\n                    })\n                    obj.success && obj.success();\n                })\n                // #endif\n            },\n            // 获取视频对象\n            getVideoContext(id) {\n                // #ifndef APP-PLUS-NVUE\n                if (!id) return this.videoContexts;\n                else\n                    for (var i = this.videoContexts.length; i--;)\n                        if (this.videoContexts[i].id == id) return this.videoContexts[i];\n                // #endif\n            },\n            // #ifdef H5 || APP-PLUS-NVUE || MP-360\n            _handleHtml(html, append) {\n                if (!append) {\n                    // 处理 tag-style 和 userAgentStyles\n                    var style = '<style>@keyframes _show{0%{opacity:0}100%{opacity:1}}img{max-width:100%;display:block}';\n                    for (var item in cfg.userAgentStyles)\n                        style += `${item}{${cfg.userAgentStyles[item]}}`;\n                    for (item in this.tagStyle)\n                        style += `${item}{${this.tagStyle[item]}}`;\n                    style += '</style>';\n                    html = style + html;\n                }\n                // 处理 rpx\n                if (html.includes('rpx'))\n                    html = html.replace(/[0-9.]+\\s*rpx/g, $ => (parseFloat($) * windowWidth / 750) + 'px');\n                return html;\n            },\n            // #endif\n            // #ifdef APP-PLUS-NVUE\n            _message(e) {\n                // 接收 web-view 消息\n                var d = e.detail.data[0];\n                switch (d.action) {\n                    case 'load':\n                        this.$emit('load');\n                        this.height = d.height;\n                        this._text = d.text;\n                        break;\n                    case 'getTitle':\n                        if (this.autosetTitle)\n                            uni.setNavigationBarTitle({\n                                title: d.title\n                            })\n                        break;\n                    case 'getImgList':\n                        this.imgList.length = 0;\n                        for (var i = d.imgList.length; i--;)\n                            this.imgList.setItem(i, d.imgList[i]);\n                        break;\n                    case 'preview':\n                        var preview = true;\n                        d.img.ignore = () => preview = false;\n                        this.$emit('imgtap', d.img);\n                        if (preview)\n                            // uni.previewImage({\n                            //     current: d.img.i,\n                            //     urls: this.imgList\n                            // })\n                        break;\n                    case 'linkpress':\n                        var jump = true,\n                            href = d.href;\n                        this.$emit('linkpress', {\n                            href,\n                            ignore: () => jump = false\n                        })\n                        if (jump && href) {\n                            if (href[0] == '#') {\n                                if (this.useAnchor)\n                                    weexDom.scrollToElement(this.$refs.web, {\n                                        offset: d.offset\n                                    })\n                            } else if (href.includes('://'))\n                                plus.runtime.openWeb(href);\n                            else\n                                uni.navigateTo({\n                                    url: href\n                                })\n                        }\n                        break;\n                    case 'error':\n                        if (d.source == 'img' && cfg.errorImg)\n                            this.imgList.setItem(d.target.i, cfg.errorImg);\n                        this.$emit('error', {\n                            source: d.source,\n                            target: d.target\n                        })\n                        break;\n                    case 'ready':\n                        this.height = d.height;\n                        if (d.ready) uni.createSelectorQuery().in(this).select('#_top').boundingClientRect().exec(res => {\n                            this.rect = res[0];\n                            this.$emit('ready', res[0]);\n                        })\n                        break;\n                    case 'click':\n                        this.$emit('click');\n                        this.$emit('tap');\n                }\n            },\n            // #endif\n        }\n    }\n</script>\n\n<style>\n    @keyframes _show {\n        0% {\n            opacity: 0;\n        }\n\n        100% {\n            opacity: 1;\n        }\n    }\n\n    /* #ifdef MP-WEIXIN */\n    :host {\n        display: block;\n        overflow: auto;\n        -webkit-overflow-scrolling: touch;\n    }\n\n    /* #endif */\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./jyf-parser.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891419426\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/book/bookDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./bookDetail.vue?vue&type=template&id=23a0f017&scoped=true&\"\nvar renderjs\nimport script from \"./bookDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./bookDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./bookDetail.vue?vue&type=style&index=0&id=23a0f017&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"23a0f017\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/book/bookDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bookDetail.vue?vue&type=template&id=23a0f017&scoped=true&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bookDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bookDetail.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"!isLoading\" class=\"container b-f p-b\">\n    <view class=\"base\">\r\n        <view class=\"title\"> {{ detail.bookName }} </view>\n        <view class=\"item\">\r\n          <view class=\"label\">姓名：</view>\n          <view class=\"value\">{{ detail.contact ? detail.contact : '' }}</view>\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"label\">日期：</view>\r\n          <view class=\"value\">{{ detail.serviceDate }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"label\">时间：</view>\r\n          <view class=\"value\">{{ detail.serviceTime }}</view>\r\n        </view>\r\n        <view class=\"item\">\r\n          <view class=\"label\">门店：</view>\r\n          <view class=\"value\">{{ detail.storeInfo ? detail.storeInfo.name : '-'}}</view>\r\n        </view>\n    </view>\n    <view class=\"book-qr\" v-if=\"detail.code\">\n      <view>\n         <image class=\"image\" :src=\"detail.qrCode\"></image>\n      </view>\n      <view class=\"qr-code\">\n          <p class=\"code\">预约码：{{ detail.code }}</p>\n          <p class=\"tips\">请出示以上券码给核销人员</p>\n      </view>\n    </view>\n    <view class=\"book-content m-top20\">\n        <view class=\"title\">备注</view>\n        <view class=\"content\"><jyf-parser :html=\"detail.remark ? detail.remark : '暂无...'\"></jyf-parser></view>\n    </view>\r\n    \r\n    <!-- 底部选项卡 -->\r\n    <view class=\"footer-fixed\" v-if=\"detail.status == 'A'\">\r\n      <view class=\"footer-container\">\r\n        <view class=\"foo-item-btn\">\r\n          <view class=\"btn-wrapper\">\r\n            <view class=\"btn-item btn-item-main\" @click=\"onCancel(detail.id)\">\r\n              <text>取消预约</text>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    \n  </view>\n</template>\n\n<script>\r\n  import jyfParser from '@/components/jyf-parser/jyf-parser'\n  import * as BookApi from '@/api/book'\n\n  export default {\n    data() {\n      return {\n        // 预约ID\n        myBookId: 0,\n        // 加载中\n        isLoading: true,\n        // 当前卡券详情\n        detail: null,\n        qrCode: '',\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      this.myBookId = options.myBookId ? options.myBookId : 0;\n      this.getBookDetail();\n    },\n\n    methods: {\n      // 获取预约详情\n      getBookDetail() {\n        const app = this\n        BookApi.myBookDetail(app.myBookId)\n          .then(result => {\n            app.detail = result.data ? result.data.bookInfo : null;\n          })\n          .finally(() => app.isLoading = false)\n      },\r\n      // 取消预约\r\n      onCancel() {\r\n        const app = this\r\n        uni.showModal({\r\n          title: '友情提示',\r\n          content: '确认取消预约吗？',\r\n          success(o) {\r\n            if (o.confirm) {\r\n              BookApi.cancel(app.myBookId)\r\n                .then(result => {\r\n                  // 显示成功信息\r\n                  app.$success(result.message)\r\n                  // 刷新当前订单数据\r\n                  app.getBookDetail()\r\n                })\r\n            }\r\n          }\r\n        });\r\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    min-height: 100vh;\n    padding: 20rpx;\n    background: #fff;\n    color:#666666;\n  }\n  .base {\n        border: dashed 5rpx #cccccc;\n        padding: 30rpx;\n        border-radius: 10rpx;\n        margin: 20rpx;\r\n        display: block;\r\n        height: auto;\r\n        min-height: 220rpx;\r\n        .title {\r\n            font-size: 36rpx;\r\n            font-weight: bold;\r\n            margin-bottom: 30rpx;\r\n        }\n        .book-main {\n            .image {\n                width: 200rpx;\n                height: 158rpx;\n                border-radius: 8rpx;\r\n                border: #cccccc solid 1rpx;\n            }\n            width: 100%;\n        }\r\n        .item {\r\n             margin-bottom: 20rpx;\r\n             font-size: 30rpx;\r\n             color: #666666;\r\n             clear: both;\r\n             .label {\r\n                 float: left;\r\n                 font-weight: bold;\r\n             }\r\n             .value {\r\n                 font-weight: normal;\r\n             }\r\n        }\n  }\n  .book-qr {\n      border: dashed 5rpx #cccccc;\n      border-radius: 10rpx;\n      margin: 20rpx;\n      text-align: center;\n      padding-top: 30rpx;\n      padding-bottom: 30rpx;\n      .image {\n          width: 360rpx;\n          height: 360rpx;\n          margin: 0 auto;\n      }\n      .qr-code{\n          .code{\n              font-weight: bold;\n              font-size: 30rpx;\n              line-height: 50rpx;\n          }\n          .tips{\n              font-size: 25rpx;\n              color:#C0C4CC;\n          }\n      }\n  }\n  .book-content {\n    padding: 30rpx;\n    border: dashed 5rpx #cccccc;\n    border-radius: 5rpx;\n    margin: 20rpx;\n    min-height: 400rpx;\n    .title {\n        margin-bottom: 15rpx;\r\n        font-weight: bold;\n    }\r\n    .content {\r\n        color: #666666;\r\n        font-size: 24rpx;\r\n    }\n  }\r\n  \r\n  /* 底部操作栏 */\r\n  .footer-fixed {\r\n    position: fixed;\r\n    bottom: var(--window-bottom);\r\n    left: 0;\r\n    right: 0;\r\n    display: flex;\r\n    height: 180rpx;\r\n    z-index: 11;\r\n    box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);\r\n    background: #fff;\r\n  }\r\n  \r\n  .footer-container {\r\n    width: 100%;\r\n    display: flex;\r\n    margin-bottom: 40rpx;\r\n  }\r\n  \r\n  // 操作按钮\r\n  .foo-item-btn {\r\n    flex: 1;\r\n    .btn-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  \r\n    .btn-item {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      height: 80rpx;\r\n      line-height: 80rpx;\r\n      margin-right: 16rpx;\r\n      margin-left: 16rpx;\r\n      text-align: center;\r\n      color: #fff;\r\n      border-radius: 40rpx;\r\n    }\r\n    // 立即领取按钮\r\n    .btn-item-main {\r\n      background: linear-gradient(to right, #f9211c, #ff6335);\r\n      &.state {\r\n        border: none;\r\n          color: #cccccc;\r\n          background: #F5F5F5;\r\n      }\r\n    }\r\n }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bookDetail.vue?vue&type=style&index=0&id=23a0f017&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./bookDetail.vue?vue&type=style&index=0&id=23a0f017&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420840\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}