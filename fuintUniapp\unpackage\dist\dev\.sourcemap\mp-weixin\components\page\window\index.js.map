{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/window/index.vue?8009", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/window/index.vue?4565", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/window/index.vue?0632", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/window/index.vue?7145", "uni-app:///components/page/window/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/window/index.vue?ef0b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/window/index.vue?0008"], "names": ["name", "props", "itemIndex", "itemStyle", "params", "dataList", "mixins", "methods"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkC7qB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA;EAEA;AACA;AACA;AACA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EAEAC;EAEA;AACA;AACA;AACA;EACAC,UAEA;AAEA;AAAA,2B;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/page/window/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=1e096c48&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=1e096c48&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1e096c48\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/page/window/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=1e096c48&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !(_vm.itemStyle.layout > -1) ? _vm.dataList.length : null\n  var g1 = !(_vm.itemStyle.layout > -1) ? _vm.dataList.length : null\n  var g2 = !(_vm.itemStyle.layout > -1) ? _vm.dataList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <!-- 图片橱窗 -->\n  <view class=\"diy-window\" :style=\"{ background: itemStyle.background, padding: `${itemStyle.paddingTop}px ${itemStyle.paddingLeft}px` }\">\n    <!-- matrix -->\n    <view v-if=\"itemStyle.layout > -1\" class=\"data-list\" :class=\"[`avg-sm-${itemStyle.layout}`]\">\n      <view v-for=\"(dataItem, index) in dataList\" :key=\"index\" class=\"data-item\" :style=\"{ padding: `${itemStyle.paddingTop}px ${itemStyle.paddingLeft}px` }\">\n        <view class=\"item-image\" @click=\"onLink(dataItem.link)\">\n          <image class=\"image\" mode=\"widthFix\" :src=\"dataItem.imgUrl\"></image>\n        </view>\n      </view>\n    </view>\n    <!-- display -->\n    <view v-else class=\"display\">\n      <view class=\"display-left\" :style=\"{ padding: `${itemStyle.paddingTop}px ${itemStyle.paddingLeft}px` }\">\n        <image class=\"image\" @click=\"onLink(dataItem.link)\" :src=\"dataList[0].imgUrl\"></image>\n      </view>\n      <view class=\"display-right\">\n        <view v-if=\"dataList.length >= 2 \" class=\"display-right1\" :style=\"{ padding: `${itemStyle.paddingTop}px ${itemStyle.paddingLeft}px` }\">\n          <image class=\"image\" @click=\"onLink(dataItem.link)\" :src=\"dataList[1].imgUrl\"></image>\n        </view>\n        <view class=\"display-right2\">\n          <view v-if=\"dataList.length >= 3 \" class=\"left\" :style=\"{ padding: `${itemStyle.paddingTop}px ${itemStyle.paddingLeft}px` }\">\n            <image class=\"image\" @click=\"onLink(dataItem.link)\" :src=\"dataList[2].imgUrl\"></image>\n          </view>\n          <view v-if=\"dataList.length >= 4 \" class=\"right\" :style=\"{ padding: `${itemStyle.paddingTop}px ${itemStyle.paddingLeft}px` }\">\n            <image class=\"image\" @click=\"onLink(dataItem.link)\" :src=\"dataList[3].imgUrl\"></image>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import mixin from '../mixin'\n  export default {\n    name: \"Window\",\n\n    /**\n     * 组件的属性列表\n     * 用于组件自定义设置\n     */\n    props: {\n      itemIndex: String,\n      itemStyle: Object,\n      params: Object,\n      dataList: Array\n    },\n\n    mixins: [mixin],\n\n    /**\n     * 组件的方法列表\n     * 更新属性和数据的方法与更新页面数据的方法类似\n     */\n    methods: {\n\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .diy-window .data-list::after {\n    clear: both;\n    content: \" \";\n    display: table;\n  }\n\n  .diy-window .data-list .data-item {\n    float: left;\n    box-sizing: border-box;\n  }\n\n  .diy-window .data-list .image {\n    display: block;\n    width: 100%;\n  }\n\n  /* 分列布局 */\n\n  .diy-window .avg-sm-2>.data-item {\n    width: 50%;\n  }\n\n  .diy-window .avg-sm-3>.data-item {\n    width: 33.33333333%;\n  }\n\n  .diy-window .avg-sm-4>.data-item {\n    width: 25%;\n  }\n\n  .diy-window .avg-sm-5>.data-item {\n    width: 20%;\n  }\n\n  /* 橱窗样式 */\n\n  .diy-window {\n    box-sizing: border-box;\n  }\n\n  .diy-window .display {\n    height: 0;\n    width: 100%;\n    margin: 0;\n    padding-bottom: 50%;\n    position: relative;\n    box-sizing: border-box;\n  }\n\n  .diy-window .display .image {\n    width: 100%;\n    height: 100%;\n  }\n\n  .diy-window .display .display-left {\n    width: 50%;\n    height: 100%;\n    position: absolute;\n    top: 0;\n    left: 0;\n    box-sizing: border-box;\n  }\n\n  .diy-window .display .display-right {\n    width: 50%;\n    height: 100%;\n    position: absolute;\n    top: 0;\n    left: 50%;\n    box-sizing: border-box;\n  }\n\n  .diy-window .display .display-right1 {\n    width: 100%;\n    height: 50%;\n    position: absolute;\n    top: 0;\n    box-sizing: border-box;\n    left: 0;\n  }\n\n  .diy-window .display .display-right2 {\n    width: 100%;\n    height: 50%;\n    position: absolute;\n    top: 50%;\n    left: 0;\n    box-sizing: border-box;\n  }\n\n  .diy-window .display .display-right2 .left {\n    width: 50%;\n    height: 100%;\n    position: absolute;\n    top: 0;\n    left: 0;\n    box-sizing: border-box;\n  }\n\n  .diy-window .display .display-right2 .right {\n    width: 50%;\n    height: 100%;\n    position: absolute;\n    top: 0;\n    left: 50%;\n    box-sizing: border-box;\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1e096c48&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=1e096c48&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426775\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}