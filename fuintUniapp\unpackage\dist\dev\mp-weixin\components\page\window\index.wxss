@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.diy-window .data-list.data-v-1e096c48::after {
  clear: both;
  content: " ";
  display: table;
}
.diy-window .data-list .data-item.data-v-1e096c48 {
  float: left;
  box-sizing: border-box;
}
.diy-window .data-list .image.data-v-1e096c48 {
  display: block;
  width: 100%;
}
/* 分列布局 */
.diy-window .avg-sm-2 > .data-item.data-v-1e096c48 {
  width: 50%;
}
.diy-window .avg-sm-3 > .data-item.data-v-1e096c48 {
  width: 33.33333333%;
}
.diy-window .avg-sm-4 > .data-item.data-v-1e096c48 {
  width: 25%;
}
.diy-window .avg-sm-5 > .data-item.data-v-1e096c48 {
  width: 20%;
}
/* 橱窗样式 */
.diy-window.data-v-1e096c48 {
  box-sizing: border-box;
}
.diy-window .display.data-v-1e096c48 {
  height: 0;
  width: 100%;
  margin: 0;
  padding-bottom: 50%;
  position: relative;
  box-sizing: border-box;
}
.diy-window .display .image.data-v-1e096c48 {
  width: 100%;
  height: 100%;
}
.diy-window .display .display-left.data-v-1e096c48 {
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
}
.diy-window .display .display-right.data-v-1e096c48 {
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  box-sizing: border-box;
}
.diy-window .display .display-right1.data-v-1e096c48 {
  width: 100%;
  height: 50%;
  position: absolute;
  top: 0;
  box-sizing: border-box;
  left: 0;
}
.diy-window .display .display-right2.data-v-1e096c48 {
  width: 100%;
  height: 50%;
  position: absolute;
  top: 50%;
  left: 0;
  box-sizing: border-box;
}
.diy-window .display .display-right2 .left.data-v-1e096c48 {
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
}
.diy-window .display .display-right2 .right.data-v-1e096c48 {
  width: 50%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 50%;
  box-sizing: border-box;
}
