{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/prestore-popup/index.vue?480b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/prestore-popup/index.vue?4925", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/prestore-popup/index.vue?ceb6", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/prestore-popup/index.vue?3710", "uni-app:///components/prestore-popup/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/prestore-popup/index.vue?54a9", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/prestore-popup/index.vue?1eba"], "names": ["name", "components", "NumberBox", "props", "value", "Type", "default", "couponInfo", "storeRule", "type", "maskCloseAble", "minBuyNum", "maxBuy<PERSON>um", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showClose", "closeImage", "data", "complete", "isShow", "noStock", "selectNum", "mounted", "that", "created", "app", "methods", "init", "open", "close", "moveHandle", "buyNow", "allZero", "selected", "couponId", "toast", "uni", "title", "icon", "computed", "watch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;AC6C9pB;AACA;AAAA,eACA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAC;MACAH;IACA;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;EACA;EACAU;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IAAA,CACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAL;gBACAA;gBACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAM;MACA;QACAN;QACAA;MACA;QACA;UACAA;UACAA;QACA;MACA;IACA;IAEAO;MACA;IAAA,CACA;IAEA;IACAC;MACA;MACA;MACA;QACA;UACAC;QACA;QACA;UACAC;QACA;UACAA;QACA;MACA;MAEA;QACA;QACA;MACA;MAEA;QAAAC;QAAAb;MAAA;IACA;IACA;IACAc;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACAnC;MACA;QACAkB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1LA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/prestore-popup/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=9768638a&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=9768638a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9768638a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/prestore-popup/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=9768638a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"pre-store-popup popup\" catchtouchmove=\"true\" :class=\"(value && complete) ? 'show' : 'none'\"\n    @touchmove.stop.prevent=\"moveHandle\">\n    <!-- 页面内容开始 -->\n    <view class=\"mask\" @click=\"close('mask')\"></view>\n    <!-- 页面开始 -->\n    <view class=\"layer attr-content\" :style=\"'border-radius: 10rpx 10rpx 0 0;'\">\n      <view class=\"specification-wrapper\">\n        <scroll-view class=\"specification-wrapper-content\" scroll-y=\"true\">\n          <view class=\"specification-header\">\n            <view class=\"specification-name\">{{couponInfo.name}}</view>\n          </view>\n          <view class=\"specification-content\">\n            <view v-for=\"(item, index) in storeRule\" :key=\"index\" class=\"store-item\">\n              <view style=\"flex: 4;\">\n                <view class=\"item-rule\">预存￥{{ item.store }} 到账 ￥{{ item.upStore }}</view>\n              </view>\n              <view style=\"flex: 1;text-align: right;\">\n                <number-box :min=\"minBuyNum\" :max=\"maxBuyNum\" :step=\"stepBuyNum\" v-model=\"selectNum[index]\"\n                  :positive-integer=\"true\">\n                </number-box>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n        <view class=\"close\" @click=\"close('close')\" v-if=\"showClose\">\n          <image class=\"close-item\" :src=\"closeImage\"></image>\n        </view>\n      </view>\n\n      <view v-if=\"noStock\" class=\"btn-wrapper\">\n        <view class=\"sure\" style=\"color:#ffffff;background-color:#cccccc\">库存没有了</view>\n      </view>\n      <view class=\"btn-wrapper\">\n        <view class=\"sure\" @click=\"buyNow\">立即预存</view>\n      </view>\n      <!-- 页面结束 -->\n    </view>\n    <!-- 页面内容结束 -->\n  </view>\n</template>\n\n<script>\n  import NumberBox from './number-box'\n\n  var that; // 当前页面对象\n  var vk; // 自定义函数集\n  export default {\n    name: 'CouponPopup',\n    components: {\n      NumberBox\n    },\n    props: {\n      // true 组件显示 false 组件隐藏\n      value: {\n        Type: Boolean,\n        default: false\n      },\n      // vk云函数路由模式参数开始-----------------------------------------------------------\n      // 卡券信息\n      couponInfo: {\n        Type: Object,\n        default: {}\n      },\n      // 预存规则\n      storeRule: {\n          type: Array,\n          default: []\n      },\n      // vk云函数路由模式参数结束-----------------------------------------------------------\n      // 点击遮罩是否关闭组件 true 关闭 false 不关闭 默认true\n      maskCloseAble: {\n        Type: Boolean,\n        default: true\n      },\n      // 最小购买数量\n      minBuyNum: {\n        Type: Number,\n        default: 0\n      },\n      // 最大购买数量\n      maxBuyNum: {\n        Type: Number,\n        default: 1000\n      },\n      // 每次点击后的数量\n      stepBuyNum: {\n        Type: Number,\n        default: 1\n      },\n      // 是否显示右上角关闭按钮\n      showClose: {\n        Type: Boolean,\n        default: true\n      },\n      // 关闭按钮的图片地址\n      closeImage: {\n        Type: String,\n        default: \"https://img.alicdn.com/imgextra/i1/121022687/O1CN01ImN0O11VigqwzpLiK_!!121022687.png\"\n      }\n    },\n    data() {\n      return {\n        complete: false, // 组件是否加载完成\n        isShow: false, // true 显示 false 隐藏\n        noStock: false,\n        selectNum: [], // 选中数量\n      };\n    },\n    mounted() {\n      that = this;\n    },\n    created() {\n        const app = this\n        this.storeRule.forEach(function(){\n            app.selectNum.push(0)\n        })\n    },\n    methods: {\n      // 初始化\n      init() {\n         //empty\n      },\n      async open() {\n        that.complete = true;\n        that.$emit(\"open\", true);\n        that.$emit(\"input\", true);\n      },\n      // 监听 - 弹出层收起\n      close(s) {\n        if (s == \"close\") {\n          that.$emit(\"input\", false);\n          that.$emit(\"close\", \"close\");\n        } else if (s == \"mask\") {\n          if (that.maskCloseAble) {\n            that.$emit(\"input\", false);\n            that.$emit(\"close\", \"mask\");\n          }\n        }\n      },\n      \n      moveHandle() {\n        //禁止父元素滑动\n      },\n      \n      // 立即预存\n      buyNow() {\n        let selected = \"\";\n        let allZero = true;\n        this.selectNum.forEach(function(num) {\n            if (num > 0) {\n                allZero = false;\n            }\n            if (selected.length === 0) {\n                selected = num\n            } else {\n                selected = selected + \",\" + num;\n            }\n        })\n        \n        if (allZero) {\n            this.$error(\"预存数量必须大于1\");\n            return false\n        }\n        \n        this.$navTo('pages/settlement/index', { couponId: this.couponInfo.id, selectNum: selected })\n      },\n      // 弹窗\n      toast(title, icon) {\n        uni.showToast({\n          title: title,\n          icon: icon\n        });\n      }\n    },\n    // 计算属性\n    computed: {\n       // empty\n    },\n    watch: {\n      value: function(val) {\n        if (val) {\n          that.open();\n        }\n      },\n    }\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n  .pre-store-popup {\n    position: fixed;\n    left: 0;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 21;\n    overflow: hidden;\n\n    &.show {\n      display: block;\n\n      .mask {\n        animation: showPopup 0.2s linear both;\n      }\n\n      .layer {\n        animation: showLayer 0.2s linear both;\r\n        padding-bottom: 50rpx;\n      }\n    }\n\n    &.hide {\n      .mask {\n        animation: hidePopup 0.2s linear both;\n      }\n\n      .layer {\n        animation: hideLayer 0.2s linear both;\n      }\n    }\n\n    &.none {\n      display: none;\n    }\n\n    .mask {\n      position: fixed;\n      top: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 1;\n      background-color: rgba(0, 0, 0, 0.65);\n    }\n\n    .layer {\n      display: flex;\n      width: 100%;\n      flex-direction: column;\n      position: fixed;\n      z-index: 99;\n      bottom: 0;\n      border-radius: 10rpx 10rpx 0 0;\n      background-color: #fff;\n\n      .specification-wrapper {\n        width: 100%;\n        padding: 30rpx 25rpx;\n        box-sizing: border-box;\n        background: #ffffff;\n\n        .specification-wrapper-content {\n          width: 100%;\n          max-height: 900rpx;\n          min-height: 300rpx;\n\n          &::-webkit-scrollbar {\n            /*隐藏滚轮*/\n            display: none;\n          }\n\n          .specification-header {\n            width: 100%;\n            display: flex;\n            flex-direction: row;\n            position: relative;\n            margin-bottom: 40rpx;\n            .specification-name{\n                font-weight: bold;\n            }\n          }\n\n          .specification-content {\n            font-weight: 500;\n            font-size: 26rpx;\n            .store-item {\n                display: flex;\n                height: 100rpx;\n                padding-top:30rpx;\n                cursor:pointer;\n                .item-rule {\n                    padding: 10rpx;\n                    border: solid 1px #f03c3c;\n                    border-radius: 10rpx;\n                    width: 400rpx;\n                    text-align: center;\n                    background: #f9211c;\n                    color: #ffffff;\n                }\n            }\n          }\n        }\n\n        .close {\n          position: absolute;\n          top: 30rpx;\n          right: 25rpx;\n          width: 50rpx;\n          height: 50rpx;\n          text-align: center;\n          line-height: 50rpx;\n\n          .close-item {\n            width: 40rpx;\n            height: 40rpx;\n          }\n        }\n      }\n\n      .btn-wrapper {\n        display: flex;\n        width: 100%;\n        height: 120rpx;\n        flex: 0 0 120rpx;\n        align-items: center;\n        justify-content: space-between;\n        padding: 0 26rpx;\n        box-sizing: border-box;\n\n        .layer-btn {\n          width: 335rpx;\n          height: 76rpx;\n          border-radius: 38rpx;\n          color: #fff;\n          line-height: 76rpx;\n          text-align: center;\n          font-weight: 500;\n          font-size: 28rpx;\n\n          &.add-cart {\n            background: #ffbe46;\n          }\n\n          &.buy {\n            background: #fe560a;\n          }\n        }\n\n        .sure {\n          width: 698rpx;\n          height: 80rpx;\n          border-radius: 40rpx;\n          color: #fff;\n          line-height: 80rpx;\n          text-align: center;\n          font-weight: 500;\n          font-size: 28rpx;\n          background:linear-gradient(to right, #f9211c, #ff6335)\n        }\n\n        .sure.add-cart {\n          background: #ff9402;\n        }\n      }\n    }\n\n    @keyframes showPopup {\n      0% {\n        opacity: 0;\n      }\n\n      100% {\n        opacity: 1;\n      }\n    }\n\n    @keyframes hidePopup {\n      0% {\n        opacity: 1;\n      }\n\n      100% {\n        opacity: 0;\n      }\n    }\n\n    @keyframes showLayer {\n      0% {\n        transform: translateY(120%);\n      }\n\n      100% {\n        transform: translateY(0%);\n      }\n    }\n\n    @keyframes hideLayer {\n      0% {\n        transform: translateY(0);\n      }\n\n      100% {\n        transform: translateY(120%);\n      }\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=9768638a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=9768638a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426260\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}