{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/detail.vue?e92e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/detail.vue?cb6a", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/detail.vue?5399", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/detail.vue?2a82", "uni-app:///pages/points/detail.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/detail.vue?117d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/points/detail.vue?05f3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MescrollBody", "Empty", "mixins", "data", "userInfo", "list", "isLoading", "upOption", "auto", "page", "size", "noMoreSize", "empty", "tip", "onShow", "methods", "upCallback", "app", "then", "catch", "toUsePoint", "toGive", "getUserInfo", "UserApi", "resolve", "reject", "getLogList", "LogApi", "timeStamp"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACiC/pB;AACA;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAGA;AAAA,eAEA;EACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;QACA;QACAC;UACAC;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAC,yBACAC;QACA;QACA;QACAD;MACA,GACAE;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC,eACAL;UACAD;UACAO;QACA,GACAL;UACA;YACAF;YACAO;UACA;YACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;UAAAlB;QAAA,GACAS;UACA;UACA;UACAD;UACAO;QACA;MACA;IACA;IACAI;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpJA;AAAA;AAAA;AAAA;AAA8uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAlwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/points/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/points/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=ec1e8658&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=ec1e8658&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ec1e8658\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/points/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=ec1e8658&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list.content, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var m0 = _vm.timeStamp(item.createTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"my-point\">\n        <view class=\"my-tip\"><text class=\"iconfont icon-jifen\"></text>我的积分余额</view>\n        <view class=\"my-account\">{{ userInfo.point ? userInfo.point : 0 }}</view>\n        <view class=\"my-gift\">\n            <!-- <view class=\"gift\" @click=\"toGive()\">转赠好友</view> -->\n            <view class=\"gift\" @click=\"toUsePoint()\">兑换积分</view>\n        </view>\n    </view>\n    <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ use: false }\" :up=\"upOption\"\n      @up=\"upCallback\">\n      <view class=\"log-list\">\n        <view v-for=\"(item, index) in list.content\" :key=\"index\" class=\"log-item\">\n          <view class=\"item-left flex-box\">\n            <view class=\"rec-status\">\n              <text>{{ item.description }}</text>\n            </view>\n            <view class=\"rec-time\">\n              <text>{{ timeStamp(item.createTime) }}</text>\n            </view>\n          </view>\n          <view class=\"item-right\" :class=\"[item.amount > 0 ? 'col-green' : 'col-6']\">\n            <text>{{ item.amount > 0 ? '+' : '' }}{{ item.amount }}</text>\n          </view>\n        </view>\n      </view>\n    </mescroll-body>\n  </view>\n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import * as LogApi from '@/api/points/log'\n  import * as UserApi from '@/api/user'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n  import Empty from '@/components/empty'\n\n  const pageSize = 10\n\n  export default {\n    components: {\n      MescrollBody,\n      Empty\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        userInfo: {},\n        // 数据记录\n        list: getEmptyPaginateObj(),\n        // 正在加载\n        isLoading: false,\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于12条才显示无更多数据\n          noMoreSize: 12,\n          // 空布局\n          empty: {\n            tip: '亲，暂无相关数据'\n          }\n        }\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onShow(options) {\n        this.getUserInfo()\n        this.getLogList(1)\n    },\n\n    methods: {\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this\n        // 设置列表数据\n        app.getLogList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length\n            const totalSize = list.totalElements\n            app.mescroll.endBySize(curPageLen, totalSize)\n          })\n          .catch(() => app.mescroll.endErr())\n      },\n      toUsePoint() {\n         this.$navTo('pages/coupon/list?type=C&needPoint=1')\n      },\n      toGive() {\n          this.$navTo('pages/points/gift')\n      },\n      // 获取当前用户信息\n      getUserInfo() {\n        const app = this\n        return new Promise((resolve, reject) => {\n            UserApi.info()\n            .then(result => {\n                  app.userInfo = result.data.userInfo\n                  resolve(app.userInfo)\n            })\n            .catch(err => {\n              if (err.result && err.result.status == 1001) {\n                  app.isLogin = false\n                  resolve(null)\n              } else {\n                  reject(err)\n              }\n            })\n        })\n      },\n      // 获取积分明细列表\n      getLogList(pageNo = 1) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          LogApi.list({ page: pageNo })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data\n              app.list.content = getMoreListData(newList, app.list, pageNo)\n              resolve(newList)\n            })\n        })\n      },\n      timeStamp: function(value) {\n          var date = new Date(value);\n          var year = date.getFullYear();\n          var month = (\"0\" + (date.getMonth() + 1)).slice(-2);\n          var sdate = (\"0\" + date.getDate()).slice(-2);\n          var hour = (\"0\" + date.getHours()).slice(-2);\n          var minute = (\"0\" + date.getMinutes()).slice(-2);\n          var second = (\"0\" + date.getSeconds()).slice(-2);\n          // 拼接\n          var result = year + \".\" + month + \".\" + sdate + \" \" + hour + \":\" + minute //+ \":\" + second;\n          // 返回\n          return result;\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n     background: #FFFFFF;\n  }\n  .my-point {\n      height: 320rpx;\n      color: #FFFFFF;\n      background: $fuint-theme;\n      padding-top: 80rpx;\n      text-align: center;\n      .my-tip {\n          text-align: center;\n      }\n      .my-account {\n          text-align: center;\n          font-size: 45rpx;\n          font-weight: bold;\n          margin-top: 14rpx;\n      }\n      .iconfont {\n          height: 30rpx;\n          width: 30rpx;\n          margin-right: 5rpx;\n      }\n      .my-gift {\n          text-align: center;\n          display: inline-block;\n          .gift {\n            height: 60rpx;\n            width: 140rpx;\n            margin-top: 20rpx;\n            line-height: 60rpx;\n            text-align: center;\n            float: left;\n            margin-right: 20rpx;\n            margin-left: 20rpx;\n            border: 1px solid #f86d48;\n            border-radius: 6rpx;\n            color: #FFFFFF;\n            background: #f86d48;\n            font-size: 22rpx;\n          }\n      }\n  }\n\n  .log-list {\n    padding: 0 30rpx;\n    background: #FFFFFF;\n    border-radius: 20rpx;\n  }\n\n  .log-item {\n    font-size: 32rpx;\n    padding: 20rpx 20rpx;\n    line-height: 1.8;\n    border-bottom: 1rpx solid rgb(238, 238, 238);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .rec-time {\n    color: #888888;\n    font-size: 24rpx;\n  }\n  // 空数据按钮\n  .empty-ipt {\n    width: 220rpx;\n    margin: 10rpx auto;\n    font-size: 28rpx;\n    height: 64rpx;\n    line-height: 64rpx;\n    text-align: center;\n    color: #fff;\n    border-radius: 5rpx;\n    background: linear-gradient(to right, $fuint-theme, $fuint-theme);\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=ec1e8658&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=ec1e8658&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420728\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}