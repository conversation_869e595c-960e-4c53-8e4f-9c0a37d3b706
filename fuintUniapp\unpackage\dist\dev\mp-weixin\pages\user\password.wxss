@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.info-list.data-v-0f099936 {
  padding-bottom: 30rpx;
  margin-top: 30rpx;
}
.info-item.data-v-0f099936 {
  margin: 20rpx auto 20rpx auto;
  padding: 30rpx 40rpx;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  background: #fff;
}
.info-item .avatar-warp.data-v-0f099936 {
  line-height: 120rpx;
}
.contacts.data-v-0f099936 {
  font-size: 30rpx;
  height: 40rpx;
}
.contacts .name.data-v-0f099936 {
  margin-left: 0px;
  float: left;
  margin-right: 10rpx;
  line-height: 40rpx;
}
.contacts .value.data-v-0f099936 {
  float: right;
  color: #999999;
  text-align: right;
}
.contacts .value .second.data-v-0f099936 {
  margin-left: 0.6rem;
}
.contacts .vcode.data-v-0f099936 {
  float: left;
  line-height: 40rpx;
}
.contacts .password.data-v-0f099936 {
  text-align: right;
  float: left;
  padding-right: 5rpx;
}
.contacts .avatar.data-v-0f099936 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 120rpx;
  border: solid 1px #cccccc;
  float: right;
}
.footer-fixed.data-v-0f099936 {
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);
  margin-top: 80rpx;
}
.footer-fixed .btn-wrapper.data-v-0f099936 {
  height: 100%;
  display: flex;
  text-align: center;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 10rpx;
}
.footer-fixed .btn-item.data-v-0f099936 {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
}
.footer-fixed .btn-item-main.data-v-0f099936 {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.footer-fixed .btn-item-back.data-v-0f099936 {
  margin-top: 20rpx;
  background: #FFFFFF;
  border: 1px solid #3f51b5;
  color: #666666;
}
