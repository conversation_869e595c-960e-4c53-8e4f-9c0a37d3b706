@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 顶部选项卡 */
.container.data-v-d1630c8e {
  min-height: 100vh;
}
.poster.data-v-d1630c8e {
  padding: 20rpx;
  text-align: center;
}
.poster .iconfont.data-v-d1630c8e {
  text-align: center;
  font-size: 120rpx;
  margin: 50rpx;
  color: #3f51b5;
}
.poster .action-btn.data-v-d1630c8e {
  text-align: center;
  margin-top: 10rpx;
}
.poster .action-btn .btn-wrapper.data-v-d1630c8e {
  height: 100%;
  display: block;
  align-items: center;
  width: 70%;
  margin: 0 auto;
}
.poster .action-btn .btn-wrapper .btn-item.data-v-d1630c8e {
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 80rpx;
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.share-popup.data-v-d1630c8e {
  padding: 20rpx;
  font-size: 30rpx;
  text-align: center;
}
.share-popup .share-title.data-v-d1630c8e {
  margin-top: 40rpx;
  font-size: 35rpx;
  font-weight: bold;
}
.share-popup .row.data-v-d1630c8e {
  margin-top: 30rpx;
  padding-top: 80rpx;
  clear: both;
  height: 240rpx;
}
.share-popup .row .col-6.data-v-d1630c8e {
  width: 50%;
  float: left;
}
.share-popup .row .col-6 .mt-1.data-v-d1630c8e {
  line-height: 60rpx;
  font-size: 24rpx;
  background: none;
}
.share-popup .row .col-6 .iconfont.data-v-d1630c8e {
  color: #3f51b5;
  font-size: 56rpx;
}
.share-popup .share-btn.data-v-d1630c8e {
  text-align: center;
}
.share-popup .share-btn .btn-wrapper.data-v-d1630c8e {
  height: 100%;
  display: block;
  align-items: center;
  width: 80%;
  margin: 0 auto;
  border: solid 2rpx #ccc;
  border-radius: 80rpx;
}
.share-popup .share-btn .btn-wrapper .btn-item.data-v-d1630c8e {
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #333;
}
.title-wrapper.data-v-d1630c8e {
  display: flex;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  color: #333;
  padding-left: 20rpx;
  font-size: 30rpx;
  font-weight: bold;
  background: #fff;
  margin-top: 50rpx;
  border-bottom: 1rpx solid #e4e4e4;
  z-index: 100;
  overflow: hidden;
  white-space: nowrap;
}
/* 分享列表 */
.share-list.data-v-d1630c8e {
  padding-top: 10rpx;
  line-height: 1;
  background: #f7f7f7;
}
.share-item.data-v-d1630c8e {
  margin-bottom: 10rpx;
  padding: 20rpx;
  background: #fff;
}
.share-item.data-v-d1630c8e:last-child {
  margin-bottom: 0;
}
.share-item .share-item-title.data-v-d1630c8e {
  max-height: 80rpx;
  font-size: 24rpx;
  color: #333;
}
.share-item .share-item-title .name.data-v-d1630c8e {
  font-weight: bold;
}
.share-item .share-item-title .no.data-v-d1630c8e {
  margin-top: 20rpx;
  font-size: 20rpx;
  color: #888888;
}
.share-item .share-item-image .image.data-v-d1630c8e {
  display: block;
  border-radius: 100rpx;
  height: 100rpx;
  width: 100rpx;
  border: 2rpx solid #cccccc;
  float: left;
  background: #ccc;
}
.share-item .share-item-footer.data-v-d1630c8e {
  margin-top: 0rpx;
  clear: both;
  text-align: right;
}
.show-type.data-v-d1630c8e {
  display: flex;
}
.show-type .share-item-left.data-v-d1630c8e {
  padding-right: 20rpx;
  width: 10rpx;
}
.show-type .share-item-title.data-v-d1630c8e {
  min-height: 100rpx;
  float: left;
  margin-left: 10rpx;
}
