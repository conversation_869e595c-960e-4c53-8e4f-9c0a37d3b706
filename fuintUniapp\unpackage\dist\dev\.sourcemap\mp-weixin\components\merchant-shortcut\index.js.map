{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/merchant-shortcut/index.vue?bc02", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/merchant-shortcut/index.vue?a7bc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/merchant-shortcut/index.vue?e5a9", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/merchant-shortcut/index.vue?ee70", "uni-app:///components/merchant-shortcut/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/merchant-shortcut/index.vue?c973", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/merchant-shortcut/index.vue?a006"], "names": ["props", "right", "type", "default", "bottom", "data", "isShow", "transparent", "methods", "onToggleShow", "app", "onTargetPage", "onMerchantPage"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;AC2B9pB;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EAEA;AACA;AACA;AACA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EAEAE;IACA;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IAEA;AACA;AACA;IACAC;MACA;MACAC;MACAA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/merchant-shortcut/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=926a6ffa&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=926a6ffa&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"926a6ffa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/merchant-shortcut/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=926a6ffa&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"shortcut\" :style=\"{ right: right, bottom: bottom }\">\n\n    <!-- 首页 -->\n    <view class=\"nav-item\" :class=\"[isShow ? 'show_60' : (transparent ? '' : 'hide_60')]\" @click=\"onTargetPage(0)\">\n      <text class=\"iconfont icon-home\"></text>\n    </view>\n    \n    <!-- 个人中心 -->\n    <view class=\"nav-item\" :class=\"[isShow ? 'show_40' : (transparent ? '' : 'hide_40')]\" @click=\"onTargetPage(3)\">\n      <text class=\"iconfont icon-profile\"></text>\n    </view>\n    \n    <!-- 商家中心 -->\n    <view class=\"nav-item\" :class=\"[isShow ? 'show_20' : (transparent ? '' : 'hide_20')]\" @click=\"onMerchantPage()\">\n      <text class=\"iconfont icon-dianpu\"></text>\n    </view>\n\n    <!-- 显示隐藏开关 -->\n    <view class=\"nav-item nav-item__switch\" :class=\"{ shortcut_click_show: isShow }\" @click=\"onToggleShow()\">\n      <text class='iconfont icon-daohang'></text>\n    </view>\n\n  </view>\n</template>\n\n<script>\n  import { getTabBarLinks } from '@/utils/app'\n  export default {\n\n    /**\n     * 组件的属性列表\n     * 用于组件自定义设置\n     */\n    props: {\n      right: {\n        type: String,\n        default: '30rpx'\n      },\n      bottom: {\n        type: String,\n        default: '200rpx'\n      }\n    },\n\n    data() {\n      return {\n        // 弹窗显示控制\n        isShow: false,\n        transparent: true\n      }\n    },\n\n    methods: {\n\n      /**\n       * 导航菜单切换事件\n       */\n      onToggleShow() {\n        const app = this\n        app.isShow = !app.isShow\n        app.transparent = false\n      },\n\n      /**\n       * 导航页面跳转\n       */\n      onTargetPage(index = 0) {\n        const tabLinks = getTabBarLinks()\n        this.$navTo(tabLinks[index])\n      },\n      onMerchantPage() { \n        this.$navTo('pages/merchant/index')\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  /* 快捷导航 */\n  .shortcut {\n    position: fixed;\n    right: 24rpx;\n    bottom: 250rpx;\n    width: 76rpx;\n    line-height: 1;\n    z-index: 5;\n    border-radius: 50%;\n  }\n\n  /* 导航菜单元素 */\n  .nav-item {\n    position: absolute;\n    bottom: 0;\n    padding: 0;\n    width: 76rpx;\n    height: 76rpx;\n    line-height: 76rpx;\n    color: #fff;\n    background: rgba(0, 0, 0, 0.4);\n    border-radius: 50%;\n    text-align: center;\n    transform: rotate(0deg);\n    opacity: 0;\n  }\n\n  .nav-item .iconfont {\n    font-size: 40rpx;\n  }\n\n  /* 导航开关 */\n  .nav-item__switch {\n    opacity: 1;\n  }\n\n  .shortcut_click_show {\n    margin-bottom: 0;\n    background: #ff5454;\n  }\n\n  /* 显示动画 */\n  .show_80 {\n    bottom: 384rpx;\n    animation: show_80 0.3s forwards;\n  }\n\n  .show_60 {\n    bottom: 288rpx;\n    animation: show_60 0.3s forwards;\n  }\n\n  .show_40 {\n    bottom: 192rpx;\n    animation: show_40 0.3s forwards;\n  }\n\n  .show_20 {\n    bottom: 96rpx;\n    animation: show_20 0.3s forwards;\n  }\n\n  @keyframes show_20 {\n    from {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n\n    to {\n      bottom: 96rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n  }\n\n  @keyframes show_40 {\n    from {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n\n    to {\n      bottom: 192rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n  }\n\n  @keyframes show_60 {\n    from {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n\n    to {\n      bottom: 288rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n  }\n\n  @keyframes show_80 {\n    from {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n\n    to {\n      bottom: 384rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n  }\n\n  /* 隐藏动画 */\n\n  .hide_80 {\n    bottom: 0;\n    animation: hide_80 0.3s;\n    opacity: 0;\n  }\n\n  .hide_60 {\n    bottom: 0;\n    animation: hide_60 0.3s;\n    opacity: 0;\n  }\n\n  .hide_40 {\n    bottom: 0;\n    animation: hide_40 0.3s;\n    opacity: 0;\n  }\n\n  .hide_20 {\n    bottom: 0;\n    animation: hide_20 0.3s;\n    opacity: 0;\n  }\n\n  @keyframes hide_20 {\n    from {\n      bottom: 96rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n\n    to {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n  }\n\n  @keyframes hide_40 {\n    from {\n      bottom: 192rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n\n    to {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n  }\n\n  @keyframes hide_60 {\n    from {\n      bottom: 288rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n\n    to {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n  }\n\n  @keyframes hide_80 {\n    from {\n      bottom: 384rpx;\n      transform: rotate(360deg);\n      opacity: 1;\n    }\n\n    to {\n      bottom: 0;\n      transform: rotate(0deg);\n      opacity: 0;\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=926a6ffa&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=926a6ffa&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426065\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}