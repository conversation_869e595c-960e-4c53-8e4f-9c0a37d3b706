@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.info-list.data-v-61bb5eaa {
  padding-bottom: 30rpx;
  margin-top: 30rpx;
}
.info-item.data-v-61bb5eaa {
  margin: 20rpx auto 20rpx auto;
  padding: 30rpx;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  border-radius: 10rpx;
  background: #fff;
}
.contacts.data-v-61bb5eaa {
  font-size: 35rpx;
  text-align: center;
}
.contacts .name.data-v-61bb5eaa {
  margin-left: 0px;
  text-align: center;
}
.info-code.data-v-61bb5eaa {
  text-align: center;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.info-code .code-text.data-v-61bb5eaa {
  margin-bottom: 50rpx;
}
.info-code .qrcode.data-v-61bb5eaa {
  width: 360rpx;
  height: 360rpx;
  margin: 0 auto;
  border: solid 1px #ccc;
}
.footer-fixed.data-v-61bb5eaa {
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);
}
.footer-fixed .btn-wrapper.data-v-61bb5eaa {
  height: 100%;
  display: flex;
  text-align: center;
  align-items: center;
  padding: 0 30rpx;
}
.footer-fixed .btn-item.data-v-61bb5eaa {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
}
.footer-fixed .btn-item-main.data-v-61bb5eaa {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
