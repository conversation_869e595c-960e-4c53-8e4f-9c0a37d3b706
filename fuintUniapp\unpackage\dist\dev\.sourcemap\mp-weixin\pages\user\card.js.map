{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/card.vue?2dbe", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/card.vue?a3ec", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/card.vue?eb49", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/card.vue?77f5", "uni-app:///pages/user/card.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/card.vue?69c2", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/card.vue?9bb2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "options", "isLoading", "userInfo", "qrCode", "onLoad", "methods", "getUserInfo", "app", "UserApi", "then", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyoB,CAAgB,uoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACyB7pB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;MACAC;MACAC,iBACAC;QACAF;QACAA;QACAA;MACA;IACA;IACAG;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAA4uC,CAAgB,kqCAAG,EAAC,C;;;;;;;;;;;ACAhwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/card.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/card.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./card.vue?vue&type=template&id=61bb5eaa&scoped=true&\"\nvar renderjs\nimport script from \"./card.vue?vue&type=script&lang=js&\"\nexport * from \"./card.vue?vue&type=script&lang=js&\"\nimport style0 from \"./card.vue?vue&type=style&index=0&id=61bb5eaa&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"61bb5eaa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/card.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./card.vue?vue&type=template&id=61bb5eaa&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./card.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./card.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"info-list\">\n      <view class=\"info-item\">\n        <view class=\"contacts\">\n            <text class=\"name\"><text class=\"iconfont icon-bangzhu\"></text>长按领取会员卡</text>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"info-code\">\n        <view class=\"code-text\">会员号：{{ userInfo.userNo }}</view>\n        <image class=\"qrcode\" :src=\"qrCode\"></image>\n    </view>\n    \n    <!-- 底部操作按钮 -->\n    <view class=\"footer-fixed\">\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" @click=\"goBack()\">返回主页</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as UserApi from '@/api/user'\n  import store from '@/store'\n\n  export default {\n    data() {\n      return {\n        //当前页面参数\n        options: {},\n        // 正在加载\n        isLoading: true,\n        userInfo: {},\n        qrCode: \"\"\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 当前页面参数\n      this.options = options\n      this.getUserInfo()\n    },\n\n    methods: {\n      /**\n       * 用户信息\n       * */\n      getUserInfo() {\n        const app = this\n        app.isLoading = true\n        UserApi.qrCode()\n          .then(result => {\n            app.userInfo = result.data.userInfo;\n            app.qrCode = result.data.wxCardQrCode;\n            app.isLoading = false;\n          })\n      },\n      goBack() {\n          this.$navTo('pages/index/index');\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .info-list {\n      padding-bottom: 30rpx;\n      margin-top: 30rpx;\n  }\n\n  // 项目内容\n  .info-item {\n    margin: 20rpx auto 20rpx auto;\n    padding: 30rpx;\n    width: 94%;\n    box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);\n    border-radius: 10rpx;\n    background: #fff;\n  }\n\n  .contacts {\n    font-size: 35rpx;\n    text-align: center;\n    .name {\n      margin-left:0px;\n      text-align: center;\n    }\n  }\n\n  .info-code {\n      text-align: center;\n      padding: 30rpx;\n      margin-bottom: 30rpx;\n      .code-text{\n          margin-bottom: 50rpx;\n      }\n      .qrcode {\n         width: 360rpx;\n         height: 360rpx;\n         margin: 0 auto;\n         border: solid 1px #ccc;\n      }\n  }\n\n  // 底部操作栏\n  .footer-fixed {\n    z-index: 11;\n    box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      text-align: center;\n      align-items: center;\n      padding: 0 30rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./card.vue?vue&type=style&index=0&id=61bb5eaa&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./card.vue?vue&type=style&index=0&id=61bb5eaa&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891423855\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}