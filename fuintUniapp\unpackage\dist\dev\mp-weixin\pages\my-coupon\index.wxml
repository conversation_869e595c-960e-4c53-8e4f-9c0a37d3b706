<view class="container data-v-271a43c1"><mescroll-body vue-id="87a29536-1" sticky="{{true}}" down="{{({use:true})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" class="data-v-271a43c1 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('87a29536-2')+','+('87a29536-1')}}" list="{{tabs}}" is-scroll="{{false}}" current="{{curTab}}" active-color="#FA2209" duration="{{0.2}}" data-event-opts="{{[['^change',[['onChangeTab']]]]}}" bind:change="__e" class="data-v-271a43c1" bind:__l="__l"></u-tabs><view class="goods-list data-v-271a43c1"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-item data-v-271a43c1"><view data-event-opts="{{[['tap',[['onDetail',['$0','$1'],[[['list.content','',index,'id']],[['list.content','',index,'type']]]]]]]}}" class="dis-flex data-v-271a43c1" bindtap="__e"><view class="goods-item_left data-v-271a43c1"><image class="image data-v-271a43c1" src="{{item.image}}"></image></view><view class="goods-item_right data-v-271a43c1"><view class="goods-name twolist-hidden data-v-271a43c1"><text class="data-v-271a43c1">{{item.name}}</text></view><view class="goods-item_desc data-v-271a43c1"><view class="desc-selling_point dis-flex data-v-271a43c1"><text class="onelist-hidden data-v-271a43c1">{{item.tips}}</text></view><view class="coupon-attr data-v-271a43c1"><view class="attr-l data-v-271a43c1"><view class="desc-goods_sales dis-flex data-v-271a43c1"><text class="data-v-271a43c1">{{item.effectiveDate}}</text></view><block wx:if="{{item.amount>0}}"><view class="desc_footer data-v-271a43c1"><text class="price_x data-v-271a43c1">{{"¥"+item.amount}}</text></view></block></view></view></view></view></view></view></block></view></mescroll-body></view>