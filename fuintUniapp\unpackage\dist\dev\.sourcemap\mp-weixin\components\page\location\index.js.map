{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/location/index.vue?1e79", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/location/index.vue?5898", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/location/index.vue?81e5", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/location/index.vue?f39f", "uni-app:///components/page/location/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/location/index.vue?e396", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/location/index.vue?f201"], "names": ["props", "itemStyle", "storeInfo", "currentDeliveryMode", "type", "default", "data", "methods", "onTargetLocation", "onSwitchDelivery", "uni", "title", "icon", "duration"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmC7qB;EAEA;AACA;AACA;AACA;EACAA;IACAC;IACAC;IACAC;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;MACA;IAAA,CACA;EACA;EAEA;AACA;AACA;AACA;EACAC;IACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;QACA;QACAC;UACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;;MAEA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC7FA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/page/location/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=5152a28d&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=5152a28d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5152a28d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/page/location/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=5152a28d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <!-- 店铺信息区域 -->\n  <view class=\"shop-header\" v-if=\"storeInfo\">\n    <view class=\"shop-info\">\n      <view class=\"shop-details\" @click=\"onTargetLocation\">\n        <view class=\"shop-name-row\">\n          <image class=\"location-icon\" src=\"@/static/location-icon.png\"></image>\n          <text class=\"shop-name\">{{ storeInfo.name }}  </text>\n          <image class=\"arrow-icon\" src=\"@/static/nav-arrow.png\"></image>\n        </view> \n      </view>\n      <view class=\"delivery-switch\">\n        <view\n          class=\"switch-item\"\n          :class=\"{ active: currentDeliveryMode === 'oneself' }\"\n          @click=\"onSwitchDelivery('oneself')\"\n        >\n          <text class=\"switch-text\">自取</text>\n        </view>\n        <view\n          class=\"switch-item\"\n          :class=\"{\n            active: currentDeliveryMode === 'express',\n            disabled: storeInfo.deliverySupported !== 'Y'\n          }\"\n          @click=\"onSwitchDelivery('express')\"\n        >\n          <text class=\"switch-text\">外送</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  export default {\n\n    /**\n     * 组件的属性列表\n     * 用于组件自定义设置\n     */\n    props: {\n      itemStyle: Object,\n      storeInfo: Object,\n      currentDeliveryMode: {\n        type: String,\n        default: 'oneself'\n      }\n    },\n\n    /**\n     * 组件的初始数据\n     */\n    data() {\n      return {\n        // currentDeliveryMode 现在从 props 获取，不再需要内部状态\n      }\n    },\n\n    /**\n     * 组件的方法列表\n     * 更新属性和数据的方法与更新页面数据的方法类似\n     */\n    methods: {\n      /**\n       * 跳转到定位页面页面\n       */\n      onTargetLocation() {\n        this.$navTo('pages/location/index')\n      },\n      \n      /**\n       * 切换配送方式\n       */\n      onSwitchDelivery(mode) {\n        if (mode === 'delivery' && this.storeInfo.deliverySupported !== 'Y') {\n          // 如果选择外送但店铺不支持，显示提示\n          uni.showToast({\n            title: '该店铺暂不支持外送服务',\n            icon: 'none',\n            duration: 2000\n          })\n          return\n        }\n        \n        // 更新当前配送模式状态\n        this.currentDeliveryMode = mode\n        \n        // 切换配送方式的逻辑\n        this.$emit('deliveryModeChange', mode)\n      }\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n/* 店铺信息头部 */\n.shop-header {\n  position: relative;\n  padding: 20rpx 32rpx 20rpx 32rpx;\n  background: #fff;\n  box-shadow: 0px 2rpx 4rpx rgba(0, 0, 0, 0.25);\n\n  .shop-info {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-end;\n\n    .shop-details {\n      display: flex;\n      flex-direction: column;\n\n      .shop-name-row {\n        display: flex;\n        align-items: center;\n        margin-bottom: 8rpx;\n\n        .location-icon {\n          width: 36rpx;\n          height: 32rpx;\n          margin-right: 10rpx;\n        }\n\n        .shop-name {\n          color: #000;\n          font-size: 42rpx;\n          font-family: FZLanTingHei-DB-GBK;\n          line-height: 40rpx;\n          margin-right: 10rpx;\n        }\n\n        .arrow-icon {\n          width: 14rpx;\n          height: 22rpx;\n        }\n      }\n\n      .shop-distance {\n        margin-left: 44rpx;\n        color: rgba(51, 51, 51, 0.6);\n        font-size: 28rpx;\n        font-family: FZLanTingHei-L-GBK;\n        line-height: 50rpx;\n        letter-spacing: 2rpx;\n      }\n\n      .shop-switch {\n        margin-left: 44rpx;\n        \n        .switch-text {\n          color: #666;\n          font-size: 24rpx;\n          line-height: 30rpx;\n        }\n      }\n    }\n\n    .delivery-switch {\n      margin-top: 16rpx;\n      background: #f2f2f2;\n      border-radius: 999rpx;\n      width: 162rpx;\n      height: 50rpx;\n      display: flex;\n      align-items: center;\n\n      .switch-item {\n        font-size: 24rpx;\n        font-family: FZLanTingHei-DB-GBK;\n        line-height: 23rpx;\n        text-align: center;\n        flex: 1;\n        color: #8c8d8f;\n        cursor: pointer;\n\n        &.active {\n          background: #232e5d;\n          border-radius: 999rpx;\n          color: #fff;\n          height: 50rpx;\n          line-height: 50rpx;\n        }\n\n        &.disabled {\n          color: #d0d0d0;\n          cursor: not-allowed;\n          opacity: 0.5;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=5152a28d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=5152a28d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424042\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}