
.mark{
    width: 750rpx;
    background: rgba(0,0,0,0.7);
    padding: 0 0 700rpx 0;
    position: absolute;
    top: 0rpx;
    left: 0rpx;
    z-index: 99;
}
.hidden{
    display: none;
}
.kong{
    width: 750rpx;
    height: 250rpx;
}
.msg{
    width: 550rpx;
    height: 450rpx;
    background: rgba(255,255,255,1);
    border-radius: 20rpx;
    margin: 0 auto;
    -webkit-animation: msgBox .2s linear;
            animation: msgBox .2s linear;
}
@-webkit-keyframes msgBox{
0%{
        -webkit-transform:translateY(50%);
                transform:translateY(50%);
        opacity: 0;
}
50%{
        -webkit-transform:translateY(25%);
                transform:translateY(25%);
        opacity: 0.5;
}
100%{
        -webkit-transform:translateY(0%);
                transform:translateY(0%);
        opacity: 1;
}
}
@keyframes msgBox{
0%{
        -webkit-transform:translateY(50%);
                transform:translateY(50%);
        opacity: 0;
}
50%{
        -webkit-transform:translateY(25%);
                transform:translateY(25%);
        opacity: 0.5;
}
100%{
        -webkit-transform:translateY(0%);
                transform:translateY(0%);
        opacity: 1;
}
}
@-webkit-keyframes numBox{
0%{
        -webkit-transform:translateY(50%);
                transform:translateY(50%);
        opacity: 0;
}
50%{
        -webkit-transform:translateY(25%);
                transform:translateY(25%);
        opacity: 0.5;
}
100%{
        -webkit-transform:translateY(0%);
                transform:translateY(0%);
        opacity: 1;
}
}
@keyframes numBox{
0%{
        -webkit-transform:translateY(50%);
                transform:translateY(50%);
        opacity: 0;
}
50%{
        -webkit-transform:translateY(25%);
                transform:translateY(25%);
        opacity: 0.5;
}
100%{
        -webkit-transform:translateY(0%);
                transform:translateY(0%);
        opacity: 1;
}
}
.msg>.img{
    padding: 20rpx 0 0 20rpx;
    font-size: 40rpx;
}
.msg>.title{
    width: 100%;
    height: 100rpx;
    line-height: 100rpx;
    font-weight: 500;
    font-size: 36rpx;
    text-align: center;
}
.msg>.subTitle{
    width: 100%;
    height: 50rpx;
    line-height: 50rpx;
    font-weight: 400;
    font-size: 32rpx;
    text-align: center;
}
.pswBox{
    width: 80%;
    height: 80rpx;
    margin: 50rpx auto 0;
    display: flex;
}
.content_item{
    flex: 2;
    text-align: center;
    line-height: 80rpx;
    border: 1rpx solid #D6D6D6;
    border-right: 0rpx solid;
}
.content_item:nth-child(1){
    border-radius: 10rpx 0 0 10rpx;
}
.content_item:nth-child(6){
    border-right: 1rpx solid #D6D6D6;
    border-radius: 0 10rpx 10rpx 0;
}
.numeric{
    height: 480rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: #EBEBEB;
    display: flex;
    justify-content: center;
    z-index: 2;
    flex-wrap: wrap;
    -webkit-animation: msgBox .2s linear;
            animation: msgBox .2s linear;
}
.num{
    box-sizing: border-box;
    width: 250rpx;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: #fff;
    font-size: 40rpx;
    color: #333;
    height: 120rpx;
    border: 1rpx solid #F2F2F2;
    border-top:none;
    border-left:none;
}
.numColor{
    background: #FF0000;
}
.forget{
    font-size: 28rpx;
    font-weight: 500;
    color: #3D84EA;
    text-align: center;
    line-height: 80rpx;
}
.amend1{
    border: 1rpx solid #CCCFD6;
    background-color: #CCCFD6;
}
.amend3{
    font-size: 60rpx;
    border: 1rpx solid #CCCFD6;
    background-color: #CCCFD6;
}
/* .amend11{
    position: absolute;
    top: 313upx;
    left: 0upx;
    background-color: #CCCFD6;
    border: 1upx solid #FF0000;
}
.amend1{
    height: 100upx !important;
    position: absolute;
    top: 306upx;
    left: 0upx;
    z-index: 99;
    background-color: #CCCFD6;
    border: 2upx solid #CCCFD6;
}
.amend2{
    position: absolute;
    top: 306upx;
    left: 250upx;
    z-index: 99;
}
.amend3{
    position: absolute;
    top: 306upx;
    left: 500upx;
    z-index: 99;
    font-size: 60upx;
    border: 0upx;
    background-color: #CCCFD6;
} */


