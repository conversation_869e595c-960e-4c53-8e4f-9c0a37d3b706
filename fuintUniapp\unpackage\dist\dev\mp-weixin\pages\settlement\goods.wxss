@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-38cab12e {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding-bottom: 200rpx;
}
.card-container.data-v-38cab12e, .pay-method.data-v-38cab12e, .goods-detail-header.data-v-38cab12e, .checkout_list.data-v-38cab12e, .flow-all-money.data-v-38cab12e, .flow-delivery.data-v-38cab12e, .flow-mode.data-v-38cab12e {
  background-color: #ffffff;
  border-radius: 30rpx;
  box-shadow: 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.25);
  margin: 20rpx 28rpx;
  padding: 40rpx;
}
.pay-type-popup.data-v-38cab12e {
  padding: 25rpx 25rpx 70rpx 25rpx;
}
.pay-type-popup .title.data-v-38cab12e {
  font-size: 30rpx;
  margin-bottom: 50rpx;
  font-weight: bold;
  text-align: center;
}
.pay-type-popup .flex-row.data-v-38cab12e {
  display: flex;
  flex-direction: row;
}
.pay-type-popup .flex-col.data-v-38cab12e {
  display: flex;
  flex-direction: column;
}
.pay-type-popup .justify-start.data-v-38cab12e {
  justify-content: flex-start;
}
.pay-type-popup .justify-end.data-v-38cab12e {
  justify-content: flex-end;
}
.pay-type-popup .justify-center.data-v-38cab12e {
  justify-content: center;
}
.pay-type-popup .justify-between.data-v-38cab12e {
  justify-content: space-between;
}
.pay-type-popup .justify-around.data-v-38cab12e {
  justify-content: space-around;
}
.pay-type-popup .items-start.data-v-38cab12e {
  align-items: flex-start;
}
.pay-type-popup .items-end.data-v-38cab12e {
  align-items: flex-end;
}
.pay-type-popup .items-center.data-v-38cab12e {
  align-items: center;
}
.pay-type-popup .items-baseline.data-v-38cab12e {
  align-items: baseline;
}
.pay-type-popup .items-stretch.data-v-38cab12e {
  align-items: stretch;
}
.pay-type-popup .self-start.data-v-38cab12e {
  align-self: flex-start;
}
.pay-type-popup .self-end.data-v-38cab12e {
  align-self: flex-end;
}
.pay-type-popup .self-center.data-v-38cab12e {
  align-self: center;
}
.pay-type-popup .self-stretch.data-v-38cab12e {
  align-self: stretch;
}
.pay-type-popup .pop-content.data-v-38cab12e {
  min-height: 140rpx;
  padding: 0 20rpx;
}
.pay-type-popup .pop-content .pay-item.data-v-38cab12e {
  padding: 30rpx;
  font-size: 30rpx;
  background: #fff;
  border: 1rpx solid #3f51b5;
  border-radius: 8rpx;
  color: #888;
  margin-bottom: 12rpx;
  text-align: center;
}
.pay-type-popup .pop-content .pay-item .item-left_icon.data-v-38cab12e {
  margin-right: 20rpx;
  font-size: 48rpx;
}
.pay-type-popup .pop-content .pay-item .item-left_icon.wechat.data-v-38cab12e {
  color: #00c800;
}
.pay-type-popup .pop-content .pay-item .item-left_icon.balance.data-v-38cab12e {
  color: #3f51b5;
}
.flow-mode.data-v-38cab12e {
  margin-top: 0;
  padding: 48rpx 28rpx 60rpx;
}
.flow-mode .store-name.data-v-38cab12e {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  letter-spacing: 3rpx;
}
.flow-delivery .flow-delivery__detail.data-v-38cab12e {
  display: flex;
  align-items: center;
}
.flow-delivery .detail-location.data-v-38cab12e {
  font-size: 36rpx;
  color: #333;
  margin-right: 20rpx;
}
.flow-delivery .detail-content.data-v-38cab12e {
  flex: 1;
}
.flow-delivery .detail-content .detail-content__title.data-v-38cab12e {
  margin-bottom: 12rpx;
  display: flex;
  align-items: center;
}
.flow-delivery .detail-content .detail-content__title .detail-content__title-phone.data-v-38cab12e {
  margin-left: 20rpx;
  color: #666;
  font-size: 28rpx;
}
.flow-delivery .detail-content .detail-content__describe.data-v-38cab12e {
  font-size: 28rpx;
  color: #b7b7b7;
  line-height: 1.5;
}
.flow-delivery .detail-content .address-info .icon.data-v-38cab12e {
  float: right;
  font-weight: bold;
  color: #333;
}
.flow-delivery .detail-content .select-address.data-v-38cab12e {
  height: 90rpx;
  line-height: 90rpx;
}
.flow-delivery .detail-content .select-address .icon.data-v-38cab12e {
  margin-left: 15rpx;
  color: #666;
}
.flow-delivery .detail-content .store .store-name.data-v-38cab12e {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 8rpx;
}
.flow-delivery .detail-content .store .store-phone.data-v-38cab12e {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 8rpx;
}
.flow-delivery .detail-content .store .store-address.data-v-38cab12e {
  font-size: 26rpx;
  color: #b7b7b7;
  line-height: 1.4;
}
.flow-delivery .detail-arrow.data-v-38cab12e {
  font-size: 28rpx;
  color: #666;
}
.section_3.data-v-38cab12e {
  background-color: #f2f2f2;
  border-radius: 50rpx;
  width: 162rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  position: relative;
}
.section_3 .text-wrapper_2.data-v-38cab12e {
  background-color: #232e5d;
  border-radius: 50rpx;
  width: 80rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  transition: left 0.3s ease;
}
.section_3 .text-wrapper_2.active-left.data-v-38cab12e {
  left: 0;
}
.section_3 .text-wrapper_2.active-right.data-v-38cab12e {
  left: 82rpx;
}
.section_3 .text-wrapper_2 .text_3.data-v-38cab12e {
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 500;
}
.section_3 .text_4.data-v-38cab12e {
  color: #8c8d8f;
  font-size: 24rpx;
  margin-left: 16rpx;
}
.flow-all-money .detail-title.data-v-38cab12e {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.flow-all-money .flow-all-list.data-v-38cab12e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
  font-size: 24rpx;
}
.flow-all-money .flow-all-list.data-v-38cab12e:last-child {
  border-bottom: none;
}
.flow-all-money .flow-all-list .flex-five.data-v-38cab12e {
  color: #333;
}
.flow-all-money .flow-all-list .col-m.data-v-38cab12e {
  color: #e12526;
  font-weight: 500;
}
.flow-all-money .flow-all-list .col-gray.data-v-38cab12e {
  color: #999;
}
.flow-all-money .ipt-wrapper textarea.data-v-38cab12e {
  font-size: 28rpx;
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #f4f4f4;
  border-radius: 8rpx;
  height: 120rpx;
  background: #fafafa;
  color: #333;
}
.flow-all-money .ipt-wrapper textarea.data-v-38cab12e::-webkit-input-placeholder {
  color: #999;
}
.flow-all-money .ipt-wrapper textarea.data-v-38cab12e::placeholder {
  color: #999;
}
.checkout_list.data-v-38cab12e {
  padding: 30rpx 40rpx;
}
.checkout_list .flow-shopList.data-v-38cab12e {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}
.checkout_list .flow-shopList.data-v-38cab12e:last-child {
  border-bottom: none;
}
.checkout_list .flow-shopList.data-v-38cab12e:first-child {
  padding-top: 0;
}
.goods-detail-header.data-v-38cab12e {
  padding: 30rpx 40rpx 20rpx;
}
.goods-detail-header .section-title.data-v-38cab12e {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.goods-detail-header .section-subtitle.data-v-38cab12e {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.flow-header-left.data-v-38cab12e {
  padding-left: 90rpx;
}
/* 商品列表项样式 - 基于模板 */
.flow-shopList.data-v-38cab12e {
  display: flex;
  align-items: flex-start;
}
.flow-shopList .flow-list-left.data-v-38cab12e {
  margin-right: 34rpx;
}
.flow-shopList .flow-list-left image.data-v-38cab12e {
  width: 126rpx;
  height: 124rpx;
  border-radius: 8rpx;
  border: none;
  background: #fff;
}
.flow-shopList .flow-list-right.data-v-38cab12e {
  flex: 1;
  padding-top: 4rpx;
}
.flow-shopList .flow-list-right .goods-name.data-v-38cab12e {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 10rpx;
}
.flow-shopList .flow-list-right .goods-props.data-v-38cab12e {
  margin-bottom: 12rpx;
}
.flow-shopList .flow-list-right .goods-props .goods-props-item.data-v-38cab12e {
  font-size: 26rpx;
  color: #666;
  line-height: 1.3;
}
.flow-shopList .flow-list-right .goods-props .goods-props-item .group-name.data-v-38cab12e {
  color: #333;
}
.flow-shopList .flow-list-right .flow-list-cont.data-v-38cab12e {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flow-shopList .flow-list-right .flow-list-cont .small.data-v-38cab12e {
  font-size: 26rpx;
  color: #666;
}
.flow-shopList .flow-list-right .flow-list-cont .flow-cont.data-v-38cab12e {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.flow-shopList .flow-list-right .flow-list-cont .flow-cont.price-delete.data-v-38cab12e {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}
/* 优惠券选择 */
.popup__coupon.data-v-38cab12e {
  width: 750rpx;
  background: #fff;
  box-sizing: border-box;
  padding: 30rpx;
}
.popup__coupon .coupon__do_not .control.data-v-38cab12e {
  width: 90%;
  height: 82rpx;
  margin-top: 30rpx;
  margin-bottom: 1rpx;
  color: #fff;
  background: #3f51b5;
  border: 1rpx solid #3f51b5;
  border-radius: 8rpx;
}
.popup__coupon .coupon__title.data-v-38cab12e {
  text-align: center;
  margin-bottom: 30rpx;
}
.popup__coupon .coupon-item.data-v-38cab12e {
  position: relative;
  overflow: hidden;
  margin-bottom: 22rpx;
}
.popup__coupon .item-wrapper.data-v-38cab12e {
  width: 100%;
  display: flex;
  background: #fff;
  border-radius: 8rpx;
  color: #fff;
  height: 180rpx;
}
.popup__coupon .item-wrapper .coupon-type.data-v-38cab12e {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 10;
  width: 128rpx;
  padding: 6rpx 0;
  background: #fa2209;
  font-size: 20rpx;
  text-align: center;
  color: #ffffff;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
  -webkit-transform-origin: 64rpx 64rpx;
          transform-origin: 64rpx 64rpx;
}
.popup__coupon .item-wrapper.color-default.data-v-38cab12e {
  background: linear-gradient(-128deg, #6bf6f6, #008a8a);
}
.popup__coupon .item-wrapper.color-gray.data-v-38cab12e {
  background: linear-gradient(-113deg, #bdbdbd, #a2a1a2);
}
.popup__coupon .item-wrapper.color-gray .coupon-type.data-v-38cab12e {
  background: #9e9e9e;
}
.popup__coupon .item-wrapper .content.data-v-38cab12e {
  flex: 1;
  padding: 30rpx 20rpx;
  border-radius: 16rpx 0 0 16rpx;
}
.popup__coupon .item-wrapper .content .title.data-v-38cab12e {
  font-size: 32rpx;
}
.popup__coupon .item-wrapper .content .bottom .time.data-v-38cab12e {
  font-size: 24rpx;
}
.popup__coupon .item-wrapper .content .bottom .receive.data-v-38cab12e {
  height: 46rpx;
  width: 122rpx;
  line-height: 46rpx;
  text-align: center;
  border: 1rpx solid #fff;
  border-radius: 6rpx;
  color: #fff;
  font-size: 24rpx;
}
.popup__coupon .item-wrapper .content .bottom .receive.state.data-v-38cab12e {
  border: none;
}
.popup__coupon .item-wrapper .tip.data-v-38cab12e {
  position: relative;
  flex: 0 0 32%;
  text-align: center;
  border-radius: 0 16rpx 16rpx 0;
}
.popup__coupon .item-wrapper .tip .money.data-v-38cab12e {
  font-weight: bold;
  font-size: 52rpx;
}
.popup__coupon .item-wrapper .tip .pay-line.data-v-38cab12e {
  font-size: 22rpx;
}
.popup__coupon .item-wrapper .split-line.data-v-38cab12e {
  position: relative;
  flex: 0 0 0;
  border-left: 4rpx solid #fff;
  margin: 0 10rpx 0 6rpx;
  background: #fff;
}
.popup__coupon .item-wrapper .split-line.data-v-38cab12e:before {
  border-radius: 0 0 16rpx 16rpx;
  top: 0;
}
.popup__coupon .item-wrapper .split-line.data-v-38cab12e:after {
  border-radius: 16rpx 16rpx 0 0;
  bottom: 0;
}
.popup__coupon .item-wrapper .split-line.data-v-38cab12e:before, .popup__coupon .item-wrapper .split-line.data-v-38cab12e:after {
  content: "";
  position: absolute;
  width: 24rpx;
  height: 12rpx;
  background: #f7f7f7;
  left: -14rpx;
  z-index: 1;
}
/* 积分抵扣 */
.points .title.data-v-38cab12e {
  margin-right: 5rpx;
}
.points .icon-help.data-v-38cab12e {
  font-size: 28rpx;
}
.points .points-money.data-v-38cab12e {
  margin-right: 20rpx;
}
.points-content.data-v-38cab12e {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 50rpx;
  text-align: left;
  color: #606266;
  height: 620rpx;
  box-sizing: border-box;
}
/* 支付方式 - 基于模板样式 */
.pay-method .flow-all-list.data-v-38cab12e {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  padding: 0 0 20rpx 0;
  border-bottom: none;
}
.pay-method-list .pay-item.data-v-38cab12e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  margin-bottom: 12rpx;
  background: #fff;
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}
.pay-method-list .pay-item.data-v-38cab12e:last-child {
  margin-bottom: 0;
}
.pay-method-list .pay-item .item-left.data-v-38cab12e {
  display: flex;
  align-items: center;
}
.pay-method-list .pay-item .item-left .item-left_icon.data-v-38cab12e {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.pay-method-list .pay-item .item-left .item-left_icon.wechat.data-v-38cab12e {
  color: #00c800;
}
.pay-method-list .pay-item .item-left .item-left_icon.balance.data-v-38cab12e {
  color: #ff9700;
}
.pay-method-list .pay-item .item-left .item-left_text.data-v-38cab12e {
  font-size: 28rpx;
  color: #333;
}
.pay-method-list .pay-item .item-left .item-left_text .balance-amount.data-v-38cab12e {
  font-size: 24rpx;
  color: #666;
  margin-left: 8rpx;
}
.pay-method-list .pay-item .item-right.data-v-38cab12e {
  color: #e12526;
  font-size: 24rpx;
}
.goods-props.data-v-38cab12e {
  padding-top: 10rpx;
  font-size: 24rpx;
  color: #999;
}
.goods-props .goods-props-item.data-v-38cab12e {
  display: inline-block;
}
.goods-props .goods-props-item .group-name.data-v-38cab12e {
  margin-right: 6rpx;
}
.right-arrow.data-v-38cab12e {
  margin-left: 16rpx;
  font-size: 26rpx;
}
.flow-fixed-footer.data-v-38cab12e {
  position: fixed;
  bottom: 0px;
  width: 100%;
  background: #fff;
  padding: 20rpx 28rpx 40rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 11;
}
.flow-fixed-footer .chackout-box.data-v-38cab12e {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flow-fixed-footer .chackout-left.data-v-38cab12e {
  flex: 1;
}
.flow-fixed-footer .chackout-left .col-amount-do.data-v-38cab12e {
  font-size: 28rpx;
  color: #333;
  text-align: right;
  padding-right: 20rpx;
}
.flow-fixed-footer .chackout-left .col-amount-do .pay-amount.data-v-38cab12e {
  color: #e12526;
  font-size: 36rpx;
  font-weight: bold;
  margin-left: 8rpx;
}
.flow-fixed-footer .chackout-left .col-amount-do .delivery-fee.data-v-38cab12e {
  margin-top: 8rpx;
  font-size: 24rpx;
  color: #666;
}
.flow-fixed-footer .chackout-right.data-v-38cab12e {
  flex: 0 0 auto;
}
.flow-fixed-footer .flow-btn.data-v-38cab12e {
  background: #333333;
  color: #ffffff;
  text-align: center;
  height: 76rpx;
  line-height: 76rpx;
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 38rpx;
  padding: 0 40rpx;
  letter-spacing: 10rpx;
  min-width: 348rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}
.flow-fixed-footer .flow-btn.disabled.data-v-38cab12e {
  background: #cccccc;
  box-shadow: none;
}
/* 商品统计框 - 基于模板样式 */
.flow-num-box.data-v-38cab12e {
  font-size: 24rpx;
  color: #333;
  padding: 40rpx 0 0;
  text-align: right;
  border-top: 1rpx solid rgba(153, 153, 153, 0.6);
}
.flow-num-box .flow-money.data-v-38cab12e {
  font-size: 36rpx;
  font-weight: bold;
  margin-left: 10rpx;
}
/* app.scss */
.flow-shopList.data-v-38cab12e {
  padding: 18rpx 0;
}
.flow-shopList .flow-list-left.data-v-38cab12e {
  margin-right: 20rpx;
}
.flow-shopList .flow-list-left image.data-v-38cab12e {
  width: 120rpx;
  height: 100rpx;
  border: 1rpx solid #eee;
  border-radius: 5rpx;
  background: #fff;
}
.flow-shopList .flow-list-right .flow-cont.data-v-38cab12e {
  font-size: 28rpx;
  color: #fa2209;
}
.flow-shopList .flow-list-right .small.data-v-38cab12e {
  font-size: 26rpx;
  color: #777;
}
.flow-shopList .flow-list-right .flow-list-cont.data-v-38cab12e {
  padding-top: 10rpx;
}
.flow-all-money.data-v-38cab12e {
  padding: 0 24rpx;
  color: #444;
}
.flow-all-money .flow-all-list.data-v-38cab12e {
  font-size: 28rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f8f8f8;
}
.flow-all-money .flow-all-list.data-v-38cab12e:last-child {
  border-bottom: none;
}
.flow-all-money .flow-all-list-cont.data-v-38cab12e {
  font-size: 28rpx;
  padding: 10rpx 0;
}
.flow-all-money .flow-arrow.data-v-38cab12e {
  justify-content: flex-end;
  align-items: center;
}
.reservation-time.data-v-38cab12e {
  color: #ff6600;
  font-weight: bold;
  font-size: 28rpx;
}
.reservation-popup.data-v-38cab12e {
  padding: 40rpx 30rpx 60rpx 30rpx;
  background-color: #ffffff;
}
.reservation-popup .popup-header.data-v-38cab12e {
  text-align: center;
  margin-bottom: 40rpx;
}
.reservation-popup .popup-header .popup-title.data-v-38cab12e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.reservation-popup .popup-header .popup-subtitle.data-v-38cab12e {
  font-size: 26rpx;
  color: #666;
  display: block;
}
.reservation-popup .datetime-picker.data-v-38cab12e {
  margin-bottom: 40rpx;
}
.reservation-popup .datetime-picker .picker-view.data-v-38cab12e {
  height: 400rpx;
}
.reservation-popup .datetime-picker .picker-view .picker-item.data-v-38cab12e {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 80rpx;
  font-size: 28rpx;
  color: #333;
}
.reservation-popup .popup-buttons.data-v-38cab12e {
  display: flex;
  justify-content: space-between;
}
.reservation-popup .popup-buttons .btn-cancel.data-v-38cab12e,
.reservation-popup .popup-buttons .btn-confirm.data-v-38cab12e {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 40rpx;
  margin: 0 20rpx;
}
.reservation-popup .popup-buttons .btn-cancel.data-v-38cab12e {
  background-color: #f5f5f5;
  color: #666;
}
.reservation-popup .popup-buttons .btn-confirm.data-v-38cab12e {
  background-color: #3f51b5;
  color: #ffffff;
}
