{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/share/index.vue?6ac8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/share/index.vue?611c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/share/index.vue?cd66", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/share/index.vue?d34f", "uni-app:///pages/share/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/share/index.vue?6ef3", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/share/index.vue?895f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MescrollBody", "mixins", "data", "url", "shareShow", "showPoster", "list", "upOption", "auto", "page", "size", "noMoreSize", "methods", "upCallback", "app", "then", "catch", "getShareList", "ShareApi", "load", "resolve", "shareNow", "doShare", "path", "query", "copyContent", "uni", "success", "title", "icon", "fail", "onShareAppMessage", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACwE9pB;AACA;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;MACA;IACA;EACA;EAEAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAC,2BACAC;QACA;QACA;QACAD;MACA,GACAE;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;UAAAT;QAAA;UAAAU;QAAA,GACAJ;UACA;UACA;UACAD;UACAA;UACAM;QACA,GACAJ;UAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;IACAK;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACAR;MACA;MAEAI;QACAK;QACAC;MACA;QACA;UACAC;QACA;MACA;MAEA;QACAC;UACAxB;UACAyB;YACAD;cACAE;cACAC;YACA;UACA;UACAC;YACAJ;cACAE;cACAC;YACA;UACA;QACA;MACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAE;MACA;MACA;QACAH;QACAL;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAS;MACA;MACA;MACA;QACAJ;QACAL;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/share/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/share/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=d1630c8e&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=d1630c8e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d1630c8e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/share/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=d1630c8e&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list.content, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var f0 = _vm._f(\"timeFormat\")(item.createTime, \"yyyy-mm-dd hh:MM\")\n    return {\n      $orig: $orig,\n      f0: f0,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.shareShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ use: false }\" :up=\"upOption\" @up=\"upCallback\">\r\n     <view class=\"poster\">\n        <view class=\"tips\">邀请好友即获得奖励！</view>\r\n          <view class=\"iconfont icon-gift\"></view>\r\n          <view class=\"action-btn\">\r\n              <view class=\"btn-wrapper\">\r\n                <view class=\"btn-item\" @click=\"shareNow\">立即邀请</view>\r\n              </view>\r\n          </view>\r\n        </view>\r\n     </view>\r\n     <!-- 分享对话框 start -->\r\n     <u-popup v-model=\"shareShow\" mode=\"bottom\" border-radius=\"14\" height=\"453\">\r\n         <view class=\"share-popup\">\r\n             <text class=\"share-title\">邀请确认</text>\r\n             <view class=\"row\">\r\n                 <view class=\"col-6\" @click=\"doShare('share')\">\r\n                     <button class=\"mt-1\" open-type=\"share\">\r\n                        <view class=\"iconfont icon-fenxiang-post\"></view>\r\n                        <text class=\"txt\">立即邀请</text>\r\n                     </button>\r\n                 </view>\r\n                <!-- <view class=\"col-6\" @click=\"doShare('copy')\">\r\n                     <button class=\"mt-1\">\r\n                        <view class=\"iconfont icon-copy\"></view>\r\n                        <text class=\"mt-1\">复制链接</text>\r\n                     </button>\r\n                 </view> -->\r\n             </view>\r\n             <view class=\"share-btn\" @click=\"shareShow = false\">\r\n                <view class=\"btn-wrapper\">\r\n                  <view class=\"btn-item\" @click=\"shareNow\">取消邀请</view>\r\n                </view>\r\n             </view>\r\n         </view>\r\n     </u-popup>\r\n     <!-- 分享对话框 end -->\r\n     \r\n     <!-- 生成海报对话框 -->\r\n     <poster-img :img-show.sync=\"showPoster\" v-if=\"showPoster\"></poster-img>\r\n     \n     <view class=\"title-wrapper\">\n       <view class=\"title\">邀请记录</view>\n     </view>\n\n     <!-- 邀请列表 start-->\n     <view class=\"share-list\">\n      <view class=\"share-item show-type\" v-for=\"(item, index) in list.content\" :key=\"index\" @click=\"onTargetDetail(item.id)\">\n        <block>\n          <view class=\"share-item-left flex-box\">\r\n            <view class=\"share-item-image\">\r\n                <image class=\"image\" :src=\"item.subUserInfo.avatar\"></image>\r\n            </view>\n            <view class=\"share-item-title twolist-hidden\">\n              <view class=\"name\">{{ item.subUserInfo.name }}</view>\r\n              <view class=\"no\">会员号：{{ item.subUserInfo.userNo }}</view>\n            </view>\n            <view class=\"share-item-footer m-top10\">\n              <text class=\"share-views f-24 col-8\">邀请时间：{{ item.createTime | timeFormat('yyyy-mm-dd hh:MM') }}</text>\n            </view>\n          </view>\n        </block>\n      </view>\n     </view>\r\n     <!-- 邀请列表 end -->\n  </mescroll-body>\r\n  \n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import * as ShareApi from '@/api/share'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\r\n  import config from '@/config'\n\n  const pageSize = 15\n\n  export default {\n    components: {\n      MescrollBody\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\r\n        // h5\r\n        url: '',\r\n        // 立即分享\r\n        shareShow: false,\r\n        // 生成海报\r\n        showPoster: false,\n        // 邀请列表\n        list: getEmptyPaginateObj(),\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于3条才显示无更多数据\n          noMoreSize: 3,\n        }\n      }\n    },\n\n    methods: {\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this;\n        // 设置列表数据\n        app.getShareList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length;\n            const totalSize = list.content.totalElements;\n            app.mescroll.endBySize(curPageLen, totalSize);\n          })\n          .catch(() => app.mescroll.endErr());\n      },\n\n      /**\n       * 获取邀请列表\n       * @param {Number} pageNo 页码\n       */\n      getShareList(pageNo = 1) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          ShareApi.list({  page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data.paginationResponse;\r\n              app.url = result.data.url;\n              app.list.content = getMoreListData(newList, app.list, pageNo);\n              resolve(newList);\n            })\n            .catch(result => reject());\n        })\n      },\r\n      \r\n      /**\r\n       * 立即邀请\r\n       */\r\n      shareNow() {\r\n         this.shareShow = true;\r\n      },\r\n      \r\n      /**\r\n       * 确认分享\r\n       */\r\n      doShare(action) {\r\n        const app = this;  \r\n        app.shareShow = false;\r\n        let copyContent = app.url + \"#?\" + app.$getShareUrlParams();\r\n        // #ifdef MP-WEIXIN\r\n        ShareApi.getMiniAppLink({\r\n            path: 'pages/index/index',\r\n            query: app.$getShareUrlParams()\r\n        }).then(res => {\r\n            if (res && res.link) {\r\n                copyContent = res.link;   \r\n            }\r\n        })\r\n        // #endif\r\n        if (action == 'copy') {\r\n            uni.setClipboardData({\r\n                data: copyContent,\r\n                success: function() {\r\n                    uni.showToast({\r\n                        title: \"复制成功\",\r\n                        icon: 'none'\r\n                    });\r\n                },\r\n                fail: () => {\r\n                    uni.showToast({\r\n                        title: \"复制失败\",\r\n                        icon: 'none'\r\n                    });\r\n                }\r\n            })\r\n        } else {\r\n            return false;\r\n        }\r\n      },\r\n      /**\r\n       * 分享当前页面\r\n       */\r\n      onShareAppMessage() {\r\n        const app = this\r\n        return {\r\n           title: config.name,\r\n           path: \"/pages/index/index?\" + app.$getShareUrlParams()\r\n        }\r\n      },\r\n      \r\n      /**\r\n       * 分享到朋友圈\r\n       * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)\r\n       * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html\r\n       */\r\n      onShareTimeline() {\r\n        const app = this\r\n        const { page } = app\r\n        return {\r\n          title: config.name,\r\n          path: \"/pages/index/index?\" + app.$getShareUrlParams()\r\n        }\r\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  /* 顶部选项卡 */\n  .container {\n    min-height: 100vh;\n  }\r\n  .poster {\r\n      padding: 20rpx;\r\n      text-align: center;\r\n      .iconfont {\r\n          text-align: center;\r\n          font-size: 120rpx;\r\n          margin: 50rpx;\r\n          color: $fuint-theme;\r\n      }\r\n      .action-btn {\r\n         text-align: center;\r\n         margin-top: 10rpx;\r\n        .btn-wrapper {\r\n          height: 100%;\r\n          display: block;\r\n          align-items: center;\r\n          width: 70%;\r\n          margin: 0 auto;\r\n          .btn-item {\r\n            font-size: 28rpx;\r\n            height: 80rpx;\r\n            line-height: 80rpx;\r\n            text-align: center;\r\n            color: #fff;\r\n            border-radius: 80rpx;\r\n            background: linear-gradient(to right, #f9211c, #ff6335);\r\n          }\r\n        }\r\n      }\r\n  }\r\n  \r\n  .share-popup {\r\n      padding: 20rpx;\r\n      font-size: 30rpx;\r\n      text-align: center;\r\n      .share-title {\r\n          margin-top: 40rpx;\r\n          font-size: 35rpx;\r\n          font-weight: bold;\r\n      }\r\n      .row {\r\n          margin-top: 30rpx;\r\n          padding-top: 80rpx;\r\n          clear: both;\r\n          height: 240rpx;\r\n          .col-6 {\r\n              width: 50%;\r\n              float: left;\r\n              .mt-1 {\r\n                 line-height: 60rpx;\r\n                 font-size: 24rpx;\r\n                 background: none;\r\n              }\r\n              .iconfont {\r\n                  color: $fuint-theme;\r\n                  font-size: 56rpx;\r\n              }\r\n          }\r\n      }\r\n      .share-btn {\r\n         text-align: center;\r\n        .btn-wrapper {\r\n          height: 100%;\r\n          display: block;\r\n          align-items: center;\r\n          width: 80%;\r\n          margin: 0 auto;\r\n          border: solid 2rpx #ccc;\r\n          border-radius: 80rpx;\r\n          .btn-item {\r\n            font-size: 28rpx;\r\n            height: 80rpx;\r\n            line-height: 80rpx;\r\n            text-align: center;\r\n            color: #333;\r\n          }\r\n        }\r\n      }\r\n  }\n\n  .title-wrapper {\n    display: flex;\n    width: 100%;\n    height: 88rpx;\r\n    line-height: 88rpx;\n    color: #333;\r\n    padding-left: 20rpx;\n    font-size: 30rpx;\r\n    font-weight: bold;\n    background: #fff;\r\n    margin-top: 50rpx;\n    border-bottom: 1rpx solid #e4e4e4;\n    z-index: 100;\n    overflow: hidden;\n    white-space: nowrap;\n  }\n\n  /* 分享列表 */\n  .share-list {\n    padding-top: 10rpx;\n    line-height: 1;\n    background: #f7f7f7;\n  }\n\n  .share-item {\n    margin-bottom: 10rpx;\n    padding: 20rpx;\n    background: #fff;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .share-item-title {\n      max-height: 80rpx;\n      font-size: 24rpx;\n      color: #333;\r\n      .name {\r\n          font-weight: bold;\r\n      }\r\n      .no {\r\n          margin-top: 20rpx;\r\n          font-size: 20rpx;\r\n          color: #888888;\r\n      }\n    }\n\n    .share-item-image .image {\n      display: block;\r\n      border-radius: 100rpx;\r\n      height: 100rpx;\r\n      width: 100rpx;\r\n      border: 2rpx solid #cccccc;\r\n      float: left;\r\n      background: #ccc;\n    }\r\n    .share-item-footer {\r\n      margin-top: 0rpx;  \r\n      clear: both;\r\n      text-align: right;\r\n    }\n  }\n\n  .show-type {\n    display: flex;\n    .share-item-left {\n      padding-right: 20rpx;\r\n      width: 10rpx;\n    }\n    .share-item-title {\n      min-height: 100rpx;\r\n      float: left;\r\n      margin-left: 10rpx;\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=d1630c8e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=d1630c8e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420804\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}