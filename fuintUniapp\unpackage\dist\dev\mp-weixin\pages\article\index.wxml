<mescroll-body vue-id="287468b6-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-2a986250 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="tabs-wrapper data-v-2a986250"><scroll-view class="scroll-view data-v-2a986250" scroll-x="{{true}}"><view data-event-opts="{{[['tap',[['onSwitchTab',[0]]]]]}}" class="{{['tab-item','data-v-2a986250',(curId==0)?'active':'']}}" bindtap="__e"><view class="value data-v-2a986250"><text class="data-v-2a986250">全部文章</text></view></view><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onSwitchTab',['$0'],[[['categoryList','',index,'id']]]]]]]}}" class="{{['tab-item','data-v-2a986250',(curId==item.categoryId)?'active':'']}}" bindtap="__e"><view class="value data-v-2a986250"><text class="data-v-2a986250">{{item.name}}</text></view></view></block></scroll-view></view><view class="article-list data-v-2a986250"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="article-item show-type data-v-2a986250" bindtap="__e"><block class="data-v-2a986250"><view class="article-item-left flex-box data-v-2a986250"><view class="article-item-title twolist-hidden data-v-2a986250"><text class="data-v-2a986250">{{item.title}}</text></view><view class="article-item-footer m-top10 data-v-2a986250"><text class="article-views f-24 col-8 data-v-2a986250">{{item.click+"次浏览"}}</text></view></view><view class="article-item-image data-v-2a986250"><image class="image data-v-2a986250" src="{{item.image}}"></image></view></block></view></block></view></mescroll-body>