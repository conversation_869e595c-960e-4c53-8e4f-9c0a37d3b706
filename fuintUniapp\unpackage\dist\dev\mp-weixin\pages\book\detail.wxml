<view class="content data-v-52bf62ce"><view hidden="{{!(storeInfo)}}" class="top-v data-v-52bf62ce"><text class="storeName data-v-52bf62ce">{{"预约【"+storeInfo.name+"】"}}</text><text data-event-opts="{{[['tap',[['toMoreStore',['$event']]]]]}}" class="moreStore data-v-52bf62ce" bindtap="__e">切换门店</text></view><view class="info-v data-v-52bf62ce"><view class="title data-v-52bf62ce">｜请选择预约日期</view><block wx:if="{{$root.g0}}"><view class="list-v data-v-52bf62ce"><block wx:for="{{dateArr}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['dateClick',[index]]]]]}}" class="{{['data-v-52bf62ce',dateIndex==index?'activeItem':'item-v']}}" bindtap="__e"><view class="data-v-52bf62ce">{{item.week}}</view><view class="data-v-52bf62ce">{{item.date}}</view></view></block></view></block><block wx:if="{{!$root.g1}}"><none vue-id="052d77f8-1" isLoading="{{false}}" custom-style="{{({padding:'30px 10px'})}}" tips="暂无可预约日期" class="data-v-52bf62ce" bind:__l="__l"></none></block></view><view class="info-v data-v-52bf62ce"><view class="title data-v-52bf62ce">｜请选择预约时段</view><block wx:if="{{$root.g2}}"><view class="list-v data-v-52bf62ce"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['timeClick',[index]]]]]}}" class="{{['data-v-52bf62ce',timeIndex==index?'activeItem':item.g3>=0?'item-v':'disable']}}" bindtap="__e"><view class="data-v-52bf62ce">{{item.$orig.time}}</view></view></block></view></block><block wx:if="{{!$root.g4}}"><none vue-id="052d77f8-2" isLoading="{{false}}" custom-style="{{({padding:'30rpx 10rpx'})}}" tips="暂无可预约时段" class="data-v-52bf62ce" bind:__l="__l"></none></block></view><view data-event-opts="{{[['tap',[['doSubmit',['$event']]]]]}}" class="btn data-v-52bf62ce" bindtap="__e">确定预约</view></view>