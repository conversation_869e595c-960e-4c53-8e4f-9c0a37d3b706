{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/member/index.vue?93d0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/member/index.vue?4297", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/member/index.vue?eee1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/member/index.vue?7637", "uni-app:///pages/merchant/member/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/member/index.vue?89fb", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/member/index.vue?f208"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MescrollBody", "mixins", "data", "categoryList", "name", "id", "memberList", "curId", "upOption", "auto", "page", "size", "noMoreSize", "onLoad", "app", "methods", "upCallback", "then", "catch", "getMemberList", "MemberApi", "dataType", "load", "resolve", "onSwitchTab", "onTargetDetail", "memberId", "onRefreshList", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC6C7qB;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;QAAAC;QAAAC;MAAA;QAAAD;QAAAC;MAAA;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;MACAC;IACA;EACA;EAEAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAF,4BACAG;QACA;QACA;QACAH;MACA,GACAI;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;UAAAC;UAAAX;QAAA;UAAAY;QAAA,GACAL;UACA;UACA;UACAH;UACAS;QACA,GACAL;UAAA;QAAA;MACA;IACA;IAEA;IACAM;MAAA;MACA;MACA;MACAV;MACA;MACAA;IACA;IAEA;IACAW;MACA;QAAAC;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACAC;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/merchant/member/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/merchant/member/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6043f55b&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6043f55b&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6043f55b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/merchant/member/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6043f55b&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.memberList.content, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.balance.toFixed(2)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ use: false }\" :up=\"upOption\" @up=\"upCallback\">\n\n    <!-- 分类列表tab -->\n    <view class=\"tabs-wrapper\">\n      <scroll-view class=\"scroll-view\" scroll-x>\n        <view class=\"tab-item\" :class=\"{ active: curId ==  0 }\" @click=\"onSwitchTab(0)\">\n          <view class=\"value\"><text>全部</text></view>\n        </view>\n        <!-- 分类列表 -->\n        <view class=\"tab-item\" :class=\"{ active: curId ==  item.id }\" @click=\"onSwitchTab(item.id)\"\n          v-for=\"(item, index) in categoryList\" :key=\"index\">\n          <view class=\"value\"><text>{{ item.name }}</text></view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 会员列表 -->\n    <view class=\"member-list\">\n      <view class=\"member-item\" v-for=\"(item, index) in memberList.content\" :key=\"index\" @click=\"onTargetDetail(item.id)\">\n        <block>\n          <view class=\"left flex-box\">\r\n            <image class=\"image\" :src=\"item.avatar\"></image>\n          </view>\n          <view class=\"right\">\n            <view class=\"base\">\r\n              <text class=\"name\">{{ item.name }}</text>\r\n              <text class=\"grade\">{{ item.gradeName ? item.gradeName : '' }}</text>\r\n            </view>\r\n            <view class=\"amount\">\r\n              <view class=\"balance\">余额：￥{{ item.balance.toFixed(2) }}</view>\r\n              <view class=\"point\">积分：{{ item.point }}</view>\r\n            </view>\r\n            <view class=\"footer\">\r\n              <text class=\"member-views f-24 col-8\">{{ item.lastLoginTime }}活跃</text>\r\n            </view>\n          </view>\n        </block>\n      </view>\n    </view>\n  </mescroll-body>\n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\r\n  import * as MemberApi from '@/api/merchant/member'\r\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n\n  const pageSize = 15\n\n  export default {\n    components: {\n      MescrollBody\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        // 分类列表\n        categoryList: [{ name : '今日活跃', id: 'todayActive' }, { name : '今日注册', id: 'todayRegister' }],\n        // 会员列表\n        memberList: getEmptyPaginateObj(),\n        // 当前选中的分类id (all则代表全部)\n        curId: 'all',\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认20\n          page: { size: pageSize },\n          // 数量要大于12条才显示无更多数据\n          noMoreSize: 12,\n        }\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      const app = this\n      if (options.dataType) {\n          app.curId = options.dataType\n      }\n    },\n\n    methods: {\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认20\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this\n        // 设置列表数据\n        app.getMemberList(page.num)\n          .then(list => {\r\n            const curPageLen = list.content.length;\r\n            const totalSize = list.totalElements;\n            app.mescroll.endBySize(curPageLen, totalSize);\n          })\n          .catch(() => app.mescroll.endErr())\n      },\n\n      /**\n       * 获取会员列表\n       * @param {Number} pageNo 页码\n       */\n      getMemberList(pageNo = 1) {\n        const app = this;\n        return new Promise((resolve, reject) => {\n          MemberApi.list({ dataType: app.curId, page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data.paginationResponse;\n              app.memberList.content = getMoreListData(newList, app.memberList, pageNo);\n              resolve(newList);\n            })\n            .catch(result => reject())\n        })\n      },\n\n      // 切换选择的分类\n      onSwitchTab(dataType = 'all') {\n        const app = this;\n        // 切换当前的分类ID\n        app.curId = dataType;\n        // 刷新列表数据\n        app.mescroll.resetUpScroll();\n      },\n\n      // 跳转会员详情页\n      onTargetDetail(memberId) {\n        this.$navTo('pages/merchant/member/detail', { memberId })\n      },\r\n      \r\n      // 刷新列表\r\n      onRefreshList() {\r\n        this.list = getEmptyPaginateObj()\r\n        setTimeout(() => {\r\n          this.mescroll.resetUpScroll()\r\n        }, 120)\r\n      },\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  /* 顶部选项卡 */\n  .container {\n    min-height: 100vh;\n  }\n  .tabs-wrapper {\n    position: sticky;\n    top: var(--window-top);\n\n    display: flex;\n    width: 100%;\n    height: 88rpx;\n    color: #333;\n    font-size: 28rpx;\n    background: #fff;\n    border-bottom: 1rpx solid #e4e4e4;\n    z-index: 100;\n    overflow: hidden;\n    white-space: nowrap;\n  }\n\n  .tab-item {\n    display: inline-block;\n    padding: 0 15rpx;\n    text-align: center;\n    min-width: 20%;\n    height: 87rpx;\n    line-height: 88rpx;\n    box-sizing: border-box;\n    .value {\n      height: 100%;\n    }\n    &.active .value {\n      color: #fd4a5f;\n      border-bottom: 4rpx solid #fd4a5f;\n    }\n  }\n\n  /* 会员列表 */\n  .member-list {\n    padding-top: 20rpx;\n    line-height: 1;\n    background: #f7f7f7;\n  }\r\n  \n  .member-item {\n    margin-bottom: 10rpx;\n    padding: 30rpx;\n    background: #fff;\r\n    border: #f5f5f5 solid 1rpx;\r\n    height: 188rpx;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n    .left {\r\n        width: 320rxp;\r\n        float: left;\r\n        .image {\r\n          display: block;\r\n          width: 120rpx;\r\n          height: 120rpx;\r\n          border-radius: 120rpx;\r\n          border: solid 1rpx #cccccc;\r\n        }\r\n    }\r\n    .right {\r\n        margin-left: 140rpx;\r\n        height: 180rpx;\r\n        .base {\n            .name {\r\n              font-weight: bold;\n              max-height: 80rpx;\n              font-size: 30rpx;\n              color: #333;\n            }\r\n            .grade {\r\n                margin-left: 20rpx;\r\n                float: right;\r\n            }\r\n        }\r\n        .amount {\r\n            margin-top: 10rpx;\r\n            .balance {\r\n               margin-top: 15rpx;\r\n            }\r\n            .point {\r\n               margin-top: 10rpx;\r\n            }\r\n        }\r\n        .footer {\r\n            margin-top: 20rpx;\r\n            .member-views {\r\n                float: right;\r\n            }\r\n        }\r\n    }\n  }\n\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6043f55b&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6043f55b&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420696\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}