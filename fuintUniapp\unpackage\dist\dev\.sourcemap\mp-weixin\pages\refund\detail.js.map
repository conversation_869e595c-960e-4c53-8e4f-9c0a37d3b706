{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/detail.vue?2dee", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/detail.vue?3117", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/detail.vue?fa0c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/detail.vue?090f", "uni-app:///pages/refund/detail.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/detail.vue?f98e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/detail.vue?f9a0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "RefundStatusEnum", "RefundTypeEnum", "isLoading", "refundId", "detail", "expressName", "expressNo", "disabled", "onLoad", "methods", "getPageData", "app", "Promise", "then", "getRefundDetail", "RefundApi", "resolve", "catch", "onGoodsDetail", "goodsId", "handlePreviewImages", "uni", "current", "urls", "onSubmit", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6J/pB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC;MACAC,qCACAC;QACAF;MACA;IACA;IAEA;IACAG;MACA;MACA;QACAC,+BACAF;UACAF;UACAK;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;QAAAC;MAAA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;MACAC;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACAb;MACA;MACAI,iEACAF;QACA;UACAF;UACAc;YACAd;YACAU;UACA;QACA;UACAV;UACAA;QACA;MACA,GACAM;QAAA;MAAA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChQA;AAAA;AAAA;AAAA;AAA8uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAlwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/refund/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/refund/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=70953b1f&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=70953b1f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"70953b1f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/refund/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=70953b1f&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.isLoading\n    ? _vm.detail.imageList && _vm.detail.imageList.length > 0\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"!isLoading\" class=\"container p-bottom\">\n\n    <!-- 顶部状态栏 -->\n    <view class=\"detail-header dis-flex flex-y-center\">\n      <view class=\"header-backdrop\">\n        <image class=\"image\" src=\"/static/order/refund-bg.png\"></image>\n      </view>\n      <view class=\"header-state\">\r\n        <text class=\"f-32 col-f\" v-if=\"detail.status == RefundStatusEnum.A.key\">{{RefundStatusEnum.A.name}}</text>\r\n        <text class=\"f-32 col-f\" v-if=\"detail.status == RefundStatusEnum.B.key\">{{RefundStatusEnum.B.name}}</text>\r\n        <text class=\"f-32 col-f\" v-if=\"detail.status == RefundStatusEnum.C.key\">{{RefundStatusEnum.C.name}}</text>\r\n        <text class=\"f-32 col-f\" v-if=\"detail.status == RefundStatusEnum.D.key\">{{RefundStatusEnum.D.name}}</text>\r\n        <text class=\"f-32 col-f\" v-if=\"detail.status == RefundStatusEnum.E.key\">{{RefundStatusEnum.E.name}}</text>\n      </view>\n    </view>\n\n    <!-- 商品详情 -->\n    <view v-for=\"(goods, index) in detail.orderInfo.goods\" :key=\"index\" class=\"detail-goods b-f m-top20 dis-flex flex-dir-row\" @click=\"onGoodsDetail(goods.goodsId)\">\n      <view class=\"left\">\n        <image class=\"goods-image\" :src=\"goods.image\"></image>\n      </view>\n      <view class=\"right dis-flex flex-box flex-dir-column flex-x-around\">\n        <view class=\"goods-name\">\n          <text class=\"twolist-hidden\">{{ goods.name }}</text>\n        </view>\n        <view class=\"dis-flex col-9 f-24\">\n          <view class=\"flex-box\">\n            <view class=\"goods-props clearfix\">\n              <view class=\"goods-props-item\" v-for=\"(props, idx) in goods.specList\" :key=\"idx\">\n                <text>{{ props.specName }}</text>\n              </view>\n            </view>\n          </view>\n          <text class=\"t-r\">×{{ goods.num }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 订单金额 -->\n    <view class=\"detail-order b-f row-block\">\n      <view class=\"item dis-flex flex-x-end flex-y-center\">\n        <text class=\"\">订单金额：</text>\n        <text class=\"col-m\">￥{{ detail.orderInfo.amount }}</text>\n      </view>\n    </view>\n\n    <!-- 已退款金额 -->\n    <view v-if=\"detail.status == 'B' && detail.type == 'A'\" class=\"detail-order b-f row-block dis-flex flex-x-end flex-y-center\">\n      <text class=\"\">已退款金额：</text>\n      <text class=\"col-m\">￥{{ detail.refundMoney }}</text>\n    </view>\n\n    <!-- 售后信息 -->\n    <view class=\"detail-refund b-f m-top20\">\n      <view class=\"detail-refund__row dis-flex\">\n        <view class=\"text\">\n          <text>售后类型：</text>\n        </view>\n        <view class=\"flex-box\">\r\n          <text v-if=\"detail.type == RefundTypeEnum.RETURN.value\">{{RefundTypeEnum.RETURN.name}}</text>\r\n          <text v-if=\"detail.type == RefundTypeEnum.EXCHANGE.value\">{{RefundTypeEnum.EXCHANGE.name}}</text>\n        </view>\n      </view>\n      <view class=\"detail-refund__row dis-flex\">\n        <view class=\"text\">\n          <text>申请原因：</text>\n        </view>\n        <view class=\"flex-box\">\n          <text>{{ detail.remark }}</text>\n        </view>\n      </view>\n      <view v-if=\"detail.imageList && detail.imageList.length > 0\" class=\"detail-refund__row dis-flex\">\n        <view class=\"text\">\n          <text>申请凭证：</text>\n        </view>\n        <view class=\"image-list flex-box\">\n          <view class=\"image-preview\" v-for=\"(item, index) in detail.imageList\" :key=\"index\">\n            <image class=\"image\" mode=\"aspectFill\" :src=\"item\" @click=\"handlePreviewImages(index)\"></image>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 售后信息 -->\n    <view v-if=\"detail.status == RefundStatusEnum.C.key\" class=\"detail-refund b-f m-top20\">\n      <view class=\"detail-refund__row dis-flex\">\n        <view class=\"text\">\n          <text class=\"col-m\">拒绝原因：</text>\n        </view>\n        <view class=\"flex-box\">\n          <text>{{ detail.remark }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 商家收货地址 -->\n    <view v-if=\"detail.status == RefundStatusEnum.B.key\" class=\"detail-address b-f m-top20\">\n      <view class=\"detail-address__row address-title\">\n        <text class=\"col-m\">退货地址</text>\n      </view>\n      <view class=\"detail-address__row address-details\">\n        <view class=\"address-details__row\">\n          <text>收货人名：{{ detail.address.name ? detail.address.name : '-'}}</text>\n        </view>\n        <view class=\"address-details__row\">\n          <text>联系电话：{{ detail.address.mobile ? detail.address.mobile : '-' }}</text>\n        </view>\n        <view class=\"address-details__row dis-flex\">\n          <view class=\"text\">\n            <text>详细地址：</text>\n          </view>\n          <view class=\"address flex-box\">\n            <text class=\"region\" v-for=\"(region, idx) in detail.address.region\" :key=\"idx\">{{ region }}</text>\n            <text class=\"detail\">{{ detail.address.detail ? detail.address.detail : '-'}}</text>\n          </view>\n        </view>\n      </view>\n      <view class=\"detail-address__row address-tips\">\n        <view class=\"f-26 col-9\">\n          <text>· 未与卖家协商一致情况下，请勿寄到付或平邮</text>\n        </view>\n        <view class=\"f-26 col-9\">\n          <text>· 请填写真实有效物流信息</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 填写物流信息 -->\n    <form v-if=\"detail.type == RefundTypeEnum.RETURN.value && detail.status == RefundStatusEnum.B.value && !detail.is_user_send\"\n      @submit=\"onSubmit()\">\n      <view class=\"detail-express b-f m-top20\">\n        <view class=\"form-group dis-flex flex-y-center\">\n          <view class=\"field\">物流公司：</view>\n          <view class=\"flex-box\">\n            <input class=\"input\" v-model=\"expressName\" placeholder=\"请填写物流公司名称\"></input>\n          </view>\n        </view>\n        <view class=\"form-group dis-flex flex-y-center\">\n          <view class=\"field\">物流单号：</view>\n          <view class=\"flex-box\">\n            <input class=\"input\" v-model=\"expressNo\" placeholder=\"请填写物流单号\"></input>\n          </view>\n        </view>\n      </view>\n      <!-- 操作按钮 -->\n      <view class=\"footer\">\n        <view class=\"btn-wrapper\">\n          <button class=\"btn-item btn-item-main btn-normal\" :class=\"{ disabled }\" formType=\"submit\">确认发货</button>\n        </view>\n      </view>\n    </form>\n\n  </view>\n</template>\n\n<script>\n  import { RefundStatusEnum, RefundTypeEnum } from '@/common/enum/order/refund'\n  import * as RefundApi from '@/api/refund'\n\n  export default {\n    data() {\n      return {\n        // 枚举类\n        RefundStatusEnum,\n        RefundTypeEnum,\n        // 正在加载\n        isLoading: true,\n        // 售后单ID\n        refundId: null,\n        // 售后单详情\n        detail: {},\n        // 物流公司\r\n        expressName: '',\r\n        // 物流单号\r\n        expressNo: '',\n        // 按钮禁用\n        disabled: false\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad({ refundId }) {\n      // 售后单ID\n      this.refundId = refundId\n      // 获取页面数据\n      this.getPageData()\n    },\n\n    methods: {\n\n      // 获取页面数据\n      getPageData() {\n        const app = this\n        app.isLoading = true\n        Promise.all([app.getRefundDetail()])\n          .then(result => {\n            app.isLoading = false\n          })\n      },\n\n      // 获取售后单详情\n      getRefundDetail() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          RefundApi.detail(app.refundId)\n            .then(result => {\n              app.detail = result.data;\n              resolve()\n            })\n            .catch(err => reject(err))\n        })\n      },\n\n      // 跳转商品详情页\n      onGoodsDetail(goodsId) {\n        this.$navTo('pages/goods/detail', { goodsId })\n      },\n\n      // 凭证图片预览\n      handlePreviewImages(index) {\n        const { detail: { images } } = this\n        const imageUrls = images.map(item => item.image_url)\n        uni.previewImage({\n          current: imageUrls[index],\n          urls: imageUrls\n        })\n      },\n\n      // 表单提交\n      onSubmit() {\n        const app = this\n        // 判断是否重复提交\n        if (app.disabled === true) return false\n        // 按钮禁用\n        app.disabled = true\n        // 提交到后端\n        RefundApi.delivery(app.refundId, app.expressName, app.expressNo)\n          .then(result => {\n              if (result.code == 200) {\r\n                  app.$toast(\"提交成功\");\r\n                  setTimeout(() => {\r\n                    app.disabled = false\r\n                    uni.navigateBack()\r\n                  }, 1500)\r\n              } else {\r\n                  app.$error(result.message);\r\n                  app.disabled = false;\r\n              }\n          })\n          .catch(err => app.disabled = false)\n      }\n\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  // 顶部状态栏\n  .detail-header {\n    position: relative;\n    width: 100%;\n    height: 140rpx;\n\n    .header-backdrop {\n      position: absolute;\n      top: 0;\n      left: 0;\n      z-index: 0;\n\n      .image {\n        display: block;\n        width: 750rpx;\n        height: 140rpx;\n      }\n    }\n  }\n\n  .header-state {\n     padding: 0rpx 130rpx;\n     font-size: 60rpx;\r\n     font-weight: bold;\n  }\n\n  /* 商品详情 */\n  .detail-goods {\n    padding: 24rpx 20rpx;\n\n    .left {\n      .goods-image {\n        display: block;\n        width: 150rpx;\n        height: 150rpx;\r\n        border-radius: 6rpx;\n      }\n    }\n\n    .right {\n      padding-left: 20rpx;\n    }\n\n    .goods-props {\n      margin-top: 14rpx;\n      // height: 40rpx;\n      color: #ababab;\n      font-size: 24rpx;\n      overflow: hidden;\n\n      .goods-props-item {\n        display: inline-block;\n        margin-right: 14rpx;\n        padding: 4rpx 16rpx;\n        border-radius: 12rpx;\n        background-color: #F5F5F5;\n        width: auto;\n      }\n    }\n  }\n\n  .detail-order {\n    padding: 15rpx 20rpx;\n    font-size: 26rpx;\n\n    .item {\n      margin-bottom: 10rpx;\n\n      &:last-child {\n        margin-bottom: 0;\n      }\n    }\n  }\n\n  /* 售后详情 */\n  .detail-refund {\n    padding: 15rpx 20rpx;\n  }\n\n  .detail-refund__row {\n    margin: 20rpx 0;\n  }\n\n  /* 申请凭证 */\n  .image-list {\n    margin-bottom: -15rpx;\n\n    .image-preview {\n      margin: 0 15rpx 15rpx 0;\n      float: left;\n\n      .image {\n        display: block;\n        width: 180rpx;\n        height: 180rpx;\n      }\n\n      &:nth-child(3n+0) {\n        margin-right: 0;\n      }\n    }\n  }\n\n  /* 商家收货地址 */\n  .detail-address {\n    padding: 20rpx 30rpx;\n  }\n\n  .address-details {\n    padding: 5rpx 0;\n    border-bottom: 1px solid #eee;\n\n    .address-details__row {\n      margin: 10rpx 0;\n    }\n  }\n\n  .address-tips {\n    line-height: 46rpx;\n  }\n\n  .detail-address__row {\n    margin: 15rpx 0;\n  }\n\n  /* 填写物流信息 */\n  .detail-express {\n    padding: 10rpx 30rpx;\n  }\n\n  .form-group {\n    height: 60rpx;\n    margin: 14rpx 0;\n\n    .input {\n      height: 100%;\n      font-size: 28rpx;\n    }\n  }\n\n\n  /* 底部操作栏 */\n\n  .footer {\n    margin-top: 60rpx;\n\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      padding: 0 20rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n\n      // 禁用按钮\n      &.disabled {\n        background: #ff9779;\n      }\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=70953b1f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=70953b1f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891422804\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}