@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 店铺信息头部 */
.shop-header.data-v-5152a28d {
  position: relative;
  padding: 20rpx 32rpx 20rpx 32rpx;
  background: #fff;
  box-shadow: 0px 2rpx 4rpx rgba(0, 0, 0, 0.25);
}
.shop-header .shop-info.data-v-5152a28d {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}
.shop-header .shop-info .shop-details.data-v-5152a28d {
  display: flex;
  flex-direction: column;
}
.shop-header .shop-info .shop-details .shop-name-row.data-v-5152a28d {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}
.shop-header .shop-info .shop-details .shop-name-row .location-icon.data-v-5152a28d {
  width: 36rpx;
  height: 32rpx;
  margin-right: 10rpx;
}
.shop-header .shop-info .shop-details .shop-name-row .shop-name.data-v-5152a28d {
  color: #000;
  font-size: 42rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 40rpx;
  margin-right: 10rpx;
}
.shop-header .shop-info .shop-details .shop-name-row .arrow-icon.data-v-5152a28d {
  width: 14rpx;
  height: 22rpx;
}
.shop-header .shop-info .shop-details .shop-distance.data-v-5152a28d {
  margin-left: 44rpx;
  color: rgba(51, 51, 51, 0.6);
  font-size: 28rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 50rpx;
  letter-spacing: 2rpx;
}
.shop-header .shop-info .shop-details .shop-switch.data-v-5152a28d {
  margin-left: 44rpx;
}
.shop-header .shop-info .shop-details .shop-switch .switch-text.data-v-5152a28d {
  color: #666;
  font-size: 24rpx;
  line-height: 30rpx;
}
.shop-header .shop-info .delivery-switch.data-v-5152a28d {
  margin-top: 16rpx;
  background: #f2f2f2;
  border-radius: 999rpx;
  width: 162rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
}
.shop-header .shop-info .delivery-switch .switch-item.data-v-5152a28d {
  font-size: 24rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 23rpx;
  text-align: center;
  flex: 1;
  color: #8c8d8f;
  cursor: pointer;
}
.shop-header .shop-info .delivery-switch .switch-item.active.data-v-5152a28d {
  background: #232e5d;
  border-radius: 999rpx;
  color: #fff;
  height: 50rpx;
  line-height: 50rpx;
}
.shop-header .shop-info .delivery-switch .switch-item.disabled.data-v-5152a28d {
  color: #d0d0d0;
  cursor: not-allowed;
  opacity: 0.5;
}
