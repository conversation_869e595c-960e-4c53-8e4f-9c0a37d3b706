@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-f7e9129e {
  padding: 20rpx;
  min-height: 100vh;
  background: #f7f7f7;
}
.search-wrapper.data-v-f7e9129e {
  display: flex;
  height: 78rpx;
}
.search-input.data-v-f7e9129e {
  width: 80%;
  background: #fff;
  border-radius: 50rpx 0 0 50rpx;
  box-sizing: border-box;
  overflow: hidden;
  border: solid 1px #cccccc;
}
.search-input .search-input-wrapper.data-v-f7e9129e {
  display: flex;
}
.search-input .search-input-wrapper .left.data-v-f7e9129e {
  display: flex;
  width: 60rpx;
  justify-content: center;
  align-items: center;
}
.search-input .search-input-wrapper .left .search-icon.data-v-f7e9129e {
  display: block;
  color: #666666;
  font-size: 30rpx;
  font-weight: bold;
}
.search-input .search-input-wrapper .right.data-v-f7e9129e {
  flex: 1;
}
.search-input .search-input-wrapper .right input.data-v-f7e9129e {
  font-size: 28rpx;
  height: 78rpx;
  line-height: 78rpx;
}
.search-input .search-input-wrapper .right input .input-placeholder.data-v-f7e9129e {
  color: #aba9a9;
}
.search-button.data-v-f7e9129e {
  width: 20%;
  box-sizing: border-box;
}
.search-button .button.data-v-f7e9129e {
  line-height: 78rpx;
  height: 78rpx;
  font-size: 28rpx;
  border-radius: 0 20px 20px 0;
  background: #3f51b5;
}
.store-list .store-info.data-v-f7e9129e {
  padding: 10px 0;
  overflow: hidden;
  border: 2rpx solid #cccccc;
  min-height: 240rpx;
  border-radius: 5rpx;
  margin-top: 10rpx;
  margin-bottom: 10rpx;
  padding: 30rpx;
  background: #FFFFFF;
}
.store-list .store-info .base-info.data-v-f7e9129e {
  float: left;
  width: 70%;
}
.store-list .store-info .base-info .name.data-v-f7e9129e {
  font-size: 34rpx;
  font-weight: bold;
  margin-top: 15rpx;
  margin-bottom: 12rpx;
  color: #666;
}
.store-list .store-info .base-info .location-icon.data-v-f7e9129e {
  color: #f03c3c;
  font-weight: bold;
}
.store-list .store-info .loc-info.data-v-f7e9129e {
  color: #666666;
  dispaly: flex;
  line-height: 240rpx;
  float: left;
  overflow: hidden;
  width: 30%;
  text-align: right;
}
.store-list .store-info .loc-info .distance.data-v-f7e9129e {
  font-weight: bold;
  color: #f03c3c;
}
