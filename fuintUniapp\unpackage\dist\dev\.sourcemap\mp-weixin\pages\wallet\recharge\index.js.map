{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/wallet/recharge/index.vue?28d7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/wallet/recharge/index.vue?6102", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/wallet/recharge/index.vue?1bfc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/wallet/recharge/index.vue?95c7", "uni-app:///pages/wallet/recharge/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/wallet/recharge/index.vue?53a0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/wallet/recharge/index.vue?e573"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLoading", "userInfo", "setting", "isOpen", "planList", "remark", "disabled", "rechargeAmount", "inputValue", "onLoad", "methods", "onSelectPlan", "onChangeMoney", "getPageData", "app", "Promise", "then", "getUserInfo", "UserApi", "resolve", "getSetting", "BalanceApi", "console", "onSubmit", "catch", "finally", "wxPayment", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkD7qB;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MACAC,mDACAC;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACAC,eACAF;UACAF;UACAK;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC,qBACAL;UACAF;UACAQ;UACAH;QACA;MACA;IACA;IAEA;IACAI;MACA;MACA;QACAT;QACA;MACA;MAEA;QACAA;QACA;MACA;;MAEA;MACAA;MACA;MACAO,0DACAL;QAAA;MAAA,GACAQ;QAAA;MAAA,GACAC;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA,4BACAV;QACAF;QACAa;UACA;UACAb;QACA;MACA,GACAU;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACrKA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/wallet/recharge/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/wallet/recharge/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=dfd304d2&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=dfd304d2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dfd304d2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/wallet/recharge/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=dfd304d2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.userInfo.id ? _vm.setting.planList.length : null\n  var g1 = _vm.userInfo.id\n    ? !_vm.setting.planList || _vm.setting.planList.length < 1\n    : null\n  var g2 = _vm.userInfo.id ? _vm.setting.remark.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\" v-if=\"userInfo.id\">\n    <view class=\"account-panel dis-flex flex-y-center\">\n      <view class=\"panel-lable\">\n        <text>账户余额</text>\n      </view>\n      <view class=\"panel-balance flex-box\">\n        <text>￥{{ userInfo.balance }}</text>\n      </view>\n    </view>\n    <view class=\"recharge-panel\">\n      <view class=\"recharge-label\">\n        <text>充值金额</text>\n      </view>\n      <view class=\"recharge-plan clearfix\" v-if=\"setting.planList.length > 0\">\n        <block v-for=\"(item, index) in setting.planList\" :key=\"index\">\n          <view class=\"recharge-plan_item\" :class=\"{ active: rechargeAmount == item.rechargeAmount }\" @click=\"onSelectPlan(item.rechargeAmount)\">\n            <view class=\"plan_money\">\n              <text>{{ item.rechargeAmount }}</text>\n            </view>\n            <view class=\"plan_gift\" v-if=\"item.giveAmount > 0\">\n              <text>送{{ item.giveAmount }}</text>\n            </view>\n          </view>\n        </block>\n      </view>\n      <!-- 手动充值输入框 -->\n      <view class=\"recharge-input\" v-if=\"!setting.planList || setting.planList.length < 1\">\n        <input type=\"digit\" placeholder=\"请输入充值金额\" v-model=\"inputValue\" @input=\"onChangeMoney\" />\n      </view>\n      <!-- 确认按钮 -->\n      <view class=\"recharge-submit btn-submit\">\n        <form @submit=\"onSubmit\">\n          <button class=\"button\" formType=\"submit\" :disabled=\"disabled\">立即充值</button>\n        </form>\n      </view>\n    </view>\n    <!-- 充值描述 -->\n    <view class=\"recharge-describe\" v-if=\"setting.remark.length > 0\">\n      <view class=\"recharge-label\">\n        <text>充值说明</text>\n      </view>\n      <view class=\"content\">\n        <text space=\"ensp\">{{setting.remark}}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as UserApi from '@/api/user'\n  import * as BalanceApi from '@/api/balance'\n  import { wxPayment } from '@/utils/app'\n\n  export default {\n    data() {\n      return {\n        // 正在加载\n        isLoading: true,\n        // 会员信息\n        userInfo: {},\n        // 充值设置\n        setting: { isOpen: false, planList: [], remark: '' },\n        // 按钮禁用\n        disabled: false,\n        // 当前选中的套餐id\n        rechargeAmount: 0,\n        // 自定义金额\n        inputValue: '',\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 获取页面数据\n      this.getPageData()\n    },\n\n    methods: {\n\n      /**\n       * 选择充值套餐\n       */\n      onSelectPlan(rechargeAmount) {\n        this.rechargeAmount = rechargeAmount\n        this.inputValue = ''\n      },\n\n      // 金额输入框\n      onChangeMoney(e) {\n        this.inputValue = e.target.value\n        this.rechargeAmount = 0\n      },\n\n      // 获取页面数据\n      getPageData() {\n        const app = this\n        app.isLoading = true\n        Promise.all([app.getUserInfo(), app.getSetting()])\n          .then(() => app.isLoading = false)\n      },\n\n      // 获取会员信息\n      getUserInfo() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          UserApi.info()\n            .then(result => {\n              app.userInfo = result.data.userInfo\n              resolve(app.userInfo)\n            })\n        })\n      },\n\n      // 获取充值设置\n      getSetting() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          BalanceApi.setting()\n            .then(result => {\n              app.setting = result.data\r\n              console.log(\"app.setting = \", app.setting)\n              resolve(app.setting)\n            })\n        })\n      },\n\n      // 立即充值\n      onSubmit(e) {\n        const app = this\r\n        if (!app.setting.isOpen) {\r\n            app.$error('当前未开启充值！')\r\n            return false\r\n        }\r\n        \r\n        if (parseFloat(app.rechargeAmount) <= 0 && app.inputValue.length < 1) {\r\n            app.$error('请确认充值金额！')\r\n            return false\r\n        }\r\n        \n        // 按钮禁用\n        app.disabled = true\n        // 提交到后端\n        BalanceApi.doRecharge(app.rechargeAmount, app.inputValue)\n          .then(result => app.wxPayment(result.data.payment))\r\n          .catch(err => app.$error('提交支付失败'))\n          .finally(() => app.disabled = false)\n      },\n\n      // 发起微信支付\n      wxPayment(option) {\n        const app = this\n        wxPayment(option)\n          .then(() => {\n            app.$success('支付成功')\n            setTimeout(() => {\n              // 获取页面数据\n              app.getPageData()\n            }, 1500)\n          })\n          .catch(err => app.$error('订单未支付'))\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  page,\n  .container {\n    background: #fff;\n  }\n\n  .container {\n    padding-bottom: 70rpx;\n  }\n\n  /* 账户面板 */\n  .account-panel {\n    width: 650rpx;\n    height: 180rpx;\n    margin: 50rpx auto;\n    padding: 0 60rpx;\n    box-sizing: border-box;\n    border-radius: 8rpx;\n    color: #fff;\n    background: $fuint-theme;\n    box-shadow: 0 5px 22px 0 rgba(0, 0, 0, 0.26);\n  }\n\n  .panel-lable {\n    font-size: 32rpx;\n  }\n\n  .recharge-label {\n    color: rgb(51, 51, 51);\n    font-size: 32rpx;\n    margin-bottom: 25rpx;\n  }\n\n  .panel-balance {\n    text-align: right;\n    font-size: 46rpx;\n  }\n\n  .recharge-panel {\n    margin-top: 60rpx;\n    padding: 0 60rpx;\n  }\n\n  /* 充值套餐 */\n  .recharge-plan {\n    .recharge-plan_item {\n      width: 192rpx;\n      padding: 15rpx 0;\n      float: left;\n      text-align: center;\n      color: #888;\n      border: 1rpx solid rgb(228, 228, 228);\n      border-radius: 5rpx;\n      margin: 0 20rpx 20rpx 0;\n\n      &:nth-child(3n + 0) {\n        margin-right: 0;\n      }\n\n      &.active {\r\n        background: #E3BE83;\n        color: #FFFFFF;\n        border: 1rpx solid #EDD2A9;\n        .plan_money {\n          color: #FFFFFF;\r\n          font-weight: bold;\n        }\n      }\n    }\n\n  }\n\n  .plan_money {\n    font-size: 32rpx;\n    color: rgb(82, 82, 82);\n  }\n\n  .plan_gift {\n    font-size: 25rpx;\n  }\n\n  .recharge-input {\n    margin-top: 25rpx;\n\n    input {\n      border: 1rpx solid rgb(228, 228, 228);\n      border-radius: 10rpx;\n      padding: 15rpx 16rpx;\n      font-size: 26rpx;\n    }\n  }\n\n  /* 立即充值 */\n  .recharge-submit {\n    margin-top: 70rpx;\n  }\n\n  .btn-submit {\n    .button {\n      font-size: 30rpx;\n      background: linear-gradient(to right, #f9211c, #ff6335);\n      border: none;\n      color: white;\n      border-radius: 40rpx;\n      padding: 0 120rpx;\n      line-height: 3;\n    }\n\n    .button[disabled] {\n      background: #ff6335;\n      border-color: #ff6335;\n      color: white;\n    }\n  }\n\n  /* 充值说明 */\n  .recharge-describe {\n    margin-top: 50rpx;\n    padding: 0 60rpx;\n    .content {\n      font-size: 26rpx;\n      line-height: 1.6;\n      color: #888;\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=dfd304d2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=dfd304d2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420793\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}