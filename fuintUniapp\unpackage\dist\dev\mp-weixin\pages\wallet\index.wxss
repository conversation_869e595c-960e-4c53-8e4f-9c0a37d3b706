
page {
  background: #fff;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-2253ba35 {
  background: #fff;
}
.space-upper.data-v-2253ba35 {
  padding: 100rpx 0;
  text-align: center;
  background: #3f51b5;
  margin: 50rpx 30rpx 10rpx 30rpx;
  border-radius: 8rpx;
}
.wallet-account.data-v-2253ba35 {
  margin-top: 20rpx;
}
.wallet-account_balance.data-v-2253ba35 {
  font-size: 52rpx;
  color: #FFFFFF;
}
.wallet-account_lable.data-v-2253ba35 {
  margin-top: 10rpx;
  color: #FFFFFF;
  font-size: 24rpx;
}
.space-lower.data-v-2253ba35 {
  margin-top: 30rpx;
  padding: 0rpx 30rpx 0rpx 30rpx;
}
.btn-recharge .btn-submit.data-v-2253ba35 {
  width: 100%;
  height: 84rpx;
  line-height: 84rpx;
  margin: 100rpx auto;
  text-align: center;
  border-radius: 5rpx;
  background: linear-gradient(to right, #f9211c, #ff6335);
  color: white;
  font-size: 30rpx;
}
.item-lable.data-v-2253ba35 {
  margin-top: 80rpx;
  font-size: 26rpx;
  color: #5e5e5e;
  padding: 0 100rpx;
}
