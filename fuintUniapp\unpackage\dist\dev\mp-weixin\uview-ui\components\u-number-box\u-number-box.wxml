<view class="u-numberbox data-v-a54b45c0"><view data-event-opts="{{[['touchstart',[['btnTouchStart',['minus']]]],['touchend',[['clearTimer',['$event']]]]]}}" class="{{['u-icon-minus','data-v-a54b45c0',(disabled||inputVal<=min)?'u-icon-disabled':'']}}" style="{{'background:'+(bgColor)+';'+('height:'+(inputHeight+'rpx')+';')+('color:'+(color)+';')}}" catchtouchstart="__e" catchtouchend="__e"><u-icon vue-id="041c7034-1" name="minus" size="{{size}}" class="data-v-a54b45c0" bind:__l="__l"></u-icon></view><input class="{{['u-number-input','data-v-a54b45c0',(disabled)?'u-input-disabled':'']}}" style="{{'color:'+(color)+';'+('font-size:'+(size+'rpx')+';')+('background:'+(bgColor)+';')+('height:'+(inputHeight+'rpx')+';')+('width:'+(inputWidth+'rpx')+';')}}" disabled="{{disabledInput||disabled}}" cursor-spacing="{{getCursorSpacing}}" type="number" data-event-opts="{{[['blur',[['onBlur',['$event']]]],['focus',[['onFocus',['$event']]]],['input',[['__set_model',['','inputVal','$event',[]]]]]]}}" value="{{inputVal}}" bindblur="__e" bindfocus="__e" bindinput="__e"/><view data-event-opts="{{[['touchstart',[['btnTouchStart',['plus']]]],['touchend',[['clearTimer',['$event']]]]]}}" class="{{['u-icon-plus','data-v-a54b45c0',(disabled||inputVal>=max)?'u-icon-disabled':'']}}" style="{{'background:'+(bgColor)+';'+('height:'+(inputHeight+'rpx')+';')+('color:'+(color)+';')}}" catchtouchstart="__e" catchtouchend="__e"><u-icon vue-id="041c7034-2" name="plus" size="{{size}}" class="data-v-a54b45c0" bind:__l="__l"></u-icon></view></view>