{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/my.vue?fe9b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/my.vue?7ff4", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/my.vue?e30c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/my.vue?2b30", "uni-app:///pages/book/my.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/my.vue?a3f7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/my.vue?ea37"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MescrollBody", "mixins", "data", "statusList", "list", "curId", "upOption", "auto", "page", "size", "noMoreSize", "onLoad", "app", "methods", "upCallback", "then", "catch", "getMyBookList", "BookApi", "status", "load", "resolve", "onSwitchTab", "onCancel", "uni", "title", "content", "success", "confirm", "doCancel", "onView", "myBookId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACsD;AACL;AACsC;;;AAGvF;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAAuoB,CAAgB,qoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2C3pB;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;MACAC;IACA;EACA;EAEAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAF,4BACAG;QACA;QACA;QACAH;MACA,GACAI;QAAA;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;QACAC;UAAAC;UAAAX;QAAA;UAAAY;QAAA,GACAL;UACA;UACA;UACAH;UACAA;UACAS;QACA,GACAL;UAAA;QAAA;MACA;IACA;IAEA;IACAM;MACA;MACA;MACAV;MACA;MACAA;MACAA;IACA;IAEA;IACAW;MACA;MACAC;QACAC;QACAC;QACAC;UAAA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAX,yBACAH;QACAH;MACA;IACA;IAEA;IACAkB;MACA;QAAAC;MAAA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC/JA;AAAA;AAAA;AAAA;AAA0uC,CAAgB,gqCAAG,EAAC,C;;;;;;;;;;;ACA9vC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/book/my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/book/my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my.vue?vue&type=template&id=0b1b2d29&scoped=true&\"\nvar renderjs\nimport script from \"./my.vue?vue&type=script&lang=js&\"\nexport * from \"./my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my.vue?vue&type=style&index=0&id=0b1b2d29&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0b1b2d29\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/book/my.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=template&id=0b1b2d29&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list.content, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var f0 = _vm._f(\"timeFormat\")(item.createTime, \"yyyy-mm-dd hh:MM\")\n    return {\n      $orig: $orig,\n      f0: f0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=script&lang=js&\"", "<template>\n  <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ use: false }\" :up=\"upOption\" @up=\"upCallback\">\n\n    <!-- 分类列表tab -->\n    <view class=\"tabs-wrapper\">\n      <scroll-view class=\"scroll-view\" scroll-x>\n        <view class=\"tab-item\" :class=\"{ active: curId == '' }\" @click=\"onSwitchTab('')\">\n          <view class=\"value\"><text>全部</text></view>\n        </view>\n        <!-- tab列表 -->\n        <view class=\"tab-item\" :class=\"{ active: curId ==  item.key }\" @click=\"onSwitchTab(item.key)\"\n          v-for=\"(item, index) in statusList\" :key=\"index\">\n          <view class=\"value\"><text>{{ item.name }}</text></view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 预约列表 -->\n    <view class=\"book-list\">\n      <view class=\"book-item\" v-for=\"(item, index) in list.content\" :key=\"index\" @click=\"onView(item.id)\">\n        <block>\n          <view class=\"flex-box\">\n            <view class=\"book-item-title\">\n              <text>{{ item.bookName }}</text>\n            </view>\r\n            <view class=\"book-content\">\r\n                <view class=\"contacts\">姓名：{{ item.contact }}</view>\r\n                <view class=\"time\">时间：{{ item.serviceDate }} {{ item.serviceTime }}</view>\r\n            </view>\n            <view class=\"book-item-footer m-top10\">\n              <text class=\"book-views f-24 col-8\">{{ item.createTime | timeFormat('yyyy-mm-dd hh:MM') }}</text>\r\n              <view class=\"btn-cancel\" v-if=\"item.status == 'A'\" @click=\"onView(item.id)\">取消</view>\r\n              <view class=\"btn-view\" @click=\"onView(item.id)\">详情</view>\n            </view>\n          </view>\n        </block>\n      </view>\n    </view>\n  </mescroll-body>\n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import * as BookApi from '@/api/book'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n\n  const pageSize = 15\n\n  export default {\n    components: {\n      MescrollBody\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        // 状态列表\n        statusList: [],\n        // 预约列表\n        list: getEmptyPaginateObj(),\n        // 当前选中的分类id (0则代表首页)\n        curId: '',\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于3条才显示无更多数据\n          noMoreSize: 3,\n        }\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      const app = this;\n      if (options.status) {\n          app.curId = options.status;\n      }\n    },\n\n    methods: {\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this;\n        // 设置列表数据\n        app.getMyBookList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length;\n            const totalSize = list.content.totalElements;\n            app.mescroll.endBySize(curPageLen, totalSize);\n          })\n          .catch(() => app.mescroll.endErr());\n      },\n\n      /**\n       * 获取预约列表\n       * @param {Number} pageNo 页码\n       */\n      getMyBookList(pageNo = 1) {\n        const app = this;\n        return new Promise((resolve, reject) => {\n          BookApi.myBookList({ status: app.curId, page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data;\n              app.list.content = getMoreListData(newList, app.list, pageNo);\r\n              app.statusList = result.data.statusList;\n              resolve(newList);\n            })\n            .catch(result => reject());\n        })\n      },\n\n      // 切换选择的分类\n      onSwitchTab(status) {\n        const app = this;\n        // 切换当前的状态\n        app.curId = status;\n        // 刷新列表数据\n        app.list = getEmptyPaginateObj();\n        app.mescroll.resetUpScroll();\n      },\r\n      \r\n      // 取消预约\r\n      onCancel(myBookId) {\r\n         const app = this;\r\n         uni.showModal({\r\n           title: \"提示\",\r\n           content: \"您确定要取消该预约吗?\",\r\n           success({ confirm }) {\r\n             confirm && app.doCancel(myBookId)\r\n           }\r\n         });\r\n      },\r\n      \r\n      // 确认取消预约\r\n      doCancel(myBookId) {\r\n        const app = this;\r\n        BookApi.cancel(myBookId)\r\n          .then(result => {\r\n            app.getPageData()\r\n          })\r\n      },\n\n      // 跳转详情页\n      onView(myBookId) {\n        this.$navTo('pages/book/bookDetail', { myBookId });\n      }\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  /* 顶部选项卡 */\n\n  .container {\n    min-height: 100vh;\n  }\n\n  .tabs-wrapper {\n    position: sticky;\n    top: var(--window-top);\n    display: flex;\n    width: 100%;\n    height: 88rpx;\n    color: #333;\n    font-size: 28rpx;\n    background: #fff;\n    border-bottom: 1rpx solid #e4e4e4;\n    z-index: 100;\n    overflow: hidden;\n    white-space: nowrap;\n  }\n\n  .tab-item {\n    display: inline-block;\n    padding: 0 15rpx;\n    text-align: center;\n    height: 87rpx;\n    line-height: 88rpx;\n    box-sizing: border-box;\n\n    .value {\n      height: 100%;\n    }\n\n    &.active .value {\n      color: #fd4a5f;\n      border-bottom: 4rpx solid #fd4a5f;\r\n      font-weight: bold;\n    }\n  }\n\n  /* 预约列表 */\n  .book-list {\n    padding-top: 20rpx;\n    line-height: 1;\n    background: #f7f7f7;\n  }\n\n  .book-item {\n    margin: 0rpx 10rpx 20rpx 10rpx;\n    padding: 30rpx;\n    background: #fff;\r\n    border-radius: 20rpx;\n    min-height: 280rpx;\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .book-item-title {\n      font-size: 32rpx;\n      color: #333;\r\n      font-weight: bold;\r\n      line-height: 40rpx;\n    }\r\n    .book-content {\r\n        margin: 30rpx 0rpx 30rpx 0rpx;\r\n        .contacts {\r\n            margin-bottom: 20rpx;\r\n        }\r\n    }\n\n    .book-item-footer {\r\n      .btn-cancel {\r\n          width: 100rpx;\r\n          border-radius: 8rpx;\r\n          padding: 10rpx 14rpx;\r\n          font-size: 28rpx;\r\n          color: #fff;\r\n          text-align: center;\r\n          border: 1rpx solid #fff;\r\n          float: right;\r\n          background: #f9211c;\r\n      }\r\n      .btn-view {\r\n          width: 100rpx;\r\n          border-radius: 8rpx;\r\n          padding: 10rpx 14rpx;\r\n          font-size: 28rpx;\r\n          color: #fff;\r\n          text-align: center;\r\n          border: 1rpx solid #fff;\r\n          float: right;\r\n          background: $fuint-theme;\r\n      }\r\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0b1b2d29&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./my.vue?vue&type=style&index=0&id=0b1b2d29&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420735\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}