{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/index.vue?1d35", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/index.vue?c60d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/index.vue?8a13", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/index.vue?eecb", "uni-app:///pages/address/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/index.vue?f55f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/index.vue?fc65"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Empty", "data", "options", "isLoading", "list", "onLoad", "onShow", "methods", "getPageData", "app", "Promise", "then", "finally", "getAddressList", "AddressApi", "resolve", "catch", "onReorder", "handleCreate", "handleUpdate", "addressId", "handleRemove", "uni", "title", "content", "success", "confirm", "onRemove", "handleSetDefault"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8C9pB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC;MACAC,oCACAC;QACA;QACAF;MACA,GACAG;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACAC,kBACAH;UACAF;UACAM;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;MACAR;QACA;MACA;IACA;IAEA;AACA;AACA;IACAS;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;QAAAC;MAAA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACAC;QACAC;QACAC;QACAC;UAAA;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACAb,kCACAH;QACAF;MACA;IACA;IAEA;AACA;AACA;AACA;IACAmB;MACA;MACAd,sCACAH;QACAF;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzKA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/address/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/address/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=5f170bce&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=5f170bce&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5f170bce\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/address/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=5f170bce&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"addres-list\">\n      <view class=\"address-item\" v-for=\"(item, index) in list\" :key=\"index\">\n        <view class=\"contacts\" @click=\"handleSetDefault(item.id)\">\n          <text class=\"name\">{{ item.name }}</text>\n          <text class=\"phone\">{{ item.phone }}</text>\n        </view>\n        <view class=\"address\" @click=\"handleSetDefault(item.id)\">\n          <text class=\"region\">{{ item.provinceName }}{{ item.cityName }}{{ item.regionName }}</text>\n          <text class=\"detail\">{{ item.detail }}</text>\n        </view>\n        <view class=\"line\"></view>\n        <view class=\"item-option\">\n          <view class=\"_left\">\n            <label class=\"item-radio\" @click.stop=\"handleSetDefault(item.id)\">\n              <radio class=\"radio\" color=\"#fa2209\" :checked=\"item.isDefault == 'Y'\"></radio>\n              <text class=\"text\">选择</text>\n            </label>\n          </view>\n          <view class=\"_right\">\n            <view class=\"events\">\n              <view class=\"event-item\" @click=\"handleUpdate(item.id)\">\n                <text class=\"iconfont icon-edit\"></text>\n                <text class=\"title\">编辑</text>\n              </view>\n              <view class=\"event-item\" @click=\"handleRemove(item.id)\">\n                <text class=\"iconfont icon-delete\"></text>\n                <text class=\"title\">删除</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <empty v-if=\"!list.length\" :isLoading=\"isLoading\" tips=\"暂无收货地址哦..\"/>\n    <!-- 底部操作按钮 -->\n    <view class=\"footer-fixed\">\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" @click=\"handleCreate()\">添加新地址</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as AddressApi from '@/api/address'\n  import Empty from '@/components/empty'\n\n  export default {\n    components: {\n      Empty\n    },\n    data() {\n      return {\n        //当前页面参数\n        options: {},\n        // 正在加载\n        isLoading: true,\n        // 收货地址列表\n        list: []\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 当前页面参数\n      this.options = options\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n      // 获取页面数据\n      this.getPageData()\n    },\n\n    methods: {\n\n      // 获取页面数据\n      getPageData() {\n        const app = this\n        app.isLoading = true\n        Promise.all([app.getAddressList()])\n          .then(() => {\n            // 列表排序把默认收货地址放到最前\n            app.onReorder()\n          })\n          .finally(() => app.isLoading = false)\n      },\n\n      // 获取收货地址列表\n      getAddressList() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          AddressApi.list()\n            .then(result => {\n              app.list = result.data.list\n              resolve(result)\n            })\n            .catch(err => reject(err))\n        })\n      },\n\n      // 列表排序把默认收货地址放到最前\n      onReorder() {\n        const app = this\n        app.list.sort(item => {\n          return item.isDefault == 'Y' ? -1 : 1\n        })\n      },\n\n      /**\n       * 添加新地址\n       */\n      handleCreate() {\n        this.$navTo('pages/address/create')\n      },\n\n      /**\n       * 编辑地址\n       * @param {int} addressId 收货地址ID\n       */\n      handleUpdate(addressId) {\n        this.$navTo('pages/address/update', { addressId })\n      },\n\n      /**\n       * 删除收货地址\n       * @param {int} addressId 收货地址ID\n       */\n      handleRemove(addressId) {\n        const app = this\n        uni.showModal({\n          title: \"提示\",\n          content: \"您确定要删除当前收货地址吗?\",\n          success({ confirm }) {\n            confirm && app.onRemove(addressId)\n          }\n        });\n      },\n\n      /**\n       * 确认删除收货地址\n       * @param {int} addressId 收货地址ID\n       */\n      onRemove(addressId) {\n        const app = this\n        AddressApi.remove(addressId, 'D')\n          .then(result => {\n            app.getPageData()\n          })\n      },\n\n      /**\n       * 设置为默认地址\n       * @param {Object} addressId\n       */\n      handleSetDefault(addressId) {\n        const app = this\n        AddressApi.setDefault(addressId, 'Y')\n          .then(result => {\n            app.options.from === 'checkout' && uni.navigateBack()\n          })\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .addres-list {\n    padding-bottom: 120rpx;\n  }\n\n  // 项目内容\n  .address-item {\n    margin: 20rpx auto 20rpx auto;\n    padding: 30rpx 40rpx;\n    width: 94%;\n    box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);\n    border-radius: 16rpx;\n    background: #fff;\n  }\n\n  .contacts {\n    font-size: 30rpx;\n    margin-bottom: 16rpx;\n\n    .name {\n      margin-right: 16rpx;\n    }\n  }\n\n  .address {\n    font-size: 28rpx;\n\n    .region {\n      margin-right: 10rpx;\n    }\n  }\n\n  .line {\n    margin: 20rpx 0;\n    border-bottom: 1rpx solid #f3f3f3;\n  }\n\n  .item-option {\n    display: flex;\n    justify-content: space-between;\n    height: 48rpx;\n\n    // 单选框\n    .item-radio {\n      font-size: 28rpx;\n\n      .radio {\n        vertical-align: middle;\n        transform: scale(0.76)\n      }\n\n      .text {\n        vertical-align: middle;\n      }\n    }\n\n    // 操作\n    .events {\n      display: flex;\n      align-items: center;\n      line-height: 48rpx;\n\n      .event-item {\n        font-size: 28rpx;\n        margin-right: 22rpx;\n        color: #4c4c4c;\n\n        &:last-child {\n          margin-right: 0;\n        }\n\n        .title {\n          margin-left: 8rpx;\n        }\n      }\n    }\n  }\n\n  // 底部操作栏\n  .footer-fixed {\n    position: fixed;\n    bottom: var(--window-bottom);\n    left: 0;\n    right: 0;\n    height: 180rpx;\n    z-index: 11;\n    box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);\n    background: #fff;\r\n    padding-bottom: 40rpx;\n\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      padding: 0 20rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=5f170bce&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=5f170bce&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420744\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}