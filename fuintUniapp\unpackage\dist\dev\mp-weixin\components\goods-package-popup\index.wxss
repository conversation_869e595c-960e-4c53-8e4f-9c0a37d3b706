@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/*  套餐弹出层样式 */
.goods-package-popup.data-v-6c4cc486 {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 999999999999;
  overflow: hidden;
}
.goods-package-popup.show.data-v-6c4cc486 {
  display: block;
}
.goods-package-popup.show .mask.data-v-6c4cc486 {
  -webkit-animation: showPopup-data-v-6c4cc486 0.2s linear both;
          animation: showPopup-data-v-6c4cc486 0.2s linear both;
}
.goods-package-popup.show .layer.data-v-6c4cc486 {
  -webkit-animation: showLayer-data-v-6c4cc486 0.2s linear both;
          animation: showLayer-data-v-6c4cc486 0.2s linear both;
}
.goods-package-popup.hide .mask.data-v-6c4cc486 {
  -webkit-animation: hidePopup-data-v-6c4cc486 0.2s linear both;
          animation: hidePopup-data-v-6c4cc486 0.2s linear both;
}
.goods-package-popup.hide .layer.data-v-6c4cc486 {
  -webkit-animation: hideLayer-data-v-6c4cc486 0.2s linear both;
          animation: hideLayer-data-v-6c4cc486 0.2s linear both;
}
.goods-package-popup.none.data-v-6c4cc486 {
  display: none;
}
.goods-package-popup .mask.data-v-6c4cc486 {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.65);
}
.goods-package-popup .layer.data-v-6c4cc486 {
  display: flex;
  width: 100%;
  max-height: 1200rpx;
  flex-direction: column;
  position: fixed;
  z-index: 999999;
  bottom: 0;
  border-radius: 10rpx 10rpx 0 0;
  background-color: #ffffff;
  margin-top: 10rpx;
  overflow-y: scroll;
}
.goods-package-popup .layer .btn-option.data-v-6c4cc486 {
  padding: 1rpx;
  display: block;
  clear: both;
  margin-bottom: 60rpx;
}
.goods-package-popup .layer .specification-wrapper.data-v-6c4cc486 {
  width: 100%;
  margin-top: 20rpx;
  padding: 30rpx 25rpx;
  box-sizing: border-box;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content.data-v-6c4cc486 {
  width: 100%;
  min-height: 300rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content.data-v-6c4cc486::-webkit-scrollbar {
  /*隐藏滚轮*/
  display: none;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header.data-v-6c4cc486 {
  width: 100%;
  display: flex;
  flex-direction: row;
  position: relative;
  margin-bottom: 40rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-left.data-v-6c4cc486 {
  width: 180rpx;
  height: 180rpx;
  flex: 0 0 180rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-left .product-img.data-v-6c4cc486 {
  width: 180rpx;
  height: 180rpx;
  background-color: #999999;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-right.data-v-6c4cc486 {
  flex: 1;
  padding: 0 35rpx 10rpx 28rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  font-weight: 500;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-right .price-content.data-v-6c4cc486 {
  color: #fe560a;
  margin-bottom: 10rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-right .price-content .sign.data-v-6c4cc486 {
  font-size: 28rpx;
  margin-right: 4rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-right .price-content .price.data-v-6c4cc486 {
  font-size: 44rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-right .choose.data-v-6c4cc486 {
  font-size: 24rpx;
  color: #333;
  min-height: 32rpx;
  font-weight: bold;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content.data-v-6c4cc486 {
  font-weight: 500;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group.data-v-6c4cc486 {
  margin-bottom: 40rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .group-title.data-v-6c4cc486 {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .group-title .required-tag.data-v-6c4cc486 {
  color: #f03c3c;
  font-size: 26rpx;
  margin-left: 10rpx;
  font-weight: normal;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .group-title .optional-tag.data-v-6c4cc486 {
  color: #666;
  font-size: 26rpx;
  margin-left: 10rpx;
  font-weight: normal;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .group-title .selection-rule.data-v-6c4cc486 {
  color: #999;
  font-size: 24rpx;
  margin-left: 20rpx;
  font-weight: normal;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item.data-v-6c4cc486 {
  display: flex;
  padding: 20rpx;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  background-color: #f8f8f8;
  align-items: center;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item.active.data-v-6c4cc486 {
  background-color: #fff4f4;
  border: 1px solid #ffdddd;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-image.data-v-6c4cc486 {
  width: 100rpx;
  height: 100rpx;
  margin-right: 20rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-image image.data-v-6c4cc486 {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-info.data-v-6c4cc486 {
  flex: 1;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-info .item-name.data-v-6c4cc486 {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 6rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-info .item-spec-text.data-v-6c4cc486 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 6rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-info .item-price.data-v-6c4cc486 {
  font-size: 24rpx;
  color: #f03c3c;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-checkbox.data-v-6c4cc486 {
  padding: 0 10rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-checkbox .checkbox.data-v-6c4cc486 {
  display: inline-block;
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-checkbox .checkbox.checked.data-v-6c4cc486 {
  background-color: #f03c3c;
  border-color: #f03c3c;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-checkbox .checkbox.checked.data-v-6c4cc486:after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 10rpx;
  border-left: 4rpx solid #fff;
  border-bottom: 4rpx solid #fff;
  -webkit-transform: rotate(-45deg);
          transform: rotate(-45deg);
  top: 10rpx;
  left: 8rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-spec.data-v-6c4cc486 {
  padding: 0 10rpx;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-spec .select-spec.data-v-6c4cc486 {
  height: 50rpx;
  line-height: 48rpx;
  padding: 0 20rpx;
  border-radius: 25rpx;
  background-color: #f8f8f8;
  color: #333;
  font-size: 24rpx;
  border: 1rpx solid #eee;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-quantity .quantity-control.data-v-6c4cc486 {
  display: flex;
  align-items: center;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-quantity .quantity-control .minus.data-v-6c4cc486, .goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-quantity .quantity-control .plus.data-v-6c4cc486 {
  display: inline-block;
  width: 50rpx;
  height: 50rpx;
  background-color: #f5f5f5;
  color: #333;
  text-align: center;
  line-height: 48rpx;
  font-size: 36rpx;
  border-radius: 50%;
  font-weight: bold;
}
.goods-package-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .package-group .item-wrapper .package-item .item-quantity .quantity-control .value.data-v-6c4cc486 {
  margin: 0 15rpx;
  font-size: 28rpx;
  min-width: 40rpx;
  text-align: center;
}
.goods-package-popup .layer .specification-wrapper .close.data-v-6c4cc486 {
  position: absolute;
  top: 30rpx;
  right: 25rpx;
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
}
.goods-package-popup .layer .specification-wrapper .close .close-item.data-v-6c4cc486 {
  width: 40rpx;
  height: 40rpx;
}
.goods-package-popup .layer .btn-wrapper.data-v-6c4cc486 {
  display: flex;
  width: 100%;
  height: 120rpx;
  flex: 0 0 120rpx;
  align-items: center;
  justify-content: space-between;
  padding: 0 26rpx;
  box-sizing: border-box;
}
.goods-package-popup .layer .btn-wrapper .layer-btn.data-v-6c4cc486 {
  width: 335rpx;
  height: 80rpx;
  border-radius: 40rpx;
  color: #fff;
  line-height: 80rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
}
.goods-package-popup .layer .btn-wrapper .layer-btn.add-cart.data-v-6c4cc486 {
  background: #ffbe46;
}
.goods-package-popup .layer .btn-wrapper .layer-btn.buy.data-v-6c4cc486 {
  background: #fe560a;
}
.goods-package-popup .layer .btn-wrapper .sure.data-v-6c4cc486 {
  width: 698rpx;
  height: 80rpx;
  border-radius: 38rpx;
  color: #fff;
  line-height: 80rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
  background: #fe560a;
}
.goods-package-popup .layer .btn-wrapper .sure.add-cart.data-v-6c4cc486 {
  background: #ff9402;
}
@-webkit-keyframes showPopup-data-v-6c4cc486 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes showPopup-data-v-6c4cc486 {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@-webkit-keyframes hidePopup-data-v-6c4cc486 {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes hidePopup-data-v-6c4cc486 {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@-webkit-keyframes showLayer-data-v-6c4cc486 {
0% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
100% {
    -webkit-transform: translateY(0%);
            transform: translateY(0%);
}
}
@keyframes showLayer-data-v-6c4cc486 {
0% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
100% {
    -webkit-transform: translateY(0%);
            transform: translateY(0%);
}
}
@-webkit-keyframes hideLayer-data-v-6c4cc486 {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
100% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
}
@keyframes hideLayer-data-v-6c4cc486 {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
100% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
}
