<view class="container data-v-0f099936"><block wx:if="{{hasPassword=='Y'}}"><view class="info-item data-v-0f099936"><view class="contacts data-v-0f099936"><text class="name data-v-0f099936">旧密码：</text><input class="weui-input value data-v-0f099936" type="password" disabled="true" placeholder="请输入旧密码" data-event-opts="{{[['tap',[['inputPassword',[1]]]],['input',[['__set_model',['','passwordOld','$event',[]]]]]]}}" value="{{passwordOld}}" bindtap="__e" bindinput="__e"/></view></view></block><view class="info-item data-v-0f099936"><view class="contacts data-v-0f099936"><text class="name data-v-0f099936">新密码：</text><input class="weui-input value data-v-0f099936" type="password" disabled="true" placeholder="请输入新密码" data-event-opts="{{[['tap',[['inputPassword',[2]]]],['input',[['__set_model',['','password','$event',[]]]]]]}}" value="{{password}}" bindtap="__e" bindinput="__e"/></view></view><view class="info-item data-v-0f099936"><view class="contacts data-v-0f099936"><text class="name data-v-0f099936">新密码确认：</text><input class="weui-input value data-v-0f099936" type="password" disabled="true" placeholder="请输入新密码确认" data-event-opts="{{[['tap',[['inputPassword',[3]]]],['input',[['__set_model',['','passwordCopy','$event',[]]]]]]}}" value="{{passwordCopy}}" bindtap="__e" bindinput="__e"/></view></view><view class="footer-fixed data-v-0f099936"><view class="btn-wrapper data-v-0f099936"><view data-event-opts="{{[['tap',[['doSubmit']]]]}}" class="btn-item btn-item-main data-v-0f099936" bindtap="__e">保存</view></view></view><key-words vue-id="5babe2e0-1" mix="{{true}}" title="{{title}}" show_key="{{showPass}}" price="{{0}}" data-event-opts="{{[['^closeFuc',[['closeFuc']]],['^getPassword',[['getPassword']]]]}}" bind:closeFuc="__e" bind:getPassword="__e" class="data-v-0f099936" bind:__l="__l"></key-words></view>