{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/index.vue?56b1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/index.vue?dd13", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/index.vue?c631", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/index.vue?03f2", "uni-app:///pages/settlement/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/index.vue?9f48", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/settlement/index.vue?b26d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "PayTypeEnum", "options", "curPayType", "remark", "disabled", "onLoad", "onShow", "methods", "getCouponDetail", "CouponApi", "then", "app", "ruleItem", "resolve", "catch", "initData", "handleSelectPayType", "onSubmitOrder", "SettlementApi", "onSubmitCallback", "finally", "navToMyOrder", "setTimeout", "onVerifyFrom"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9BA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkE9pB;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IAAA;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IAAA,mDAEA,wDACA,qDACA,sDACA,wDACA,0DACA,oDACA;EAEA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACA;QACAC,+BACAC;UACAC;UACAA;UACA;UACA;UACAC;YACA;YACA;YACA;YACAD;YACAA;YACAA;YACA;cACAA;gBAAA;gBAAA;gBAAA;gBAAA;cAAA;YACA;UACA;UACAE;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEAC;MACA,qBACA,sBACA,yBACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAGA;MACA;QACAN;QACA;MACA;MACA;MACA;QACA;MACA;MACA;MACAA;MACA;MACAO,6GACAR;QAAA;MAAA,GACAI;QACA;UACA;UACA;YACAH;YACA;UACA;QACA;QACAA;MACA;IACA;IAEA;IACAQ;MACA;MACA;MACA;QACA,yCACAT;UAAA;QAAA,GACAI;UAAA;QAAA,GACAM;UACAT;UACAA;QACA;MACA;;MAEA;MACA;QACAA;QACAA;QACAA;MACA;IACA;IAEA;IACAU;MAAA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAZ;QACA;MACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;AChOA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/settlement/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/settlement/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=60591ae5&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=60591ae5&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"60591ae5\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/settlement/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=60591ae5&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.storeRule, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.amount.toFixed(2)\n    return {\n      $orig: $orig,\n      g0: g0,\n    }\n  })\n  var g1 = _vm.totalAmount.toFixed(2)\n  var g2 = _vm.totalAmount.toFixed(2)\n  var g3 = _vm.getTotalAmount.toFixed(2)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container p-bottom\">\n    <!-- 清单列表 -->\n    <view class=\"m-top20\">\n      <view v-for=\"(item, index) in storeRule\" :key=\"index\" class=\"checkout_list\">\n        <view class=\"flow-shopList dis-flex\">\n          <view class=\"flow-list-right flex-box\">\n            <text class=\"goods-name twolist-hidden\">预存￥{{ item.store }} 到账 ￥{{ item.upStore }}</text>\n            <view class=\"flow-list-cont dis-flex flex-x-between flex-y-center\">\n              <text class=\"small\">X{{ item.num }} </text>\n              <text class=\"flow-cont\">￥{{ item.amount.toFixed(2) }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\r\n\t  \n      <view class=\"flow-num-box b-f\">\n        <text>共{{ totalNum }}张，合计：</text>\n        <text class=\"flow-money col-m\">￥{{ totalAmount.toFixed(2) }}</text>\n      </view>\n    </view>\n\n    <!-- 支付方式 -->\n    <view class=\"pay-method flow-all-money b-f m-top20\">\n      <view class=\"flow-all-list dis-flex\">\n        <text class=\"flex-five\">支付方式</text>\n      </view>\n      <!-- 微信支付 -->\n      <view class=\"pay-item dis-flex flex-x-between\" @click=\"handleSelectPayType(PayTypeEnum.WECHAT.value)\">\n        <view class=\"item-left dis-flex flex-y-center\">\n          <view class=\"item-left_icon wechat\">\n            <text class=\"iconfont icon-weixinzhifu\"></text>\n          </view>\n          <view class=\"item-left_text\">\n            <text>{{ PayTypeEnum.WECHAT.name }}</text>\n          </view>\n        </view>\n        <view class=\"item-right col-m\" v-if=\"curPayType == PayTypeEnum.WECHAT.value\">\n          <text class=\"iconfont icon-duihao\"></text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 买家留言 -->\n    <view class=\"flow-all-money b-f m-top20\">\n      <view class=\"ipt-wrapper\">\r\n        <textarea v-model=\"remark\" rows=\"3\" maxlength=100 placeholder=\"买家留言 (选填,100字以内)\" type=\"text\"></textarea>\r\n      </view>\n    </view>\n\n    <!-- 提交订单 -->\n    <view class=\"flow-fixed-footer b-f m-top10\">\n      <view class=\"dis-flex chackout-box\">\n        <view class=\"chackout-left pl-12\">\n          <view class=\"col-amount-do\">应付金额：￥{{ totalAmount.toFixed(2) }}</view>\n          <view class=\"col-amount-view\">实得金额：￥{{ getTotalAmount.toFixed(2) }}</view>\n        </view>\n        <view class=\"chackout-right\" @click=\"onSubmitOrder()\">\n          <view class=\"flow-btn f-32\" :class=\"{ disabled }\">提交订单</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as Verify from '@/utils/verify'\n  import * as CouponApi from '@/api/coupon'\n  import * as SettlementApi from '@/api/settlement'\n  import PayTypeEnum from '@/common/enum/order/PayType'\n  import { wxPayment } from '@/utils/app'\n\n  export default {\n    data() {\n      return {\n        // 枚举类\n        PayTypeEnum,\n        // 当前页面参数\n        options: {},\n        // 当前选中的支付方式\n        curPayType: PayTypeEnum.WECHAT.value,\n        // 买家留言\n        remark: '',\n        // 禁用submit按钮\n        disabled: false,\n        // 按钮禁用\n        disabled: false,\n        couponId: 0,\n        selectNum: \"\",\n        storeRule: [],\n        totalAmount: 0,\n        getTotalAmount: 0,\n        totalNum: 0\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      this.options = options\n      this.couponId = options.couponId\n      this.selectNum = options.selectNum\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n      // 获取当前订单信息\n      this.getCouponDetail()\n    },\n\n    methods: {\n\n      // 获取卡券信息\n      getCouponDetail() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          CouponApi.detail(app.couponId)\n            .then(result => {\n              app.initData()\n              app.couponInfo = result.data\n                let ruleItem = app.couponInfo.inRule.split(\",\")\n              let selected = app.selectNum.split(\",\")\n                ruleItem.forEach(function(item, index) {\n                    let rule = item.split(\"_\")\n                    let num = selected[index]\n                    let amount = parseFloat(rule[0]) * num\n                    app.totalAmount += parseFloat(amount)\n                    app.getTotalAmount += parseFloat(rule[1] * num)\n                    app.totalNum += parseInt(num) \n                    if (num > 0) {\n                       app.storeRule.push({\"store\": parseFloat(rule[0]), \"num\": parseInt(num), \"amount\": parseFloat(amount), \"upStore\": parseFloat(rule[1])})\n                    }\n                })\n              resolve(result)\n            })\n            .catch(err => reject(err))\n        })\n      },\n      \n      initData() {\n        this.storeRule = [],\n        this.totalAmount = 0,\n        this.getTotalAmount = 0,\n        this.totalNum = 0\n      },\n      \n      // 选择支付方式\n      handleSelectPayType(value) {\n        this.curPayType = value\n      },\n\n      // 订单提交\n      onSubmitOrder() {\r\n\t\t  \r\n\t  \n        const app = this\n        if (app.disabled) {\r\n            app.$toast('请勿重复提交订单哦');\n            return false\n        }\n        // 表单验证\n        if (!app.onVerifyFrom()) {\n            return false\n        }\n        // 按钮禁用\n        app.disabled = true\n        // 请求api\n        SettlementApi.submit(app.couponId, app.selectNum, \"prestore\", app.remark, 0, 0, 0, \"\", 0, 0, 0, \"\", \"JSAPI\")\n          .then(result => app.onSubmitCallback(result))\n          .catch(err => {\n            if (err.result) {\n              const errData = err.result.data\n              if (errData.isCreated) {\n                  app.navToMyOrder(errData.orderInfo.id)\n                  return false\n              }\n            }\n            app.disabled = false\n          })\n      },\n\n      // 订单提交成功后回调\n      onSubmitCallback(result) {\n        const app = this\n        // 发起微信支付\n        if (result.data.payType == PayTypeEnum.WECHAT.value) {\n          wxPayment(result.data.payment)\n            .then(() => app.$success('支付成功'))\n            .catch(err => app.$error('支付失败'))\n            .finally(() => {\n              app.disabled = false\n              app.navToMyOrder(result.data.orderInfo.id)\n            })\n        }\n        \n        // 余额支付\n        if (result.data.payType == PayTypeEnum.BALANCE.value) {\n          app.$success('支付成功')\n          app.disabled = false\n          app.navToMyOrder(result.data.orderInfo.id)\n        }\n      },\n\n      // 跳转到我的订单(等待1秒)\n      navToMyOrder(orderId) {\n        setTimeout(() => {\n          this.$navTo('pages/order/detail?orderId='+orderId)\n        }, 1000)\n      },\n\n      // 表单验证\n      onVerifyFrom() {\n        const app = this\n        if (app.hasError) {\n          app.$toast(app.errorMsg)\n          return false\n        }\n        return true\n      },\n\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  @import \"./style.scss\";\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=60591ae5&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=60591ae5&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751892058627\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}