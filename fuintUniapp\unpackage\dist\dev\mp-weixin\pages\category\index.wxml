<view class="container data-v-2f754cba"><block wx:if="{{storeInfo}}"><location vue-id="819668e8-1" storeInfo="{{storeInfo}}" currentDeliveryMode="{{currentDeliveryMode}}" data-event-opts="{{[['^deliveryModeChange',[['onDeliveryModeChange']]]]}}" bind:deliveryModeChange="__e" class="data-v-2f754cba" bind:__l="__l"></location></block><search vue-id="819668e8-2" tips="请输入搜索关键字..." data-event-opts="{{[['^event',[['$navTo',['pages/search/index']]]]]}}" bind:event="__e" class="data-v-2f754cba" bind:__l="__l"></search><block wx:if="{{$root.g0>0}}"><view class="main-content data-v-2f754cba"><scroll-view class="category-nav data-v-2f754cba" style="{{'height:'+(scrollHeight+'px')+';'}}" scroll-y="{{true}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="nav-item-wrapper data-v-2f754cba"><block wx:if="{{item.total}}"><text class="cart-badge data-v-2f754cba">{{item.total}}</text></block><block wx:if="{{index===0}}"><view data-event-opts="{{[['tap',[['handleSelectNav',[index]]]]]}}" class="{{['popular-category','data-v-2f754cba',(curIndex==index)?'selected':'']}}" bindtap="__e"><view class="popular-badge data-v-2f754cba">人气TOP</view><text class="category-title data-v-2f754cba">人气推荐</text><view class="category-subtitle data-v-2f754cba"><text class="en-title data-v-2f754cba">POPULAR</text><text class="en-subtitle data-v-2f754cba">Recommendations</text></view></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleSelectNav',[index]]]]]}}" class="{{['normal-category','data-v-2f754cba',(curIndex==index)?'selected':'']}}" bindtap="__e"><block wx:if="{{item.logo}}"><image class="category-logo data-v-2f754cba" lazy-load="{{true}}" lazy-load-margin="{{0}}" src="{{item.logo}}"></image></block><text class="category-name data-v-2f754cba">{{item.name}}</text></view></block></view></block></scroll-view><scroll-view class="goods-content data-v-2f754cba" style="{{'height:'+(scrollHeight+'px')+';'}}" scroll-top="{{scrollTop}}" scroll-y="{{true}}" scroll-with-animation="{{true}}" data-event-opts="{{[['scroll',[['onScroll',['$event']]]]]}}" bindscroll="__e"><view class="goods-wrapper data-v-2f754cba"><block wx:for="{{$root.l1}}" wx:for-item="category" wx:for-index="categoryIndex" wx:key="categoryIndex"><view class="category-section data-v-2f754cba" id="{{'category-'+categoryIndex}}"><view class="category-title data-v-2f754cba" id="{{'category-title-'+categoryIndex}}"><text class="title-text data-v-2f754cba">{{category.$orig.name}}</text></view><block wx:if="{{category.g1}}"><view class="goods-list data-v-2f754cba"><block wx:for="{{category.l0}}" wx:for-item="item" wx:for-index="idx" wx:key="idx"><view class="goods-item data-v-2f754cba"><block wx:if="{{item.$orig.logo}}"><image class="goods-image data-v-2f754cba" lazy-load="{{true}}" lazy-load-margin="{{0}}" src="{{item.$orig.logo}}" data-event-opts="{{[['tap',[['onTargetGoods',['$0'],[[['list','',categoryIndex],['goodsList','',idx,'id']]]]]]]}}" bindtap="__e"></image></block><view class="goods-info data-v-2f754cba"><text class="goods-name data-v-2f754cba">{{item.$orig.name}}</text><view class="goods-desc data-v-2f754cba"><text class="desc-text data-v-2f754cba">{{item.$orig.salePoint||'精选优质食材，新鲜制作，香甜多汁，搭配优质原料，口感细腻顺滑，每一口都充...'}}</text></view><view class="goods-action data-v-2f754cba"><view class="price-section data-v-2f754cba"><block wx:if="{{!isMemberPrice}}"><text class="price-symbol data-v-2f754cba">￥</text><text class="price-main data-v-2f754cba">{{item.g2}}</text><text class="price-decimal data-v-2f754cba">{{item.g3}}</text><text class="price-suffix data-v-2f754cba">起</text><block wx:if="{{item.$orig.gradePrice>0}}"><text data-event-opts="{{[['tap',[['onVipPrice']]]]}}" class="vip-price data-v-2f754cba" bindtap="__e">{{"￥"+item.$orig.gradePrice}}</text></block></block><block wx:if="{{isMemberPrice}}"><text class="price-symbol data-v-2f754cba">￥</text><text class="price-main data-v-2f754cba">{{item.g4}}</text><text class="price-decimal data-v-2f754cba">{{item.g5}}</text><text class="price-suffix data-v-2f754cba">起</text></block></view><view class="cart-controls data-v-2f754cba"><block wx:if="{{item.$orig.isSingleSpec==='Y'&&item.$orig.type!=='package'}}"><view class="single-spec data-v-2f754cba"><block wx:if="{{item.$orig.buyNum}}"><view data-event-opts="{{[['tap',[['onSaveCart',['$0','-'],[[['list','',categoryIndex],['goodsList','',idx,'id']]]]]]]}}" class="control-minus data-v-2f754cba" bindtap="__e"></view></block><block wx:if="{{item.$orig.buyNum}}"><view class="control-num data-v-2f754cba">{{item.$orig.buyNum||0}}</view></block><block wx:if="{{item.$orig.stock>0}}"><view data-event-opts="{{[['tap',[['onSaveCart',['$0','+'],[[['list','',categoryIndex],['goodsList','',idx,'id']]]]]]]}}" class="control-add data-v-2f754cba" bindtap="__e"></view></block></view></block><block wx:if="{{item.$orig.isSingleSpec==='N'}}"><view class="multi-spec data-v-2f754cba"><block wx:if="{{item.$orig.buyNum}}"><text class="spec-badge data-v-2f754cba">{{item.$orig.buyNum}}</text></block><view data-event-opts="{{[['tap',[['onShowSkuPopup',[2,'$0'],[[['list','',categoryIndex],['goodsList','',idx,'id']]]]]]]}}" class="spec-button data-v-2f754cba" bindtap="__e">选规格</view></view></block><block wx:if="{{item.$orig.type==='package'}}"><view class="multi-spec data-v-2f754cba"><block wx:if="{{item.$orig.buyNum}}"><text class="spec-badge data-v-2f754cba">{{item.$orig.buyNum}}</text></block><view data-event-opts="{{[['tap',[['onShowPackagePopup',['$0'],[[['list','',categoryIndex],['goodsList','',idx,'id']]]]]]]}}" class="spec-button data-v-2f754cba" bindtap="__e">选套餐</view></view></block></view></view></view></view></block></view></block><block wx:if="{{!category.g6}}"><view class="empty-category data-v-2f754cba"><text class="empty-text data-v-2f754cba">该分类暂无商品</text></view></block></view></block><block wx:if="{{!$root.g7}}"><empty vue-id="819668e8-3" isLoading="{{isLoading}}" tips="暂无商品~" class="data-v-2f754cba" bind:__l="__l"></empty></block></view></scroll-view></view></block><block wx:if="{{!isLoading&&showSkuPopup&&skuMode!==3}}"><sku-popup vue-id="819668e8-4" gradeInfo="{{gradeInfo}}" hafanInfo="{{hafanInfo}}" skuMode="{{skuMode}}" goods="{{goods}}" value="{{showSkuPopup}}" data-event-opts="{{[['^addCart',[['onAddCart']]],['^input',[['__set_model',['','showSkuPopup','$event',[]]]]]]}}" bind:addCart="__e" bind:input="__e" class="data-v-2f754cba" bind:__l="__l"></sku-popup></block><block wx:if="{{!isLoading&&showPackagePopup}}"><package-popup vue-id="819668e8-5" value="{{showPackagePopup}}" gradeInfo="{{gradeInfo}}" hafanInfo="{{hafanInfo}}" goods="{{goods}}" data-event-opts="{{[['^input',[['e0']]],['^addCart',[['onAddCart']]]]}}" bind:input="__e" bind:addCart="__e" class="data-v-2f754cba" bind:__l="__l"></package-popup></block><view class="cart-footer data-v-2f754cba"><view class="cart-container data-v-2f754cba"><image class="cart-bg data-v-2f754cba" src="/static/cart-bg.png"></image><view class="cart-info data-v-2f754cba"><image class="cart-icon data-v-2f754cba" src="/static/cart-icon.png" data-event-opts="{{[['tap',[['goCart']]]]}}" bindtap="__e"></image><text class="cart-count data-v-2f754cba">{{"共计"+totalNum+"件"}}</text><view class="total-amount data-v-2f754cba"><text class="total-label data-v-2f754cba">总金额</text><view class="amount-display data-v-2f754cba"><text class="amount-symbol data-v-2f754cba">￥</text><text class="amount-main data-v-2f754cba">{{$root.g8}}</text><text class="amount-decimal data-v-2f754cba">{{$root.g9}}</text></view></view></view><view data-event-opts="{{[['tap',[['doSubmit']]]]}}" class="checkout-button data-v-2f754cba" bindtap="__e"><text class="checkout-text data-v-2f754cba">去结算</text></view></view></view><block wx:if="{{!$root.g10}}"><empty vue-id="819668e8-6" isLoading="{{isLoading}}" class="data-v-2f754cba" bind:__l="__l"></empty></block></view>