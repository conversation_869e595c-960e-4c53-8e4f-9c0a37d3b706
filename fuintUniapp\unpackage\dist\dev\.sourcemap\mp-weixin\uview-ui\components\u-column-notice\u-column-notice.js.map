{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-column-notice/u-column-notice.vue?56be", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-column-notice/u-column-notice.vue?feec", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-column-notice/u-column-notice.vue?4534", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-column-notice/u-column-notice.vue?c10e", "uni-app:///uview-ui/components/u-column-notice/u-column-notice.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-column-notice/u-column-notice.vue?320a", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-column-notice/u-column-notice.vue?2c33"], "names": ["props", "list", "type", "default", "volumeIcon", "moreIcon", "closeIcon", "autoplay", "color", "bgColor", "direction", "show", "fontSize", "duration", "volumeSize", "speed", "isCircular", "mode", "playState", "disable<PERSON><PERSON>ch", "padding", "computed", "computeColor", "textStyle", "style", "vertical", "computeBgColor", "data", "methods", "click", "close", "getMore", "change"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,kpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCkCvrB;EACAA;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;EACA;EACAkB;IACA;IACAC;MACA;MACA;MAAA,KACA,+CACA;IACA;IACA;IACAC;MACA;MACA,8CACA;MACAC;MACA;IACA;IACA;IACAC;MACA,iDACA;IACA;IACA;IACAC;MACA,2CACA;IACA;EACA;EACAC;IACA;MACA;IAAA,CACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC3LA;AAAA;AAAA;AAAA;AAAkxC,CAAgB,6qCAAG,EAAC,C;;;;;;;;;;;ACAtyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-column-notice/u-column-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-column-notice.vue?vue&type=template&id=475fdbf0&scoped=true&\"\nvar renderjs\nimport script from \"./u-column-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./u-column-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-column-notice.vue?vue&type=style&index=0&id=475fdbf0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"475fdbf0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-column-notice/u-column-notice.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-column-notice.vue?vue&type=template&id=475fdbf0&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.textStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-column-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-column-notice.vue?vue&type=script&lang=js&\"", "<template>\n    <view\n        class=\"u-notice-bar\"\n        :style=\"{\n            background: computeBgColor,\n            padding: padding\n        }\"\n        :class=\"[\n            type ? `u-type-${type}-light-bg` : ''\n        ]\"\n    >\n        <view class=\"u-icon-wrap\">\n            <u-icon class=\"u-left-icon\" v-if=\"volumeIcon\" name=\"volume-fill\" :size=\"volumeSize\" :color=\"computeColor\"></u-icon>\n        </view>\n        <swiper :disable-touch=\"disableTouch\" @change=\"change\" :autoplay=\"autoplay && playState == 'play'\" :vertical=\"vertical\" circular :interval=\"duration\" class=\"u-swiper\">\n            <swiper-item v-for=\"(item, index) in list\" :key=\"index\" class=\"u-swiper-item\">\n                <view\n                    class=\"u-news-item u-line-1\"\n                    :style=\"[textStyle]\"\n                    @tap=\"click(index)\"\n                    :class=\"['u-type-' + type]\"\n                >\n                    {{ item }}\n                </view>\n            </swiper-item>\n        </swiper>\n        <view class=\"u-icon-wrap\">\n            <u-icon @click=\"getMore\" class=\"u-right-icon\" v-if=\"moreIcon\" name=\"arrow-right\" :size=\"26\" :color=\"computeColor\"></u-icon>\n            <u-icon @click=\"close\" class=\"u-right-icon\" v-if=\"closeIcon\" name=\"close\" :size=\"24\" :color=\"computeColor\"></u-icon>\n        </view>\n    </view>\n</template>\n\n<script>\nexport default {\n    props: {\n        // 显示的内容，数组\n        list: {\n            type: Array,\n            default() {\n                return [];\n            }\n        },\n        // 显示的主题，success|error|primary|info|warning\n        type: {\n            type: String,\n            default: 'warning'\n        },\n        // 是否显示左侧的音量图标\n        volumeIcon: {\n            type: Boolean,\n            default: true\n        },\n        // 是否显示右侧的右箭头图标\n        moreIcon: {\n            type: Boolean,\n            default: false\n        },\n        // 是否显示右侧的关闭图标\n        closeIcon: {\n            type: Boolean,\n            default: false\n        },\n        // 是否自动播放\n        autoplay: {\n            type: Boolean,\n            default: true\n        },\n        // 文字颜色，各图标也会使用文字颜色\n        color: {\n            type: String,\n            default: ''\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: ''\n        },\n        // 滚动方向，row-水平滚动，column-垂直滚动\n        direction: {\n            type: String,\n            default: 'row'\n        },\n        // 是否显示\n        show: {\n            type: Boolean,\n            default: true\n        },\n        // 字体大小，单位rpx\n        fontSize: {\n            type: [Number, String],\n            default: 26\n        },\n        // 滚动一个周期的时间长，单位ms\n        duration: {\n            type: [Number, String],\n            default: 2000\n        },\n        // 音量喇叭的大小\n        volumeSize: {\n            type: [Number, String],\n            default: 34\n        },\n        // 水平滚动时的滚动速度，即每秒滚动多少rpx，这有利于控制文字无论多少时，都能有一个恒定的速度\n        speed: {\n            type: Number,\n            default: 160\n        },\n        // 水平滚动时，是否采用衔接形式滚动\n        isCircular: {\n            type: Boolean,\n            default: true\n        },\n        // 滚动方向，horizontal-水平滚动，vertical-垂直滚动\n        mode: {\n            type: String,\n            default: 'horizontal'\n        },\n        // 播放状态，play-播放，paused-暂停\n        playState: {\n            type: String,\n            default: 'play'\n        },\n        // 是否禁止用手滑动切换\n        // 目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序\n        disableTouch: {\n            type: Boolean,\n            default: true\n        },\n        // 通知的边距\n        padding: {\n            type: [Number, String],\n            default: '18rpx 24rpx'\n        }\n    },\n    computed: {\n        // 计算字体颜色，如果没有自定义的，就用uview主题颜色\n        computeColor() {\n            if (this.color) return this.color;\n            // 如果是无主题，就默认使用content-color\n            else if(this.type == 'none') return '#606266';\n            else return this.type;\n        },\n        // 文字内容的样式\n        textStyle() {\n            let style = {};\n            if (this.color) style.color = this.color;\n            else if(this.type == 'none') style.color = '#606266';\n            style.fontSize = this.fontSize + 'rpx';\n            return style;\n        },\n        // 垂直或者水平滚动\n        vertical() {\n            if(this.mode == 'horizontal') return false;\n            else return true;\n        },\n        // 计算背景颜色\n        computeBgColor() {\n            if (this.bgColor) return this.bgColor;\n            else if(this.type == 'none') return 'transparent';\n        }\n    },\n    data() {\n        return {\n            // animation: false\n        };\n    },\n    methods: {\n        // 点击通告栏\n        click(index) {\n            this.$emit('click', index);\n        },\n        // 点击关闭按钮\n        close() {\n            this.$emit('close');\n        },\n        // 点击更多箭头按钮\n        getMore() {\n            this.$emit('getMore');\n        },\n        change(e) {\n            let index = e.detail.current;\n            if(index == this.list.length - 1) {\n                this.$emit('end');\n            }\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/style.components.scss\";\n\n.u-notice-bar {\n    width: 100%;\n    @include vue-flex;\n    align-items: center;\n    justify-content: center;\n    flex-wrap: nowrap;\n    padding: 18rpx 24rpx;\n    overflow: hidden;\n}\n\n.u-swiper {\n    font-size: 26rpx;\n    height: 32rpx;\n    @include vue-flex;\n    align-items: center;\n    flex: 1;\n    margin-left: 12rpx;\n}\n\n.u-swiper-item {\n    @include vue-flex;\n    align-items: center;\n    overflow: hidden;\n}\n\n.u-news-item {\n    overflow: hidden;\n}\n\n.u-right-icon {\n    margin-left: 12rpx;\n    /* #ifndef APP-NVUE */\n    display: inline-flex;        \n    /* #endif */\n    align-items: center;\n}\n\n.u-left-icon {\n    /* #ifndef APP-NVUE */\n    display: inline-flex;        \n    /* #endif */\n    align-items: center;\n}\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-column-notice.vue?vue&type=style&index=0&id=475fdbf0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-column-notice.vue?vue&type=style&index=0&id=475fdbf0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426958\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}