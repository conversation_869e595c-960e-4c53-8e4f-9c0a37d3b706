<view class="data-v-5fc0cfe7"><goods-package-popup vue-id="cf4ca402-1" value="{{value}}" selectedSkus="{{selectedSkus}}" border-radius="20" goods="{{goods}}" mode="{{2}}" defaultPrice="{{gradeInfo.grade>1&&goods.gradePrice>0?goods.gradePrice:goods.price}}" gradeInfo="{{gradeInfo}}" defaultStock="{{goods.stock}}" maskCloseAble="{{true}}" data-event-opts="{{[['^input',[['onChangeValue']]],['^open',[['openPackagePopup']]],['^close',[['closePackagePopup']]],['^addCart',[['addCart']]],['^buyNow',[['buyNow']]],['^showSkuPopup',[['showSkuPopup']]]]}}" bind:input="__e" bind:open="__e" bind:close="__e" bind:addCart="__e" bind:buyNow="__e" bind:showSkuPopup="__e" class="data-v-5fc0cfe7" bind:__l="__l"></goods-package-popup><block wx:if="{{isShowSkuPopup}}"><sku-popup vue-id="cf4ca402-2" skuMode="{{5}}" goodsId="{{currentGoods?currentGoods.id:''}}" goods="{{currentGoods}}" gradeInfo="{{gradeInfo}}" value="{{isShowSkuPopup}}" data-event-opts="{{[['^confirm',[['onConfirmSku']]],['^input',[['__set_model',['','isShowSkuPopup','$event',[]]],['onSkuPopupInput']]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-5fc0cfe7" bind:__l="__l"></sku-popup></block></view>