{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/index.vue?9719", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/index.vue?3cda", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/index.vue?1558", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/index.vue?b302", "uni-app:///components/page/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/index.vue?026f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/index.vue?a800"], "names": ["name", "components", "Search", "Images", "Banner", "Window", "Videos", "Article", "Notice", "NavBar", "Goods", "Service", "Blank", "Guide", "RichText", "props", "items", "type", "default"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC0E9pB;EACAA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACA;AACA;AACA;AACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAqtC,CAAgB,2oCAAG,EAAC,C;;;;;;;;;;;ACAzuC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/page/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=5d79f066&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/page/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=5d79f066&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page-items\">\n    <block v-for=\"(item, index) in items\" :key=\"index\">\n      <!-- 搜索框 -->\n      <block v-if=\"item.type === 'search'\">\n        <Search :itemStyle=\"item.style\" :params=\"item.params\" />\n      </block>\n      <!-- 图片组 -->\n      <block v-if=\"item.type === 'image'\">\n        <Images :itemStyle=\"item.style\" :params=\"item.params\" :dataList=\"item.data\" />\n      </block>\n      <!-- 轮播图 -->\n      <block v-if=\"item.type === 'banner'\">\n        <Banner :itemStyle=\"item.style\" :params=\"item.params\" :dataList=\"item.data\" />\n      </block>\n      <!-- 图片橱窗 -->\n      <block v-if=\"item.type === 'window'\">\n        <Window :itemStyle=\"item.style\" :params=\"item.params\" :dataList=\"item.data\" />\n      </block>\n      <!-- 视频 -->\n      <block v-if=\"item.type === 'video'\">\n        <Videos :itemStyle=\"item.style\" :params=\"item.params\" />\n      </block>\n      <!-- 文章组 -->\n      <block v-if=\"item.type === 'article'\">\n        <Article :params=\"item.params\" :dataList=\"item.data\" />\n      </block>\n      <!-- 店铺公告 -->\n      <block v-if=\"item.type === 'notice'\">\n        <Notice :itemStyle=\"item.style\" :params=\"item.params\" />\n      </block>\n      <!-- 导航 -->\n      <block v-if=\"item.type === 'navBar'\">\n        <NavBar :itemStyle=\"item.style\" :params=\"item.params\" :dataList=\"item.data\" />\n      </block>\n      <!-- 商品 -->\n      <block v-if=\"item.type === 'goods'\">\n        <Goods :itemStyle=\"item.style\" :params=\"item.params\" :dataList=\"item.data\" />\n      </block>\n      <!-- 在线客服 -->\n      <block v-if=\"item.type === 'service'\">\n        <Service :itemStyle=\"item.style\" :params=\"item.params\" />\n      </block>\n      <!-- 辅助空白 -->\n      <block v-if=\"item.type === 'blank'\">\n        <Blank :itemStyle=\"item.style\" />\n      </block>\n      <!-- 辅助线 -->\n      <block v-if=\"item.type === 'guide'\">\n        <Guide :itemStyle=\"item.style\" />\n      </block>\n      <!-- 富文本 -->\n      <block v-if=\"item.type === 'richText'\">\n        <RichText :itemStyle=\"item.style\" :params=\"item.params\" />\n      </block>\n    </block>\n  </view>\n</template>\n\n<script>\n  import Search from './search'\n  import Images from './image'\n  import Banner from './banner'\n  import Window from './window'\n  import Videos from './video'\n  import Article from './article'\n  import Notice from './notice'\n  import NavBar from './navBar'\n  import Goods from './goods'\n  import Service from './service'\n  import Blank from './blank'\n  import Guide from './guide'\n  import RichText from './richText'\n\n  export default {\n    name: \"Page\",\n    components: {\n      Search,\n      Images,\n      Banner,\n      Window,\n      Videos,\n      Article,\n      Notice,\n      NavBar,\n      Goods,\n      Service,\n      Blank,\n      Guide,\n      RichText\n    },\n    /**\n     * 组件的属性列表\n     * 用于组件自定义设置\n     */\n    props: {\n      items: {\n        type: Array,\n        default () {\n          return []\n        }\n      }\n    },\n  }\n</script>\n<style lang=\"scss\">\n  // 组件样式\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426012\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}