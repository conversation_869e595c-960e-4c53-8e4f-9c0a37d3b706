{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?2ec1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?57e8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?37e4", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?ba85", "uni-app:///pages/address/update.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?11cb", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?8fa1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?6c88", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/address/update.vue?a990"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "required", "message", "trigger", "phone", "validator", "region", "type", "detail", "components", "SelectRegion", "data", "form", "rules", "isLoading", "disabled", "addressId", "onLoad", "onReady", "methods", "getDetail", "AddressApi", "then", "app", "createFormData", "createRegion", "label", "value", "handleSubmit", "uni", "finally"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACa;AACyB;;;AAG3F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,4QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgC/pB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAH;IACAC;IACAC;EACA;IACA;IACAE;MACA;MACA;IACA;IACAH;IACA;IACAC;EACA;EACAG;IACAL;IACAC;IACAC;IACAI;EACA;EACAC;IACAP;IACAC;IACAC;EACA;AACA;AAAA,eAEA;EACAM;IACAC;EACA;EACAC;IACA;MACAC;QACAZ;QACAI;QACAE;QACAE;MACA;MACAK;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA;IACA;IACA;EACA;EAEA;EACAC;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC,iCACAC;QACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACAZ;MACAA;MACAA;MACAA;IACA;IAEAa;MACA;QACAC;QACAC;MACA;QACAD;QACAC;MACA;QACAD;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACAL;QACA;UACAA;UACAF,kKACAC;YACAC;YACAM;UACA,GACAC;YAAA;UAAA;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAA86B,CAAgB,w4BAAG,EAAC,C;;;;;;;;;;;ACAl8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA8uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAlwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/address/update.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/address/update.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./update.vue?vue&type=template&id=483ea1fd&scoped=true&\"\nvar renderjs\nimport script from \"./update.vue?vue&type=script&lang=js&\"\nexport * from \"./update.vue?vue&type=script&lang=js&\"\nimport style0 from \"./update.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./update.vue?vue&type=style&index=1&id=483ea1fd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"483ea1fd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/address/update.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./update.vue?vue&type=template&id=483ea1fd&scoped=true&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-form/u-form\" */ \"@/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-form-item/u-form-item\" */ \"@/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    selectRegion: function () {\n      return import(\n        /* webpackChunkName: \"components/select-region/select-region\" */ \"@/components/select-region/select-region.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./update.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./update.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 标题 -->\n    <view class=\"page-title\">收货地址</view>\n    <!-- 表单组件 -->\n    <view class=\"form-wrapper\">\n      <u-form :model=\"form\" ref=\"uForm\" label-width=\"140rpx\">\n        <u-form-item label=\"姓名\" prop=\"name\">\n          <u-input v-model=\"form.name\" placeholder=\"请输入收货人姓名\" />\n        </u-form-item>\n        <u-form-item label=\"电话\" prop=\"phone\">\n          <u-input v-model=\"form.phone\" placeholder=\"请输入收货人手机号\" />\n        </u-form-item>\n        <u-form-item label=\"地区\" prop=\"region\">\n          <select-region v-model=\"form.region\" />\n        </u-form-item>\n        <u-form-item label=\"详细地址\" prop=\"detail\" :border-bottom=\"false\">\n          <u-input v-model=\"form.detail\" placeholder=\"街道门牌、楼层等信息\" />\n        </u-form-item>\n      </u-form>\n    </view>\n    <!-- 操作按钮 -->\n    <view class=\"footer\">\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" :class=\"{ disabled }\" @click=\"handleSubmit()\">保存</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import SelectRegion from '@/components/select-region/select-region'\n  import { isMobile } from '@/utils/verify'\n  import * as AddressApi from '@/api/address'\n\n  // 表单验证规则\n  const rules = {\n    name: [{\n      required: true,\n      message: '请输入姓名',\n      trigger: ['blur', 'change']\n    }],\n    phone: [{\n      required: true,\n      message: '请输入手机号',\n      trigger: ['blur', 'change']\n    }, {\n      // 自定义验证函数\n      validator: (rule, value, callback) => {\n        // 返回true表示校验通过，返回false表示不通过\n        return isMobile(value)\n      },\n      message: '手机号码不正确',\n      // 触发器可以同时用blur和change\n      trigger: ['blur'],\n    }],\n    region: [{\n      required: true,\n      message: '请选择省市区',\n      trigger: ['blur', 'change'],\n      type: 'array'\n    }],\n    detail: [{\n      required: true,\n      message: '请输入详细地址',\n      trigger: ['blur', 'change']\n    }],\n  }\n\n  export default {\n    components: {\n      SelectRegion\n    },\n    data() {\n      return {\n        form: {\n          name: '',\n          phone: '',\n          region: [],\n          detail: ''\n        },\n        rules,\n        // 加载中\n        isLoading: true,\n        // 按钮禁用\n        disabled: false,\n        // 当前收货地址ID\n        addressId: null\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad({ addressId }) {\n      // 当前收货地址ID\n      this.addressId = addressId\n      // 获取当前记录详情\n      this.getDetail()\n    },\n\n    // 必须要在onReady生命周期，因为onLoad生命周期组件可能尚未创建完毕\n    onReady() {\n      this.$refs.uForm.setRules(this.rules)\n    },\n\n    methods: {\n\n      // 获取当前记录详情\n      getDetail() {\n        const app = this\n        AddressApi.detail(app.addressId)\n          .then(result => {\n            const detail = result.data.address\n            app.createFormData(detail)\n          })\n      },\n\n      // 生成默认的表单数据\n      createFormData(detail) {\n        const { form } = this\n        form.name = detail.name\n        form.phone = detail.mobile\n        form.detail = detail.detail\n        form.region = this.createRegion(detail)\n      },\n\n      createRegion(detail) {\n        return [{\n          label: detail.provinceName,\n          value: detail.provinceId\n        }, {\n          label: detail.cityName,\n          value: detail.cityId\n        }, {\n          label: detail.regionName,\n          value: detail.regionId\n        }]\n      },\n\n      // 表单提交\n      handleSubmit() {\n        const app = this\n        if (app.disabled) {\n          return false\n        }\n        app.$refs.uForm.validate(valid => {\n          if (valid) {\n            app.disabled = true\n            AddressApi.save(app.form.name, app.form.phone, app.form.region[0].value, app.form.region[1].value, app.form.region[2].value, app.form.detail, 'A', app.addressId)\n              .then(result => {\n                app.$toast(result.message)\n                uni.navigateBack()\n              })\n              .finally(() => app.disabled = false)\n          }\n        })\n      }\n\n    }\n  }\n</script>\n\n<style>\n  page {\n    background: #f7f8fa;\n  }\n</style>\n<style lang=\"scss\" scoped>\n  .page-title {\n    width: 94%;\n    margin: 0 auto;\n    padding-top: 40rpx;\n    font-size: 28rpx;\n    color: rgba(69, 90, 100, 0.6);\n  }\n\n  .form-wrapper {\n    margin: 20rpx auto 20rpx auto;\n    padding: 0 40rpx;\n    width: 94%;\n    box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);\n    border-radius: 16rpx;\n    background: #fff;\n  }\n\n  /* 底部操作栏 */\n\n  .footer {\n    margin-top: 60rpx;\n\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      padding: 0 20rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n\n      // 禁用按钮\n      &.disabled {\n        background: #ff9779;\n      }\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./update.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./update.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891418486\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./update.vue?vue&type=style&index=1&id=483ea1fd&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./update.vue?vue&type=style&index=1&id=483ea1fd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420517\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}