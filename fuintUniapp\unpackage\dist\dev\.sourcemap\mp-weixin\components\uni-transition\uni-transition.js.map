{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/uni-transition/uni-transition.vue?24da", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/uni-transition/uni-transition.vue?e564", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/uni-transition/uni-transition.vue?fa3c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/uni-transition/uni-transition.vue?c5ff", "uni-app:///components/uni-transition/uni-transition.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/uni-transition/uni-transition.vue?ff44", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/uni-transition/uni-transition.vue?87f6"], "names": ["name", "props", "show", "type", "default", "modeClass", "duration", "styles", "data", "isShow", "transform", "ani", "in", "active", "watch", "handler", "immediate", "computed", "stylesObject", "transfrom", "created", "methods", "change", "detail", "open", "clearTimeout", "setTimeout", "close", "_animation", "getTranfrom", "_modeClassArr", "mode", "modestr", "toLine"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmpB,CAAgB,ipBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWvqB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,gBAgBA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;QACA;MACA;IACA;EACA;EACAI;IACA;MACAC;MACAC;MACAC;QAAAC;QACAC;MACA;IACA;EACA;EACAC;IACAZ;MACAa;QACA;UACA;QACA;UACA;QACA;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA,6CACA;QACA;MAAA,EACA;MACA;MACA;QACA;QACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACA;EACAC;IACAC;MACA;QACAC;MACA;IACA;IACAC;MAAA;MACAC;MACA;MACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACAC;UACA;QACA;MACA;IAEA;IACAC;MACAF;MACA;IACA;IACAG;MAAA;MACA;MAmBA;MACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;QACA;UACA;QACA;QACA;UACAL;QACA;MAEA;IAGA;IACAM;MACA;QACAnB;MACA;MACA;QACA;UACA;YACAH;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;UACA;YACAA;YACA;QAAA;MAEA;MACA;IACA;IACAuB;MACA;MACA;QACA;QACAC;UACAC;QACA;QACA;MACA;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACtNA;AAAA;AAAA;AAAA;AAAs7B,CAAgB,g5BAAG,EAAC,C;;;;;;;;;;;ACA18B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/uni-transition/uni-transition.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-transition.vue?vue&type=template&id=cce16df8&\"\nvar renderjs\nimport script from \"./uni-transition.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-transition.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-transition.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/uni-transition/uni-transition.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=template&id=cce16df8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=script&lang=js&\"", "<template>\n    <view v-if=\"isShow\" ref=\"ani\" class=\"uni-transition\" :class=\"[ani.in]\" :style=\"'transform:' +transform+';'+stylesObject\"\n     @click=\"change\">\n         <slot></slot>\n    </view>\n</template>\n\n<script>\n    // #ifdef APP-NVUE\n    const animation = uni.requireNativePlugin('animation');\n    // #endif\n    /**\n     * Transition 过渡动画\n     * @description 简单过渡动画组件\n     * @tutorial https://ext.dcloud.net.cn/plugin?id=985\n     * @property {Boolean} show = [false|true] 控制组件显示或隐藏\n     * @property {Array} modeClass = [fade|slide-top|slide-right|slide-bottom|slide-left|zoom-in|zoom-out] 过渡动画类型\n     *  @value fade 渐隐渐出过渡\n     *  @value slide-top 由上至下过渡\n     *  @value slide-right 由右至左过渡\n     *  @value slide-bottom 由下至上过渡\n     *  @value slide-left 由左至右过渡\n     *  @value zoom-in 由小到大过渡\n     *  @value zoom-out 由大到小过渡\n     * @property {Number} duration 过渡动画持续时间\n     * @property {Object} styles 组件样式，同 css 样式，注意带’-‘连接符的属性需要使用小驼峰写法如：`backgroundColor:red`\n     */\n    export default {\n        name: 'uniTransition',\n        props: {\n            show: {\n                type: Boolean,\n                default: false\n            },\n            modeClass: {\n                type: Array,\n                default () {\n                    return []\n                }\n            },\n            duration: {\n                type: Number,\n                default: 300\n            },\n            styles: {\n                type: Object,\n                default () {\n                    return {}\n                }\n            }\n        },\n        data() {\n            return {\n                isShow: false,\n                transform: '',\n                ani: { in: '',\n                    active: ''\n                }\n            };\n        },\n        watch: {\n            show: {\n                handler(newVal) {\n                    if (newVal) {\n                        this.open()\n                    } else {\n                        this.close()\n                    }\n                },\n                immediate: true\n            }\n        },\n        computed: {\n            stylesObject() {\n                let styles = {\n                    ...this.styles,\n                    'transition-duration': this.duration / 1000 + 's'\n                }\n                let transfrom = ''\n                for (let i in styles) {\n                    let line = this.toLine(i)\n                    transfrom += line + ':' + styles[i] + ';'\n                }\n                return transfrom\n            }\n        },\n        created() {\n            // this.timer = null\n            // this.nextTick = (time = 50) => new Promise(resolve => {\n            //     clearTimeout(this.timer)\n            //     this.timer = setTimeout(resolve, time)\n            //     return this.timer\n            // });\n        },\n        methods: {\n            change() {\n                this.$emit('click', {\n                    detail: this.isShow\n                })\n            },\n            open() {\n                clearTimeout(this.timer)\n                this.isShow = true\n                this.transform = ''\n                this.ani.in = ''\n                for (let i in this.getTranfrom(false)) {\n                    if (i === 'opacity') {\n                        this.ani.in = 'fade-in'\n                    } else {\n                        this.transform += `${this.getTranfrom(false)[i]} `\n                    }\n                }\n                this.$nextTick(() => {\n                    setTimeout(() => {\n                        this._animation(true)\n                    }, 50)\n                })\n\n            },\n            close(type) {\n                clearTimeout(this.timer)\n                this._animation(false)\n            },\n            _animation(type) {\n                let styles = this.getTranfrom(type)\n                // #ifdef APP-NVUE\n                if(!this.$refs['ani']) return\n                animation.transition(this.$refs['ani'].ref, {\n                    styles,\n                    duration: this.duration, //ms\n                    timingFunction: 'ease',\n                    needLayout: false,\n                    delay: 0 //ms\n                }, () => {\n                    if (!type) {\n                        this.isShow = false\n                    }\n                    this.$emit('change', {\n                        detail: this.isShow\n                    })\n                })\n                // #endif\n                // #ifndef APP-NVUE\n                this.transform = ''\n                for (let i in styles) {\n                    if (i === 'opacity') {\n                        this.ani.in = `fade-${type?'out':'in'}`\n                    } else {\n                        this.transform += `${styles[i]} `\n                    }\n                }\n                this.timer = setTimeout(() => {\n                    if (!type) {\n                        this.isShow = false\n                    }\n                    this.$emit('change', {\n                        detail: this.isShow\n                    })\n\n                }, this.duration)\n                // #endif\n\n            },\n            getTranfrom(type) {\n                let styles = {\n                    transform: ''\n                }\n                this.modeClass.forEach((mode) => {\n                    switch (mode) {\n                        case 'fade':\n                            styles.opacity = type ? 1 : 0\n                            break;\n                        case 'slide-top':\n                            styles.transform += `translateY(${type?'0':'-100%'}) `\n                            break;\n                        case 'slide-right':\n                            styles.transform += `translateX(${type?'0':'100%'}) `\n                            break;\n                        case 'slide-bottom':\n                            styles.transform += `translateY(${type?'0':'100%'}) `\n                            break;\n                        case 'slide-left':\n                            styles.transform += `translateX(${type?'0':'-100%'}) `\n                            break;\n                        case 'zoom-in':\n                            styles.transform += `scale(${type?1:0.8}) `\n                            break;\n                        case 'zoom-out':\n                            styles.transform += `scale(${type?1:1.2}) `\n                            break;\n                    }\n                })\n                return styles\n            },\n            _modeClassArr(type) {\n                let mode = this.modeClass\n                if (typeof(mode) !== \"string\") {\n                    let modestr = ''\n                    mode.forEach((item) => {\n                        modestr += (item + '-' + type + ',')\n                    })\n                    return modestr.substr(0, modestr.length - 1)\n                } else {\n                    return mode + '-' + type\n                }\n            },\n            // getEl(el) {\n            //     console.log(el || el.ref || null);\n            //     return el || el.ref || null\n            // },\n            toLine(name) {\n                return name.replace(/([A-Z])/g, \"-$1\").toLowerCase();\n            }\n        }\n    }\n</script>\n\n<style>\n    .uni-transition {\n        transition-timing-function: ease;\n        transition-duration: 0.3s;\n        transition-property: transform, opacity;\n    }\n\n    .fade-in {\n        opacity: 0;\n    }\n\n    .fade-active {\n        opacity: 1;\n    }\n\n    .slide-top-in {\n        /* transition-property: transform, opacity; */\n        transform: translateY(-100%);\n    }\n\n    .slide-top-active {\n        transform: translateY(0);\n        /* opacity: 1; */\n    }\n\n    .slide-right-in {\n        transform: translateX(100%);\n    }\n\n    .slide-right-active {\n        transform: translateX(0);\n    }\n\n    .slide-bottom-in {\n        transform: translateY(100%);\n    }\n\n    .slide-bottom-active {\n        transform: translateY(0);\n    }\n\n    .slide-left-in {\n        transform: translateX(-100%);\n    }\n\n    .slide-left-active {\n        transform: translateX(0);\n        opacity: 1;\n    }\n\n    .zoom-in-in {\n        transform: scale(0.8);\n    }\n\n    .zoom-out-active {\n        transform: scale(1);\n    }\n\n    .zoom-out-in {\n        transform: scale(1.2);\n    }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-transition.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891422861\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}