{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/level.vue?b747", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/level.vue?684d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/level.vue?f77e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/level.vue?660a", "uni-app:///pages/user/level.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/level.vue?0797", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/level.vue?b034"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userYearlyPaymentAmount", "gradeInfo", "memberGrade", "progressPercent", "onLoad", "methods", "getUserInfo", "UserApi", "then", "catch", "console", "goBack", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6B9pB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACAC,eACAC;QACA;UACA;UACA;UACA;;UAEA;UACA;YACA;YACA;UACA;QACA;MACA,GACAC;QACAC;MACA;IACA;IAEA;IACAC;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/level.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/level.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./level.vue?vue&type=template&id=78a9bd0e&scoped=true&\"\nvar renderjs\nimport script from \"./level.vue?vue&type=script&lang=js&\"\nexport * from \"./level.vue?vue&type=script&lang=js&\"\nimport style0 from \"./level.vue?vue&type=style&index=0&id=78a9bd0e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"78a9bd0e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/level.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./level.vue?vue&type=template&id=78a9bd0e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./level.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./level.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"level-detail-container\">\n    <!-- 会员等级背景图 -->\n    <view class=\"level-bg-section\">\n      <image class=\"level-bg-image\" src=\"https://hatea.zhijuchina.com/static/level_bg.png\" mode=\"aspectFill\"></image>\n      \n      <!-- 当前会员等级信息覆盖层 -->\n      <view class=\"level-info-overlay\">\n        <view class=\"current-level-badge\">\n          <text class=\"level-name\">{{ gradeInfo && gradeInfo.name ? gradeInfo.name : '普通会员' }}</text>\n        </view>\n         \n      </view>\n    </view>\n\n    <!-- 会员权益图片 -->\n    <view class=\"benefits-section\">\n      <image class=\"benefits-image\" src=\"https://hatea.zhijuchina.com/static/quanyi.png\" mode=\"aspectFill\"></image>\n    </view>\n \n\n    <!-- 返回按钮 -->\n    <view class=\"back-button\" @click=\"goBack\">\n      <text class=\"back-text\">返回</text>\n    </view>\n  </view>\n</template>\n\n<script>\nimport * as UserApi from '@/api/user'\n\nexport default {\n  data() {\n    return {\n      userYearlyPaymentAmount: 0,\n      gradeInfo: {},\n      memberGrade: [],\n      progressPercent: 0\n    }\n  },\n  \n  onLoad() {\n    this.getUserInfo()\n  },\n  \n  methods: {\n    // 获取用户信息\n    getUserInfo() {\n      UserApi.info()\n        .then(result => {\n          if (result.data) {\n            this.userYearlyPaymentAmount = result.data.userYearlyPaymentAmount || 0\n            this.gradeInfo = result.data.gradeInfo || {}\n            this.memberGrade = result.data.memberGrade || []\n            \n            // 计算进度百分比\n            if (this.memberGrade[0] && this.memberGrade[0].catchValue) {\n              this.progressPercent = (this.userYearlyPaymentAmount / this.memberGrade[0].catchValue) * 100\n              if (this.progressPercent > 100) this.progressPercent = 100\n            }\n          }\n        })\n        .catch(err => {\n          console.log('获取用户信息失败', err)\n        })\n    },\n    \n    // 返回上一页\n    goBack() {\n      uni.navigateBack()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.level-detail-container {\n  min-height: 100vh;\n  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);\n}\n\n.level-bg-section {\n  position: relative;\n  height: 400rpx;\n  overflow: hidden;\n}\n\n.level-bg-image {\n  width: 100%;\n  height: 42vw;\n  object-fit: cover;\n}\n\n.level-info-overlay {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0; \n  display: flex;\n  flex-direction: column;\n  justify-content: center; \n  padding: 40rpx;\n}\n\n.current-level-badge {\n   \n  padding: 16rpx 40rpx;\n  margin-bottom: 170rpx;\n}\n\n.level-name {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.level-progress-info {\n  text-align: center;\n  color: #fff;\n}\n\n.progress-label {\n  font-size: 28rpx;\n  display: block;\n  margin-bottom: 16rpx;\n}\n\n.progress-numbers {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 20rpx;\n}\n\n.current-amount {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #ffd700;\n}\n\n.separator {\n  font-size: 28rpx;\n  margin: 0 10rpx;\n}\n\n.target-amount {\n  font-size: 28rpx;\n} \n \n.benefits-image {\n  width: 100vw;\n  object-fit: cover;\n  height: 122vw;\n}\n\n.level-list-section {\n  margin: 40rpx 30rpx;\n}\n\n.section-title {\n  margin-bottom: 30rpx;\n}\n\n.title-text {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.level-items {\n  background: #fff;\n  border-radius: 20rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);\n}\n\n.level-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 30rpx;\n  border-bottom: 1rpx solid #f0f0f0;\n  \n  &:last-child {\n    border-bottom: none;\n  }\n  \n  &.current-level {\n    background: linear-gradient(90deg, #fff7e6 0%, #ffffff 100%);\n  }\n}\n\n.level-info {\n  display: flex;\n  flex-direction: column;\n  gap: 8rpx;\n}\n\n.level-title {\n  font-size: 30rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.level-requirement {\n  font-size: 24rpx;\n  color: #666;\n}\n\n.level-status {\n  .status-current {\n    font-size: 24rpx;\n    color: #ff9500;\n    background: #fff7e6;\n    padding: 8rpx 16rpx;\n    border-radius: 20rpx;\n    font-weight: bold;\n  }\n  \n  .status-achieved {\n    font-size: 24rpx;\n    color: #52c41a;\n    background: #f6ffed;\n    padding: 8rpx 16rpx;\n    border-radius: 20rpx;\n  }\n  \n  .status-pending {\n    font-size: 24rpx;\n    color: #999;\n    background: #f5f5f5;\n    padding: 8rpx 16rpx;\n    border-radius: 20rpx;\n  }\n}\n\n.back-button {\n  position: fixed;\n  bottom: 60rpx;\n  left: 50%;\n  transform: translateX(-50%);\n  background: linear-gradient(90deg, #ff9500 0%, #ffab00 100%);\n  color: white;\n  padding: 24rpx 60rpx;\n  border-radius: 50rpx;\n  box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.3);\n}\n\n.back-text {\n  font-size: 32rpx;\n  font-weight: bold;\n}\n</style>", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./level.vue?vue&type=style&index=0&id=78a9bd0e&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./level.vue?vue&type=style&index=0&id=78a9bd0e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891423692\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}