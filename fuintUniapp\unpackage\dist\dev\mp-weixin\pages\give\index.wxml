<view class="container data-v-3e30f78d"><mescroll-body vue-id="f02309ce-1" sticky="{{true}}" down="{{({native:true})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" class="data-v-3e30f78d vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-tabs vue-id="{{('f02309ce-2')+','+('f02309ce-1')}}" list="{{tabs}}" is-scroll="{{false}}" current="{{curTab}}" active-color="#FA2209" duration="{{0.2}}" data-event-opts="{{[['^change',[['onChangeTab']]]]}}" bind:change="__e" class="data-v-3e30f78d" bind:__l="__l"></u-tabs><view class="widget-list data-v-3e30f78d"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="widget-detail data-v-3e30f78d"><view class="row-block dis-flex flex-y-center data-v-3e30f78d"><view class="flex-box data-v-3e30f78d">{{item.$orig.createTime}}</view><view class="flex-box t-r data-v-3e30f78d"><text class="mobile data-v-3e30f78d">{{item.g0+'****'+item.g1}}</text></view></view><view class="detail-goods row-block dis-flex data-v-3e30f78d"><view class="goods-right flex-box data-v-3e30f78d"><view class="goods-name data-v-3e30f78d"><text class="twolist-hidden data-v-3e30f78d">{{item.$orig.couponNames}}</text></view><view class="goods-props clearfix data-v-3e30f78d"><view class="goods-props-item data-v-3e30f78d"><text class="data-v-3e30f78d">{{"￥"+item.$orig.money}}</text></view></view><view class="goods-num t-r data-v-3e30f78d"><text class="f-26 col-8 data-v-3e30f78d">{{"×"+item.$orig.num}}</text></view></view></view><view class="detail-order row-block data-v-3e30f78d"><view class="item dis-flex flex-x-end flex-y-center data-v-3e30f78d"><text class="data-v-3e30f78d">转赠总金额：</text><text class="col-m data-v-3e30f78d">{{"￥"+item.$orig.money*item.$orig.num}}</text></view></view></view></block></view></mescroll-body></view>