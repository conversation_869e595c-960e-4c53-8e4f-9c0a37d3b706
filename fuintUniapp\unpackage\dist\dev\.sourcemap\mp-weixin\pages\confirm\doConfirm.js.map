{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/confirm/doConfirm.vue?ff2a", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/confirm/doConfirm.vue?0b26", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/confirm/doConfirm.vue?7ed7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/confirm/doConfirm.vue?1758", "uni-app:///pages/confirm/doConfirm.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/confirm/doConfirm.vue?c41c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/confirm/doConfirm.vue?d5bf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Shortcut", "data", "code", "userCouponId", "userCouponCode", "isLoading", "detail", "rowCount", "dataList", "result", "form", "onLoad", "methods", "getCouponDetail", "myCouponApi", "then", "app", "finally", "doConfirm", "confirmApi", "resolve", "getDataList", "c", "getRowCount"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,8OAEN;AACP,KAAK;AACL;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA8oB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwDlqB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;QAAA;QAAA;QAAA;MAAA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC,4DACAC;QACAC;QACAA;QACAA;MACA,GACAC;QAAA;MAAA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;UACAF;UACA;QACA;UACAA;UACA;QACA;MACA;MACA;QACA;MACA;MAEAA;MACAG,wEACAJ;QACA;UACAC;UACA;UACAA;UACAA;UACAA;UACAA;UACAA;QACA;UACA;QACA;QACAA;QACAI;MACA;IACA;IACA;IACAC;MACA;MACA;QACAL;UAAA;QAAA;QACA;UACAA;YAAA;UAAA;QACA;MACA;QACA;QACA;QACA;UACAA;YAAA;UAAA;UACA;YACA;cACAA;gBAAA;cAAA;cACAM;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3KA;AAAA;AAAA;AAAA;AAAivC,CAAgB,uqCAAG,EAAC,C;;;;;;;;;;;ACArwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/confirm/doConfirm.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/confirm/doConfirm.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./doConfirm.vue?vue&type=template&id=8ca7a606&scoped=true&\"\nvar renderjs\nimport script from \"./doConfirm.vue?vue&type=script&lang=js&\"\nexport * from \"./doConfirm.vue?vue&type=script&lang=js&\"\nimport style0 from \"./doConfirm.vue?vue&type=style&index=0&id=8ca7a606&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8ca7a606\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/confirm/doConfirm.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./doConfirm.vue?vue&type=template&id=8ca7a606&scoped=true&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-form/u-form\" */ \"@/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-form-item/u-form-item\" */ \"@/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-input/u-input\" */ \"@/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uniRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-row/components/uni-row/uni-row\" */ \"@/uni_modules/uni-row/components/uni-row/uni-row.vue\"\n      )\n    },\n    uniCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-row/components/uni-col/uni-col\" */ \"@/uni_modules/uni-row/components/uni-col/uni-col.vue\"\n      )\n    },\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./doConfirm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./doConfirm.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"!isLoading\" class=\"container b-f p-b\">\n    <view class=\"base\">\n        <view class=\"coupon-image\">\n            <image class=\"image\" :src=\"detail.image\"></image>\n        </view>\n        <view class=\"coupon-title\">\n          <view class=\"name\">{{ detail.name }}</view>\n          <view v-if=\"detail.amount > 0\" class=\"price\"><span class=\"label\">面额：</span>￥{{ detail.amount }}</view>\n          <view v-if=\"detail.type == 'P'\" class=\"balance\"><span class=\"label\">余额：</span>￥{{ detail.balance }}</view>\n          <view v-if=\"detail.tips\" class=\"tips\">{{ detail.tips }}</view>\n          <view class=\"time\">有效期：{{ detail.effectiveDate }}</view>\n        </view>\n        <view v-if=\"detail.status=='B'\" class=\"icon-can\"></view>\n        <view v-else-if=\"detail.status=='C'\" class=\"icon-cannot\"></view>\n    </view>\n    <view class=\"confirm-form\">\n        <u-form :model=\"form\" label-width=\"140rpx\">\n          <u-form-item class=\"input\" v-if=\"detail.type === 'P'\" label=\"金额:\">\n            <u-input v-model=\"form.amount\" placeholder=\"核销金额\" />\n          </u-form-item>\n          <view v-if=\"detail.type === 'T'\" class=\"coupon-timer\">\n            <view class=\"tips\">完成情况({{detail.confirmCount}}/{{detail.useRule}})</view>\n            <uni-row class=\"time-row\" v-for=\"row in dataList\" :key=\"row.id\">\n                <uni-col :span=\"rowCount\" v-for=\"item in row.data\" :key=\"item.isActive\" class=\"time-item\">\n                    <view v-if=\"item.isActive == true\" class=\"time active\"></view>\n                    <view v-else class=\"time\"></view>\n                </uni-col>\n            </uni-row>\n          </view>\n          <u-form-item class=\"input\" label=\"备注:\" :border-bottom=\"false\">\n            <u-input v-model=\"form.remark\" placeholder=\"核销备注\" />\n          </u-form-item>\n        </u-form>\n    </view>\n    <view class=\"coupon-content m-top20\">\n        <view class=\"title\">使用须知</view>\n        <view class=\"content\"><jyf-parser :html=\"detail.description\"></jyf-parser></view>\n    </view>\n    <view class=\"footer-fixed\">\n      <view v-if=\"detail.status == 'A'\" class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" @click=\"doConfirm()\">确定核销</view>\n      </view>\n      <view v-else-if=\"detail.status == 'B'\" class=\"btn-wrapper\">\n        <view class=\"btn-item cannot\">已经核销</view>\n      </view>\n      <view v-else class=\"btn-wrapper\">\n        <view class=\"btn-item cannot\">不可核销</view>\n      </view>\n    </view>\n    <!-- 快捷导航 -->\n    <shortcut/>\n  </view>\n</template>\n\n<script>\n  import * as myCouponApi from '@/api/myCoupon'\n  import * as confirmApi from '@/api/confirm'\n  import Shortcut from '@/components/merchant-shortcut'\n  \n  export default {\n    components: {\n        Shortcut\n    },\n    data() {\n      return {\n        // 会员会员卡券编码\n        code: null,\n        userCouponId: null,\n        userCouponCode: null,\n        // 加载中\n        isLoading: true,\n        // 当前卡券详情\n        detail: null,\n        rowCount: 0,\n        dataList: [],\n        // 核销结果\n        result: false,\n        form: {'code': '', 'amount': '', 'remark': ''}\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 记录ID\n      this.userCouponId = options.id\n      // 核销二维码\n      this.userCouponCode = options.code\n      // 获取卡券详情\n      this.getCouponDetail()\n    },\n\n    methods: {\n      // 获取卡券详情\n      getCouponDetail() {\n        const app = this\n        myCouponApi.detail(0, app.userCouponId, app.userCouponCode)\n          .then(result => {\n            app.detail = result.data\n            app.getRowCount(app.detail.useRule)\n            app.getDataList(app.detail.useRule, app.detail.confirmCount)\n          })\n          .finally(() => app.isLoading = false)\n      },\n       doConfirm() {\n          const app = this\n          // 储值卡核销金额\n          if (app.detail.type == 'P') {\n              if (app.form.amount <= 0) {\n                app.$error(\"核销金额不能小于0，请输入\");\n                return false\n              } else if (app.form.amount > app.detail.amount) {\n                app.$error(\"核销金额不能大于\" + app.detail.amount + \"，请重新输入\");\n                return false\n              }\n          }\r\n          if (app.isLoading) {\r\n              return false;\r\n          }\n          \n          app.isLoading = true;\n          confirmApi.doConfirm(app.detail.code, app.form.amount, app.form.remark)\n            .then(result => {\n                if (result.data) {\n                    app.getCouponDetail()\n                    this.$success(\"核销成功\")\n                    app.detail.status = result.data.status\n                    app.detail.balance = result.data.balance\n                    app.form.amount = \"\"\n                    app.form.remark = \"\"\n                    app.getCouponDetail()\n                } else {\n                    this.$error(result.message)\n                }\n                app.isLoading = false\n                resolve(result)\n            });\n      },\n      // 组织数据\n      getDataList(num, use) {\n                const app = this\n                if (num <= 4 && num > 0) {\n                    app.dataList = [{\"data\": []}]\n                    for (let i = 1; i <= num; i++) {\n                        app.dataList[0].data.push({\"isActive\": (i <= use ? true : false)})\n                    }\n                } else {\n                    let rowCount = Math.ceil(num / 4)\n                    let c = 1;\n                    for (let i = 0; i < rowCount; i++) {\n                        app.dataList[i] = {\"data\": []}\n                        for (let j = 1; j <= 4; j++) {\n                            if (c <= num) {\n                                 app.dataList[i].data.push({\"isActive\": (c <= use ? true : false)})\n                               c++\n                          }\n                        }\n                    }\n                }\n      },\n      // 计算行数\n      getRowCount(num) {\n         if (num < 4 && num > 0) {\n             this.rowCount = 24 / num\n         } else if (num >= 4) {\n             this.rowCount = 6\n         }\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    min-height: 100vh;\n    background: #fff;\n    color:#666666;\n  }\n  \n  .base {\n      border: dashed 5rpx #cccccc;\n      padding: 30rpx;\n      border-radius: 10rpx;\n      margin: 20rpx;\n      height: 270rpx;\n      .coupon-image {\n          float: left;\n          margin-top: 10rpx;\n          .image {\n              width: 200rpx;\n              height: 158rpx;\n              border-radius: 8rpx;\n          }\n          width: 30%;\n      }\n      .coupon-title {\n          float: left;\n          margin-left: 30rpx;\n          overflow: hidden;\n          width: 65%;\n          .name {\n             font-weight: bold;\n          }\n          .price {\n            margin-top:20rpx;\n            color:#666;\n            font-size: 20rpx;\n            .label {\n                font-size:20rpx;\n            }\n          }\n          .balance {\n              margin-top:20rpx;\n              color:#f9211c;\n              font-size:20rpx;\n          }\n          .tips {\n             margin-top:20rpx;\n             font-size: 20rpx;\n          }\n          .time {\n             margin-top: 10rpx;\n             font-size: 20rpx;\n             color: #666666;\n          }\n      }\n  }\n  .confirm-form {\n    border: dashed 5rpx #cccccc;\n    padding: 30rpx;\n    border-radius: 10rpx;\n    margin: 20rpx;\n    .input {\n        border: solid 1px #ccc;\n        padding-left: 20rpx;\n        margin-bottom: 10rpx;\n        border-radius: 10rpx;\n        width: 98%;\n        display: inline-flex;\n    }\n    .coupon-timer {\n          border-radius: 10rpx;\n          clear: both;\n          overflow: hidden;\n          margin-bottom: 10rpx;\n          height: 100%;\n          .tips {\n              margin-bottom: 60rpx;\n          }\n          .time-row {\n              margin-bottom: 10rpx;\n              height: 100rpx;\n              display: flex;\n          }\n          .time-item {\n              padding-top: 10rpx;\n              text-align: center;\n              align-items: center;\n              justify-content: center;\n          }\n          .time {\n              height: 80rpx;\n              margin-bottom: 30rpx;\n              text-align: center;\n              padding-top: 20rpx;\n              border-radius: 40rpx;\n              color: #ffffff;\n              font-weight: bold;\n              background: url('~@/static/confirm/undo.png') no-repeat center center;\n              background-size: contain;\n          }\n          .active {\n              background: url('~@/static/confirm/do.png') no-repeat center center;\n              background-size: contain;\n              border: solid 1px #ffffff;\n          }\n          min-height: 160rpx;\n    }\n  }\n  .coupon-content {\n    font-size: 28rpx;\n    padding: 15rpx;\n    border: dashed 5rpx #cccccc;\n    border-radius: 5rpx;\n    margin: 20rpx;\n    min-height: 400rpx;\n    .title {\n        margin-bottom: 15rpx;\n    }\n  }\n  \n  .footer-fixed {\n      position: fixed;\n      width: 100%;\n      bottom: var(--window-bottom);\n      height: 180rpx;\r\n      padding-bottom: 30rpx;\n      z-index: 11;\n      margin-top: 20rpx;\n      .btn-wrapper {\n        height: 100%;\n        display: flex;\n        align-items: center;\n        padding: 0 20rpx;\n      .cannot {\n        border-radius: 38rpx;\n        color: #fff;\n        line-height: 80rpx;\n        text-align: center;\n        font-weight: 500;\n        font-size: 28rpx;\n        background:linear-gradient(to right, #cccccc, #cccccc)\n      }\n      }\n  \n      .btn-item {\n        flex: 1;\n        font-size: 28rpx;\n        height: 80rpx;\n        line-height: 80rpx;\n        text-align: center;\n        color: #ffffff;\n        border-radius: 40rpx;\n      }\n  \n      .btn-item-main {\n        background: linear-gradient(to right, #f9211c, #ff6335);\n      }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./doConfirm.vue?vue&type=style&index=0&id=8ca7a606&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./doConfirm.vue?vue&type=style&index=0&id=8ca7a606&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425028\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}