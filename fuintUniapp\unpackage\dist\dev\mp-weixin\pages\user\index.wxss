@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-137d5072 {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding-top: 216rpx;
}
.container-bg-img.data-v-137d5072 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vw;
  object-fit: cover;
  z-index: 1;
}
.new-user-layout.data-v-137d5072 {
  padding: 0 34rpx 0 26rpx;
  position: absolute;
  top: 43vw;
  left: 0;
  width: 100vw;
  z-index: 2;
}
.brand-header.data-v-137d5072 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 56rpx;
}
.brand-slogan.data-v-137d5072 {
  color: #486731;
  font-size: 64rpx;
  font-family: Inter;
  line-height: 59rpx;
  letter-spacing: 6rpx;
  margin-bottom: 34rpx;
}
.brand-name.data-v-137d5072 {
  color: #486731;
  font-size: 48rpx;
  font-family: Inter;
  line-height: 35rpx;
  letter-spacing: 5rpx;
}
.member-card-wrapper.data-v-137d5072 {
  margin-bottom: 20rpx;
}
.member-number-bg.data-v-137d5072 {
  padding: 40rpx 0 16rpx;
  background-color: #cccccc;
  border-radius: 24rpx;
  padding-left: 48rpx;
  margin-bottom: 20rpx;
}
.member-number.data-v-137d5072 {
  font-size: 24rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 22rpx;
  color: #333333;
}
.member-info-card.data-v-137d5072 {
  padding: 44rpx 28rpx 44rpx 44rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);
}
.member-content.data-v-137d5072 {
  padding: 0 6rpx;
}
.user-greeting.data-v-137d5072 {
  font-size: 32rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 30rpx;
  color: #333333;
  margin-bottom: 16rpx;
  text-align: right;
}
.avatar-group.data-v-137d5072 {
  position: absolute;
  right: 60rpx;
  top: 10rpx;
}
.avatar-logo.data-v-137d5072 {
  width: 300rpx;
  height: 220rpx;
}
.avatar-small.data-v-137d5072 {
  width: 72rpx;
  height: 74rpx;
}
.avatar-icon.data-v-137d5072 {
  width: 78rpx;
  height: 72rpx;
}
.member-stats.data-v-137d5072 {
  margin-top: 20rpx;
}
.grade-badge.data-v-137d5072 {
  padding: 8rpx 0;
  background-color: #203274;
  border-radius: 16rpx;
  width: 114rpx;
  text-align: center;
  margin-bottom: 40rpx;
}
.grade-text.data-v-137d5072 {
  color: #ffffff;
  font-size: 20rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 19rpx;
}
.stats-row.data-v-137d5072 {
  display: flex;
  align-items: center;
}
.stat-item.data-v-137d5072 {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 4rpx 0;
}
.stat-number.data-v-137d5072 {
  font-size: 32rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 27rpx;
  color: #333333;
  margin-bottom: 22rpx;
}
.stat-label.data-v-137d5072 {
  font-size: 24rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 22rpx;
  color: #333333;
}
.stats-divider.data-v-137d5072 {
  margin: 0 32rpx 0 20rpx;
  width: 2rpx;
  height: 80rpx;
}
.experience-section.data-v-137d5072 {
  margin-top: 38rpx;
}
.experience-label.data-v-137d5072 {
  display: flex;
  align-items: baseline;
  margin-bottom: 20rpx;
}
.label-text.data-v-137d5072 {
  font-size: 24rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 22rpx;
  color: #333333;
}
.experience-numbers.data-v-137d5072 {
  margin-left: 12rpx;
  line-height: 28rpx;
  height: 28rpx;
}
.current-exp.data-v-137d5072 {
  font-size: 34rpx;
  font-family: FZLanTingHei-DB-GBK;
  color: #333333;
}
.exp-separator.data-v-137d5072 {
  font-size: 30rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 25rpx;
  color: #333333;
}
.max-exp.data-v-137d5072 {
  color: #333333;
  font-size: 28rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 23rpx;
}
.progress-bar.data-v-137d5072 {
  display: flex;
  align-items: center;
}
.progress-track.data-v-137d5072 {
  flex: 1;
  background-color: #d9d9d9;
  border-radius: 198rpx;
  height: 16rpx;
  margin-right: 12rpx;
}
.progress-fill.data-v-137d5072 {
  background-color: #22326e;
  border-radius: 198rpx;
  width: 120rpx;
  height: 16rpx;
}
.progress-max.data-v-137d5072 {
  font-size: 24rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 19rpx;
  color: #333333;
}
.order-section.data-v-137d5072 {
  margin-top: 26rpx;
  padding: 40rpx 0 20rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);
}
.order-grid.data-v-137d5072 {
  display: flex;
  margin: 0 52rpx;
}
.order-item.data-v-137d5072 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8rpx 0;
}
.order-icon.data-v-137d5072 {
  width: 55rpx;
  height: 55rpx;
  margin-bottom: 14rpx;
}
.order-text.data-v-137d5072 {
  font-size: 20rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 19rpx;
  color: #333333;
}
.function-section.data-v-137d5072 {
  margin-top: 26rpx;
  padding: 40rpx 48rpx 20rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);
}
.function-title.data-v-137d5072 {
  font-size: 30rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 28rpx;
  letter-spacing: 3rpx;
  color: #333333;
  margin-bottom: 52rpx;
}
.function-grid.data-v-137d5072 {
  display: flex;
  margin: 0 12rpx;
}
.function-item.data-v-137d5072 {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12rpx 0;
}
.function-icon.data-v-137d5072 {
  width: 55rpx;
  height: 55rpx;
  margin-bottom: 18rpx;
}
.function-text.data-v-137d5072 {
  font-size: 20rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 19rpx;
  color: #333333;
}
.logout-tips.data-v-137d5072 {
  margin: 28rpx 0 0 8rpx;
  color: #999999;
  font-size: 22rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 21rpx;
  letter-spacing: 2rpx;
}
/* 未登录状态样式 */
.login-prompt-card.data-v-137d5072 {
  padding: 60rpx 44rpx;
  background-color: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);
  text-align: center;
}
.login-content.data-v-137d5072 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.login-greeting.data-v-137d5072 {
  font-size: 32rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 30rpx;
  color: #333333;
  margin-bottom: 20rpx;
}
.login-message.data-v-137d5072 {
  font-size: 24rpx;
  font-family: FZLanTingHei-L-GBK;
  line-height: 22rpx;
  color: #666666;
  margin-bottom: 40rpx;
}
.login-button.data-v-137d5072 {
  background-color: #486731;
  border-radius: 30rpx;
  padding: 16rpx 40rpx;
}
.login-btn-text.data-v-137d5072 {
  color: #ffffff;
  font-size: 28rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 26rpx;
}
/* 火炬币样式 */
.torch-coin-item.data-v-137d5072 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.torch-value.data-v-137d5072 {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 18rpx;
}
.torch-amount.data-v-137d5072 {
  font-size: 20rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 19rpx;
  color: #333333;
}
.torch-label.data-v-137d5072 {
  display: flex;
  justify-content: center;
  align-items: center;
}
.torch-text.data-v-137d5072 {
  font-size: 20rpx;
  font-family: FZLanTingHei-DB-GBK;
  line-height: 19rpx;
  color: #333333;
}
