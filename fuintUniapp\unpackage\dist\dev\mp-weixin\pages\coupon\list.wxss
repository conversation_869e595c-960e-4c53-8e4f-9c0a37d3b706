@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.header.data-v-616a857e {
  display: block;
  align-items: center;
  text-align: center;
  height: 103rpx;
  background: #fff;
}
.header .search.data-v-616a857e {
  flex: 1;
}
.header .show-view.data-v-616a857e {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 36rpx;
  color: #505050;
}
.store-sort.data-v-616a857e {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  display: flex;
  padding: 20rpx 0;
  font-size: 28rpx;
  background: #fff;
  color: #000;
  z-index: 99;
}
.store-sort .sort-item.data-v-616a857e {
  flex-basis: 33.3333%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50rpx;
}
.store-sort .sort-item.active.data-v-616a857e {
  color: #e49a3d;
}
.store-sort .sort-item-price .price-arrow.data-v-616a857e {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #000;
}
.store-sort .sort-item-price .price-arrow .icon.active.data-v-616a857e {
  color: #e49a3d;
}
.store-sort .sort-item-price .price-arrow .icon.up.data-v-616a857e {
  margin-bottom: -16rpx;
}
.store-sort .sort-item-price .price-arrow .icon.down.data-v-616a857e {
  margin-top: -16rpx;
}
.goods-list.data-v-616a857e {
  padding: 4rpx;
  box-sizing: border-box;
}
.empty-ipt.data-v-616a857e {
  width: 220rpx;
  margin: 10rpx auto;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  color: #fff;
  border-radius: 5rpx;
  background: linear-gradient(to right, #3f51b5, #3f51b5);
}
.goods-list.column-1 .goods-item.data-v-616a857e {
  width: 100%;
  height: 260rpx;
  margin-bottom: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background: #fff;
  line-height: 1.6;
}
.goods-list.column-1 .goods-item.data-v-616a857e:last-child {
  margin-bottom: 0;
}
.goods-list.column-1 .goods-item_left.data-v-616a857e {
  display: flex;
  width: 35%;
  background: #fff;
  align-items: center;
}
.goods-list.column-1 .goods-item_left .image.data-v-616a857e {
  display: block;
  width: 200rpx;
  height: 157rpx;
  margin-top: 20rpx;
  border-radius: 6rpx;
  border: solid 1rpx #cccccc;
}
.goods-list.column-1 .goods-item_right.data-v-616a857e {
  position: relative;
  flex: 1;
}
.goods-list.column-1 .goods-item_right .goods-name.data-v-616a857e {
  margin-top: 30rpx;
  height: 44rpx;
  line-height: 1.3;
  white-space: normal;
  color: #484848;
  font-size: 30rpx;
}
.goods-list.column-1 .goods-item_desc.data-v-616a857e {
  margin-top: 0rpx;
}
.goods-list.column-1 .goods-item_desc .coupon-attr .attr-l.data-v-616a857e {
  float: left;
  width: 70%;
}
.goods-list.column-1 .goods-item_desc .coupon-attr .attr-r.data-v-616a857e {
  margin-top: 0rpx;
  float: left;
}
.goods-list.column-1 .desc-selling_point.data-v-616a857e {
  width: 400rpx;
  font-size: 24rpx;
  color: #e49a3d;
}
.goods-list.column-1 .receive.data-v-616a857e {
  height: 46rpx;
  width: 128rpx;
  line-height: 46rpx;
  text-align: center;
  border: 1px solid #f8df00;
  border-radius: 5rpx;
  color: #f86d48;
  background: #f8df98;
  font-size: 22rpx;
}
.goods-list.column-1 .receive.state.data-v-616a857e {
  border: none;
  color: #cccccc;
  background: #F5F5F5;
}
.goods-list.column-1 .desc-goods_sales.data-v-616a857e {
  color: #999;
  font-size: 24rpx;
}
.goods-list.column-1 .desc_footer.data-v-616a857e {
  font-size: 24rpx;
}
.goods-list.column-1 .desc_footer .price_x.data-v-616a857e {
  margin-right: 16rpx;
  color: #f03c3c;
  font-size: 30rpx;
}
.goods-list.column-1 .desc_footer .price_y.data-v-616a857e {
  text-decoration: line-through;
}
.goods-item.data-v-616a857e {
  float: left;
  box-sizing: border-box;
  padding: 6rpx;
}
.goods-item .goods-image.data-v-616a857e {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  background: #fff;
}
.goods-item .goods-image.data-v-616a857e:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.goods-item .goods-image .image.data-v-616a857e {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover;
}
.goods-item .detail.data-v-616a857e {
  padding: 8rpx;
  background: #fff;
}
.goods-item .detail .goods-name.data-v-616a857e {
  height: 64rpx;
  line-height: 32rpx;
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
}
.goods-item .detail .detail-price .goods-price.data-v-616a857e {
  margin-right: 8rpx;
}
.goods-item .detail .detail-price .line-price.data-v-616a857e {
  text-decoration: line-through;
}
