<block wx:if="{{!isLoading}}"><view class="container b-f p-b data-v-6927eef9"><view class="base data-v-6927eef9"><view class="coupon-main data-v-6927eef9"><view class="left data-v-6927eef9"><image class="image data-v-6927eef9" src="{{detail.image}}"></image></view><view class="right data-v-6927eef9"><view class="item data-v-6927eef9"><view class="name data-v-6927eef9">{{detail.name?detail.name:''}}</view></view><block wx:if="{{detail.type=='P'}}"><view class="item data-v-6927eef9"><view class="amount data-v-6927eef9">￥<text class="num data-v-6927eef9">{{detail.balance}}</text></view></view></block></view></view><block wx:if="{{detail.amount>0}}"><view class="item data-v-6927eef9"><view class="label data-v-6927eef9">卡券面额：</view><view class="amount data-v-6927eef9">{{"￥"+detail.amount}}</view></view></block><view class="item data-v-6927eef9"><view class="label data-v-6927eef9">有效期至：</view><view class="data-v-6927eef9">{{detail.effectiveDate}}</view></view><block wx:if="{{detail.code&&detail.status=='A'&&detail.isGive}}"><view data-event-opts="{{[['tap',[['give']]]]}}" class="gift data-v-6927eef9" bindtap="__e"><text class="data-v-6927eef9">转赠好友</text></view></block></view><view class="coupon-qr data-v-6927eef9"><view class="data-v-6927eef9"><image class="image data-v-6927eef9" src="{{detail.qrCode}}"></image></view><view class="qr-code data-v-6927eef9"><view class="code _p data-v-6927eef9">{{"卡号："+detail.code}}</view><view class="tips _p data-v-6927eef9">请出示以上卡号给核销人员</view></view></view><view class="coupon-content m-top20 data-v-6927eef9"><view class="title data-v-6927eef9">使用须知</view><view class="content data-v-6927eef9"><jyf-parser vue-id="88531da6-1" html="{{detail.description?detail.description:'暂无...'}}" class="data-v-6927eef9" bind:__l="__l"></jyf-parser></view></view><shortcut vue-id="88531da6-2" class="data-v-6927eef9" bind:__l="__l"></shortcut><view class="give-popup data-v-6927eef9"><uni-popup vue-id="88531da6-3" type="dialog" data-ref="givePopup" class="data-v-6927eef9 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('88531da6-4')+','+('88531da6-3')}}" mode="input" title="转赠给好友" type="info" placeholder="输入好友手机号码" before-close="{{true}}" data-event-opts="{{[['^close',[['cancelGive']]],['^confirm',[['doGive']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-6927eef9" bind:__l="__l"></uni-popup-dialog></uni-popup></view></view></block>