{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/custom/index.vue?fc29", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/custom/index.vue?8755", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/custom/index.vue?7fd8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/custom/index.vue?4390", "uni-app:///pages/custom/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/custom/index.vue?7c7b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/custom/index.vue?5587"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "data", "options", "page", "items", "onLoad", "methods", "getPageData", "Api", "then", "app", "finally", "setPageBar", "uni", "title", "frontColor", "backgroundColor", "onPullDownRefresh", "onShareAppMessage", "path", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACQ9pB;AAAA;AAAA;;;;;;;;;;;;;AAGA;AAAA,eAEA;EACAC;IACAD;EACA;EACAE;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAC,mBACAC;QACA;QACA;QACAC;QACAA;QACA;QACAA;MACA,GACAC;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACAC;QACAC;MACA;MACA;MACAD;QACAE;QACAC;MACA;IACA;EAEA;EAEA;AACA;AACA;EACAC;IACA;IACA;MACAJ;IACA;EACA;EAEA;AACA;AACA;EACAK;IACA;IACA;IACA;MACAJ;MACAK;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAN;MACAK;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/custom/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/custom/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=63eafbad&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=63eafbad&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"63eafbad\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/custom/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=63eafbad&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 店铺页面组件 -->\n    <Page :items=\"items\" />\n  </view>\n</template>\n\n<script>\n  import * as Api from '@/api/page'\n  import Page from '@/components/page'\n\n  const App = getApp()\n\n  export default {\n    components: {\n      Page\n    },\n    data() {\n      return {\n        // 页面参数\n        options: {},\n        // 页面属性\n        page: {},\n        // 页面元素\n        items: []\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 当前页面参数\n      this.options = options\n      // 加载页面数据\n      this.getPageData()\n    },\n    methods: {\n\n      /**\n       * 加载页面数据\n       * @param {Object} callback\n       */\n      getPageData(callback) {\n        const app = this\n        const pageId = app.options.pageId || 0\n        Api.detail(pageId)\n          .then(result => {\n            // 设置页面数据\n            const { data: { pageData } } = result\n            app.page = pageData.page\n            app.items = pageData.items\n            // 设置顶部导航栏栏\n            app.setPageBar();\n          })\n          .finally(() => callback && callback())\n      },\n\n      /**\n       * 设置顶部导航栏\n       */\n      setPageBar() {\n        const { page } = this\n        // 设置页面标题\n        uni.setNavigationBarTitle({\n          title: page.params.title\n        })\n        // 设置navbar标题、颜色\n        uni.setNavigationBarColor({\n          frontColor: page.style.titleTextColor === 'white' ? '#ffffff' : '#000000',\n          backgroundColor: page.style.titleBackgroundColor\n        })\n      }\n\n    },\n\n    /**\n     * 下拉刷新\n     */\n    onPullDownRefresh() {\n      // 获取首页数据\n      this.getPageData(() => {\n        uni.stopPullDownRefresh()\n      })\n    },\n\n    /**\n     * 分享当前页面\n     */\n    onShareAppMessage() {\n      const app = this\n      const { page } = app\n      return {\n        title: page.params.share_title,\n        path: \"/pages/index/index?\" + app.$getShareUrlParams()\n      }\n    },\n\n    /**\n     * 分享到朋友圈\n     * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)\n     * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html\n     */\n    onShareTimeline() {\n      const app = this\n      const { page } = app\n      return {\n        title: page.params.share_title,\n        path: \"/pages/index/index?\" + app.$getShareUrlParams()\n      }\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    background: #fff;\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=63eafbad&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=63eafbad&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424017\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}