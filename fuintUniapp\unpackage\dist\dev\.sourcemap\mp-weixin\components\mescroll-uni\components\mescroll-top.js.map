{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/components/mescroll-top.vue?f4ab", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/components/mescroll-top.vue?df3c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/components/mescroll-top.vue?70a6", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/components/mescroll-top.vue?04ec", "uni-app:///components/mescroll-uni/components/mescroll-top.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/components/mescroll-top.vue?aeab", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/components/mescroll-top.vue?9ff3"], "names": ["props", "option", "value", "computed", "mOption", "left", "right", "methods", "addUnit", "toTopClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACa;;;AAGxE;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,+oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCcprB;EACAA;IACA;IACAC;IACA;IACAC;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAy8B,CAAgB,84BAAG,EAAC,C;;;;;;;;;;;ACA79B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/mescroll-uni/components/mescroll-top.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mescroll-top.vue?vue&type=template&id=f59b820c&\"\nvar renderjs\nimport script from \"./mescroll-top.vue?vue&type=script&lang=js&\"\nexport * from \"./mescroll-top.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mescroll-top.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/mescroll-uni/components/mescroll-top.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=template&id=f59b820c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.mOption.src ? _vm.addUnit(_vm.mOption.bottom) : null\n  var m1 = _vm.mOption.src ? _vm.addUnit(_vm.mOption.width) : null\n  var m2 = _vm.mOption.src ? _vm.addUnit(_vm.mOption.radius) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=script&lang=js&\"", "<!-- 回到顶部的按钮 -->\n<template>\n    <image\n        v-if=\"mOption.src\"\n        class=\"mescroll-totop\"\n        :class=\"[value ? 'mescroll-totop-in' : 'mescroll-totop-out', {'mescroll-totop-safearea': mOption.safearea}]\"\n        :style=\"{'z-index':mOption.zIndex, 'left': left, 'right': right, 'bottom':addUnit(mOption.bottom), 'width':addUnit(mOption.width), 'border-radius':addUnit(mOption.radius)}\"\n        :src=\"mOption.src\"\n        mode=\"widthFix\"\n        @click=\"toTopClick\"\n    />\n</template>\n\n<script>\nexport default {\n    props: {\n        // up.toTop的配置项\n        option: Object,\n        // 是否显示\n        value: false\n    },\n    computed: {\n        // 支付宝小程序需写成计算属性,prop定义default仍报错\n        mOption(){\n            return this.option || {}\n        },\n        // 优先显示左边\n        left(){\n            return this.mOption.left ? this.addUnit(this.mOption.left) : 'auto';\n        },\n        // 右边距离 (优先显示左边)\n        right() {\n            return this.mOption.left ? 'auto' : this.addUnit(this.mOption.right);\n        }\n    },\n    methods: {\n        addUnit(num){\n            if(!num) return 0;\n            if(typeof num === 'number') return num + 'rpx';\n            return num\n        },\n        toTopClick() {\n            this.$emit('input', false); // 使v-model生效\n            this.$emit('click'); // 派发点击事件\n        }\n    }\n};\n</script>\n\n<style>\n/* 回到顶部的按钮 */\n.mescroll-totop {\n    z-index: 9990;\n    position: fixed !important; /* 加上important避免编译到H5,在多mescroll中定位失效 */\n    right: 20rpx;\n    bottom: 120rpx;\n    width: 72rpx;\n    height: auto;\n    border-radius: 50%;\n    opacity: 0;\n    transition: opacity 0.5s; /* 过渡 */\n    margin-bottom: var(--window-bottom); /* css变量 */\n}\n\n/* 适配 iPhoneX */\n@supports (bottom: constant(safe-area-inset-bottom)) or (bottom: env(safe-area-inset-bottom)) {\n    .mescroll-totop-safearea {\n        margin-bottom: calc(var(--window-bottom) + constant(safe-area-inset-bottom)); /* window-bottom + 适配 iPhoneX */\n        margin-bottom: calc(var(--window-bottom) + env(safe-area-inset-bottom));\n    }\n}\n\n/* 显示 -- 淡入 */\n.mescroll-totop-in {\n    opacity: 1;\n}\n\n/* 隐藏 -- 淡出且不接收事件*/\n.mescroll-totop-out {\n    opacity: 0;\n    pointer-events: none;\n}\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-top.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420149\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}