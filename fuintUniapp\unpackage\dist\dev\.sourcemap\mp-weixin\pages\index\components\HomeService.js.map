{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeService.vue?b882", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeService.vue?6dc6", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeService.vue?4b06", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeService.vue?9c03", "uni-app:///pages/index/components/HomeService.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeService.vue?9a4d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeService.vue?1e50"], "names": ["props", "data", "type", "default", "userInfo", "id", "avatar", "name", "balance", "point", "methods", "goUrl", "storage", "orderMode"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,8oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgBnrB;;;;;;;;;;;;;;;;eACA;EACAA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;QAAAE;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;MACA;;MAEA;MACAC;MACA;QAAAC;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxCA;AAAA;AAAA;AAAA;AAA8wC,CAAgB,yqCAAG,EAAC,C;;;;;;;;;;;ACAlyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/components/HomeService.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./HomeService.vue?vue&type=template&id=0bfe49da&scoped=true&\"\nvar renderjs\nimport script from \"./HomeService.vue?vue&type=script&lang=js&\"\nexport * from \"./HomeService.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HomeService.vue?vue&type=style&index=0&id=0bfe49da&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0bfe49da\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/HomeService.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeService.vue?vue&type=template&id=0bfe49da&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeService.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeService.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"content\">\r\n        <view class=\"entrance\">\r\n            <view class=\"item\">\r\n                <image class=\"icon\" src=\"/static/nav/store.png\" @click=\"goUrl('oneself')\"></image>\r\n                <view class=\"title\">到店取餐</view>\r\n            </view>\r\n           <view class=\"item\">\r\n                <image class=\"icon\" src=\"/static/nav/send.png\" @click=\"goUrl('express')\"></image>\r\n                <view class=\"title\">外卖到家</view>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport storage from '@/utils/storage'\r\nexport default {\r\n    props: {\r\n        data: {\r\n            type: Array,\r\n            default: []\r\n        },\r\n        userInfo: {\r\n            type: Object,\r\n            default: { id: '', avatar: '', name: '', balance: '', point: '' }\r\n        }\r\n    },\r\n    methods: {\r\n        goUrl(orderMode) {\r\n            if(!this.userInfo || !this.userInfo.id){\r\n                this.$navTo('pages/login/index')\r\n                return;\r\n            }\r\n            \r\n            // 保存当前的orderMode到本地存储\r\n            storage.set('current_order_mode', orderMode)\r\n            this.$navTo('pages/category/index', { orderMode });\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.entrance {\r\n    position: relative;\r\n    margin-top: -20rpx;\r\n    margin-bottom: 30rpx;\r\n    border-radius: 10rpx;\r\n    background-color: #ffffff;\r\n    box-shadow: #666;\r\n    padding: 40rpx 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    border: solid 1rpx #ccc;\r\n    margin: 10rpx 10rpx 25rpx 10rpx;\r\n    .item {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: center;\r\n        align-items: center;\r\n        position: relative;\r\n\r\n        &:nth-child(1):after {\r\n            content: '';\r\n            position: absolute;\r\n            width: 1rpx;\r\n            background-color: #ccc;\r\n            right: 0;\r\n            height: 100%;\r\n            transform: scaleX(0.5) scaleY(0.8);\r\n        }\r\n\r\n        .icon {\r\n            width: 120rpx;\r\n            height: 120rpx;\r\n            margin: 28rpx;\r\n        }\r\n\r\n        .title {\r\n            font-size: 36rpx;\r\n            color: #666;\r\n            font-weight: 600;\r\n        }\r\n\r\n        .content {\r\n            font-size: 28rpx;\r\n            color: #666;\r\n            font-weight: 400;\r\n        }\r\n    }\r\n}\r\n\r\n</style>\r\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeService.vue?vue&type=style&index=0&id=0bfe49da&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeService.vue?vue&type=style&index=0&id=0bfe49da&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425175\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}