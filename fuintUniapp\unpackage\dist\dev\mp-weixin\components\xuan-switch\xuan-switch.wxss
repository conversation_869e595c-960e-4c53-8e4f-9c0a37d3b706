@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.switch-container.data-v-6e6e5df4 {
  display: flex;
  flex-direction: row;
  width: 398rpx;
  height: 76rpx;
  border-radius: 80rpx;
  border: 3rpx solid #3f51b5;
  font-weight: bold;
  position: relative;
}
.switch-container .switch_view.data-v-6e6e5df4 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  border-radius: 100rpx;
}
.switch-container .switch_view .switch-item.data-v-6e6e5df4 {
  color: #666;
  font-size: 24rpx;
  height: 100%;
  width: 40%;
  border-radius: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.switch-container .position_view.data-v-6e6e5df4 {
  position: absolute;
  top: 0;
  left: 0;
  width: 60%;
  height: 100%;
  border-radius: 100rpx;
  background: #007aff;
}
.switch-container .disabled.data-v-6e6e5df4 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  background: #fff;
  opacity: 0.6;
  border-radius: 100rpx;
}
