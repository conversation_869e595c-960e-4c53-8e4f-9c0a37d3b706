<view class="container data-v-ec1e8658"><view class="my-point data-v-ec1e8658"><view class="my-tip data-v-ec1e8658"><text class="iconfont icon-jifen data-v-ec1e8658"></text>我的积分余额</view><view class="my-account data-v-ec1e8658">{{userInfo.point?userInfo.point:0}}</view><view class="my-gift data-v-ec1e8658"><view data-event-opts="{{[['tap',[['toUsePoint']]]]}}" class="gift data-v-ec1e8658" bindtap="__e">兑换积分</view></view></view><mescroll-body vue-id="97b1429c-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-ec1e8658 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="log-list data-v-ec1e8658"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="log-item data-v-ec1e8658"><view class="item-left flex-box data-v-ec1e8658"><view class="rec-status data-v-ec1e8658"><text class="data-v-ec1e8658">{{item.$orig.description}}</text></view><view class="rec-time data-v-ec1e8658"><text class="data-v-ec1e8658">{{item.m0}}</text></view></view><view class="{{['item-right','data-v-ec1e8658',item.$orig.amount>0?'col-green':'col-6']}}"><text class="data-v-ec1e8658">{{(item.$orig.amount>0?'+':'')+item.$orig.amount}}</text></view></view></block></view></mescroll-body></view>