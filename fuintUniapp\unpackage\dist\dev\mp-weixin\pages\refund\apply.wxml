<block wx:if="{{!isLoading}}"><view class="container data-v-36a440e0"><block wx:for="{{orderInfo.goods}}" wx:for-item="goods" wx:for-index="idx" wx:key="idx"><view class="goods-detail b-f dis-flex flex-dir-row data-v-36a440e0"><view class="left data-v-36a440e0"><image class="goods-image data-v-36a440e0" src="{{goods.image}}"></image></view><view class="right dis-flex flex-box flex-dir-column flex-x-around data-v-36a440e0"><view class="goods-name data-v-36a440e0"><text class="twolist-hidden data-v-36a440e0">{{goods.name}}</text></view><view class="dis-flex col-9 f-24 data-v-36a440e0"><view class="flex-box data-v-36a440e0"><view class="goods-props clearfix data-v-36a440e0"><block wx:for="{{goods.specList}}" wx:for-item="props" wx:for-index="idx" wx:key="idx"><view class="goods-props-item data-v-36a440e0"><text class="data-v-36a440e0">{{props.specValue}}</text></view></block></view></view><text class="t-r data-v-36a440e0">{{"×"+goods.num}}</text></view></view></view></block><view class="row-service b-f m-top20 data-v-36a440e0"><view class="row-title data-v-36a440e0">服务类型</view><view class="service-switch dis-flex data-v-36a440e0"><block wx:for="{{RefundTypeEnum.data}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onSwitchService',['$0'],[[['RefundTypeEnum.data','',index,'value']]]]]]]}}" class="{{['switch-item','data-v-36a440e0',(formData.type==item.value)?'active':'']}}" bindtap="__e">{{item.name}}</view></block></view></view><view class="row-money b-f m-top20 dis-flex data-v-36a440e0"><view class="row-title data-v-36a440e0">订单总金额</view><view class="money col-m data-v-36a440e0">{{"￥"+orderInfo.amount}}</view></view><view class="row-money b-f m-top20 dis-flex data-v-36a440e0"><view class="row-title data-v-36a440e0">实际付款</view><view class="money col-m data-v-36a440e0">{{"￥"+orderInfo.payAmount}}</view></view><view class="row-textarea b-f m-top20 data-v-36a440e0"><view class="row-title data-v-36a440e0">申请原因</view><view class="content data-v-36a440e0"><textarea class="textarea data-v-36a440e0" maxlength="2000" placeholder="请详细填写申请原因，建议您先与卖家沟通!" placeholderStyle="color:#ccc" data-event-opts="{{[['input',[['__set_model',['$0','content','$event',[]],['formData']]]]]}}" value="{{formData.content}}" bindinput="__e"></textarea></view></view><block wx:if="{{false}}"><view class="row-voucher b-f m-top20 data-v-36a440e0"><view class="row-title data-v-36a440e0">上传凭证 (最多6张)</view><view class="image-list data-v-36a440e0"><block wx:for="{{imageList}}" wx:for-item="image" wx:for-index="imageIndex" wx:key="imageIndex"><view class="image-preview data-v-36a440e0"><text data-event-opts="{{[['tap',[['deleteImage',[imageIndex]]]]]}}" class="image-delete iconfont icon-shanchu data-v-36a440e0" bindtap="__e"></text><image class="image data-v-36a440e0" mode="aspectFill" src="{{image.path}}"></image></view></block><block wx:if="{{$root.g0<maxImageLength}}"><view data-event-opts="{{[['tap',[['chooseImage']]]]}}" class="image-picker data-v-36a440e0" bindtap="__e"><text class="choose-icon iconfont icon-tubiao_xiangji data-v-36a440e0"></text><text class="choose-text data-v-36a440e0">上传图片</text></view></block></view></view></block><view class="footer-fixed data-v-36a440e0"><view class="btn-wrapper data-v-36a440e0"><view data-event-opts="{{[['tap',[['handleSubmit']]]]}}" class="{{['btn-item','btn-item-main','data-v-36a440e0',(disabled)?'disabled':'']}}" bindtap="__e">确认提交</view></view></view></view></block>