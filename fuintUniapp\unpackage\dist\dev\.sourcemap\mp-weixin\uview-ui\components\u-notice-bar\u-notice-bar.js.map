{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-notice-bar/u-notice-bar.vue?69ae", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-notice-bar/u-notice-bar.vue?a8ec", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-notice-bar/u-notice-bar.vue?465d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-notice-bar/u-notice-bar.vue?c8ae", "uni-app:///uview-ui/components/u-notice-bar/u-notice-bar.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-notice-bar/u-notice-bar.vue?bf31", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-notice-bar/u-notice-bar.vue?9c24"], "names": ["name", "props", "list", "type", "default", "volumeIcon", "volumeSize", "moreIcon", "closeIcon", "autoplay", "color", "bgColor", "mode", "show", "fontSize", "duration", "speed", "isCircular", "playState", "disable<PERSON><PERSON>ch", "borderRadius", "padding", "noListHidden", "computed", "isShow", "methods", "click", "close", "getMore", "end"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4OAEN;AACP,KAAK;AACL;AACA,aAAa,8PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,+oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiDprB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA9BA,gBA+BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;EACA;EACAmB;IACA;IACAC;MACA,gGACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAA+wC,CAAgB,0qCAAG,EAAC,C;;;;;;;;;;;ACAnyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-notice-bar/u-notice-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-notice-bar.vue?vue&type=template&id=087a7280&scoped=true&\"\nvar renderjs\nimport script from \"./u-notice-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-notice-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-notice-bar.vue?vue&type=style&index=0&id=087a7280&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"087a7280\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-notice-bar/u-notice-bar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-notice-bar.vue?vue&type=template&id=087a7280&scoped=true&\"", "var components\ntry {\n  components = {\n    uRowNotice: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-row-notice/u-row-notice\" */ \"@/uview-ui/components/u-row-notice/u-row-notice.vue\"\n      )\n    },\n    uColumnNotice: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-column-notice/u-column-notice\" */ \"@/uview-ui/components/u-column-notice/u-column-notice.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-notice-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-notice-bar.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"u-notice-bar-wrap\" v-if=\"isShow\" :style=\"{\n        borderRadius: borderRadius + 'rpx',\n    }\">\n        <block v-if=\"mode == 'horizontal' && isCircular\">\n            <u-row-notice\n                :type=\"type\"\n                :color=\"color\"\n                :bgColor=\"bgColor\"\n                :list=\"list\"\n                :volumeIcon=\"volumeIcon\"\n                :moreIcon=\"moreIcon\"\n                :volumeSize=\"volumeSize\"\n                :closeIcon=\"closeIcon\"\n                :mode=\"mode\"\n                :fontSize=\"fontSize\"\n                :speed=\"speed\"\n                :playState=\"playState\"\n                :padding=\"padding\"\n                @getMore=\"getMore\"\n                @close=\"close\"\n                @click=\"click\"\n            ></u-row-notice>\n        </block>\n        <block v-if=\"mode == 'vertical' || (mode == 'horizontal' && !isCircular)\">\n            <u-column-notice\n                :type=\"type\"\n                :color=\"color\"\n                :bgColor=\"bgColor\"\n                :list=\"list\"\n                :volumeIcon=\"volumeIcon\"\n                :moreIcon=\"moreIcon\"\n                :closeIcon=\"closeIcon\"\n                :mode=\"mode\"\n                :volumeSize=\"volumeSize\"\n                :disable-touch=\"disableTouch\"\n                :fontSize=\"fontSize\"\n                :duration=\"duration\"\n                :playState=\"playState\"\n                :padding=\"padding\"\n                @getMore=\"getMore\"\n                @close=\"close\"\n                @click=\"click\"\n                @end=\"end\"\n            ></u-column-notice>\n        </block>\n    </view>\n</template>\n<script>\n/**\n * noticeBar 滚动通知\n * @description 该组件用于滚动通告场景，有多种模式可供选择\n * @tutorial https://www.uviewui.com/components/noticeBar.html\n * @property {Array} list 滚动内容，数组形式，见上方说明\n * @property {String} type 显示的主题（默认warning）\n * @property {Boolean} volume-icon 是否显示小喇叭图标（默认true）\n * @property {Boolean} more-icon 是否显示右边的向右箭头（默认false）\n * @property {Boolean} close-icon 是否显示关闭图标（默认false）\n * @property {Boolean} autoplay 是否自动播放（默认true）\n * @property {String} color 文字颜色\n * @property {String Number} bg-color 背景颜色\n * @property {String} mode 滚动模式（默认horizontal）\n * @property {Boolean} show 是否显示（默认true）\n * @property {String Number} font-size 字体大小，单位rpx（默认28）\n * @property {String Number} volume-size 左边喇叭的大小（默认34）\n * @property {String Number} duration 滚动周期时长，只对步进模式有效，横向衔接模式无效，单位ms（默认2000）\n * @property {String Number} speed 水平滚动时的滚动速度，即每秒移动多少距离，只对水平衔接方式有效，单位rpx（默认160）\n * @property {String Number} font-size 字体大小，单位rpx（默认28）\n * @property {Boolean} is-circular mode为horizontal时，指明是否水平衔接滚动（默认true）\n * @property {String} play-state 播放状态，play - 播放，paused - 暂停（默认play）\n * @property {String Nubmer} border-radius 通知栏圆角（默认为0）\n * @property {String Nubmer} padding 内边距，字符串，与普通的内边距css写法一直（默认\"18rpx 24rpx\"）\n * @property {Boolean} no-list-hidden 列表为空时，是否显示组件（默认false）\n * @property {Boolean} disable-touch 是否禁止通过手动滑动切换通知，只有mode = vertical，或者mode = horizontal且is-circular = false时有效（默认true）\n * @event {Function} click 点击通告文字触发，只有mode = vertical，或者mode = horizontal且is-circular = false时有效\n * @event {Function} close 点击右侧关闭图标触发\n * @event {Function} getMore 点击右侧向右图标触发\n * @event {Function} end 列表的消息每次被播放一个周期时触发，只有mode = vertical，或者mode = horizontal且is-circular = false时有效\n * @example <u-notice-bar :more-icon=\"true\" :list=\"list\"></u-notice-bar>\n */\nexport default {\n    name: \"u-notice-bar\",\n    props: {\n        // 显示的内容，数组\n        list: {\n            type: Array,\n            default() {\n                return [];\n            }\n        },\n        // 显示的主题，success|error|primary|info|warning\n        type: {\n            type: String,\n            default: 'warning'\n        },\n        // 是否显示左侧的音量图标\n        volumeIcon: {\n            type: Boolean,\n            default: true\n        },\n        // 音量喇叭的大小\n        volumeSize: {\n            type: [Number, String],\n            default: 34\n        },\n        // 是否显示右侧的右箭头图标\n        moreIcon: {\n            type: Boolean,\n            default: false\n        },\n        // 是否显示右侧的关闭图标\n        closeIcon: {\n            type: Boolean,\n            default: false\n        },\n        // 是否自动播放\n        autoplay: {\n            type: Boolean,\n            default: true\n        },\n        // 文字颜色，各图标也会使用文字颜色\n        color: {\n            type: String,\n            default: ''\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: ''\n        },\n        // 滚动方向，horizontal-水平滚动，vertical-垂直滚动\n        mode: {\n            type: String,\n            default: 'horizontal'\n        },\n        // 是否显示\n        show: {\n            type: Boolean,\n            default: true\n        },\n        // 字体大小，单位rpx\n        fontSize: {\n            type: [Number, String],\n            default: 28\n        },\n        // 滚动一个周期的时间长，单位ms\n        duration: {\n            type: [Number, String],\n            default: 2000\n        },\n        // 水平滚动时的滚动速度，即每秒滚动多少rpx，这有利于控制文字无论多少时，都能有一个恒定的速度\n        speed: {\n            type: [Number, String],\n            default: 160\n        },\n        // 水平滚动时，是否采用衔接形式滚动\n        // 水平衔接模式，采用的是swiper组件，水平滚动\n        isCircular: {\n            type: Boolean,\n            default: true\n        },\n        // 播放状态，play-播放，paused-暂停\n        playState: {\n            type: String,\n            default: 'play'\n        },\n        // 是否禁止用手滑动切换\n        // 目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序\n        disableTouch: {\n            type: Boolean,\n            default: true\n        },\n        // 滚动通知设置圆角\n        borderRadius: {\n            type: [Number, String],\n            default: 0\n        },\n        // 通知的边距\n        padding: {\n            type: [Number, String],\n            default: '18rpx 24rpx'\n        },\n        // list列表为空时，是否显示组件\n        noListHidden: {\n            type: Boolean,\n            default: true\n        }\n    },\n    computed: {\n        // 如果设置show为false，或者设置了noListHidden为true，且list长度又为零的话，隐藏组件\n        isShow() {\n            if(this.show == false || (this.noListHidden == true && this.list.length == 0)) return false;\n            else return true;\n        }\n    },\n    methods: {\n        // 点击通告栏\n        click(index) {\n            this.$emit('click', index);\n        },\n        // 点击关闭按钮\n        close() {\n            this.$emit('close');\n        },\n        // 点击更多箭头按钮\n        getMore() {\n            this.$emit('getMore');\n        },\n        // 滚动一个周期结束，只对垂直，或者水平步进形式有效\n        end() {\n            this.$emit('end');\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/style.components.scss\";\n\n.u-notice-bar-wrap {\n    overflow: hidden;\n}\n\n.u-notice-bar {\n    padding: 18rpx 24rpx;\n    overflow: hidden;\n}\n\n.u-direction-row {\n    @include vue-flex;\n    align-items: center;\n    justify-content: space-between;\n}\n\n.u-left-icon {\n    @include vue-flex;\n    align-items: center;\n}\n\n.u-notice-box {\n    flex: 1;\n    @include vue-flex;\n    overflow: hidden;\n    margin-left: 12rpx;\n}\n\n.u-right-icon {\n    margin-left: 12rpx;\n    @include vue-flex;\n    align-items: center;\n}\n\n.u-notice-content {\n    line-height: 1;\n    white-space: nowrap;\n    font-size: 26rpx;\n    animation: u-loop-animation 10s linear infinite both;\n    text-align: right;\n    // 这一句很重要，为了能让滚动左右连接起来\n    padding-left: 100%;\n}\n\n@keyframes u-loop-animation {\n    0% {\n        transform: translate3d(0, 0, 0);\n    }\n\n    100% {\n        transform: translate3d(-100%, 0, 0);\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-notice-bar.vue?vue&type=style&index=0&id=087a7280&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-notice-bar.vue?vue&type=style&index=0&id=087a7280&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426905\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}