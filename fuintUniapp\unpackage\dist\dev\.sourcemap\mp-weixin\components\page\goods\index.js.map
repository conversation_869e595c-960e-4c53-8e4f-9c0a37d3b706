{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/goods/index.vue?cb74", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/goods/index.vue?8daa", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/goods/index.vue?0db8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/goods/index.vue?79c0", "uni-app:///components/page/goods/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/goods/index.vue?55e0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/goods/index.vue?45d4"], "names": ["name", "props", "itemIndex", "itemStyle", "params", "isReflash", "components", "MescrollBody", "mixins", "data", "list", "upOption", "auto", "page", "size", "noMoreSize", "watch", "methods", "onTargetGoods", "goodsId", "upCallback", "app", "then", "catch", "getGoodsList", "console", "pageSize", "GoodsApi", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACmE7qB;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAA;EAEA;AACA;AACA;AACA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EAEAC;IACAC;EACA;EAEAC;EAEAC;IACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;MACA;IACA;EACA;EACAC;IACAX;MACA;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;AACA;EACAY;IAEA;AACA;AACA;IACAC;MACA;QAAAC;MAAA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAC,2BACAC;QACA;QACA;QACAD;MACA,GACAE;QACAF;MACA;IACA;IAEA;AACA;AACA;AACA;IACAG;MACA;MACAC;MACA;QAAAZ;QAAAa;MAAA;MACA;QACAC,uBACAL;UACA;UACA;UACAD;UACAO;QACA,GACAL;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvKA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/page/goods/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=672a4bd8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=672a4bd8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"672a4bd8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/page/goods/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=672a4bd8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    _vm.itemStyle.column === 1 ? _vm.itemStyle.show.includes(\"goodsName\") : null\n  var g1 =\n    _vm.itemStyle.column === 1\n      ? _vm.itemStyle.show.includes(\"sellingPoint\")\n      : null\n  var g2 =\n    _vm.itemStyle.column === 1\n      ? _vm.itemStyle.show.includes(\"goodsSales\")\n      : null\n  var g3 =\n    _vm.itemStyle.column === 1\n      ? _vm.itemStyle.show.includes(\"goodsPrice\")\n      : null\n  var g5 = !(_vm.itemStyle.column === 1)\n    ? _vm.itemStyle.show.includes(\"goodsName\")\n    : null\n  var g6 = !(_vm.itemStyle.column === 1)\n    ? _vm.itemStyle.show.includes(\"goodsPrice\")\n    : null\n  var l0 = _vm.__map(_vm.list.content, function (dataItem, index) {\n    var $orig = _vm.__get_orig(dataItem)\n    var g4 =\n      _vm.itemStyle.column === 1\n        ? _vm.itemStyle.show.includes(\"linePrice\") && dataItem.linePrice > 0\n        : null\n    var g7 = !(_vm.itemStyle.column === 1)\n      ? _vm.itemStyle.show.includes(\"linePrice\") && dataItem.linePrice > 0\n      : null\n    return {\n      $orig: $orig,\n      g4: g4,\n      g7: g7,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g5: g5,\n        g6: g6,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <!-- 商品列表 -->\r\n  <view class=\"goods-container\">\r\n      <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ native: true }\" @down=\"downCallback\" :up=\"upOption\" @up=\"upCallback\">\n      <view class=\"diy-goods\" :style=\"{ background: itemStyle.background }\">\n        <view class=\"goods-list\" :class=\"[`display__${itemStyle.display}`, `column__${itemStyle.column}`]\">\n          <scroll-view :scroll-x=\"itemStyle.display === 'slide'\">\n            <view class=\"goods-item\" v-for=\"(dataItem, index) in list.content\" :key=\"index\" @click=\"onTargetGoods(dataItem.id)\">\n              <!-- 单列商品 -->\n              <block v-if=\"itemStyle.column === 1\">\n                <view class=\"dis-flex\">\n                  <!-- 商品图片 -->\n                  <view class=\"goods-item_left\">\n                    <image class=\"image\" lazy-load :lazy-load-margin=\"0\" :src=\"dataItem.logo\"></image>\n                  </view>\n                  <view class=\"goods-item_right\">\n                    <!-- 商品名称 -->\n                    <view v-if=\"itemStyle.show.includes('goodsName')\" class=\"goods-name twoline-hide\">\n                      <text>{{ dataItem.name }}</text>\n                    </view>\n                    <view class=\"goods-item_desc\">\n                      <!-- 商品卖点 -->\n                      <view v-if=\"itemStyle.show.includes('sellingPoint')\" class=\"desc-selling_point dis-flex\">\n                        <text class=\"oneline-hide\">{{ dataItem.salePoint ? dataItem.salePoint : '' }}</text>\n                      </view>\n                      <!-- 商品销量 -->\n                      <view v-if=\"itemStyle.show.includes('goodsSales')\" class=\"desc-goods_sales dis-flex\">\n                        <text>已售{{ dataItem.initSale ? dataItem.initSale : 0 }}件</text>\n                      </view>\n                      <!-- 商品价格 -->\n                      <view class=\"desc_footer\">\n                        <text v-if=\"itemStyle.show.includes('goodsPrice')\" class=\"price_x\">¥{{ dataItem.price ? dataItem.price : '0.00' }}</text>\n                        <text class=\"price_y col-9\" v-if=\"itemStyle.show.includes('linePrice') && dataItem.linePrice > 0\">¥{{ dataItem.linePrice }}</text>\n                        <view class=\"buy-now\">去购买</view>\n                      </view>\n                    </view>\n                  </view>\n                </view>\n              </block>\n              <!-- 多列商品 -->\n              <block v-else>\n                <!-- 商品图片 -->\n                <view class=\"goods-image\">\n                  <image class=\"image\" lazy-load :lazy-load-margin=\"0\" mode=\"aspectFill\" :src=\"dataItem.logo\"></image>\n                </view>\n                <view class=\"detail\">\n                  <!-- 商品标题 -->\n                  <view v-if=\"itemStyle.show.includes('goodsName')\" class=\"goods-name twoline-hide\">\n                    {{ dataItem.name }}\n                  </view>\n                  <!-- 商品价格 -->\n                  <view class=\"detail-price oneline-hide\">\n                    <text v-if=\"itemStyle.show.includes('goodsPrice')\" class=\"goods-price f-30 col-m\">￥{{ dataItem.price }}</text>\n                    <text v-if=\"itemStyle.show.includes('linePrice') && dataItem.linePrice > 0\" class=\"line-price col-9 f-24\">￥{{ dataItem.linePrice }}</text>\n                  </view>\n                </view>\n              </block>\n            </view>\n          </scroll-view>\n        </view>\n      </view>\r\n      </mescroll-body>\r\n  </view>\n</template>\n\n<script>\r\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\r\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\r\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\r\n  import * as GoodsApi from '@/api/goods'\r\n  \r\n  const pageSize = 10;\r\n  \n  export default {\n    name: \"Goods\",\n    \r\n    /**\n     * 组件的属性列表\n     * 用于组件自定义设置\n     */\n    props: {\n      itemIndex: String,\n      itemStyle: Object,\n      params: Object,\n      isReflash: Boolean\n    },\r\n    \r\n    components: {\r\n      MescrollBody\r\n    },\r\n    \r\n    mixins: [MescrollMixin],\r\n    \r\n    data() {\r\n      return {\r\n        list: getEmptyPaginateObj(),\r\n        // 上拉加载配置\r\n        upOption: {\r\n          // 首次自动执行\r\n          auto: true,\r\n          // 每页数据的数量; 默认10\r\n          page: { size: pageSize },\r\n          // 数量要大于1条才显示无更多数据\r\n          noMoreSize: 1,\r\n        }\r\n      }\r\n    },\r\n    watch: {\r\n      isReflash(value) {\r\n         if (value) {\r\n             this.getGoodsList(1);\r\n         }\r\n      }\r\n    },\n\n    /**\n     * 组件的方法列表\n     * 更新属性和数据的方法与更新页面数据的方法类似\n     */\n    methods: {\n\n      /**\n       * 跳转商品详情页\n       */\n      onTargetGoods(goodsId) {\n        this.$navTo(`pages/goods/detail`, { goodsId })\n      },\r\n      \r\n      /**\r\n       * 上拉加载的回调 (页面初始化时也会执行一次)\r\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\r\n       * @param {Object} page\r\n       */\r\n      upCallback(page) {\r\n        const app = this\r\n        // 设置列表数据\r\n        app.getGoodsList(page.num)\r\n          .then(list => {\r\n              const curPageLen = list.content.length;\r\n              const totalSize = list.totalElements;\r\n              app.mescroll.endBySize(curPageLen, totalSize);\r\n          })\r\n          .catch(() => {\r\n              app.mescroll.endErr();\r\n          })\r\n      },\r\n      \r\n      /**\r\n       * 获取商品列表\r\n       * @param {number} pageNo 页码\r\n       */\r\n      getGoodsList(pageNo) {\r\n        const app = this\r\n        console.log('pageNo==', pageNo);\r\n        const param = { page: pageNo, pageSize: pageSize }\r\n        return new Promise((resolve, reject) => {\r\n          GoodsApi.search(param)\r\n            .then(result => {\r\n              // 合并新数据\r\n              const newList = result.data;\r\n              app.list.content = getMoreListData(newList, app.list, pageNo)\r\n              resolve(newList)\r\n            })\r\n            .catch(reject)\r\n        })\r\n      }\n    }\n  }\n</script>\n<style lang=\"scss\" scoped>\r\n  .goods-container {\n      .diy-goods {\n        .goods-list {\n          padding: 4rpx;\n          box-sizing: border-box;\n          .goods-item {\n            box-sizing: border-box;\n            padding: 6rpx;\n\n            .goods-image {\n              position: relative;\n              width: 100%;\n              height: 0;\n              padding-bottom: 100%;\n              overflow: hidden;\n              background: #fff;\n\n              &:after {\n                content: '';\n                display: block;\n                margin-top: 100%;\n              }\n\n              .image {\n                position: absolute;\n                width: 100%;\n                height: 100%;\n                top: 0;\n                left: 0;\n                -o-object-fit: cover;\n                object-fit: cover;\n              }\n            }\n\n            .detail {\n              padding: 8rpx;\n              background: #fff;\n\n              .goods-name {\n                height: 64rpx;\n                line-height: 1.3;\n                white-space: normal;\n                color: #484848;\n                font-size: 26rpx;\n                font-weight: bold;\n              }\n\n              .detail-price {\n                .goods-price {\n                  margin-right: 8rpx;\n                }\n\n                .line-price {\n                  text-decoration: line-through;\n                }\n              }\n            }\n          }\n\n          &.display__slide {\n            white-space: nowrap;\n            font-size: 0;\n\n            .goods-item {\n              display: inline-block;\n            }\n          }\n\n          &.display__list {\n            .goods-item {\n              float: left;\n            }\n          }\n\n          &.column__2 {\n            .goods-item {\n              width: 50%;\n            }\n          }\n\n          &.column__3 {\n            .goods-item {\n              width: 33.33333%;\n            }\n          }\n\n          &.column__1 {\n            .goods-item {\n              width: 100%;\n              height: 250rpx;\n              margin-bottom: 10rpx;\n              padding: 20rpx;\n              box-sizing: border-box;\n              background: #fff;\n              line-height: 1.6;\n              border: 1rpx #F5f5f5 solid;\n              &:last-child {\n                margin-bottom: 0;\n              }\n            }\n\n            .goods-item_left {\n              display: flex;\n              width: 40%;\n              background: #fff;\n              align-items: center;\n\n              .image {\n                display: block;\n                width: 220rpx;\n                height: 200rpx;\n                border-radius: 10rpx;\n                border: 1rpx #cccccc solid;\n              }\n            }\n\n            .goods-item_right {\n              position: relative;\n              width: 60%;\n              .goods-name {\n                margin-top: 20rpx;\n                max-height: 69rpx;\n                line-height: 1.3;\n                white-space: normal;\n                color: #484848;\n                font-size: 30rpx;\n                font-weight: bold;\r\n                overflow: hidden;\r\n                display: -webkit-box;\r\n                -webkit-box-orient: vertical;\r\n                -webkit-line-clamp: 2;\n              }\n            }\n\n            .goods-item_desc {\n              margin-top: 0rpx;\n            }\n\n            .desc-selling_point {\n              width: 400rpx;\n              font-size: 24rpx;\n              color: #e49a3d;\n            }\n\n            .desc-goods_sales {\n              color: #999;\n              font-size: 24rpx;\n            }\n\n            .desc_footer {\n              font-size: 24rpx;\n\n              .price_x {\n                margin-right: 16rpx;\n                color: #f03c3c;\n                font-size: 33rpx;\r\n                font-weight: bold;\n              }\n\n              .price_y {\n                text-decoration: line-through;\n              }\n              \n              .buy-now {\n                  color: #FFFFFF;\n                  float: right;\n                  margin-right: 20rpx;\n                  border: solid 1rpx $fuint-theme;\n                  background: $fuint-theme;\n                  padding: 8rpx 20rpx 8rpx 20rpx;\n                  border-radius: 5rpx;\n                  display: block;\n              }\n            }\n          }\n        }\n      }\r\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=672a4bd8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=672a4bd8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425947\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}