{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-sku-popup/index.vue?e19d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-sku-popup/index.vue?1422", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-sku-popup/index.vue?408f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-sku-popup/index.vue?014c", "uni-app:///components/goods-sku-popup/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-sku-popup/index.vue?847e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-sku-popup/index.vue?c451"], "names": ["name", "components", "NumberBox", "props", "value", "Type", "default", "goods", "goodsId", "action", "noStockText", "stockText", "goodsIdName", "skuIdName", "skuListName", "specListName", "stockName", "skuName", "skuArrName", "defaultSingleSkuName", "mode", "maskCloseAble", "borderRadius", "goodsThumbName", "minBuyNum", "maxBuy<PERSON>um", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "priceColor", "buyNowText", "buyNowColor", "buyNowBackgroundColor", "addCartText", "addCartColor", "addCartBackgroundColor", "disableStyle", "activedStyle", "btnStyle", "showClose", "closeImage", "defaultStock", "defaultPrice", "gradeInfo", "hafanInfo", "data", "complete", "goodsInfo", "isShow", "initKey", "shopItemInfo", "selectArr", "subIndex", "selectShop", "selectNum", "outFoStock", "mounted", "that", "vk", "methods", "init", "console", "updateGoodsInfo", "Object", "open", "close", "moveHandle", "skuClick", "checkSelectShop", "checkInpath", "specList", "checkItem", "skuList", "i", "stockNum", "items", "arr", "checkSelectComplete", "addCart", "success", "buyNow", "onConfirm", "toast", "uni", "title", "icon", "getListItem", "item", "autoClickSku", "filters", "priceFilter", "n", "computed", "hafanLevel", "isMemberPrice"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoF9pB;AACA;AAAA,eACA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;IACA;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;IACA;IACAyB;MACA1B;MACAC;IACA;IACA;IACA0B;MACA3B;MACAC;IACA;IACA;IACA2B;MACA5B;MACAC;IACA;IACA;IACA4B;MACA7B;MACAC;IACA;IACA;IACA6B;MACA9B;MACAC;IACA;IACA;IACA8B;MACA/B;MACAC;IACA;IACA;IACA+B;MACAhC;MACAC;IACA;IACA;IACAgC;MACAjC;MACAC;IACA;IACA;IACAiC;MACAlC;MACAC;IACA;IACA;IACAkC;MACAnC;MACAC;IACA;IACAmC;MACApC;MACAC;IACA;IACAoC;MACArC;MACAC;IACA;EACA;EACAqC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;IACAC;IAEA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAH;MACAA;MACAA;MACAA;MACAA;MACAA;MAEA;MACAI;MACAJ;QACAA;QACAA;MACA;MAEAA;MACAA;MACAA;IACA;IACA;IACAK;MACAD;MACA;MACA;QACAJ;QACAA;MACA;QACAA;MACA;MACA;QACAA;QACAA;MACA;MACA;MACA;MACAM;MACAN;MACAA;MACAA;IACA;IACAO;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAP;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAQ;MACA;QACAR;QACAA;MACA;QACA;UACAA;UACAA;QACA;MACA;IACA;IACAS;MACA;IAAA,CACA;IACA;IACAC;MACA;QACA;UACAV;UACAA;QACA;UACAA;UACAA;QACA;QACAA;QACA;QACAA;MACA;IACA;IACA;IACAW;MACA;MACA;QAAA;MAAA;QACAX;QACAI;QACAJ;MACA;QACAA;MACA;IACA;IACA;IACAY;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;QACA;QACA;UACA;YACA;UACA;UACA;UACAZ;UACA;YAAA;UAAA;UACA;YACAa;UACA;YACAA;UACA;QACA;MACA;MACAb;IACA;IACA;IACAc;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;UACAC;QACA;UACAC;QACA;MACA;MACA;QACAjB;MACA;MACA;MACA,4BACA;QACA,mBACAkB,8BACA;UACA,kBACAC;YACA;YACA;cACAnB;YACA;YACA;UACA,GACA;QACA,GACA,CACA,GACA,CACA,CACA;MACA,GACA,CACA,GACA,CACA;IACA;IACA;IACAoB;MAAA;MACA;MACA;QACA;QACA;UACA;QACA;UACApB;QACA;MACA;QACAA;MACA;IACA;IACA;IACAqB;MACArB;QACAsB;UACA1B;UACAI;QACA;MACA;IACA;IACA;IACAuB;MACAvB;QACAsB;UACA1B;UACAI;QACA;MACA;IACA;IACA;IACAwB;MACAxB;QACAsB;UACA1B;UACAI;QACA;MACA;IACA;IACA;IACAyB;MACAC;QACAC;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UACA;YACAC;YACA;UACA;QACA;UACA;YACAA;YACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MAEA;QACA;QAEA;UACA;UACA;YACA/B;YACA;UACA;QACA;MACA;IACA;EACA;EACA;EACAgC;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;EACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnjBA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/goods-sku-popup/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=63515b46&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=63515b46&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"63515b46\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/goods-sku-popup/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=63515b46&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var f0 = _vm._f(\"priceFilter\")(\n    (_vm.isMemberPrice ? _vm.selectShop.gradePrice : _vm.selectShop.price) ||\n      _vm.defaultPrice\n  )\n  var g0 = _vm.selectArr.every(function (val) {\n    return val == \"\"\n  })\n  var g1 = !g0 ? _vm.selectArr.join(\" \") : null\n  var l1 = _vm.__map(_vm.goodsInfo[_vm.specListName], function (item, index1) {\n    var $orig = _vm.__get_orig(item)\n    var l0 = _vm.__map(item.list, function (item_value, index2) {\n      var $orig = _vm.__get_orig(item_value)\n      var s0 = _vm.__get_style([\n        item_value.ishow ? \"\" : _vm.disableStyle,\n        item_value.ishow ? _vm.btnStyle : \"\",\n        _vm.subIndex[index1] == index2 ? _vm.activedStyle : \"\",\n      ])\n      return {\n        $orig: $orig,\n        s0: s0,\n      }\n    })\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        f0: f0,\n        g0: g0,\n        g1: g1,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"goods-sku-popup popup\" catchtouchmove=\"true\" :class=\"(value && complete) ? 'show' : 'none'\"\r\n    @touchmove.stop.prevent=\"moveHandle\">\r\n    <!-- 页面内容开始 -->\r\n    <view class=\"mask\" @click=\"close('mask')\"></view>\r\n    <!-- 页面开始 -->\r\n    <view class=\"layer attr-content\" :style=\"'border-radius: '+borderRadius+'rpx '+borderRadius+'rpx 0 0;'\">\r\n      <view class=\"specification-wrapper\">\r\n        <scroll-view class=\"specification-wrapper-content\" scroll-y=\"true\" style=\"height:70vh;\">\r\n          <view class=\"specification-header\">\r\n            <view class=\"specification-left\">\r\n              <image class=\"product-img\" :src=\"selectShop.image ? selectShop.image : goodsInfo[goodsThumbName]\"\r\n                mode=\"aspectFill\"></image>\r\n            </view>\r\n            <view class=\"specification-right\">\r\n              <view class=\"price-content\" :style=\"'color: '+priceColor+' ;'\">\r\n                <text class=\"sign\">¥</text>\r\n                <text class=\"price\">{{ ((isMemberPrice ? selectShop.gradePrice  :  selectShop.price) || defaultPrice) | priceFilter }}</text>\r\n              </view>\r\n              <!-- <view class=\"inventory\">{{ stockText }}：{{ selectShop[stockName] || defaultStock }}</view> -->\r\n              <view class=\"choose\" v-show=\"goodsInfo[specListName] && goodsInfo[specListName][0].name !== defaultSingleSkuName\">\r\n                <text v-if=\"!selectArr.every(val => val == '')\">已选：{{ selectArr.join(' ') }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n\r\n          <view class=\"specification-content\">\r\n            <view v-show=\"goodsInfo[specListName][0].name !== defaultSingleSkuName\" class=\"specification-item\" v-for=\"(item, index1) in goodsInfo[specListName]\" :key=\"index1\">\r\n              <view class=\"item-title\">{{ item.name }}</view>\r\n              <view class=\"item-wrapper\">\r\n                <view class=\"item-content\" @tap=\"skuClick(item_value, index1, $event, index2)\" v-for=\"(item_value, index2) in item.list\"\r\n                  :key=\"index2\" :class=\"[item_value.ishow ? '' : 'noactived', subIndex[index1] == index2 ? 'actived' : '']\"\r\n                  :style=\"[item_value.ishow ? '' : disableStyle,\r\n                                                    item_value.ishow ? btnStyle :'',\r\n                                                    subIndex[index1] == index2 ? activedStyle : ''\r\n                                    ]\">\r\n                  {{ item_value.name }}\r\n                </view>\r\n              </view>\r\n            </view>\r\n            <view style=\"display: flex;\">\r\n              <view style=\"flex: 1;\">\r\n                <text style=\"font-size: 26rpx; color: #333; line-height: 50rpx;\">数量</text>\r\n              </view>\r\n              <view style=\"flex: 4;text-align: right;\">\r\n                <number-box :min=\"minBuyNum\" :max=\"maxBuyNum\" :step=\"stepBuyNum\" v-model=\"selectNum\"\r\n                  :positive-integer=\"true\">\r\n                </number-box>\r\n              </view>\r\n            </view>\r\n\r\n          </view>\r\n        </scroll-view>\r\n        <view class=\"close\" @click=\"close('close')\" v-if=\"showClose\">\r\n          <image class=\"close-item\" :src=\"closeImage\"></image>\r\n        </view>\r\n      </view>\r\n      <view class=\"btn-option\">\r\n          <view class=\"btn-wrapper\" v-if=\"outFoStock\">\r\n            <view class=\"sure\" style=\"color:#ffffff;background-color:#cccccc\">{{ noStockText }}</view>\r\n          </view>\r\n          <view class=\"btn-wrapper\" v-else-if=\"mode == 5\">\r\n            <view class=\"sure\" @click=\"onConfirm\" :style=\"'color:'+buyNowColor+';background:'+buyNowBackgroundColor\">确认选择</view>\r\n          </view>\r\n          <view class=\"btn-wrapper\" v-else-if=\"mode == 1\">\r\n            <view class=\"sure add-cart\" style=\"border-radius:38rpx 0rpx 0rpx 38rpx;\" @click=\"addCart\" :style=\"'color:'+addCartColor+';background:'+addCartBackgroundColor\">{{ addCartText }}</view>\r\n            <view class=\"sure\" style=\"border-radius:0rpx 38rpx 38rpx 0rpx;\" @click=\"buyNow\" :style=\"'color:'+buyNowColor+';background-color:'+buyNowBackgroundColor\">{{ buyNowText }}</view>\r\n          </view>\r\n          <view class=\"btn-wrapper\" v-else-if=\"mode == 2\">\r\n            <view class=\"sure add-cart\" @click=\"addCart\" :style=\"'color:'+addCartColor+';background:'+addCartBackgroundColor\">{{ addCartText }}</view>\r\n          </view>\r\n          <view class=\"btn-wrapper\" v-else-if=\"mode == 3\">\r\n            <view class=\"sure\" @click=\"buyNow\" :style=\"'color:'+buyNowColor+';background:'+buyNowBackgroundColor\">{{ buyNowText }}</view>\r\n          </view>\r\n      </view>\r\n      <!-- 页面结束 -->\r\n    </view>\r\n    <!-- 页面内容结束 -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import NumberBox from './number-box'\r\n\r\n  var that; // 当前页面对象\r\n  var vk; // 自定义函数集\r\n  export default {\r\n    name: 'GoodsSkuPopup',\r\n    components: {\r\n      NumberBox\r\n    },\r\n    props: {\r\n      // true 组件显示 false 组件隐藏\r\n      value: {\r\n        Type: Boolean,\r\n        default: false\r\n      },\r\n      goods: {\r\n        Type: Object,\r\n        default: null\r\n      },\r\n      // vk云函数路由模式参数开始-----------------------------------------------------------\r\n      // 商品id\r\n      goodsId: {\r\n        Type: String,\r\n        default: \"\"\r\n      },\r\n      // vk路由模式框架下的云函数地址\r\n      action: {\r\n        Type: String,\r\n        default: \"\"\r\n      },\r\n      // vk云函数路由模式参数结束-----------------------------------------------------------\r\n      // 该商品已抢完时的按钮文字\r\n      noStockText: {\r\n        Type: String,\r\n        default: \"该商品已抢完\"\r\n      },\r\n      // 库存文字\r\n      stockText: {\r\n        Type: String,\r\n        default: \"库存\"\r\n      },\r\n      // 商品表id的字段名\r\n      goodsIdName: {\r\n        Type: String,\r\n        default: \"_id\"\r\n      },\r\n      // sku表id的字段名\r\n      skuIdName: {\r\n        Type: String,\r\n        default: \"_id\"\r\n      },\r\n      // sku_list的字段名\r\n      skuListName: {\r\n        Type: String,\r\n        default: \"sku_list\"\r\n      },\r\n      // spec_list的字段名\r\n      specListName: {\r\n        Type: String,\r\n        default: \"spec_list\"\r\n      },\r\n      // stock的字段名\r\n      stockName: {\r\n        Type: String,\r\n        default: \"stock\"\r\n      },\r\n      // sku_name的字段名\r\n      skuName: {\r\n        Type: String,\r\n        default: \"sku_name\"\r\n      },\r\n      // sku组合路径的字段名\r\n      skuArrName: {\r\n        Type: String,\r\n        default: \"sku_name_arr\"\r\n      },\r\n      // 默认单规格时的规格组名称\r\n      defaultSingleSkuName: {\r\n        Type: String,\r\n        default: \"默认\"\r\n      },\r\n      // 模式 1:都显示 2:只显示购物车 3:只显示立即购买 4:显示缺货按钮 5:套餐内规格选择 默认 1\r\n      mode: {\r\n        Type: Number,\r\n        default: 1\r\n      },\r\n      // 点击遮罩是否关闭组件 true 关闭 false 不关闭 默认true\r\n      maskCloseAble: {\r\n        Type: Boolean,\r\n        default: true\r\n      },\r\n      // 顶部圆角值\r\n      borderRadius: {\r\n        Type: [String, Number],\r\n        default: 0\r\n      },\r\n      // 商品缩略图字段名(未选择sku时)\r\n      goodsThumbName: {\r\n        Type: [String],\r\n        default: \"goods_thumb\"\r\n      },\r\n      // 最小购买数量\r\n      minBuyNum: {\r\n        Type: Number,\r\n        default: 1\r\n      },\r\n      // 最大购买数量\r\n      maxBuyNum: {\r\n        Type: Number,\r\n        default: 100000\r\n      },\r\n      // 每次点击后的数量\r\n      stepBuyNum: {\r\n        Type: Number,\r\n        default: 1\r\n      },\r\n      // 价格的字体颜色\r\n      priceColor: {\r\n        Type: String,\r\n        default: \"#fe560a\"\r\n      },\r\n      // 立即购买按钮的文字\r\n      buyNowText: {\r\n        Type: String,\r\n        default: \"立即购买\"\r\n      },\r\n      // 立即购买按钮的字体颜色\r\n      buyNowColor: {\r\n        Type: String,\r\n        default: \"#ffffff\"\r\n      },\r\n      // 立即购买按钮的背景颜色\r\n      buyNowBackgroundColor: {\r\n        Type: String,\r\n       default: \"linear-gradient(to right, $fuint-theme, $fuint-theme)\"\r\n      },\r\n      // 加入购物车按钮的文字\r\n      addCartText: {\r\n        Type: String,\r\n        default: \"加入购物车\"\r\n      },\r\n      // 加入购物车按钮的字体颜色\r\n      addCartColor: {\r\n        Type: String,\r\n        default: \"#ffffff\"\r\n      },\r\n      // 加入购物车按钮的背景颜色\r\n      addCartBackgroundColor: {\r\n        Type: String,\r\n        default: \"linear-gradient(to right, $fuint-theme, $fuint-theme)\"\r\n      },\r\n      // 不可点击时,按钮的样式\r\n      disableStyle: {\r\n        Type: Object,\r\n        default: null\r\n      },\r\n      // 按钮点击时的样式\r\n      activedStyle: {\r\n        Type: Object,\r\n        default: null\r\n      },\r\n      // 按钮常态的样式\r\n      btnStyle: {\r\n        Type: Object,\r\n        default: null\r\n      },\r\n      // 是否显示右上角关闭按钮\r\n      showClose: {\r\n        Type: Boolean,\r\n        default: true\r\n      },\r\n      // 关闭按钮的图片地址\r\n      closeImage: {\r\n        Type: String,\r\n        default: \"https://img.alicdn.com/imgextra/i1/121022687/O1CN01ImN0O11VigqwzpLiK_!!121022687.png\"\r\n      },\r\n      // 默认库存数量 (未选择sku时)\r\n      defaultStock: {\r\n        Type: Number,\r\n        default: 0\r\n      },\r\n      // 默认显示的价格 (未选择sku时)\r\n      defaultPrice: {\r\n        Type: Number,\r\n        default: 0\r\n      },\r\n\t  gradeInfo: {\r\n        Type: Object,\r\n        default: {}\r\n      },\r\n      hafanInfo: {\r\n        Type: Object,\r\n        default: {}\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        complete: false, // 组件是否加载完成\r\n        goodsInfo: {}, // 商品信息\r\n        isShow: false, // true 显示 false 隐藏\r\n        initKey: true, // 是否已初始化\r\n        shopItemInfo: {}, // 存放要和选中的值进行匹配的数据\r\n        selectArr: [], // 存放被选中的值\r\n        subIndex: [], // 是否选中 因为不确定是多规格还是单规格，所以这里定义数组来判断\r\n        selectShop: {}, // 存放最后选中的商品\r\n        selectNum: this.minBuyNum, // 选中数量\r\n        outFoStock: false, // 是否全部sku都缺货\r\n      };\r\n    },\r\n    mounted() {\r\n      that = this;\r\n      vk = that.vk;\r\n \r\n      if (this.value) {\r\n          this.open();\r\n      }\r\n    },\r\n    methods: {\r\n      // 初始化\r\n      init() {\r\n        // 清空之前的数据\r\n        that.selectArr = [];\r\n        that.subIndex = [];\r\n        that.selectShop = {};\r\n        that.selectNum = that.minBuyNum;\r\n        that.outFoStock = false;\r\n        that.shopItemInfo = {};\r\n\r\n        let specListName = that.specListName;\r\n        console.log('that.goodsInfo :>> ', that.goodsInfo);\r\n        that.goodsInfo[specListName].map(item => {\r\n             that.selectArr.push('');\r\n             that.subIndex.push(-1);\r\n        });\r\n\r\n        that.checkItem(); // 计算sku里面规格形成路径\r\n        that.checkInpath(-1); // 传-1是为了不跳过循环\r\n        that.autoClickSku(); // 自动选择sku策略\r\n      }, \r\n      // 更新商品信息(库存、名称、图片)\r\n      updateGoodsInfo(goodsInfo) {\r\n\t\t  console.log('goodsInfo',goodsInfo)\r\n        let skuListName = that.skuListName;\r\n        if (JSON.stringify(that.goodsInfo) === \"{}\" || that.goodsInfo[that.goodsIdName] !== goodsInfo[that.goodsIdName]) {\r\n            that.goodsInfo = goodsInfo;\r\n            that.initKey = true;\r\n        } else {\r\n            that.goodsInfo[skuListName] = goodsInfo[skuListName];\r\n        }\r\n        if (that.initKey) {\r\n            that.initKey = false;\r\n            that.init();\r\n        }\r\n        // 更新选中sku的库存信息\r\n        let select_sku_info = that.getListItem(that.goodsInfo[skuListName], that.skuIdName, that.selectShop[that.skuIdName]);\r\n        Object.assign(that.selectShop, select_sku_info);\r\n        that.complete = true;\r\n        that.$emit(\"open\", true);\r\n        that.$emit(\"input\", true);\r\n      },\r\n      async open() {\r\n        that.updateGoodsInfo(this.goods);\r\n      },\r\n      // 监听 - 弹出层收起\r\n      close(s) {\r\n        if (s == \"close\") {\r\n          that.$emit(\"input\", false);\r\n          that.$emit(\"close\", \"close\");\r\n        } else if (s == \"mask\") {\r\n          if (that.maskCloseAble) {\r\n            that.$emit(\"input\", false);\r\n            that.$emit(\"close\", \"mask\");\r\n          }\r\n        }\r\n      },\r\n      moveHandle() {\r\n        //禁止父元素滑动\r\n      },\r\n      // sku按钮的点击事件\r\n      skuClick(value, index1, event, index2) {\r\n        if (value.ishow) {\r\n          if (that.selectArr[index1] != value.name) {\r\n              that.$set(that.selectArr, index1, value.name);\r\n              that.$set(that.subIndex, index1, index2);\r\n          } else {\r\n              that.$set(that.selectArr, index1, '');\r\n              that.$set(that.subIndex, index1, -1);\r\n          }\r\n          that.checkInpath(index1);\r\n          // 如果全部选完\r\n          that.checkSelectShop();\r\n        }\r\n      },\r\n      // 检测是否已经选完sku\r\n      checkSelectShop() {\r\n        // 如果全部选完\r\n        if (that.selectArr.every(item => item != '')) {\r\n            that.selectShop = that.shopItemInfo[that.selectArr];\r\n\t\t\tconsole.log('selectShop',  that.selectShop)\r\n            that.selectNum = that.minBuyNum;\r\n        } else {\r\n            that.selectShop = {};\r\n        }\r\n      },\r\n      // 检查路径\r\n      checkInpath(clickIndex) {\r\n        let specListName = that.specListName;\r\n        //循环所有属性判断哪些属性可选\r\n        //当前选中的兄弟节点和已选中属性不需要循环\r\n        let specList = that.goodsInfo[specListName];\r\n        for (let i = 0, len = specList.length; i < len; i++) {\r\n          if (i == clickIndex) {\r\n              continue;\r\n          }\r\n          let len2 = specList[i].list.length;\r\n          for (let j = 0; j < len2; j++) {\r\n            if (that.subIndex[i] != -1 && j == that.subIndex[i]) {\r\n                continue;\r\n            }\r\n            let choosed_copy = [...that.selectArr];\r\n            that.$set(choosed_copy, i, specList[i].list[j].name);\r\n            let choosed_copy2 = choosed_copy.filter(item => item !== '' && typeof item !== 'undefined');\r\n            if (that.shopItemInfo.hasOwnProperty(choosed_copy2)) {\r\n                specList[i].list[j].ishow = true;\r\n            } else {\r\n                specList[i].list[j].ishow = false;\r\n            }\r\n          }\r\n        }\r\n        that.$set(that.goodsInfo, specListName, specList);\r\n      },\r\n      // 计算sku里面规格形成路径\r\n      checkItem() {\r\n        let skuListName = that.skuListName;\r\n        // console.time('计算有多小种可选路径需要的时间是');\r\n        // 去除库存小于等于0的商品sku\r\n        let skuList = that.goodsInfo[skuListName];\r\n        let stockNum = 0;\r\n        for (let i = 0; i < skuList.length; i++) {\r\n          if (skuList[i][that.stockName] <= 0) {\r\n              skuList.splice(i, 1);\r\n              i--;\r\n          } else {\r\n              stockNum += skuList[i][that.stockName];\r\n          }\r\n        }\r\n        if (stockNum <= 0) {\r\n            that.outFoStock = true;\r\n        }\r\n        // 计算有多小种可选路径\r\n        let result = skuList.reduce(\r\n          (arrs, items) => {\r\n            return arrs.concat(\r\n              items[that.skuArrName].reduce(\r\n                (arr, item) => {\r\n                  return arr.concat(\r\n                    arr.map(item2 => {\r\n                      // 利用对象属性的唯一性实现二维数组去重\r\n                      if (!that.shopItemInfo.hasOwnProperty([...item2, item])) {\r\n                          that.shopItemInfo[[...item2, item]] = items;\r\n                      }\r\n                      return [...item2, item];\r\n                    })\r\n                  );\r\n                },\r\n                [\r\n                  []\r\n                ]\r\n              )\r\n            );\r\n          },\r\n          [\r\n            []\r\n          ]\r\n        );\r\n      },\r\n      // 检测sku选项是否已全部选完,且有库存\r\n      checkSelectComplete(obj = {}) {\r\n        let selectShop = that.selectShop;\r\n        if (selectShop && selectShop[that.skuIdName] !== undefined) {\r\n          // 判断库存\r\n          if (that.selectNum <= selectShop[that.stockName]) {\r\n            if (typeof obj.success == \"function\") obj.success(selectShop);\r\n          } else {\r\n            that.toast(that.stockText + \"不足\", \"none\")\r\n          }\r\n        } else {\r\n          that.toast(\"请先选择对应规格\", \"none\");\r\n        }\r\n      },\r\n      // 加入购物车\r\n      addCart() {\r\n        that.checkSelectComplete({\r\n          success: function(selectShop) {\r\n            selectShop.buy_num = that.selectNum;\r\n            that.$emit(\"add-cart\", selectShop);\r\n          }\r\n        });\r\n      },\r\n      // 立即购买\r\n      buyNow() {\r\n        that.checkSelectComplete({\r\n          success: function(selectShop) {\r\n            selectShop.buy_num = that.selectNum;\r\n            that.$emit(\"buy-now\", selectShop);\r\n          }\r\n        });\r\n      },\r\n      // 确认选择\r\n      onConfirm() {\r\n        that.checkSelectComplete({\r\n          success: function(selectShop) {\r\n            selectShop.buy_num = that.selectNum;\r\n            that.$emit(\"confirm\", selectShop);\r\n          }\r\n        });\r\n      },\r\n      // 弹窗\r\n      toast(title, icon) {\r\n        uni.showToast({\r\n          title: title,\r\n          icon: icon\r\n        });\r\n      },\r\n      // 获取对象数组中的某一个item,根据指定的键值\r\n      getListItem(list, key, value) {\r\n        let item;\r\n        for (let i in list) {\r\n          if (typeof value == \"object\") {\r\n            if (JSON.stringify(list[i][key]) === JSON.stringify(value)) {\r\n                item = list[i];\r\n                break;\r\n            }\r\n          } else {\r\n            if (list[i][key] === value) {\r\n                item = list[i];\r\n                break;\r\n            }\r\n          }\r\n        }\r\n        return item;\r\n      },\r\n      // 自动选择sku前提是只有一组sku,默认自动选择最前面的有库存的sku\r\n      autoClickSku() {\r\n        let skuList = that.goodsInfo[that.skuListName];\r\n        let specListArr = that.goodsInfo[that.specListName];\r\n        \r\n        if (specListArr.length == 1) {\r\n          let specList = specListArr[0].list;\r\n        \r\n          for (let i = 0; i < specList.length; i++) {\r\n            let sku = that.getListItem(skuList, that.skuArrName, [specList[i].name]);\r\n            if (sku) {\r\n                that.skuClick(specList[i], 0, {}, i);\r\n                break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 过滤器\r\n    filters: {\r\n      // 金额显示过滤器\r\n      priceFilter(n = 0) {\r\n        if (typeof n == \"string\") {\r\n            n = parseFloat(n)\r\n        }\r\n        return n ? n.toFixed(2) : n\r\n      }\r\n    },\r\n    // 计算属性\r\n    computed: {\r\n      // 计算哈帆会员等级\r\n      hafanLevel() {\r\n        return this.hafanInfo?.premium?.level || 'free';\r\n      },\r\n      // 计算是否为会员价格\r\n      isMemberPrice() {\r\n        return (this.gradeInfo && this.gradeInfo.grade > 1) || (this.hafanLevel !== 'free');\r\n      }\r\n    },\r\n  };\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  /*  sku弹出层 */\r\n  .goods-sku-popup {\r\n    position: fixed;\r\n    left: 0;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    z-index: 999999999999;\r\n    overflow: hidden;\r\n    &.show {\r\n      display: block;\r\n\r\n      .mask {\r\n        animation: showPopup 0.2s linear both;\r\n      }\r\n\r\n      .layer {\r\n        animation: showLayer 0.2s linear both;\r\n      }\r\n    }\r\n\r\n    &.hide {\r\n      .mask {\r\n        animation: hidePopup 0.2s linear both;\r\n      }\r\n\r\n      .layer {\r\n        animation: hideLayer 0.2s linear both;\r\n      }\r\n    }\r\n\r\n    &.none {\r\n      display: none;\r\n    }\r\n\r\n    .mask {\r\n      position: fixed;\r\n      top: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 1;\r\n      background-color: rgba(0, 0, 0, 0.65);\r\n    }\r\n\r\n    .layer {\r\n      display: flex;\r\n      width: 100%;\r\n      max-height: 1200rpx;\r\n      flex-direction: column;\r\n      position: fixed;\r\n      z-index: 999999;\r\n      bottom: 0;\r\n      border-radius: 10rpx 10rpx 0 0;\r\n      background-color: #ffffff;\r\n      margin-top: 10rpx;\r\n      overflow-y: scroll;\r\n      .btn-option {\r\n          padding: 1rpx;\r\n          display: block;\r\n          clear: both;\r\n          margin-bottom: 60rpx;\r\n      }\r\n      .specification-wrapper {\r\n        width: 100%;\r\n        margin-top: 20rpx;\r\n        padding: 30rpx 25rpx;\r\n        box-sizing: border-box;\r\n        .specification-wrapper-content {\r\n          width: 100%;\r\n          min-height: 300rpx;\r\n\r\n          &::-webkit-scrollbar {\r\n            /*隐藏滚轮*/\r\n            display: none;\r\n          }\r\n\r\n          .specification-header {\r\n            width: 100%;\r\n            display: flex;\r\n            flex-direction: row;\r\n            position: relative;\r\n            margin-bottom: 40rpx;\r\n\r\n            .specification-left {\r\n              width: 180rpx;\r\n              height: 180rpx;\r\n              flex: 0 0 180rpx;\r\n\r\n              .product-img {\r\n                width: 180rpx;\r\n                height: 180rpx;\r\n                background-color: #999999;\r\n              }\r\n            }\r\n\r\n            .specification-right {\r\n              flex: 1;\r\n              padding: 0 35rpx 10rpx 28rpx;\r\n              box-sizing: border-box;\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: flex-end;\r\n              font-weight: 500;\r\n\r\n              .price-content {\r\n                color: #fe560a;\r\n                margin-bottom: 10rpx;\r\n\r\n                .sign {\r\n                  font-size: 28rpx;\r\n                  margin-right: 4rpx;\r\n                }\r\n\r\n                .price {\r\n                  font-size: 44rpx;\r\n                }\r\n              }\r\n\r\n              .inventory {\r\n                font-size: 24rpx;\r\n                color: #525252;\r\n                margin-bottom: 14rpx;\r\n              }\r\n\r\n              .choose {\r\n                font-size: 24rpx;\r\n                color: #525252;\r\n                min-height: 32rpx;\r\n              }\r\n            }\r\n          }\r\n\r\n          .specification-content {\r\n            font-weight: 500;\r\n            .specification-item {\r\n              margin-bottom: 40rpx;\r\n\r\n              &:last-child {\r\n                margin-bottom: 0;\r\n              }\r\n\r\n              .item-title {\r\n                margin-bottom: 15rpx;\r\n                font-size: 32rpx;\r\n                font-weight: bold;\r\n                color: #000000;\r\n              }\r\n\r\n              .item-wrapper {\r\n                display: flex;\r\n                flex-direction: row;\r\n                flex-flow: wrap;\r\n                margin-bottom: -20rpx;\r\n\r\n                .item-content {\r\n                  display: block;\r\n                  padding: 10rpx 20rpx;\r\n                  min-width: 110rpx;\r\n                  text-align: center;\r\n                  font-size: 24rpx;\r\n                  border-radius: 30rpx;\r\n                  background-color: #ffffff;\r\n                  color: #333333;\r\n                  margin-right: 20rpx;\r\n                  margin-bottom: 20rpx;\r\n                  border: 2rpx solid #cccccc;\r\n                  box-sizing: border-box;\r\n\r\n                  &.actived {\r\n                    border-color: #fe560a;\r\n                    color: #fe560a;\r\n                  }\r\n\r\n                  &.noactived {\r\n                    // background-color: #e4e4e4;\r\n                    // border-color: #e4e4e4;\r\n                    // color: #9e9e9e;\r\n                    // text-decoration: line-through;\r\n                    color: #c8c9cc;\r\n                    background: #f2f3f5;\r\n                    border-color: #f2f3f5;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .close {\r\n          position: absolute;\r\n          top: 30rpx;\r\n          right: 25rpx;\r\n          width: 50rpx;\r\n          height: 50rpx;\r\n          text-align: center;\r\n          line-height: 50rpx;\r\n\r\n          .close-item {\r\n            width: 40rpx;\r\n            height: 40rpx;\r\n          }\r\n        }\r\n      }\r\n\r\n      .btn-wrapper {\r\n        display: flex;\r\n        width: 100%;\r\n        height: 120rpx;\r\n        flex: 0 0 120rpx;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        padding: 0 26rpx;\r\n        box-sizing: border-box;\r\n\r\n        .layer-btn {\r\n          width: 335rpx;\r\n          height: 80rpx;\r\n          border-radius: 40rpx;\r\n          color: #fff;\r\n          line-height: 80rpx;\r\n          text-align: center;\r\n          font-weight: 500;\r\n          font-size: 28rpx;\r\n\r\n          &.add-cart {\r\n            background: #ffbe46;\r\n          }\r\n\r\n          &.buy {\r\n            background: #fe560a;\r\n          }\r\n        }\r\n\r\n        .sure {\r\n          width: 698rpx;\r\n          height: 80rpx;\r\n          border-radius: 38rpx;\r\n          color: #fff;\r\n          line-height: 80rpx;\r\n          text-align: center;\r\n          font-weight: 500;\r\n          font-size: 28rpx;\r\n          background: #fe560a;\r\n        }\r\n\r\n        .sure.add-cart {\r\n          background: #ff9402;\r\n        }\r\n      }\r\n    }\r\n\r\n    @keyframes showPopup {\r\n      0% {\r\n        opacity: 0;\r\n      }\r\n\r\n      100% {\r\n        opacity: 1;\r\n      }\r\n    }\r\n\r\n    @keyframes hidePopup {\r\n      0% {\r\n        opacity: 1;\r\n      }\r\n\r\n      100% {\r\n        opacity: 0;\r\n      }\r\n    }\r\n\r\n    @keyframes showLayer {\r\n      0% {\r\n        transform: translateY(120%);\r\n      }\r\n\r\n      100% {\r\n        transform: translateY(0%);\r\n      }\r\n    }\r\n\r\n    @keyframes hideLayer {\r\n      0% {\r\n        transform: translateY(0);\r\n      }\r\n\r\n      100% {\r\n        transform: translateY(120%);\r\n      }\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=63515b46&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=63515b46&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426034\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}