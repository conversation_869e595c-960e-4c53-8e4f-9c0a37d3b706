<view class="switch-container data-v-6e6e5df4" style="{{'background:'+(bj_color)+';'}}"><view class="switch_view data-v-6e6e5df4"><view class="{{['switch-item','data-v-6e6e5df4',(isSwitch)?'checked_switch':'']}}" style="{{(isSwitch?'color:'+checked_color:'')}}" animation="{{animationData2}}" data-event-opts="{{[['tap',[['changeSwitch',[true]]]]]}}" catchtap="__e">{{''+switchList[0]+''}}</view><view class="{{['switch-item','data-v-6e6e5df4',(!isSwitch)?'checked_switch':'']}}" style="{{(!isSwitch?'color:'+checked_color:'')}}" animation="{{animationData3}}" data-event-opts="{{[['tap',[['changeSwitch',[false]]]]]}}" catchtap="__e">{{''+switchList[1]+''}}</view></view><block wx:if="{{disabled}}"><view class="disabled data-v-6e6e5df4"></view></block><view class="position_view data-v-6e6e5df4" style="{{'background:'+(checked_bj_color)+';'}}" animation="{{animationData1}}"></view></view>