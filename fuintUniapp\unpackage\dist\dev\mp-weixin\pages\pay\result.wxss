@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.success.data-v-c3675ac6 {
  width: 100%;
  text-align: center;
  margin-top: 200rpx;
}
.success .result.data-v-c3675ac6 {
  font-size: 35rpx;
  text-align: center;
  padding: 10rpx 10rpx 10rpx 50rpx;
  height: 70rpx;
}
.success .result .icon.data-v-c3675ac6 {
  width: 45rpx;
  height: 45rpx;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
}
.success .result .text.data-v-c3675ac6 {
  text-align: center;
  height: 100%;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
  color: #00B83F;
  font-weight: bold;
}
.success .amount.data-v-c3675ac6 {
  font-weight: bold;
  font-size: 65rpx;
  margin-top: 50rpx;
  margin-bottom: 50rpx;
  color: #000000;
}
.success .point.data-v-c3675ac6 {
  font-size: 30rpx;
}
.attention.data-v-c3675ac6 {
  width: 100%;
  text-align: center;
  margin-top: 14rpx;
}
.confirm.data-v-c3675ac6 {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
  width: 300rpx;
  margin: 50rpx auto;
  background: #3f51b5;
}
