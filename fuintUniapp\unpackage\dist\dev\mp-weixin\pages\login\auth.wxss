@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-0a348a72 {
  padding: 0 60rpx;
  font-size: 32rpx;
  background: #fff;
  min-height: 100vh;
}
.wechatapp.data-v-0a348a72 {
  padding: 80rpx 0 48rpx;
  border-bottom: 1rpx solid #e3e3e3;
  margin-bottom: 72rpx;
  text-align: center;
}
.wechatapp .header.data-v-0a348a72 {
  width: 190rpx;
  height: 190rpx;
  border: 4rpx solid #fff;
  margin: 0 auto 0;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 2rpx 0 10rpx rgba(50, 50, 50, 0.3);
}
.auth-title.data-v-0a348a72 {
  color: #585858;
  font-size: 34rpx;
  margin-bottom: 40rpx;
}
.auth-subtitle.data-v-0a348a72 {
  color: #888;
  margin-bottom: 88rpx;
  font-size: 28rpx;
}
.login-btn.data-v-0a348a72 {
  padding: 0 20rpx;
}
.login-btn .button.data-v-0a348a72 {
  height: 88rpx;
  line-height: 88rpx;
  background: #3f51b5;
  color: #fff;
  font-size: 30rpx;
  border-radius: 12rpx;
  text-align: center;
}
.no-login-btn.data-v-0a348a72 {
  margin-top: 24rpx;
  padding: 0 20rpx;
}
.no-login-btn .button.data-v-0a348a72 {
  height: 88rpx;
  line-height: 88rpx;
  background: #dfdfdf;
  color: #fff;
  font-size: 30rpx;
  border-radius: 12rpx;
  text-align: center;
}
