@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.ws-privacy-popup.data-v-ee2dccb4 {
  padding: 24rpx;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  background: #fff;
  border-radius: 10rpx;
  margin-top: 50rpx;
}
.ws-privacy-popup .content-scroll.data-v-ee2dccb4 {
  min-height: 400rpx;
  max-height: 750rpx;
}
.ws-privacy-popup__header.data-v-ee2dccb4 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 52rpx;
  font-size: 36rpx;
  font-family: PingFangSC-Medium, PingFang SC;
  font-weight: 550;
  color: #1a1a1a;
  line-height: 52rpx;
  margin-bottom: 48rpx;
}
.ws-privacy-popup__container.data-v-ee2dccb4 {
  width: 100%;
  box-sizing: border-box;
  font-size: 28rpx;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #333333;
  line-height: 48rpx;
  margin-bottom: 48rpx;
}
.ws-privacy-popup__container-protocol.data-v-ee2dccb4 {
  font-weight: 550;
  color: #3f51b5;
}
.ws-privacy-popup__footer.data-v-ee2dccb4 {
  display: flex;
  flex-direction: column;
  margin-top: 30rpx;
}
.ws-privacy-popup__footer .is-disagree.data-v-ee2dccb4,
.ws-privacy-popup__footer .is-agree.data-v-ee2dccb4 {
  width: 100%;
  height: 88rpx;
  text-align: center;
  background: #ffffff;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #666666;
}
.ws-privacy-popup__footer .is-agree.data-v-ee2dccb4 {
  background: #3f51b5;
  color: #ffffff;
  margin-bottom: 18rpx;
}
.ws-privacy-popup__footer button.data-v-ee2dccb4 {
  border: none;
  outline: none;
}
.ws-privacy-popup__footer button.data-v-ee2dccb4::after {
  border: none;
}
