{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-tabs/u-tabs.vue?7a0e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-tabs/u-tabs.vue?05c8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-tabs/u-tabs.vue?a2ee", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-tabs/u-tabs.vue?5d92", "uni-app:///uview-ui/components/u-tabs/u-tabs.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-tabs/u-tabs.vue?1711", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-tabs/u-tabs.vue?bf24"], "names": ["name", "props", "isScroll", "type", "default", "list", "current", "height", "fontSize", "duration", "activeColor", "inactiveColor", "<PERSON><PERSON><PERSON><PERSON>", "barHeight", "gutter", "bgColor", "count", "offset", "bold", "activeItemStyle", "showBar", "barStyle", "itemWidth", "data", "scrollLeft", "tabQueryInfo", "componentWidth", "scrollBarLeft", "parentLeft", "id", "currentIndex", "barFirstTimeMove", "watch", "immediate", "handler", "computed", "tabBarStyle", "width", "transform", "Object", "tabItemStyle", "padding", "flex", "style", "methods", "init", "tabRect", "clickTab", "getTabRect", "query", "size", "rect", "scrollByIndex", "setTimeout", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA0pB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqB9qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1BA,gBA2BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAJ;MACAG;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;QACA;MACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;QACA;MACA;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;QACA;MACA;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;EACA;EACAmB;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACA;IACA3B;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MACA2B;MACAC;QAAA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;QACAC;QACA;QACA;QACA;QACA/B;QACA;QACA;MACA;MACAgC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;UACAjC;UACA;UACA;UACA;UACAkC;UACAC;UACAL;QACA;QACA;QACA;QACA;UACAM;UACA;UACAA;QACA;UACAA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACAC;UACAC;UACAC;QACA;MACA;MACA;MACAF,WACA;QACA;QACA;QACA;MACA,aACA;IACA;IACA;IACAG;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;UACA;QACA;MACA;IACA;EACA;EACAC;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC9SA;AAAA;AAAA;AAAA;AAAywC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACA7xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-tabs/u-tabs.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-tabs.vue?vue&type=template&id=3b2b1a80&scoped=true&\"\nvar renderjs\nimport script from \"./u-tabs.vue?vue&type=script&lang=js&\"\nexport * from \"./u-tabs.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-tabs.vue?vue&type=style&index=0&id=3b2b1a80&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3b2b1a80\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-tabs/u-tabs.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=template&id=3b2b1a80&scoped=true&\"", "var components\ntry {\n  components = {\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-badge/u-badge\" */ \"@/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s0 = _vm.__get_style([_vm.tabItemStyle(index)])\n    return {\n      $orig: $orig,\n      s0: s0,\n    }\n  })\n  var s1 = _vm.showBar ? _vm.__get_style([_vm.tabBarStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"u-tabs\" :style=\"{\n        background: bgColor\n    }\">\n        <!-- $u.getRect()对组件根节点无效，因为写了.in(this)，故这里获取内层接点尺寸 -->\n        <view :id=\"id\">\n            <scroll-view scroll-x class=\"u-scroll-view\" :scroll-left=\"scrollLeft\" scroll-with-animation>\n                <view class=\"u-scroll-box\" :class=\"{'u-tabs-scorll-flex': !isScroll}\">\n                    <view class=\"u-tab-item u-line-1\" :id=\"'u-tab-item-' + index\" v-for=\"(item, index) in list\" :key=\"index\" @tap=\"clickTab(index)\"\n                     :style=\"[tabItemStyle(index)]\">\n                        <u-badge :count=\"item[count] || item['count'] || 0\" :offset=\"offset\" size=\"mini\"></u-badge>\n                        {{ item[name] || item['name']}}\n                    </view>\n                    <view v-if=\"showBar\" class=\"u-tab-bar\" :style=\"[tabBarStyle]\"></view>\n                </view>\n            </scroll-view>\n        </view>\n    </view>\n</template>\n\n<script>\n    /**\n     * tabs 标签\n     * @description 该组件，是一个tabs标签组件，在标签多的时候，可以配置为左右滑动，标签少的时候，可以禁止滑动。 该组件的一个特点是配置为滚动模式时，激活的tab会自动移动到组件的中间位置。\n     * @tutorial https://www.uviewui.com/components/tabs.html\n     * @property {Boolean} is-scroll tabs是否可以左右拖动（默认true）\n     * @property {Array} list 标签数组，元素为对象，如[{name: '推荐'}]\n     * @property {String Number} current 指定哪个tab为激活状态（默认0）\n     * @property {String Number} height 导航栏的高度，单位rpx（默认80）\n     * @property {String Number} font-size tab文字大小，单位rpx（默认30）\n     * @property {String Number} duration 滑块移动一次所需的时间，单位秒（默认0.5）\n     * @property {String} active-color 滑块和激活tab文字的颜色（默认#2979ff）\n     * @property {String} inactive-color tabs文字颜色（默认#303133）\n     * @property {String Number} bar-width 滑块宽度，单位rpx（默认40）\n     * @property {Object} active-item-style 活动tabs item的样式，对象形式\n     * @property {Object} bar-style 底部滑块的样式，对象形式\n     * @property {Boolean} show-bar 是否显示底部的滑块（默认true）\n     * @property {String Number} bar-height 滑块高度，单位rpx（默认6）\n     * @property {String Number} item-width 标签的宽度（默认auto）\n     * @property {String Number} gutter 单个tab标签的左右内边距之和，单位rpx（默认40）\n     * @property {String} bg-color tabs导航栏的背景颜色（默认#ffffff）\n     * @property {String} name 组件内部读取的list参数中的属性名（tab名称），见官网说明（默认name）\n     * @property {String} count 组件内部读取的list参数中的属性名（badge徽标数），同name属性的使用，见官网说明（默认count）\n     * @property {Array} offset 设置badge徽标数的位置偏移，格式为 [x, y]，也即设置的为top和right的值，单位rpx（默认[5, 20]）\n     * @property {Boolean} bold 激活选项的字体是否加粗（默认true）\n     * @event {Function} change 点击标签时触发\n     * @example <u-tabs ref=\"tabs\" :list=\"list\" :is-scroll=\"false\"></u-tabs>\n     */\n    export default {\n        name: \"u-tabs\",\n        props: {\n            // 导航菜单是否需要滚动，如只有2或者3个的时候，就不需要滚动了，此时使用flex平分tab的宽度\n            isScroll: {\n                type: Boolean,\n                default: true\n            },\n            //需循环的标签列表\n            list: {\n                type: Array,\n                default () {\n                    return [];\n                }\n            },\n            // 当前活动tab的索引\n            current: {\n                type: [Number, String],\n                default: 0\n            },\n            // 导航栏的高度和行高\n            height: {\n                type: [String, Number],\n                default: 80\n            },\n            // 字体大小\n            fontSize: {\n                type: [String, Number],\n                default: 30\n            },\n            // 过渡动画时长, 单位ms\n            duration: {\n                type: [String, Number],\n                default: 0.5\n            },\n            // 选中项的主题颜色\n            activeColor: {\n                type: String,\n                default: '#2979ff'\n            },\n            // 未选中项的颜色\n            inactiveColor: {\n                type: String,\n                default: '#303133'\n            },\n            // 菜单底部移动的bar的宽度，单位rpx\n            barWidth: {\n                type: [String, Number],\n                default: 40\n            },\n            // 移动bar的高度\n            barHeight: {\n                type: [String, Number],\n                default: 6\n            },\n            // 单个tab的左或有内边距（左右相同）\n            gutter: {\n                type: [String, Number],\n                default: 30\n            },\n            // 导航栏的背景颜色\n            bgColor: {\n                type: String,\n                default: '#ffffff'\n            },\n            // 读取传入的数组对象的属性(tab名称)\n            name: {\n                type: String,\n                default: 'name'\n            },\n            // 读取传入的数组对象的属性(徽标数)\n            count: {\n                type: String,\n                default: 'count'\n            },\n            // 徽标数位置偏移\n            offset: {\n                type: Array,\n                default: () => {\n                    return [5, 20]\n                }\n            },\n            // 活动tab字体是否加粗\n            bold: {\n                type: Boolean,\n                default: true\n            },\n            // 当前活动tab item的样式\n            activeItemStyle: {\n                type: Object,\n                default() {\n                    return {}\n                }\n            },\n            // 是否显示底部的滑块\n            showBar: {\n                type: Boolean,\n                default: true\n            },\n            // 底部滑块的自定义样式\n            barStyle: {\n                type: Object,\n                default() {\n                    return {}\n                }\n            },\n            // 标签的宽度\n            itemWidth: {\n                type: [Number, String],\n                default: 'auto'\n            }\n        },\n        data() {\n            return {\n                scrollLeft: 0, // 滚动scroll-view的左边滚动距离\n                tabQueryInfo: [], // 存放对tab菜单查询后的节点信息\n                componentWidth: 0, // 屏幕宽度，单位为px\n                scrollBarLeft: 0, // 移动bar需要通过translateX()移动的距离\n                parentLeft: 0, // 父元素(tabs组件)到屏幕左边的距离\n                id: this.$u.guid(), // id值\n                currentIndex: this.current,\n                barFirstTimeMove: true, // 滑块第一次移动时(页面刚生成时)，无需动画，否则给人怪异的感觉\n            };\n        },\n        watch: {\n            // 监听tab的变化，重新计算tab菜单的布局信息，因为实际使用中菜单可能是通过\n            // 后台获取的（如新闻app顶部的菜单），获取返回需要一定时间，所以list变化时，重新获取布局信息\n            list(n, o) {\n                // list变动时，重制内部索引，否则可能导致超出数组边界的情况\n                if(n.length !== o.length) this.currentIndex = 0;\n                // 用$nextTick等待视图更新完毕后再计算tab的局部信息，否则可能因为tab还没生成就获取，就会有问题\n                this.$nextTick(() => {\n                    this.init();\n                });\n            },\n            current: {\n                immediate: true,\n                handler(nVal, oVal) {\n                    // 视图更新后再执行移动操作\n                    this.$nextTick(() => {\n                        this.currentIndex = nVal;\n                        this.scrollByIndex();\n                    });\n                }\n            },\n        },\n        computed: {\n            // 移动bar的样式\n            tabBarStyle() {\n                let style = {\n                    width: this.barWidth + 'rpx',\n                    transform: `translate(${this.scrollBarLeft}px, -100%)`,\n                    // 滑块在页面渲染后第一次滑动时，无需动画效果\n                    'transition-duration': `${this.barFirstTimeMove ? 0 : this.duration }s`,\n                    'background-color': this.activeColor,\n                    height: this.barHeight + 'rpx',\n                    // 设置一个很大的值，它会自动取能用的最大值，不用高度的一半，是因为高度可能是单数，会有小数出现\n                    'border-radius': `${this.barHeight / 2}px`\n                };\n                Object.assign(style, this.barStyle);\n                return style;\n            },\n            // tab的样式\n            tabItemStyle() {\n                return (index) => {\n                    let style = {\n                        height: this.height + 'rpx',\n                        'line-height': this.height + 'rpx',\n                        'font-size': this.fontSize + 'rpx',\n                        'transition-duration': `${this.duration}s`,\n                        padding: this.isScroll ? `0 ${this.gutter}rpx` : '',\n                        flex: this.isScroll ? 'auto' : '1',\n                        width: this.$u.addUnit(this.itemWidth)\n                    };\n                    // 字体加粗\n                    if (index == this.currentIndex && this.bold) style.fontWeight = 'bold';\n                    if (index == this.currentIndex) {\n                        style.color = this.activeColor;\n                        // 给选中的tab item添加外部自定义的样式\n                        style = Object.assign(style, this.activeItemStyle);\n                    } else {\n                        style.color = this.inactiveColor;\n                    }\n                    return style;\n                }\n            }\n        },\n        methods: {\n            // 设置一个init方法，方便多处调用\n            async init() {\n                // 获取tabs组件的尺寸信息\n                let tabRect = await this.$uGetRect('#' + this.id);\n                // tabs组件距离屏幕左边的宽度\n                this.parentLeft = tabRect.left;\n                // tabs组件的宽度\n                this.componentWidth = tabRect.width;\n                this.getTabRect();\n            },\n            // 点击某一个tab菜单\n            clickTab(index) {\n                // 点击当前活动tab，不触发事件\n                if(index == this.currentIndex) return ;\n                // 发送事件给父组件\n                this.$emit('change', index);\n            },\n            // 查询tab的布局信息\n            getTabRect() {\n                // 创建节点查询\n                let query = uni.createSelectorQuery().in(this);\n                // 历遍所有tab，这里是执行了查询，最终使用exec()会一次性返回查询的数组结果\n                for (let i = 0; i < this.list.length; i++) {\n                    // 只要size和rect两个参数\n                    query.select(`#u-tab-item-${i}`).fields({\n                        size: true,\n                        rect: true\n                    });\n                }\n                // 执行查询，一次性获取多个结果\n                query.exec(\n                    function(res) {\n                        this.tabQueryInfo = res;\n                        // 初始化滚动条和移动bar的位置\n                        this.scrollByIndex();\n                    }.bind(this)\n                );\n            },\n            // 滚动scroll-view，让活动的tab处于屏幕的中间位置\n            scrollByIndex() {\n                // 当前活动tab的布局信息，有tab菜单的width和left(为元素左边界到父元素左边界的距离)等信息\n                let tabInfo = this.tabQueryInfo[this.currentIndex];\n                if (!tabInfo) return;\n                // 活动tab的宽度\n                let tabWidth = tabInfo.width;\n                // 活动item的左边到tabs组件左边的距离，用item的left减去tabs的left\n                let offsetLeft = tabInfo.left - this.parentLeft;\n                // 将活动的tabs-item移动到屏幕正中间，实际上是对scroll-view的移动\n                let scrollLeft = offsetLeft - (this.componentWidth - tabWidth) / 2;\n                this.scrollLeft = scrollLeft < 0 ? 0 : scrollLeft;\n                // 当前活动item的中点点到左边的距离减去滑块宽度的一半，即可得到滑块所需的移动距离\n                let left = tabInfo.left + tabInfo.width / 2 - this.parentLeft;\n                // 计算当前活跃item到组件左边的距离\n                this.scrollBarLeft = left - uni.upx2px(this.barWidth) / 2;\n                // 第一次移动滑块的时候，barFirstTimeMove为true，放到延时中将其设置false\n                // 延时是因为scrollBarLeft作用于computed计算时，需要一个过程需，否则导致出错\n                if(this.barFirstTimeMove == true) {\n                    setTimeout(() => {\n                        this.barFirstTimeMove = false;\n                    }, 100)\n                }\n            }\n        },\n        mounted() {\n            this.init();\n        }\n    };\n</script>\n\n<style lang=\"scss\" scoped>\n    @import \"../../libs/css/style.components.scss\";\n\n    view,\n    scroll-view {\n        box-sizing: border-box;\n    }\n\n    /* #ifndef APP-NVUE */\n    ::-webkit-scrollbar,\n    ::-webkit-scrollbar,\n    ::-webkit-scrollbar {\n        display: none;\n        width: 0 !important;\n        height: 0 !important;\n        -webkit-appearance: none;\n        background: transparent;\n    }\n    /* #endif */\n\n    .u-scroll-box {\n        position: relative;\n        /* #ifdef MP-TOUTIAO */\n        white-space: nowrap;\n        /* #endif */\n    }\n\n    /* #ifdef H5 */\n    // 通过样式穿透，隐藏H5下，scroll-view下的滚动条\n    scroll-view ::v-deep ::-webkit-scrollbar {\n        display: none;\n        width: 0 !important;\n        height: 0 !important;\n        -webkit-appearance: none;\n        background: transparent;\n    }\n    /* #endif */\n\n    .u-scroll-view {\n        width: 100%;\n        white-space: nowrap;\n        position: relative;\n    }\n\n    .u-tab-item {\n        position: relative;\n        /* #ifndef APP-NVUE */\n        display: inline-block;\n        /* #endif */\n        text-align: center;\n        transition-property: background-color, color;\n    }\n\n    .u-tab-bar {\n        position: absolute;\n        bottom: 0;\n    }\n\n    .u-tabs-scorll-flex {\n        @include vue-flex;\n        justify-content: space-between;\n    }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=style&index=0&id=3b2b1a80&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabs.vue?vue&type=style&index=0&id=3b2b1a80&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425043\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}