<view class="container data-v-1ed35bd0"><block wx:if="{{isLoading}}"><view class="loading data-v-1ed35bd0"><u-loading vue-id="88cec194-1" mode="circle" class="data-v-1ed35bd0" bind:__l="__l"></u-loading></view></block><block wx:else><view data-event-opts="{{[['tap',[['handleSelect']]]]}}" class="field-body data-v-1ed35bd0" bindtap="__e"><view class="field-value onelist-hidden data-v-1ed35bd0">{{valueText?valueText:placeholder}}</view></view></block><u-select vue-id="88cec194-2" mode="mutil-column-auto" list="{{options}}" default-value="{{defaultValue}}" value="{{show}}" data-event-opts="{{[['^confirm',[['onConfirm']]],['^input',[['__set_model',['','show','$event',[]]]]]]}}" bind:confirm="__e" bind:input="__e" class="data-v-1ed35bd0" bind:__l="__l"></u-select></view>