@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-ec1e8658 {
  background: #FFFFFF;
}
.my-point.data-v-ec1e8658 {
  height: 320rpx;
  color: #FFFFFF;
  background: #3f51b5;
  padding-top: 80rpx;
  text-align: center;
}
.my-point .my-tip.data-v-ec1e8658 {
  text-align: center;
}
.my-point .my-account.data-v-ec1e8658 {
  text-align: center;
  font-size: 45rpx;
  font-weight: bold;
  margin-top: 14rpx;
}
.my-point .iconfont.data-v-ec1e8658 {
  height: 30rpx;
  width: 30rpx;
  margin-right: 5rpx;
}
.my-point .my-gift.data-v-ec1e8658 {
  text-align: center;
  display: inline-block;
}
.my-point .my-gift .gift.data-v-ec1e8658 {
  height: 60rpx;
  width: 140rpx;
  margin-top: 20rpx;
  line-height: 60rpx;
  text-align: center;
  float: left;
  margin-right: 20rpx;
  margin-left: 20rpx;
  border: 1px solid #f86d48;
  border-radius: 6rpx;
  color: #FFFFFF;
  background: #f86d48;
  font-size: 22rpx;
}
.log-list.data-v-ec1e8658 {
  padding: 0 30rpx;
  background: #FFFFFF;
  border-radius: 20rpx;
}
.log-item.data-v-ec1e8658 {
  font-size: 32rpx;
  padding: 20rpx 20rpx;
  line-height: 1.8;
  border-bottom: 1rpx solid #eeeeee;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rec-time.data-v-ec1e8658 {
  color: #888888;
  font-size: 24rpx;
}
.empty-ipt.data-v-ec1e8658 {
  width: 220rpx;
  margin: 10rpx auto;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  color: #fff;
  border-radius: 5rpx;
  background: linear-gradient(to right, #3f51b5, #3f51b5);
}
