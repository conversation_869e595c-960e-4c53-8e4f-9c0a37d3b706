{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeTakeCoupon.vue?fb53", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeTakeCoupon.vue?d159", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeTakeCoupon.vue?cabf", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeTakeCoupon.vue?3772", "uni-app:///pages/index/components/HomeTakeCoupon.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeTakeCoupon.vue?b848", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeTakeCoupon.vue?7cbb"], "names": ["data", "methods", "closePopup", "claimGift", "CouponApi", "rev", "console", "$error"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,ipBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACctrB;AAAA;AAAA;;;;;;;;;;;;;;eACA;EACAA;IACA,QAEA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACAC;gBACA;kBACA;gBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnCA;AAAA;AAAA;AAAA;AAA28B,CAAgB,g5BAAG,EAAC,C;;;;;;;;;;;ACA/9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/components/HomeTakeCoupon.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./HomeTakeCoupon.vue?vue&type=template&id=04a62cc2&\"\nvar renderjs\nimport script from \"./HomeTakeCoupon.vue?vue&type=script&lang=js&\"\nexport * from \"./HomeTakeCoupon.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HomeTakeCoupon.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/HomeTakeCoupon.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeTakeCoupon.vue?vue&type=template&id=04a62cc2&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeTakeCoupon.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeTakeCoupon.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"popup\">\n      <view class=\"popup-content\">\n        <view class=\"title\">温馨提示</view>\n        <view class=\"content\">您已成为新会员，可领取价值109元的礼包！</view>\n        <view class=\"buttons\">\n          <button class=\"cancel\" @click=\"closePopup\">稍后再说</button>\n          <button class=\"confirm\" @click=\"claimGift\">立即领取</button>\n        </view>\n      </view>\n    </view>\n</template>\n\n<script>\r\n  import * as CouponApi from '@/api/coupon'\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\t\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tclosePopup(){\r\n\t\t\t\tthis.$emit('close');\r\n\t\t\t},\r\n\t\t\tasync claimGift(){\r\n\t\t\t\tconst rev = await CouponApi.takeNewMemberCoupons()\r\n\t\t\t\tconsole.log(rev);\r\n\t\t\t\tif(rev.code == 200){\r\n\t\t\t\t\tthis.$navTo('pages/my-coupon/index')\r\n\t\t\t\t}else{\r\n\t\t\t\t\t$error(rev.message);\r\n\t\t\t\t}\r\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n.popup {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0, 0, 0, 0.5);\n  display: flex;\n  justify-content: center;\n  align-items: center;\r\n  z-index: 100;\n}\n\n.popup-content {\n  background: #fff;\n  padding: 20px;\n  border-radius: 10px;\n  text-align: center;\n}\n\n.title {\n  font-size: 18px;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n.content {\n  font-size: 14px;\n  color: #666;\n  margin-bottom: 20px;\n}\n\n.buttons button {\n  margin: 10px;\n  padding: 5px 10px;\n  border-radius: 5px;\n}\n\n.confirm {\n  background: #007AFF;\n  color: #fff;\n}\n\n.cancel {\n  background: #ccc;\n  color: #333;\n}\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeTakeCoupon.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeTakeCoupon.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425162\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}