{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/detail.vue?d3bc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/detail.vue?e69f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/detail.vue?7d07", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/detail.vue?db1e", "uni-app:///pages/book/detail.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/detail.vue?88b6", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/detail.vue?0971"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "None", "data", "bookId", "bookInfo", "dateArr", "week", "date", "timeArr", "dateIndex", "timeIndex", "storeInfo", "bookable", "is<PERSON><PERSON><PERSON>", "onLoad", "onShow", "uni", "methods", "getBookDetail", "app", "BookApi", "then", "finally", "toMoreStore", "getStoreInfo", "SettingApi", "doSubmit", "title", "content", "success", "time", "timeClick", "dateClick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpCA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgC/pB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;MAAA;QAAAD;QAAAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;MACAC,2BACAC;QACAF;QACAA;QACAA;QACAA;MACA,GACAG;QAAA;MAAA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAC,yBACAJ;QACAF;MACA;IACA;IACA;IACAO;MACA;MACA;QACAP;QACA;MACA;MAEAH;QACAW;QACAC;QACAC;UACA;YACA;YACA;YACA;cAAA1B;cAAAG;cAAAC;cAAAuB;YAAA;YACAd;YACAG;UACA;QACA;MACA;IACA;IACA;IACAY;MACA;MACA;QACA;MACA;MACAZ;MACAA;IACA;IACA;IACAa;MACA;MACAb;MACAA;MACA;MACA;MACAC;QAAAjB;QAAAI;QAAAuB;MAAA,GACAT;QACA;UACAF;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5IA;AAAA;AAAA;AAAA;AAA8uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAlwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/book/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/book/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=52bf62ce&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=52bf62ce&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52bf62ce\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/book/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=52bf62ce&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.dateArr && _vm.dateArr.length > 0\n  var g1 = _vm.dateArr.length\n  var g2 = _vm.timeArr && _vm.timeArr.length > 0\n  var l0 = g2\n    ? _vm.__map(_vm.timeArr, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g3 = !(_vm.timeIndex == index)\n          ? _vm.bookable.indexOf(item.time)\n          : null\n        return {\n          $orig: $orig,\n          g3: g3,\n        }\n      })\n    : null\n  var g4 = _vm.timeArr.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        l0: l0,\n        g4: g4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"content\">\n        <view class=\"top-v\" v-show=\"storeInfo\"><text class=\"storeName\">预约【{{ storeInfo.name }}】</text><text class=\"moreStore\" @click=\"toMoreStore\">切换门店</text></view>\r\n        \r\n        <view class=\"info-v\">\n            <view class=\"title\">\n                ｜请选择预约日期\n            </view>\n                <view v-if=\"dateArr && dateArr.length > 0\" class=\"list-v\">\n                    <view @click=\"dateClick(index)\" v-for=\"(item, index) in dateArr\" :key=\"index\" :class=\"[dateIndex==index?'activeItem':'item-v']\">\n                        <view>{{item.week}}</view>\n                        <view>{{ item.date }}</view>\n                    </view>\n                </view>\r\n                <none v-if=\"!dateArr.length\" :isLoading=\"false\" :custom-style=\"{ padding: '30px 10px' }\" tips=\"暂无可预约日期\"></none>\n        </view>\n        <view class=\"info-v\">\n            <view class=\"title\">\n                ｜请选择预约时段\n            </view>\n                <view v-if=\"timeArr && timeArr.length > 0\" class=\"list-v\">\n                    <view @click=\"timeClick(index)\" v-for=\"(item, index) in timeArr\" :key=\"index\"  :class=\"[timeIndex==index?'activeItem' : (bookable.indexOf(item.time) >= 0 ? 'item-v' : 'disable') ]\">\n                        <view>{{ item.time }}</view>\n                    </view>\n                </view>\r\n                <none v-if=\"!timeArr.length\" :isLoading=\"false\" :custom-style=\"{ padding: '30rpx 10rpx' }\" tips=\"暂无可预约时段\"></none>\n        </view>\n        <view class=\"btn\" @click=\"doSubmit\">确定预约</view>\n    </view>\n</template>\n\n<script>\r\n    import * as BookApi from '@/api/book'\r\n    import * as SettingApi from '@/api/setting'\r\n    import None from '@/components/none'\n    export default {\r\n        components: {\r\n          None\r\n        },\r\n        data() {\r\n            return {\r\n                // 预约项目ID\r\n                bookId: null,\r\n                // 当前预约详情\r\n                bookInfo: null,\n                dateArr: [ { week: '星期六', date : '8月17号' }, { week: '星期日', date : '8月18号' }], \n                timeArr: [ '09:00-12:00', '14:00-15:00' ],\r\n                dateIndex: 0,\r\n                timeIndex: 100000,\n                storeInfo: null,\r\n                bookable: [],\r\n                isCheck: false\n            }\r\n        },\r\n        onLoad(options) {\r\n            // 记录预约ID\r\n            this.bookId = options.bookId;\r\n            // 获取预约详情\r\n            this.getBookDetail();\n        },\r\n        onShow() {\r\n            uni.removeStorageSync('bookData');\n            this.getStoreInfo();\n            this.dateIndex = 0;\n            this.timeIndex = 100000;\n        },\n        methods: {\r\n            // 获取预约项目详情\r\n            getBookDetail() {\r\n              const app = this;\r\n              app.isLoading = true;\r\n              BookApi.detail(app.bookId)\r\n                .then(result => {\r\n                    app.bookInfo = result.data.bookInfo;\r\n                    app.dateArr = app.bookInfo.dateList;\r\n                    app.timeArr = app.bookInfo.timeList;\r\n                    app.dateClick(app.dateIndex);\r\n                })\r\n                .finally(() => app.isLoading = false)\r\n            },\r\n            // 切换门店\n            toMoreStore() {\n               this.$navTo('pages/location/index');\n            },\r\n            // 获取店铺详情\r\n            getStoreInfo() {\r\n               const app = this;\r\n               SettingApi.storeDetail()\r\n               .then(result => {\r\n                   app.storeInfo = result.data.storeInfo;\r\n               })\r\n            },\r\n            // 确定预约\n            doSubmit() {\n                let app = this;\r\n                if (!app.isCheck) {\r\n                    app.$toast(\"请选择预约时间！\");\r\n                    return false;\r\n                }\r\n                \n                uni.showModal({\n                    title: '提示',\n                    content: '确定预约【'+app.storeInfo.name+'】吗?',\n                    success: function (res) {\n                        if (res.confirm) {\r\n                            let dates = app.bookInfo.serviceDates.split(\",\");\r\n                            let week = app.dateArr[app.dateIndex].week;\n                            let data = { bookId: app.bookId, week: week, date : dates[app.dateIndex], time: app.timeArr[app.timeIndex].time };\n                            uni.setStorageSync('bookData', data);\r\n                            app.$navTo('pages/book/submit');\n                        }\n                    }\n                });\n            },\r\n            // 选择时段\n            timeClick(index) {\r\n                const app = this;\r\n                if (app.bookable.indexOf(app.timeArr[index].time) < 0) {\r\n                    return false;\r\n                }\n                app.timeIndex = index;\r\n                app.isCheck = true;\n            },\r\n            // 选择日期\n            dateClick(index) {\r\n                const app = this;\n                app.dateIndex = index;\n                app.timeIndex = 100000;\r\n                let dates = app.bookInfo.serviceDates.split(\",\");\r\n                let times = app.timeArr;\r\n                BookApi.bookable({ bookId: app.bookId, date: dates[app.dateIndex], time: '' })\r\n                  .then(result => {\r\n                     if (result.data) {\r\n                         app.bookable = result.data;\r\n                     } else {\r\n                         app.bookable = [];\r\n                     }\r\n                  })\n            }\n        }\r\n    }\r\n</script>\n\n<style lang=\"scss\" scoped>\n    .content {\n        .top-v {\n            margin: 20rpx;\n            .storeName {\n                font-weight: bold;\n                font-size: 32rpx;\n            }\n            .moreStore {\n                float: right;\n                color: $fuint-theme;\n                border: 1rpx solid $fuint-theme;\n                padding: 6rpx;\n                border-radius: 20rpx;\n                font-size: 24rpx;\n            }\n        }\n        padding-bottom: 50rpx;\n    }\n    .getInfo-v {\n        background-color: #fff;\n        padding: 50rpx 30rpx;\n        border-radius: 20rpx;\n        width: 600rpx;\n        .getInfo-btn{\n            background-color: $fuint-theme;\n            color: #fff;\n            padding: 20rpx;\n            border-radius: 10rpx;\n            margin-top: 30rpx;\n            text-align: center;\n        }\n    }\n    .btn {\n        margin: 20rpx auto;\n        background-color: $fuint-theme;\n        padding: 20rpx;\n        border-radius: 40rpx;\n        text-align: center;\n        color: #fff;\n        width: 680rpx;\n        font-size: 30rpx;\n        margin-top: 50rpx;\n    }\n    \n    .info-v {\n        padding: 20rpx;\n        background-color: #fff;\n        margin-bottom: 20rpx;\n        .title {\n            font-weight: bold;\n            color: $fuint-theme;\n        }\n        .list-v {\n            padding: 20rpx;\n            display: flex;\n            flex-wrap: wrap;\n            .item-v {\n                border-radius: 12rpx;\n                font-size: 30rpx;\n                margin-top: 10rpx;\n                margin-left: 10rpx;\r\n                font-weight: bold;\n                width: 30%;\n                border: 1rpx solid #ccc;\n                text-align: center;\n                padding: 20rpx;\n            }\n            .activeItem {\n                font-size: 30rpx;\n                border-radius: 12rpx;\n                margin-top: 10rpx;\n                margin-left: 10rpx;\n                width: 30%;\n                font-weight: bold;\n                background-color: $fuint-theme;\n                border: 1rpx solid #ccc;\n                color: #fff;\n                text-align: center;\n                padding: 20rpx;\n            }\r\n            .disable {\r\n                border-radius: 12rpx;\r\n                font-size: 30rpx;\r\n                margin-top: 10rpx;\r\n                margin-left: 10rpx;\r\n                font-weight: bold;\r\n                width: 30%;\r\n                border: 1rpx solid #ccc;\r\n                text-align: center;\r\n                color: white !important;\r\n                background-color: rgb(188, 188, 188) !important;\r\n                padding: 20rpx;\r\n            }\n        }\n    }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=52bf62ce&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&id=52bf62ce&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420761\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}