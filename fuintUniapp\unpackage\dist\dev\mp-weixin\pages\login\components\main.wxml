<view class="container data-v-248061c3"><view class="header data-v-248061c3"><view class="title data-v-248061c3"><view data-event-opts="{{[['tap',[['switchLoginType',['account']]]]]}}" class="item data-v-248061c3" bindtap="__e"><text class="{{['data-v-248061c3',loginType==='account'?'active':'']}}">{{accountTitle}}</text></view><view data-event-opts="{{[['tap',[['switchLoginType',['sms']]]]]}}" class="item data-v-248061c3" bindtap="__e"><text class="{{['data-v-248061c3',loginType==='sms'?'active':'']}}">短信登录</text></view></view></view><block wx:if="{{loginType==='account'}}"><view class="login-form data-v-248061c3"><view class="form-item data-v-248061c3"><text class="iconfont icon-sy-yh data-v-248061c3"></text><input class="form-item--input uni-input data-v-248061c3" type="text" maxlength="30" clearable="true" placeholder="请输入您的用户名" data-event-opts="{{[['input',[['__set_model',['','account','$event',[]]]]]]}}" value="{{account}}" bindinput="__e"/></view><view class="form-item data-v-248061c3"><text class="iconfont icon-suo data-v-248061c3"></text><input class="form-item--input data-v-248061c3" type="password" autocomplete="off" maxlength="30" minlength="1" value="{{password}}" placeholder="请输入您的密码" data-event-opts="{{[['input',[['__set_model',['','password','$event',[]]]]]]}}" bindinput="__e"/></view><block wx:if="{{isRegister}}"><view class="form-item data-v-248061c3"><text class="iconfont icon-suo data-v-248061c3"></text><input class="form-item--input data-v-248061c3" type="password" autocomplete="off" maxlength="30" value="{{password1}}" placeholder="请再次输入密码" data-event-opts="{{[['input',[['__set_model',['','password1','$event',[]]]]]]}}" bindinput="__e"/></view></block><view class="form-item data-v-248061c3"><text class="iconfont icon-tuxingyanzhengma data-v-248061c3"></text><input class="form-item--input data-v-248061c3" type="text" maxlength="5" placeholder="请输入图形验证码" data-event-opts="{{[['input',[['__set_model',['','captchaCode','$event',[]]]]]]}}" value="{{captchaCode}}" bindinput="__e"/><view class="form-item--parts data-v-248061c3"><view data-event-opts="{{[['tap',[['getCaptcha']]]]}}" class="captcha data-v-248061c3" bindtap="__e"><image class="image data-v-248061c3" src="{{captcha}}"></image></view></view></view><block wx:if="{{!isRegister}}"><view data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" class="login-button data-v-248061c3" bindtap="__e"><text class="data-v-248061c3">立即登录</text></view></block><block wx:if="{{isRegister}}"><view data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" class="login-button data-v-248061c3" bindtap="__e"><text class="data-v-248061c3">立即注册</text></view></block><view data-event-opts="{{[['tap',[['handleCancel',['$event']]]]]}}" class="cancel-button data-v-248061c3" bindtap="__e"><text class="data-v-248061c3">取消</text></view><block wx:if="{{!isRegister}}"><view data-event-opts="{{[['tap',[['toRegister']]]]}}" class="register data-v-248061c3" bindtap="__e">还没有账号？去注册</view></block><block wx:if="{{isRegister}}"><view data-event-opts="{{[['tap',[['toRegister']]]]}}" class="register data-v-248061c3" bindtap="__e">已有账号？立即登录</view></block></view></block><block wx:if="{{loginType==='sms'}}"><view class="login-form data-v-248061c3"><view class="form-item data-v-248061c3"><text class="iconfont icon-shoujihao data-v-248061c3"></text><text class="pre-mobile data-v-248061c3">+86</text><input class="form-item--input data-v-248061c3" style="padding-left:12rpx;" type="number" maxlength="11" placeholder="请输入手机号码" data-event-opts="{{[['input',[['__set_model',['','mobile','$event',[]]]]]]}}" value="{{mobile}}" bindinput="__e"/></view><view class="form-item data-v-248061c3"><text class="iconfont icon-tuxingyanzhengma data-v-248061c3"></text><input class="form-item--input data-v-248061c3" type="text" maxlength="5" placeholder="请输入图形验证码" data-event-opts="{{[['input',[['__set_model',['','captchaCode','$event',[]]]]]]}}" value="{{captchaCode}}" bindinput="__e"/><view class="form-item--parts data-v-248061c3"><view data-event-opts="{{[['tap',[['getCaptcha']]]]}}" class="captcha data-v-248061c3" bindtap="__e"><image class="image data-v-248061c3" src="{{captcha}}"></image></view></view></view><view class="form-item data-v-248061c3"><text class="iconfont icon-yanzhengma data-v-248061c3"></text><input class="form-item--input data-v-248061c3" type="number" maxlength="6" placeholder="请输入短信验证码" data-event-opts="{{[['input',[['__set_model',['','smsCode','$event',[]]]]]]}}" value="{{smsCode}}" bindinput="__e"/><view class="form-item--parts data-v-248061c3"><view data-event-opts="{{[['tap',[['handelSmsCaptcha']]]]}}" class="captcha-sms data-v-248061c3" bindtap="__e"><block wx:if="{{!smsState}}"><text class="activate data-v-248061c3">获取验证码</text></block><block wx:else><text class="un-activate data-v-248061c3">{{"重新发送("+times+")秒"}}</text></block></view></view></view><view data-event-opts="{{[['tap',[['handleSubmit',['$event']]]]]}}" class="login-button data-v-248061c3" bindtap="__e"><text class="data-v-248061c3">验证码登录</text></view></view></block></view>