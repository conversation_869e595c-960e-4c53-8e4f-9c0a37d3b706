{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/index.vue?2d07", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/index.vue?3fce", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/index.vue?54de", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/index.vue?9475", "uni-app:///pages/login/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/index.vue?98de", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/index.vue?99a0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Main", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "isShowUserInfo", "isExistUserInfo", "partyData", "onShow", "store", "onLoad", "app", "methods", "getMpLogin", "Login<PERSON><PERSON>", "code", "isPrompt", "then", "console", "resolve", "catch", "passwordLogin", "onGetUserInfoSuccess", "userInfo", "o<PERSON>h", "onShowRegister"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACQ9pB;AACA;AAGA;AAAA;AAAA;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACAC;IACAC;EACA;EAEAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAC;IACA;IACA,2BAgBA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;;IAEAC;IAGA;IACA;MACAA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACA;QACAC;UAAAC;QAAA;UAAAC;QAAA,GACAC;UACAC;UACAC;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;QAAAP;QAAAQ;MACA;MACA;QAAAC;QAAAT;QAAAQ;MAAA;MACA;MACA;IACA;IAEA;IACAE;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/GA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4586967a&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4586967a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=4586967a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <div>\n    <MpWeixin v-if=\"isShowUserInfo\" @passwordLogin=\"passwordLogin\" @success=\"onGetUserInfoSuccess\" />\n    <Main v-else :isParty=\"isExistUserInfo\" :partyData=\"partyData\" />\n  </div>\n</template>\n\n<script>\r\nimport store from '@/store'\nimport { checkLogin, isWechat } from '../../utils/app'\nimport Main from './components/main'\nimport MpWeixin from './components/mp-weixin'\r\nimport * as LoginApi from '@/api/login'\n\n  export default {\n    components: {\n      Main,\n      MpWeixin\n    },\n\n    data() {\n      return {\n        // 是否显示获取用户信息组件\n        isShowUserInfo: false,\n        // 是否已获取到了用户信息\n        isExistUserInfo: false,\n        // 第三方用户信息数据\n        partyData: {}\n      }\n    },\r\n    \r\n    /**\r\n     * 生命周期函数--监听页面显示\r\n     */\r\n    onShow() {\r\n      const app = this\r\n      const isLogin = checkLogin();\r\n      if (isLogin) {\r\n          store.dispatch('Logout');\r\n      }\r\n      if (isWechat()) {\r\n          // #ifdef H5\r\n          uni.showLoading({ title: '页面加载中..', mask:true });\r\n          LoginApi.authLoginConfig()\r\n            .then(result => {\r\n                uni.hideLoading();\r\n                if (result.data.appId && result.data.domain) {\r\n                   const appId = result.data.appId;\r\n                   const domain = result.data.domain;\r\n                   const redirect_uri = encodeURIComponent(domain + \"#pages/login/auth\");\r\n                   const url = \"https://open.weixin.qq.com/connect/oauth2/authorize?appid=\" + appId + \"&redirect_uri=\"+ redirect_uri +\"&response_type=code&scope=snsapi_userinfo&state=1#wechat_redirect\";\r\n                   window.location.href = url;\r\n                   return true; \r\n                }\r\n          })\r\n          // #endif\r\n      }\r\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      const app = this\n      // 只有微信小程序才显示获取用户信息按钮\n      // #ifdef MP-WEIXIN\n      app.isShowUserInfo = wx.canIUse('getUserProfile')\n      // #endif\r\n      \r\n      const code = options.code;\r\n      if (code) {\r\n          app.getMpLogin(code);\r\n      }\n    },\n\n    methods: {\n\r\n      // 获取openid\r\n      getMpLogin(code) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          LoginApi.mpWxLogin({ code : code}, { isPrompt: false })\n            .then(result => {\n                console.log(\"login-info\", result);\n                resolve(result)\n            })\n            .catch(err => reject(err))\n        })\n      },\r\n      \r\n      // 使用账号密码登录\r\n      passwordLogin(show) {\r\n         this.isShowUserInfo = show;\r\n      },\r\n\n      // 获取到用户信息的回调函数\n      onGetUserInfoSuccess({ oauth, code, userInfo }) {\n        // 记录第三方用户信息数据\n        this.partyData = { oauth, code, userInfo }\n        // 显示注册页面\n        this.onShowRegister()\n      },\n\n      // 显示注册页面\n      onShowRegister() {\n        // 是否显示获取用户信息组件\n        this.isShowUserInfo = false\n        // 是否已获取到了用户信息\n        this.isExistUserInfo = true\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  //empty\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424011\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}