{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-badge/u-badge.vue?6d29", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-badge/u-badge.vue?4203", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-badge/u-badge.vue?7c53", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-badge/u-badge.vue?485b", "uni-app:///uview-ui/components/u-badge/u-badge.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-badge/u-badge.vue?597e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-badge/u-badge.vue?fe48"], "names": ["name", "props", "type", "default", "size", "isDot", "count", "overflowCount", "showZero", "offset", "absolute", "fontSize", "color", "bgColor", "isCenter", "computed", "boxStyle", "style", "showText", "show"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7BA;AAAA;AAAA;AAAA;AAA2pB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmB/qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,gBAiBA;EACAA;EACAC;IACA;IACAC;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;IACA;IACA;IACAK;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACA;IACAC;MACA;MACA;QACAC;QACAA;QACA;QACAA;MACA;QACAA;QACAA;QACAA;MACA;MACA;MACA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA,+BACA;QACA,oFACA;MACA;IACA;IACA;IACAC;MACA;MACA,iEACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACxIA;AAAA;AAAA;AAAA;AAA0wC,CAAgB,qqCAAG,EAAC,C;;;;;;;;;;;ACA9xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-badge/u-badge.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-badge.vue?vue&type=template&id=2b1712d8&scoped=true&\"\nvar renderjs\nimport script from \"./u-badge.vue?vue&type=script&lang=js&\"\nexport * from \"./u-badge.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-badge.vue?vue&type=style&index=0&id=2b1712d8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2b1712d8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-badge/u-badge.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=template&id=2b1712d8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show\n    ? _vm.__get_style([\n        {\n          top: _vm.offset[0] + \"rpx\",\n          right: _vm.offset[1] + \"rpx\",\n          fontSize: _vm.fontSize + \"rpx\",\n          position: _vm.absolute ? \"absolute\" : \"static\",\n          color: _vm.color,\n          backgroundColor: _vm.bgColor,\n        },\n        _vm.boxStyle,\n      ])\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=script&lang=js&\"", "<template>\n    <view v-if=\"show\" class=\"u-badge\" :class=\"[\n            isDot ? 'u-badge-dot' : '', \n            size == 'mini' ? 'u-badge-mini' : '',\n            type ? 'u-badge--bg--' + type : ''\n        ]\" :style=\"[{\n            top: offset[0] + 'rpx',\n            right: offset[1] + 'rpx',\n            fontSize: fontSize + 'rpx',\n            position: absolute ? 'absolute' : 'static',\n            color: color,\n            backgroundColor: bgColor\n        }, boxStyle]\"\n    >\n        {{showText}}\n    </view>\n</template>\n\n<script>\n    /**\n     * badge 角标\n     * @description 本组件一般用于展示头像的地方，如个人中心，或者评论列表页的用户头像展示等场所。\n     * @tutorial https://www.uviewui.com/components/badge.html\n     * @property {String Number} count 展示的数字，大于 overflowCount 时显示为 ${overflowCount}+，为0且show-zero为false时隐藏\n     * @property {Boolean} is-dot 不展示数字，只有一个小点（默认false）\n     * @property {Boolean} absolute 组件是否绝对定位，为true时，offset参数才有效（默认true）\n     * @property {String Number} overflow-count 展示封顶的数字值（默认99）\n     * @property {String} type 使用预设的背景颜色（默认error）\n     * @property {Boolean} show-zero 当数值为 0 时，是否展示 Badge（默认false）\n     * @property {String} size Badge的尺寸，设为mini会得到小一号的Badge（默认default）\n     * @property {Array} offset 设置badge的位置偏移，格式为 [x, y]，也即设置的为top和right的值，单位rpx。absolute为true时有效（默认[20, 20]）\n     * @property {String} color 字体颜色（默认#ffffff）\n     * @property {String} bgColor 背景颜色，优先级比type高，如设置，type参数会失效\n     * @property {Boolean} is-center 组件中心点是否和父组件右上角重合，优先级比offset高，如设置，offset参数会失效（默认false）\n     * @example <u-badge type=\"error\" count=\"7\"></u-badge>\n     */\n    export default {\n        name: 'u-badge',\n        props: {\n            // primary,warning,success,error,info\n            type: {\n                type: String,\n                default: 'error'\n            },\n            // default, mini\n            size: {\n                type: String,\n                default: 'default'\n            },\n            //是否是圆点\n            isDot: {\n                type: Boolean,\n                default: false\n            },\n            // 显示的数值内容\n            count: {\n                type: [Number, String],\n            },\n            // 展示封顶的数字值\n            overflowCount: {\n                type: Number,\n                default: 99\n            },\n            // 当数值为 0 时，是否展示 Badge\n            showZero: {\n                type: Boolean,\n                default: false\n            },\n            // 位置偏移\n            offset: {\n                type: Array,\n                default: () => {\n                    return [20, 20]\n                }\n            },\n            // 是否开启绝对定位，开启了offset才会起作用\n            absolute: {\n                type: Boolean,\n                default: true\n            },\n            // 字体大小\n            fontSize: {\n                type: [String, Number],\n                default: '24'\n            },\n            // 字体演示\n            color: {\n                type: String,\n                default: '#ffffff'\n            },\n            // badge的背景颜色\n            bgColor: {\n                type: String,\n                default: ''\n            },\n            // 是否让badge组件的中心点和父组件右上角重合，配置的话，offset将会失效\n            isCenter: {\n                type: Boolean,\n                default: false\n            }\n        },\n        computed: {\n            // 是否将badge中心与父组件右上角重合\n            boxStyle() {\n                let style = {};\n                if(this.isCenter) {\n                    style.top = 0;\n                    style.right = 0;\n                    // Y轴-50%，意味着badge向上移动了badge自身高度一半，X轴50%，意味着向右移动了自身宽度一半\n                    style.transform = \"translateY(-50%) translateX(50%)\";\n                } else {\n                    style.top = this.offset[0] + 'rpx';\n                    style.right = this.offset[1] + 'rpx';\n                    style.transform = \"translateY(0) translateX(0)\";\n                }\n                // 如果尺寸为mini，后接上scal()\n                if(this.size == 'mini') {\n                    style.transform = style.transform + \" scale(0.8)\";\n                }\n                return style;\n            },\n            // isDot类型时，不显示文字\n            showText() {\n                if(this.isDot) return '';\n                else {\n                    if(this.count > this.overflowCount) return `${this.overflowCount}+`;\n                    else return this.count;\n                }\n            },\n            // 是否显示组件\n            show() {\n                // 如果count的值为0，并且showZero设置为false，不显示组件\n                if(this.count == 0 && this.showZero == false) return false;\n                else return true;\n            }\n        }\n    }\n</script>\n\n<style lang=\"scss\" scoped>\n    @import \"../../libs/css/style.components.scss\";\n    \n    .u-badge {\n        /* #ifndef APP-NVUE */\n        display: inline-flex;\n        /* #endif */\n        justify-content: center;\n        align-items: center;\n        line-height: 24rpx;\n        padding: 4rpx 8rpx;\n        border-radius: 100rpx;\n        z-index: 9;\n        \n        &--bg--primary {\n            background-color: $u-type-primary;\n        }\n        \n        &--bg--error {\n            background-color: $u-type-error;\n        }\n        \n        &--bg--success {\n            background-color: $u-type-success;\n        }\n        \n        &--bg--info {\n            background-color: $u-type-info;\n        }\n        \n        &--bg--warning {\n            background-color: $u-type-warning;\n        }\n    }\n    \n    .u-badge-dot {\n        height: 16rpx;\n        width: 16rpx;\n        border-radius: 100rpx;\n        line-height: 1;\n    }\n    \n    .u-badge-mini {\n        transform: scale(0.8);\n        transform-origin: center center;\n    }\n    \n    // .u-primary {\n    //     background: $u-type-primary;\n    //     color: #fff;\n    // }\n    \n    // .u-error {\n    //     background: $u-type-error;\n    //     color: #fff;\n    // }\n    \n    // .u-warning {\n    //     background: $u-type-warning;\n    //     color: #fff;\n    // }\n    \n    // .u-success {\n    //     background: $u-type-success;\n    //     color: #fff;\n    // }\n    \n    // .u-black {\n    //     background: #585858;\n    //     color: #fff;\n    // }\n    \n    .u-info {\n        background-color: $u-type-info;\n        color: #fff;\n    }\n</style>", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=style&index=0&id=2b1712d8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-badge.vue?vue&type=style&index=0&id=2b1712d8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425080\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}