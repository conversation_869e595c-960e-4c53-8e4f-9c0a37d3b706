@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.u-select__action.data-v-a577ac80 {
  position: relative;
  line-height: 70rpx;
  height: 70rpx;
}
.u-select__action__icon.data-v-a577ac80 {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transition: -webkit-transform 0.4s;
  transition: transform 0.4s;
  transition: transform 0.4s, -webkit-transform 0.4s;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
  z-index: 1;
}
.u-select__action__icon--reverse.data-v-a577ac80 {
  -webkit-transform: rotate(-180deg) translateY(50%);
          transform: rotate(-180deg) translateY(50%);
}
.u-select__hader__title.data-v-a577ac80 {
  color: #606266;
}
.u-select--border.data-v-a577ac80 {
  border-radius: 6rpx;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
}
.u-select__header.data-v-a577ac80 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 40rpx;
}
.u-select__body.data-v-a577ac80 {
  width: 100%;
  height: 500rpx;
  overflow: hidden;
  background-color: #fff;
}
.u-select__body__picker-view.data-v-a577ac80 {
  height: 100%;
  box-sizing: border-box;
}
.u-select__body__picker-view__item.data-v-a577ac80 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #303133;
  padding: 0 8rpx;
}
