{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/index.vue?4e69", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/index.vue?d9d7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/index.vue?7994", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/index.vue?819d", "uni-app:///pages/user/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/index.vue?9b2e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/index.vue?79f2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "id", "name", "icon", "count", "type", "url", "components", "data", "SettingKeyEnum", "$platform", "isLoading", "is<PERSON>ogin", "setting", "userYearlyPaymentAmount", "userLevelProgress", "userInfo", "avatar", "gradeId", "mobile", "balance", "gradeInfo", "hafanInfo", "isMerchant", "gradeEndTime", "assets", "prestore", "timer", "coupon", "service", "orderNavbar", "todoCounts", "payment", "current", "showPopup", "memberGrade", "curGrade", "onShow", "methods", "getPageData", "app", "Promise", "then", "callback", "catch", "console", "initService", "item", "newService", "initOrderTabbar", "newItem", "newOrderNavbar", "getSetting", "resolve", "getUserInfo", "UserApi", "reject", "getUserAssets", "getTodoCounts", "toPay", "paid", "OrderApi", "goLogin", "onShowPopup", "toMemberCode", "userId", "onTargetOrder", "dataType", "onTargetMyCoupon", "uni", "tmplIds", "success", "fail", "MessageApi", "keys", "complete", "handleService", "handleCustomerService", "title", "onNavigateToTorchMiniProgram", "appId", "envVersion", "goToLevelDetail", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+I9pB;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;AAEA;AACA,mBACA;EAAAC;EAAAC;EAAAC;AAAA,GACA;EAAAF;EAAAC;EAAAC;EAAAC;AAAA,GACA;EAAAH;EAAAC;EAAAC;EAAAC;AAAA,EACA;;AAEA;AACA;AACA;AACA;AACA,eACA;EAAAH;EAAAC;EAAAC;EAAAE;EAAAC;AAAA,EACA;AAAA,eAEA;EACAC;IACA;EAAA,CACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACA;MACAC;QAAAf;QAAAC;QAAAe;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MACA;MACAC;MACA;MACAC;MACA;MACAC;QAAAC;MAAA;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EAGA;AACA;AACA;EACAC;IACA;IACA;;IAEA;IACA;;IAEA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACAC;MACAC,6FACAC;QACAF;QACA;QACAA;QACA;QACAA;QACA;QACAG;MACA,GACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAjB;QACA;UACAkB;QACA;QACAC;MACA;MACAR;IACA;IAEA;IACAS;MACA;MACA;MACAnB;QACA;QACA;UACAoB;QACA;QACAC;MACA;MACAX;IACA;IAEA;IACAY;MACA;MACA;QACAZ;QACAa;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACAd;MACA;QACAe,eACAb;UAAA;UACA;YAAA;YACAF;YACAA;UACA;YACAA;YACAA;cAAAvC;cAAAC;cAAAe;cAAAC;cAAAC;cAAAC;YAAA;UACA;;UAEA;UACA;YACA;YACA;UACA;UACAoB;UACAA;UACAA;UACAA;UACAA;UACAA;UACAA;UACAa;UACAA;QACA,GACAT;UACA;YACAJ;YACAa;UACA;YACAG;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAF,iBACAb;UAAA;UACAF;YAAAd;YAAAC;YAAAC;UAAA;UACAyB;QACA,GACAT;UACA;YACAJ;YACAA;cAAAd;cAAAC;cAAAC;YAAA;YACAyB;UACA;YACAG;UACA;QACA;MACA;IACA;IAEA;IACAE;MACA;MACA;QACA;UACAlB;YAAAR;YAAA2B;YAAAC;UAAA;UACAP;QACA;UACAQ,sBACAnB;YACAF;cAAAR;cAAA2B;cAAAC;YAAA;YACAP;UACA,GACAT;YACAJ;cAAAR;cAAA2B;cAAAC;YAAA;YACAJ;UACA;QACA;MACA;IACA;IAEA;IACAM;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAAC;MAAA;IACA;IAEA;IACAC;MACArB;MACA;MACA;QAAAsB;MAAA;IACA;IAEA;IACAC;MACAC;QACAC;QACAC;QACAC;UACA3B;QACA;MACA;MACA;MACA;QAEA4B;UAAAC;QAAA;UACA;UACA9E;YAAA0E;YACAC;cACA1B;YACA;YAAA2B;cACA3B;YACA;YAAA8B;cACAnC;YACA;UAAA;QACA;MAKA;QACAA;MACA;IACA;IAEA;IACAoC;MAAA;MACA;IACA;IAEA;IACAC;MAEA;;MAGA;MACAR;QACAS;QACA3E;MACA;IACA;IAEA;IACA4E;MACA;MAGAV;QACAW;QAAA;QACA;QACAC;QAAA;QACAV;UACA1B;QACA;QACA2B;UACA3B;UACAwB;YACAS;YACA3E;UACA;QACA;MACA;IASA;IAEA;IACA+E;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;MACAd;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACheA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=137d5072&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"137d5072\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=137d5072&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 =\n    !_vm.isLoading &&\n    _vm.isLogin &&\n    _vm.userInfo &&\n    _vm.userInfo.id &&\n    _vm.isLogin &&\n    _vm.hafanInfo &&\n    _vm.hafanInfo.wallet\n      ? (_vm.hafanInfo.wallet.balance / 100).toFixed(2)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n    <image class=\"container-bg-img\" src=\"https://hatea.zhijuchina.com/static/my_bg.png\"></image>\r\n    <view v-if=\"!isLoading\" class=\"new-user-layout\">\r\n \r\n      <!-- 会员信息卡片 -->\r\n      <view class=\"member-card-wrapper\">\r\n        <!-- 已登录状态 -->\r\n        <block v-if=\"isLogin && userInfo && userInfo.id\">\r\n          <!-- 会员信息卡片 -->\r\n          <view class=\"member-info-card\">\r\n            <view class=\"member-content\">\r\n              <!-- 用户名 -->\r\n              <text class=\"user-greeting\">Hi {{ userInfo.name ? userInfo.name : '微信用户' }}</text>\r\n              \r\n              <!-- 头像组 -->\r\n              <view class=\"avatar-group\">\r\n                <image class=\"avatar-logo\" src=\"https://hatea.zhijuchina.com/static/logo.png?_t=1\"></image> \r\n              </view>\r\n\r\n              <!-- 会员等级和积分 -->\r\n              <view class=\"member-stats\">\r\n                <view class=\"member-grade-section\">\r\n                  <view class=\"grade-badge\"  @click=\"goToLevelDetail\">\r\n                    <text class=\"grade-text\">{{ gradeInfo && gradeInfo.name ? gradeInfo.name : '普通会员' }}</text>\r\n                  </view>\r\n                  \r\n                  <view class=\"stats-row\">\r\n                    <!-- 注释：积分显示\r\n                    <view class=\"stat-item\">\r\n                      <text class=\"stat-number\">{{ userInfo.point ? userInfo.point : '0' }}</text>\r\n                      <text class=\"stat-label\">积分</text>\r\n                    </view>\r\n                    <image class=\"stats-divider\" src=\"/static/user-divider.png\"></image>\r\n                    -->\r\n                    <view class=\"stat-item\" @click=\"onTargetMyCoupon\">\r\n                      <text class=\"stat-number\">{{ assets.coupon ? assets.coupon : '0' }}</text>\r\n                      <text class=\"stat-label\">优惠券</text>\r\n                    </view>\r\n                    <image class=\"stats-divider\" src=\"/static/user-divider.png\"></image>\r\n                    <view class=\"stat-item\" @click=\"onNavigateToTorchMiniProgram\">\r\n                      <text class=\"stat-number\">{{ (isLogin && hafanInfo && hafanInfo.wallet) ? (hafanInfo.wallet.balance / 100).toFixed(2) : '0.00' }}</text>\r\n                      <text class=\"stat-label\">火炬币</text>\r\n                    </view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n\r\n              <!-- 注释：经验值进度 -->\r\n              <view class=\"experience-section\" @click=\"goToLevelDetail\">\r\n                <view class=\"experience-label\">\r\n                  <text class=\"label-text\">当前消费金额</text>\r\n                  <view class=\"experience-numbers\">\r\n                    <text class=\"current-exp\">{{ userYearlyPaymentAmount ? userYearlyPaymentAmount : '0' }}</text>\r\n                    <text class=\"exp-separator\">/</text>\r\n                    <text class=\"max-exp\">{{ memberGrade[0] ? memberGrade[0].catchValue || 'Max' : 'Max' }}</text>\r\n                  </view>\r\n                </view>\r\n                <view class=\"progress-bar\">\r\n                  <view class=\"progress-track\">\r\n                    <view class=\"progress-fill\" :style=\"{ width: userLevelProgress + 'px' }\"></view>\r\n                  </view>\r\n                  <!-- <text class=\"progress-max\">Max</text> -->\r\n                </view>\r\n              </view>\r\n             \r\n            </view>\r\n          </view>\r\n          \r\n          <!-- 会员号 -->\r\n          <view class=\"member-number-bg\">\r\n            <text class=\"member-number\">会员号：{{ userInfo.userNo ? userInfo.userNo : '8576539564120' }}</text>\r\n          </view>\r\n\r\n        </block>\r\n\r\n        <!-- 未登录状态 -->\r\n        <block v-else>\r\n          <view class=\"login-prompt-card\">\r\n            <view class=\"login-content\">\r\n              <text class=\"login-greeting\">Hi 游客</text>\r\n              <text class=\"login-message\">请登录查看会员信息</text>\r\n              <view class=\"login-button\" @click=\"goLogin\">\r\n                <text class=\"login-btn-text\">立即登录</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </block>\r\n      </view>\r\n\r\n      <!-- 订单管理 -->\r\n      <view class=\"order-section\">\r\n        <view class=\"order-grid\">\r\n          <view class=\"order-item\" @click=\"onTargetOrder({id: 'all'})\">\r\n            <image class=\"order-icon\" src=\"/static/icon-all-orders.png\"></image>\r\n            <text class=\"order-text\">全部订单</text>\r\n          </view>\r\n          <view class=\"order-item\" @click=\"onTargetOrder({id: 'toPay'})\">\r\n            <image class=\"order-icon\" src=\"/static/icon-pending-payment.png\"></image>\r\n            <text class=\"order-text\">待支付</text>\r\n          </view>\r\n          <view class=\"order-item\" @click=\"onTargetOrder({id: 'paid'})\">\r\n            <image class=\"order-icon\" src=\"/static/icon-paid.png\"></image>\r\n            <text class=\"order-text\">已支付</text>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 我的功能 -->\r\n      <view class=\"function-section\">\r\n        <text class=\"function-title\">我的功能</text>\r\n        <view class=\"function-grid\">\r\n          <view class=\"function-item\" @click=\"handleService({url: 'pages/user/setting'})\">\r\n            <image class=\"function-icon\" src=\"/static/icon-personal-info.png\"></image>\r\n            <text class=\"function-text\">个人信息</text>\r\n          </view>\r\n          \r\n          <!-- 注释：优惠券兑换功能\r\n          <view class=\"function-item\" @click=\"onTargetMyCoupon('C')\">\r\n            <image class=\"function-icon\" src=\"/static/icon-coupon-exchange.png\"></image>\r\n            <text class=\"function-text\">优惠券兑换</text>\r\n          </view>\r\n          -->\r\n          <!-- 注释：在线客服功能\r\n          <view class=\"function-item\" @click=\"handleCustomerService\">\r\n            <image class=\"function-icon\" src=\"/static/icon-customer-service.png\"></image>\r\n            <text class=\"function-text\">在线客服</text>\r\n          </view>\r\n          -->\r\n        </view>\r\n      </view>\r\n\r\n      <!-- 注释：注销账号提示\r\n      <text class=\"logout-tips\">如您需要注销账户，请联系客服进行处理</text>\r\n      -->\r\n\r\n      <!-- 会员等级弹窗 -->\r\n      <!-- <Popup v-if=\"!isLoading\" v-model=\"showPopup\" @onPaySuccess=\"getPageData\" :memberGrade=\"curGrade\"/> -->\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import SettingKeyEnum from '@/common/enum/setting/Key'\r\n  import SettingModel from '@/common/model/Setting'\r\n  import * as UserApi from '@/api/user'\r\n  import * as OrderApi from '@/api/order'\r\n  import * as MessageApi from '@/api/message'\r\n  import { checkLogin, showMessage } from '@/utils/app'\r\n  // import Popup from './components/Popup'\r\n\r\n  // 订单操作\r\n  const orderNavbar = [\r\n    { id: 'all', name: '全部订单', icon: 'qpdingdan' },\r\n    { id: 'toPay', name: '待支付', icon: 'daifukuan', count: 0 },\r\n    { id: 'paid', name: '已支付', icon: 'daishouhuo', count: 0 }\r\n  ]\r\n\r\n  /**\r\n   * 我的服务\r\n   * id: 标识; name: 标题名称; icon: 图标; type 类型(link和button); url: 跳转的链接\r\n   */\r\n  const service = [\r\n    { id: 'setting', name: '个人信息', icon: 'shezhi1', type: 'link', url: 'pages/user/setting' },\r\n  ]\r\n\r\n  export default {\r\n    components: {\r\n      // Popup\r\n    },\r\n    data() {\r\n      return {\r\n        // 枚举类\r\n        SettingKeyEnum,\r\n        // 当前运行的终端 (此处并不冗余,因为微信小程序端view层无法直接读取$platform)\r\n        $platform: this.$platform,\r\n        // 正在加载\r\n        isLoading: true,\r\n        // 是否已登录\r\n        isLogin: false,\r\n        // 系统设置\r\n        setting: {},\r\n        userYearlyPaymentAmount: 0,\r\n        userLevelProgress: 0.1,\r\n        // 当前用户信息\r\n        userInfo: { id: 0, name: '', avatar: '', gradeId: 0, mobile: '', balance: 0 },\r\n        gradeInfo: {},\r\n        hafanInfo: {},\r\n        isMerchant: false,\r\n        gradeEndTime: '',\r\n        // 账户资产\r\n        assets: { prestore: '0', timer: '0', coupon: '0' },\r\n        // 我的服务\r\n        service,\r\n        // 订单操作\r\n        orderNavbar,\r\n        // 当前用户待处理的订单数量\r\n        todoCounts: { payment: 0 },\r\n        current: 0,\r\n        // 显示、隐藏弹窗\r\n        showPopup: false,\r\n        memberGrade: [],\r\n        curGrade: {}\r\n      }\r\n    },\r\n    \r\n\r\n    /**\r\n     * 生命周期函数--监听页面显示\r\n     */\r\n    onShow(options) {\r\n      // 获取页面数据\r\n      this.getPageData()\r\n      \r\n      // 判断是否已登录\r\n      this.isLogin = checkLogin()\r\n      \r\n      // 消息显示\r\n      showMessage();\r\n    },\r\n\r\n    methods: {\r\n      // 获取页面数据\r\n      getPageData(callback) {\r\n        const app = this\r\n        app.isLoading = true\r\n        Promise.all([app.getSetting(), app.getUserInfo(), app.getUserAssets(), app.getTodoCounts()])\r\n          .then(result => {\r\n            app.isLoading = false\r\n            // 初始化我的服务数据\r\n            app.initService()\r\n            // 初始化订单操作数据\r\n            app.initOrderTabbar()\r\n            // 执行回调函数\r\n            callback && callback()\r\n          })\r\n          .catch(err => {\r\n            console.log('catch', err)\r\n          })\r\n      },\r\n\r\n      // 初始化我的服务数据\r\n      initService() {\r\n        const app = this\r\n        const newService = []\r\n        service.forEach(item => {\r\n          if (item.id === 'points') {\r\n            item.name = '我的积分'\r\n          }\r\n          newService.push(item)\r\n        })\r\n        app.service = newService\r\n      },\r\n\r\n      // 初始化订单操作数据\r\n      initOrderTabbar() {\r\n        const app = this\r\n        const newOrderNavbar = []\r\n        orderNavbar.forEach(item => {\r\n          const newItem = {...item}\r\n          if (newItem.hasOwnProperty('count')) {\r\n              newItem.count = app.isLogin && app.todoCounts ? (app.todoCounts[newItem.id] || 0) : 0\r\n          }\r\n          newOrderNavbar.push(newItem)\r\n        })\r\n        app.orderNavbar = newOrderNavbar\r\n      },\r\n\r\n      // 获取设置\r\n      getSetting() {\r\n        const app = this\r\n        return new Promise((resolve) => {\r\n          app.setting = {}\r\n          resolve(app.setting)\r\n        })\r\n      },\r\n\r\n      // 获取当前用户信息\r\n      getUserInfo() {\r\n        const app = this\r\n        app.showPopup = false;\r\n        return new Promise((resolve, reject) => {\r\n            UserApi.info()\r\n            .then(result => {\r\n              if (result.data?.userInfo) {\r\n                  app.userInfo = result.data?.userInfo || {}\r\n                  app.isLogin = true\r\n              } else {\r\n                  app.isLogin = false\r\n                  app.userInfo = { id: 0, name: '', avatar: '', gradeId: 0, mobile: '', balance: 0 }\r\n              }\r\n              \r\n              // 强制领取会员卡\r\n              if (result.data?.openWxCard && app.userInfo) {\r\n                  this.$navTo('pages/user/card?userId='+app.userInfo.id);\r\n                  return false;\r\n              }\r\n              app.userYearlyPaymentAmount = result.data?.userYearlyPaymentAmount || 0;\r\n              app.userLevelProgress = app.userYearlyPaymentAmount / (app.memberGrade[0] ? app.memberGrade[0].catchValue : 999999);\r\n              app.gradeInfo = result.data?.gradeInfo || {};\r\n              app.hafanInfo = result.data?.hafanInfo|| {}; \r\n              app.memberGrade = result.data?.memberGrade|| {};\r\n              app.gradeEndTime = result.data?.gradeEndTime|| {};\r\n              app.isMerchant = result.data?.isMerchant|| {};              \r\n              resolve(app.userInfo);\r\n              resolve(app.gradeInfo);\r\n            })\r\n            .catch(err => {\r\n              if (err.result && err.result.status == 1001) {\r\n                app.isLogin = false\r\n                resolve(null)\r\n              } else {\r\n                reject(err)\r\n              }\r\n            })\r\n        })\r\n      },\r\n\r\n      // 获取账户资产\r\n      getUserAssets() {\r\n        const app = this\r\n        return new Promise((resolve, reject) => {\r\n            UserApi.assets()\r\n            .then(result => {\r\n              app.assets = result.data?.asset || { prestore: '0', timer: '0', coupon: '0' }\r\n              resolve(app.assets)\r\n            })\r\n            .catch(err => {\r\n              if (err.result && err.result.status == 1001) {\r\n                app.isLogin = false\r\n                app.assets = { prestore: '0', timer: '0', coupon: '0' }\r\n                resolve(null)\r\n              } else {\r\n                reject(err)\r\n              }\r\n            })\r\n        })\r\n      },\r\n\r\n      // 获取当前用户待处理的事项数量\r\n      getTodoCounts() {\r\n        const app = this\r\n        return new Promise((resolve, reject) => {\r\n          if (!app.isLogin) {\r\n            app.todoCounts = { payment: 0, toPay: 0, paid: 0 }\r\n            resolve(null)\r\n          } else {\r\n            OrderApi.todoCounts()\r\n              .then(result => {\r\n                app.todoCounts = result.data || { payment: 0, toPay: 0, paid: 0 }\r\n                resolve(app.todoCounts)\r\n              })\r\n              .catch(err => {\r\n                app.todoCounts = { payment: 0, toPay: 0, paid: 0 }\r\n                reject(err)\r\n              })\r\n          }\r\n        })\r\n      },\r\n      \r\n      // 跳转登录页面\r\n      goLogin() {\r\n        this.$navTo('pages/login/index')\r\n      },\r\n      \r\n      // 会员等级\r\n      onShowPopup(index) {\r\n        this.showPopup = !this.showPopup\r\n        this.current = index\r\n        this.curGrade = this.memberGrade[index]\r\n      },\r\n      \r\n      // 跳转到会员码\r\n      toMemberCode(userId) {\r\n          !this.isLogin && this.$navTo('pages/login/index')\r\n          this.$navTo('pages/user/code', { userId: userId})\r\n      },\r\n\r\n      // 跳转到订单页\r\n      onTargetOrder(item) {\r\n          console.log('我的订单..')\r\n          !this.isLogin && this.$navTo('pages/login/index')\r\n          this.$navTo('pages/order/index', { dataType: item.id })\r\n      },\r\n\r\n      // 跳转到我的卡券列表页\r\n      onTargetMyCoupon(type) {\r\n\t\t  uni.requestSubscribeMessage({\r\n\t\t  \t\ttmplIds: ['iN-nhCWdmmaP2gAS69lFbjHCHbSHU3F4en1vVWGFLTs','eTqcnyNUQAm2fkMddmEU3RwuJozteKz7A2Acdzh_6y8','kmdYCkoClvvaFts0hFACpeda7-FQT1TjBbWwGZLpY5s'],\r\n\t\t  \t\tsuccess (res) { },\r\n\t\t  \t\tfail  (res) {\r\n\t\t  \t\t\tconsole.log(res);\r\n\t\t  \t\t},\r\n\t\t  })\r\n          const app = this\r\n          if (app.isLogin) {\r\n              // #ifdef MP-WEIXIN\r\n              MessageApi.getSubTemplate({keys: \"couponExpire,couponArrival\"}).then(result => {\r\n                  const templateIds = result.data\r\n                  wx.requestSubscribeMessage({tmplIds: templateIds, \r\n                  success(res) {\r\n                      console.log(\"调用成功！\")\r\n                  }, fail(res) {\r\n                      console.log(\"调用失败:\", res)\r\n                  }, complete() {\r\n                      app.$navTo('pages/my-coupon/index?type='+type)\r\n                  }})\r\n              })\r\n              // #endif\r\n              // #ifndef MP-WEIXIN\r\n                 app.$navTo('pages/my-coupon/index?type='+type)\r\n              // #endif\r\n          } else {\r\n              app.$navTo('pages/login/index')\r\n          }\r\n      },\r\n\r\n      // 跳转到服务页面\r\n      handleService({ url }) {\r\n          this.$navTo(url)\r\n      },\r\n\r\n      // 处理客服功能\r\n      handleCustomerService() {\r\n        // #ifdef MP-WEIXIN\r\n        // 微信小程序可以使用客服功能\r\n        // #endif\r\n        \r\n        // 其他平台提示\r\n        uni.showToast({\r\n          title: '客服功能暂未开放',\r\n          icon: 'none'\r\n        })\r\n      },\r\n\r\n      // 跳转到火炬小程序\r\n      onNavigateToTorchMiniProgram() {\r\n        const app = this\r\n        \r\n        // #ifdef MP-WEIXIN\r\n        uni.navigateToMiniProgram({\r\n          appId: 'wxadcaadb160a0b371', // 请替换为实际的火炬小程序appId\r\n          // path: '', // 火炬小程序的页面路径，如果是首页可以留空\r\n          envVersion: 'release', // 版本类型：develop（开发版），trial（体验版），release（正式版）\r\n          success: function(res) {\r\n            console.log('跳转火炬小程序成功', res)\r\n          },\r\n          fail: function(err) {\r\n            console.log('跳转火炬小程序失败', err)\r\n            uni.showToast({\r\n              title: '跳转失败，请稍后重试',\r\n              icon: 'none'\r\n            })\r\n          }\r\n        })\r\n        // #endif\r\n        \r\n        // #ifndef MP-WEIXIN\r\n        uni.showToast({\r\n          title: '仅支持在微信小程序中跳转',\r\n          icon: 'none'\r\n        })\r\n        // #endif\r\n      },\r\n\r\n      // 跳转到会员等级详情页面\r\n      goToLevelDetail() {\r\n        this.$navTo('pages/user/level')\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 下拉刷新\r\n     */\r\n    onPullDownRefresh() {\r\n      // 获取首页数据\r\n      this.getPageData(() => {\r\n        uni.stopPullDownRefresh()\r\n      })\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n  background-color: #f2f2f2;\r\n  min-height: 100vh;\r\n  padding-top: 216rpx;\r\n}\r\n\r\n.container-bg-img {\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 100vw;\r\n  object-fit: cover;\r\n  z-index: 1;\r\n}\r\n\r\n.new-user-layout {\r\n  padding: 0 34rpx 0 26rpx;\r\n  position: absolute;\r\n  top: 43vw;\r\n  left: 0;\r\n  width: 100vw;\r\n  z-index: 2;\r\n}\r\n\r\n.brand-header {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin-bottom: 56rpx;\r\n}\r\n\r\n.brand-slogan {\r\n  color: #486731;\r\n  font-size: 64rpx;\r\n  font-family: Inter;\r\n  line-height: 59rpx;\r\n  letter-spacing: 6rpx;\r\n  margin-bottom: 34rpx;\r\n}\r\n\r\n.brand-name {\r\n  color: #486731;\r\n  font-size: 48rpx;\r\n  font-family: Inter;\r\n  line-height: 35rpx;\r\n  letter-spacing: 5rpx;\r\n}\r\n\r\n.member-card-wrapper {\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.member-number-bg {\r\n  padding: 40rpx 0 16rpx;\r\n  background-color: #cccccc;\r\n  border-radius: 24rpx;\r\n  padding-left: 48rpx;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.member-number {\r\n  font-size: 24rpx;\r\n  font-family: FZLanTingHei-L-GBK;\r\n  line-height: 22rpx;\r\n  color: #333333;\r\n}\r\n\r\n.member-info-card {\r\n  padding: 44rpx 28rpx 44rpx 44rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);\r\n}\r\n\r\n.member-content {\r\n  padding: 0 6rpx;\r\n}\r\n\r\n.user-greeting {\r\n  font-size: 32rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 30rpx;\r\n  color: #333333;\r\n  margin-bottom: 16rpx;\r\n  text-align: right;\r\n}\r\n\r\n.avatar-group {\r\n  position: absolute;\r\n  right: 60rpx;\r\n  top: 10rpx;\r\n}\r\n.avatar-logo{\r\n  width: 300rpx;\r\n  height: 220rpx;\r\n}\r\n\r\n.avatar-small {\r\n  width: 72rpx;\r\n  height: 74rpx;\r\n}\r\n\r\n.avatar-icon {\r\n  width: 78rpx;\r\n  height: 72rpx;\r\n}\r\n\r\n.member-stats {\r\n  margin-top: 20rpx;\r\n}\r\n\r\n.grade-badge {\r\n  padding: 8rpx 0;\r\n  background-color: #203274;\r\n  border-radius: 16rpx;\r\n  width: 114rpx;\r\n  text-align: center;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.grade-text {\r\n  color: #ffffff;\r\n  font-size: 20rpx;\r\n  font-family: FZLanTingHei-L-GBK;\r\n  line-height: 19rpx;\r\n}\r\n\r\n.stats-row {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.stat-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  margin: 4rpx 0;\r\n}\r\n\r\n.stat-number {\r\n  font-size: 32rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 27rpx;\r\n  color: #333333;\r\n  margin-bottom: 22rpx;\r\n}\r\n\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  font-family: FZLanTingHei-L-GBK;\r\n  line-height: 22rpx;\r\n  color: #333333;\r\n}\r\n\r\n.stats-divider {\r\n  margin: 0 32rpx 0 20rpx;\r\n  width: 2rpx;\r\n  height: 80rpx;\r\n}\r\n\r\n.experience-section {\r\n  margin-top: 38rpx;\r\n}\r\n\r\n.experience-label {\r\n  display: flex;\r\n  align-items: baseline;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.label-text {\r\n  font-size: 24rpx;\r\n  font-family: FZLanTingHei-L-GBK;\r\n  line-height: 22rpx;\r\n  color: #333333;\r\n}\r\n\r\n.experience-numbers {\r\n  margin-left: 12rpx;\r\n  line-height: 28rpx;\r\n  height: 28rpx;\r\n}\r\n\r\n.current-exp {\r\n  font-size: 34rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  color: #333333;\r\n}\r\n\r\n.exp-separator {\r\n  font-size: 30rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 25rpx;\r\n  color: #333333;\r\n}\r\n\r\n.max-exp {\r\n  color: #333333;\r\n  font-size: 28rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 23rpx;\r\n}\r\n\r\n.progress-bar {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.progress-track {\r\n  flex: 1;\r\n  background-color: #d9d9d9;\r\n  border-radius: 198rpx;\r\n  height: 16rpx;\r\n  margin-right: 12rpx;\r\n}\r\n\r\n.progress-fill {\r\n  background-color: #22326e;\r\n  border-radius: 198rpx;\r\n  width: 120rpx;\r\n  height: 16rpx;\r\n}\r\n\r\n.progress-max {\r\n  font-size: 24rpx;\r\n  font-family: FZLanTingHei-L-GBK;\r\n  line-height: 19rpx;\r\n  color: #333333;\r\n}\r\n\r\n.order-section {\r\n  margin-top: 26rpx;\r\n  padding: 40rpx 0 20rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);\r\n}\r\n\r\n.order-grid {\r\n  display: flex;\r\n  margin: 0 52rpx;\r\n}\r\n\r\n.order-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 8rpx 0;\r\n}\r\n\r\n.order-icon {\r\n  width: 55rpx;\r\n  height: 55rpx;\r\n  margin-bottom: 14rpx;\r\n}\r\n\r\n.order-text {\r\n  font-size: 20rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 19rpx;\r\n  color: #333333;\r\n}\r\n\r\n.function-section {\r\n  margin-top: 26rpx;\r\n  padding: 40rpx 48rpx 20rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);\r\n}\r\n\r\n.function-title {\r\n  font-size: 30rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 28rpx;\r\n  letter-spacing: 3rpx;\r\n  color: #333333;\r\n  margin-bottom: 52rpx;\r\n}\r\n\r\n.function-grid {\r\n  display: flex;\r\n  margin: 0 12rpx;\r\n}\r\n\r\n.function-item {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  padding: 12rpx 0;\r\n}\r\n\r\n.function-icon {\r\n  width: 55rpx;\r\n  height: 55rpx;\r\n  margin-bottom: 18rpx;\r\n}\r\n\r\n.function-text {\r\n  font-size: 20rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 19rpx;\r\n  color: #333333;\r\n}\r\n\r\n.logout-tips {\r\n  margin: 28rpx 0 0 8rpx;\r\n  color: #999999;\r\n  font-size: 22rpx;\r\n  font-family: FZLanTingHei-L-GBK;\r\n  line-height: 21rpx;\r\n  letter-spacing: 2rpx;\r\n}\r\n\r\n/* 未登录状态样式 */\r\n.login-prompt-card {\r\n  padding: 60rpx 44rpx;\r\n  background-color: #ffffff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0px 8rpx 8rpx rgba(0, 0, 0, 0.25);\r\n  text-align: center;\r\n}\r\n\r\n.login-content {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n}\r\n\r\n.login-greeting {\r\n  font-size: 32rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 30rpx;\r\n  color: #333333;\r\n  margin-bottom: 20rpx;\r\n}\r\n\r\n.login-message {\r\n  font-size: 24rpx;\r\n  font-family: FZLanTingHei-L-GBK;\r\n  line-height: 22rpx;\r\n  color: #666666;\r\n  margin-bottom: 40rpx;\r\n}\r\n\r\n.login-button {\r\n  background-color: #486731;\r\n  border-radius: 30rpx;\r\n  padding: 16rpx 40rpx;\r\n}\r\n\r\n.login-btn-text {\r\n  color: #ffffff;\r\n  font-size: 28rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 26rpx;\r\n}\r\n\r\n/* 火炬币样式 */\r\n.torch-coin-item {\r\n  display: flex;\r\n  flex-direction: column;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.torch-value {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  margin-bottom: 18rpx;\r\n}\r\n\r\n.torch-amount {\r\n  font-size: 20rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 19rpx;\r\n  color: #333333;\r\n}\r\n\r\n.torch-label {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n\r\n.torch-text {\r\n  font-size: 20rpx;\r\n  font-family: FZLanTingHei-DB-GBK;\r\n  line-height: 19rpx;\r\n  color: #333333;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420854\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}