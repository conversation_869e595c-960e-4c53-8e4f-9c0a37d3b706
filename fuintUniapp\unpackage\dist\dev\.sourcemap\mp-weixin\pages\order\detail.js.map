{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/detail.vue?6d87", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/detail.vue?0479", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/detail.vue?be34", "uni-app:///pages/order/detail.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/detail.vue?8c8f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/detail.vue?33bc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/detail.vue?90e5", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/order/detail.vue?feb2"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Shortcut", "data", "DeliveryStatusEnum", "DeliveryTypeEnum", "OrderStatusEnum", "PayStatusEnum", "PayTypeEnum", "ReceiptStatusEnum", "orderId", "fapiaoUrl", "tableId", "isLoading", "order", "setting", "showPayPopup", "reflash", "computed", "reservationStatusText", "reservationStatusClass", "onLoad", "onShow", "methods", "getOrderDetail", "app", "OrderApi", "then", "handleCopy", "uni", "success", "handleTargetGoods", "goodsId", "handleApplyRefund", "handleApplyFapiao", "rev", "url", "handleRefundDetail", "refundId", "onCancel", "title", "content", "onContinue", "onPay", "onReceipt", "onSelectPayType", "catch", "onSubmitCallback", "setTimeout", "finally", "handleRate"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACa;AACyB;;;AAG3F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,gQAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,6MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iBAAiB;AACjB;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChGA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC+O/pB;AAQA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;MACA;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC;MACAC,6BACAC;QACAF;QACAA;QACAA;MACA;IACA;IAEA;IACAG;MACA;MACAC;QACA1B;QACA2B;UACAL;QACA;MACA;IACA;IAEA;IACAM;MACA;QACA;UAAAC;QAAA;MACA;IACA;IAEA;IACAC;MACA;QAAAvB;MAAA;IACA;IAEAwB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAR;cAAA;gBAAAS;gBACAC;gBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QAAAC;MAAA;IACA;IAEA;IACAC;MACA;MACAV;QACAW;QACAC;QACAX;UACA;YACAJ,6BACAC;cACA;cACAF;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAiB;MACA;MACAb;MACAA;IACA;IAEA;IACAc;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAf;QACAW;QACAC;QACAX;UACA;YACAJ,0BACAC;cACA;cACAF;cACA;cACAA;YACA;UACA;QACA;MACA;IACA;IAEA;IACAoB;MACA;MACA;MACA;MACA;MACAnB,mCACAC;QAAA;MAAA,GACAmB;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACA;UACAtB;QACA;UACAA;QACA;QACA;MACA;;MAEA;MACA;QACA,yCACAE;UACAF;UACAuB;YACAvB;UACA;QACA,GACAqB;UACArB;QACA,GACAwB;UACAxB;QACA;MACA;MACA;MACA;QACA;UACAA;UACAA;UACAuB;YACA;YACAvB;UACA;QACA;UACAA;QACA;MACA;IACA;IAEA;IACAyB;MACA;MACA;MACAzB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9eA;AAAA;AAAA;AAAA;AAA86B,CAAgB,w4BAAG,EAAC,C;;;;;;;;;;;ACAl8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA8uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAlwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=57d42baa&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./detail.vue?vue&type=style&index=1&id=57d42baa&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57d42baa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=57d42baa&scoped=true&\"", "var components\ntry {\n  components = {\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-popup/components/uni-popup/uni-popup\" */ \"@/uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\n      )\n    },\n    uniLink: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-link/components/uni-link/uni-link\" */ \"@/uni_modules/uni-link/components/uni-link/uni-link.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-popup/u-popup\" */ \"@/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.isLoading\n    ? _vm.order.orderMode == \"oneself\" &&\n      _vm.order.type == \"goods\" &&\n      _vm.order.pickupCode &&\n      _vm.order.payStatus == \"B\" &&\n      !_vm.order.tableInfo &&\n      ![\"C\", \"H\", \"G\"].includes(_vm.order.status)\n    : null\n  var g1 = !_vm.isLoading ? _vm.order.goods && _vm.order.goods.length > 0 : null\n  var l1 =\n    !_vm.isLoading && g1\n      ? _vm.__map(_vm.order.goods, function (goods, idx) {\n          var $orig = _vm.__get_orig(goods)\n          var l0 =\n            goods.num > 0\n              ? _vm.__map(goods.specList, function (props, idx) {\n                  var $orig = _vm.__get_orig(props)\n                  var g2 = goods.specList.length\n                  return {\n                    $orig: $orig,\n                    g2: g2,\n                  }\n                })\n              : null\n          return {\n            $orig: $orig,\n            l0: l0,\n          }\n        })\n      : null\n  var g3 = !_vm.isLoading\n    ? _vm.PayTypeEnum.getNameByValue(_vm.order.payType)\n    : null\n  var g4 = !_vm.isLoading\n    ? _vm.PayTypeEnum.getNameByValue(_vm.order.payType)\n    : null\n  var g5 =\n    !_vm.isLoading && _vm.order.payAmount\n      ? _vm.order.payAmount.toFixed(2)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l1: l1,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"!isLoading\" class=\"page\">\n    <!-- 订单状态卡片 -->\n    <view class=\"status-card\">\n      <text class=\"status-title\">\n        <text v-if=\"order.status == OrderStatusEnum.CREATED.value\">{{OrderStatusEnum.CREATED.name}}</text>\n        <text v-else-if=\"order.status == OrderStatusEnum.PAID.value\">{{OrderStatusEnum.PAID.name}}</text>\n        <text v-else-if=\"order.status == OrderStatusEnum.DELIVERY.value\">{{OrderStatusEnum.DELIVERY.name}}</text>\n        <text v-else-if=\"order.status == OrderStatusEnum.DELIVERED.value\">{{OrderStatusEnum.DELIVERED.name}}</text>\n        <text v-else-if=\"order.status == OrderStatusEnum.RECEIVED.value\">{{OrderStatusEnum.RECEIVED.name}}</text>\n        <text v-else-if=\"order.status == OrderStatusEnum.CANCEL.value\">{{OrderStatusEnum.CANCEL.name}}</text>\n        <text v-else-if=\"order.status == OrderStatusEnum.REFUND.value\">{{OrderStatusEnum.REFUND.name}}</text>\n      </text>\n      \n      <view class=\"status-content\">\n        <text class=\"status-message\">\n          <text v-if=\"order.status == OrderStatusEnum.RECEIVED.value\">欢迎光顾，期待您下次光临</text>\n          <text v-else-if=\"order.status == OrderStatusEnum.PAID.value\">订单已支付，正在为您准备</text>\n          <text v-else-if=\"order.status == OrderStatusEnum.CREATED.value\">请尽快完成支付</text>\n          <text v-else>感谢您的支持</text>\n        </text>\n        \n        <!-- 评价按钮（仅已完成订单显示） -->\n        <view v-if=\"order.status == OrderStatusEnum.RECEIVED.value\" class=\"rate-button\" @click=\"handleRate\">\n          <text class=\"rate-text\">评价一下</text>\n        </view>\n      </view>\n      \n      <!-- 取餐码 -->\n      <view v-if=\"order.orderMode == 'oneself' && order.type == 'goods' && order.pickupCode && order.payStatus == 'B' && !order.tableInfo && ( !['C', 'H', 'G'].includes(order.status))\" class=\"pickup-code\">\n        <text class=\"pickup-label\">取餐码：</text>\n        <text class=\"pickup-number\">{{ order.pickupCode }}</text>\n      </view>\n    </view>\n\n    <!-- 积分奖励提示 -->\n    <view v-if=\"order.status == OrderStatusEnum.RECEIVED.value && order.pointAmount > 0\" class=\"points-card\">\n      <text class=\"points-text\">获得{{ order.pointAmount }}积分</text>\n    </view>\n\n    <!-- 商品详情卡片 -->\n    <view class=\"goods-detail-card\" v-if=\"order.goods && order.goods.length > 0\">\n      <view class=\"goods-header\">\n        <text class=\"goods-header-title\">商品详情</text>\n      </view>\n      \n      <view class=\"goods-list\">\n        <view class=\"goods-item\" v-for=\"(goods, idx) in order.goods\" :key=\"idx\" v-if=\"goods.num > 0\">\n          <image class=\"goods-image\" :src=\"goods.image\" mode=\"aspectFill\" @click=\"handleTargetGoods(goods.goodsId, goods.type)\"></image>\n          <view class=\"goods-info\">\n            <text class=\"goods-name\">{{goods.name}}</text>\n            <text class=\"goods-specs\">\n              <text v-for=\"(props, idx) in goods.specList\" :key=\"idx\">{{ props.specValue }}{{ idx < goods.specList.length - 1 ? '，' : '' }}</text>\n            </text>\n            <text class=\"goods-quantity\">x{{goods.num}}</text>\n          </view>\n        </view>\n      </view>\n\n      <!-- 费用明细 -->\n      <view class=\"fee-details\">\n        <view v-if=\"order.deliveryFee > 0\" class=\"fee-item\">\n          <text class=\"fee-label\">配送费</text>\n        </view>\n        <view v-if=\"order.discount > 0\" class=\"fee-item\">\n          <text class=\"fee-label\">优惠券</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 快递配送：配送地址 -->\n    <view v-if=\"order.address\" class=\"address-card\">\n      <view class=\"address-header\">\n        <text class=\"address-type\">[快递配送]</text>\n      </view>\n      <view class=\"address-info\">\n        <text class=\"contact-name\">{{ order.address.name }}</text>\n        <text class=\"contact-phone\">{{ order.address.mobile }}</text>\n      </view>\n      <view class=\"address-detail\">\n        <text class=\"address-region\">{{ order.address.provinceName }}{{ order.address.cityName }}{{ order.address.regionName }}</text>\n        <text class=\"address-full\">{{ order.address.detail }}</text>\n      </view>\n    </view>\n    \n    <!-- 门店自提：自提地址 -->\n    <view v-if=\"order.orderMode == 'oneself' && order.type == 'goods'\" class=\"address-card\">\n      <view class=\"address-header\">\n        <text class=\"address-type\" v-if=\"!order.tableInfo\">[门店自提]</text>\n        <text class=\"address-type\" v-if=\"order.tableInfo\">[桌台点餐] {{order.tableInfo.code}}</text>\n      </view>\n      <view class=\"address-info\">\n        <text class=\"contact-name\">{{ order.storeInfo.name }}</text>\n        <text class=\"contact-phone\">{{ order.storeInfo.phone }}</text>\n      </view>\n      <view class=\"address-detail\">\n        <text class=\"address-full\">{{ order.storeInfo.address }}</text>\n      </view>\n    </view>\n\n    <!-- 订单信息卡片 -->\n    <view class=\"order-info-card\">\n      <text class=\"info-card-title\">订单信息</text>\n      <view class=\"info-divider\"></view>\n      \n      <view class=\"info-list\">\n        <view class=\"info-row\">\n          <text class=\"info-label\">订单编号</text>\n          <view class=\"info-value-wrapper\">\n            <text class=\"info-value\">{{order.orderSn}}</text>\n            <view class=\"copy-button\" @click=\"handleCopy(order.orderSn)\">\n              <text class=\"copy-text\">复制</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"info-row\">\n          <text class=\"info-label\">下单时间</text>\n          <text class=\"info-value\">{{order.createTime}}</text>\n        </view>\n        \n        <view class=\"info-row\">\n          <text class=\"info-label\">支付方式</text>\n          <text class=\"info-value\">{{ PayTypeEnum.getNameByValue(order.payType) }}</text>\n        </view>\n        \n        <view class=\"info-row\">\n          <text class=\"info-label\">支付金额</text>\n          <view class=\"payment-info\">\n            <text class=\"payment-method\">{{ PayTypeEnum.getNameByValue(order.payType) }}：</text>\n            <text class=\"payment-symbol\">￥</text>\n            <text class=\"payment-amount\">{{order.payAmount ? order.payAmount.toFixed(2) : '0.00'}}</text>\n          </view>\n        </view>\n        \n        <view class=\"info-row\">\n          <text class=\"info-label\">备注信息</text>\n          <text class=\"info-value\">{{order.remark ? order.remark : '无'}}</text>\n        </view>\n\n        <!-- 预约取餐信息 -->\n        <view class=\"info-row\" v-if=\"order.isReservation === 'Y'\">\n          <text class=\"info-label\">预约取餐</text>\n          <view class=\"reservation-info\">\n            <text class=\"reservation-time\">{{order.reservationTime}}</text>\n            <text class=\"reservation-status\" :class=\"reservationStatusClass\">\n              {{reservationStatusText}}\n            </text>\n          </view>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部提示 -->\n    <view class=\"bottom-notice\">\n      <text class=\"notice-text\">如有疑问，请联系店员或致电门店</text>\n    </view>\n\n    <!-- 底部操作按钮 -->\n    <view class=\"footer-fixed\" v-if=\"order.status == OrderStatusEnum.CREATED.value\">\n      <view class=\"btn-wrapper\">\n        <block v-if=\"!order.tableInfo\">\n          <view class=\"btn-item\" @click=\"onCancel(order.id)\">取消订单</view>\n        </block>\n        <block v-if=\"order.tableInfo\">\n          <view class=\"btn-item\" @click=\"onContinue(order.id, order.tableInfo.id)\">继续点单</view>\n        </block>\n        <block>\n          <view class=\"btn-item active\" @click=\"onPay(order.id)\">去支付</view>\n        </block>\n      </view>\n    </view>\n    \n\t\n   <uni-popup ref=\"fapiaoPopup\" background-color=\"#fff\">\n\t   <view style=\"padding: 30px;\">\n\t   <uni-link :href=\"fapiaoUrl\"  fontSize=\"26\"  showUnderLine text=\"点击前往获取发票\"></uni-link>\n\t   </view>\n   </uni-popup>\n    <!-- 已支付的订单 -->\n   <view class=\"footer-fixed\" v-if=\"(order.payStatus == OrderStatusEnum.PAID.value) && !order.tableInfo\"> \n      <view class=\"btn-wrapper\">\n\t\t  <block v-if=\"order.hfOrder\">\n\t\t\t <view class=\"btn-item active\" @click=\"handleApplyFapiao(order.orderSn)\">申请发票</view>\n\t\t   </block>\n       <!-- <block v-if=\"!order.refundInfo\">\n          <view class=\"btn-item active\" @click=\"handleApplyRefund(order.id)\">申请售后</view>\n        </block>\n        <block v-if=\"order.refundInfo\">\n          <view class=\"btn-item common\" @click=\"handleRefundDetail(order.refundInfo.id)\">售后详情</view>\n        </block> -->\n      </view>\n    </view>\n    \n    <view class=\"footer-fixed\" v-if=\"order.status == OrderStatusEnum.DELIVERED.value\">\n      <view class=\"btn-wrapper\">\n        <block>\n          <view class=\"btn-item active\" @click=\"onReceipt(order.id)\">确认收货</view>\n        </block>\n      </view>\n    </view>\n\n    <!-- 支付方式弹窗 -->\n    <u-popup v-model=\"showPayPopup\" mode=\"bottom\" :closeable=\"true\">\n      <view class=\"pay-popup\">\n        <view class=\"title\">请选择支付方式</view>\n        <view class=\"pop-content\">\n          <!-- 微信支付 -->\n          <view class=\"pay-item dis-flex flex-x-between\" @click=\"onSelectPayType(PayTypeEnum.WECHAT.value)\">\n            <view class=\"item-left dis-flex flex-y-center\">\n              <view class=\"item-left_icon wechat\">\n                <text class=\"iconfont icon-weixinzhifu\"></text>\n              </view>\n              <view class=\"item-left_text\">\n                <text>{{ PayTypeEnum.WECHAT.name }}</text>\n              </view>\n            </view>\n          </view>\n          <!-- 余额支付 -->\n         <!-- <view v-if=\"order.type != 'recharge' && order.type != 'prestore'\" class=\"pay-item dis-flex flex-x-between\" @click=\"onSelectPayType(PayTypeEnum.BALANCE.value)\">\n            <view class=\"item-left dis-flex flex-y-center\">\n              <view class=\"item-left_icon balance\">\n                <text class=\"iconfont icon-qiandai\"></text>\n              </view>\n              <view class=\"item-left_text\">\n                <text>{{ PayTypeEnum.BALANCE.name }}</text>\n              </view>\n            </view>\n          </view> -->\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 快捷导航 -->\n    <shortcut/>\n  </view>\n</template>\n\n<script>\n  import {\n    DeliveryStatusEnum,\n    DeliveryTypeEnum,\n    OrderStatusEnum,\n    PayStatusEnum,\n    PayTypeEnum,\n    ReceiptStatusEnum\n  } from '@/common/enum/order'\n  import * as OrderApi from '@/api/order'\n  import { wxPayment } from '@/utils/app'\n  import Shortcut from '@/components/shortcut'\n\n  export default {\n    components: {\n       Shortcut\n    },\n    data() {\n      return {\n        // 枚举类\n        DeliveryStatusEnum,\n        DeliveryTypeEnum,\n        OrderStatusEnum,\n        PayStatusEnum,\n        PayTypeEnum,\n        ReceiptStatusEnum,\n        // 当前订单ID\n        orderId: null,\n\t\tfapiaoUrl: '',\n        // 桌码ID\n        tableId: 0,\n        // 加载中\n        isLoading: true,\n        // 当前订单详情\n        order: {},\n        // 当前设置\n        setting: {},\n        // 支付方式弹窗\n        showPayPopup: false,\n        // 刷新页面\n        reflash: false\n      }\n    },\n\n    computed: {\n      // 预约状态文本\n      reservationStatusText() {\n        if (!this.order.reservationStatus) {\n          return '待处理';\n        }\n        switch (this.order.reservationStatus) {\n          case 'A':\n            return '待处理';\n          case 'B':\n            return '已处理';\n          default:\n            return '待处理';\n        }\n      },\n\n      // 预约状态样式类\n      reservationStatusClass() {\n        if (!this.order.reservationStatus) {\n          return 'status-pending';\n        }\n        switch (this.order.reservationStatus) {\n          case 'A':\n            return 'status-pending';\n          case 'B':\n            return 'status-processed';\n          default:\n            return 'status-pending';\n        }\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad({ orderId }) {\n      // 当前订单ID\n      this.orderId = orderId;\n      this.tableId = uni.getStorageSync(\"tableId\") ? uni.getStorageSync(\"tableId\") : 0;\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n      // 获取当前订单信息\n      this.getOrderDetail();\n    },\n\n    methods: {\n\n      // 获取当前订单信息\n      getOrderDetail() {\n        const app = this\n        app.isLoading = true\n        OrderApi.detail(app.orderId)\n          .then(result => {\n            app.order = result.data\n            app.setting = result.data\n            app.isLoading = false\n          })\n      },\n\n      // 复制指定内容\n      handleCopy(value) {\n        const app = this\n        uni.setClipboardData({\n          data: value,\n          success() {\n            app.$toast('复制成功')\n          }\n        })\n      },\n\n      // 跳转到商品详情页面\n      handleTargetGoods(goodsId, type) {\n        if (goodsId && parseInt(goodsId) > 0) {\n            this.$navTo('pages/goods/detail', { goodsId })\n        }\n      },\n\n      // 跳转到申请售后页面\n      handleApplyRefund(orderId) {\n        this.$navTo('pages/refund/apply', { orderId })\n      },\n\t  \n\t  async handleApplyFapiao(orderSn) {\n\t    const rev = await OrderApi.getInvoiceQrCode(orderSn);\n\t\tconst url =rev.data;\n\t\tthis.fapiaoUrl = url\n\t\t\n\t\tthis.$refs.fapiaoPopup.open('center')\n\t  },\n      \n      // 售后详情\n      handleRefundDetail(refundId) {\n        this.$navTo('pages/refund/detail', { refundId })\n      },\n\n      // 取消订单\n      onCancel() {\n        const app = this\n        uni.showModal({\n          title: '友情提示',\n          content: '确认要取消该订单吗？',\n          success(o) {\n            if (o.confirm) {\n              OrderApi.cancel(app.orderId)\n                .then(result => {\n                  // 显示成功信息\n                  app.$success(result.message);\n                  // 刷新当前订单数据\n                  app.getOrderDetail();\n                })\n            }\n          }\n        });\n      },\n      // 继续点单\n      onContinue(orderId, tableId) {\n         this.$navTo('pages/category/index');\n         uni.setStorageSync('orderId', orderId);\n         uni.setStorageSync('tableId', tableId);\n      },\n\n      // 点击去支付\n      onPay() {\n        // 显示支付方式弹窗\n        this.showPayPopup = true;\n      },\n      \n      // 确认收货\n      onReceipt(orderId) {\n          const app = this\n          uni.showModal({\n            title: '友情提示',\n            content: '确认收到商品了吗？',\n            success(o) {\n              if (o.confirm) {\n                OrderApi.receipt(orderId)\n                  .then(result => {\n                    // 显示成功信息\n                    app.$success(result.message)\n                    // 刷新当前订单数据\n                    app.getOrderDetail()\n                  })\n              }\n            }\n          });\n       },\n\n      // 选择支付方式\n      onSelectPayType(payType) {\n        const app = this\n        // 隐藏支付方式弹窗\n        this.showPayPopup = false\n        // 发起支付请求\n        OrderApi.pay(app.orderId, payType)\n          .then(result => app.onSubmitCallback(result))\n          .catch(err => err)\n      },\n\n      // 订单提交成功后回调\n      onSubmitCallback(result) {\n        const app = this;\n        if (!result.data) {\n            if (result.message) {\n                app.$error(result.message);\n            } else {\n                app.$error('支付失败');\n            }\n            return false;\n        }\n        \n        // 发起微信支付\n        if (result.data.payType == PayTypeEnum.WECHAT.value) {\n            wxPayment(result.data.payment)\n              .then(() => {\n                app.$success('支付成功');\n                setTimeout(() => {\n                   app.getOrderDetail();\n                }, 1500)\n              })\n              .catch(err => {\n                 app.$error('订单未支付');\n              })\n              .finally(() => {\n                 app.disabled = false;\n              })\n         }\n         // 余额支付\n         if (result.data.payType == PayTypeEnum.BALANCE.value) {\n            if (result.data.orderInfo.payStatus == 'B') {\n                app.$success('支付成功');\n                app.disabled = false;\n                setTimeout(() => {\n                    // 刷新当前订单数据\n                    app.getOrderDetail();\n                }, 1500)\n            } else {\n                app.$error('支付失败');\n            }\n         }\n       },\n\n       // 处理评价\n       handleRate() {\n         const app = this;\n         // 跳转到评价页面或显示评价弹窗\n         app.$toast('评价功能待开发');\n       }\n    }\n  }\n</script>\n\n<style>\n  page {\n    background: #f2f2f2;\n  }\n</style>\n<style lang=\"scss\" scoped>\n  // 通用布局类\n  .flex-row {\n    display: flex;\n    flex-direction: row;\n  }\n  \n  .flex-col {\n    display: flex;\n    flex-direction: column;\n  }\n  \n  .items-center {\n    align-items: center;\n  }\n  \n  .justify-between {\n    justify-content: space-between;\n  }\n  \n  .justify-center {\n    justify-content: center;\n  }\n  \n  .self-stretch {\n    align-self: stretch;\n  }\n  \n  .self-center {\n    align-self: center;\n  }\n\n  // 页面容器\n  .page {\n    padding-bottom: 204rpx;\n    background-color: #f2f2f2;\n    min-height: 100vh;\n  }\n\n  .header-wrapper {\n    padding: 16rpx 0 24rpx;\n    background-color: #fff;\n    position: relative;\n  }\n\n  .header-title {\n    color: #000;\n    font-size: 40rpx;\n    font-weight: 600;\n    line-height: 38rpx;\n    letter-spacing: 4rpx;\n    text-align: center;\n  }\n\n  .header-icon {\n    position: absolute;\n    right: 204rpx;\n    bottom: 23rpx;\n    border-radius: 50%;\n    width: 70rpx;\n    height: 72rpx;\n  }\n\n  // 订单状态卡片\n  .status-card {\n    margin: 6rpx 26rpx 0 34rpx;\n    padding: 56rpx 0 42rpx;\n    background-color: #fff;\n    border-radius: 18rpx;\n    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .status-title {\n    color: #000;\n    font-size: 36rpx;\n    font-weight: 300;\n    line-height: 33rpx;\n    letter-spacing: 4rpx;\n    text-shadow: 0 0 #000;\n  }\n\n  .status-content {\n    margin-top: 42rpx;\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n  }\n\n  .status-message {\n    color: #404040;\n    font-size: 24rpx;\n    font-weight: 300;\n    line-height: 25rpx;\n  }\n\n  .rate-button {\n    margin-top: 42rpx;\n    padding: 24rpx 0 28rpx;\n    background-color: #fff;\n    border-radius: 39rpx;\n    width: 348rpx;\n    border: 1rpx solid #333;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .rate-text {\n    color: #404040;\n    font-size: 24rpx;\n    font-weight: 600;\n    line-height: 23rpx;\n    letter-spacing: 4rpx;\n  }\n\n  .pickup-code {\n    margin-top: 30rpx;\n    color: #404040;\n    font-size: 24rpx;\n    display: flex;\n    align-items: center;\n\n    .pickup-label {\n      margin-right: 10rpx;\n    }\n\n    .pickup-number {\n      font-size: 32rpx;\n      font-weight: 600;\n      color: #2b387e;\n    }\n  }\n\n  // 积分奖励卡片\n  .points-card {\n    margin: 24rpx 26rpx 0 34rpx;\n    padding: 28rpx 0;\n    background-color: #fff;\n    border-radius: 18rpx;\n    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);\n    display: flex;\n    justify-content: flex-start;\n    align-items: center;\n  }\n\n  .points-text {\n    margin-left: 32rpx;\n    color: #404040;\n    font-size: 24rpx;\n    font-weight: 300;\n    line-height: 22rpx;\n  }\n\n  // 商品详情卡片\n  .goods-detail-card {\n    margin: 24rpx 26rpx 0 34rpx;\n    padding: 0 32rpx 116rpx;\n    background-color: #fff;\n    border-radius: 18rpx;\n    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);\n  }\n\n  .goods-header {\n    padding-top: 40rpx;\n    border-bottom: 2rpx solid rgba(0, 0, 0, 0.2);\n  }\n\n  .goods-header-title {\n    color: #000;\n    font-size: 28rpx;\n    font-weight: 600;\n    line-height: 26rpx;\n    letter-spacing: 3rpx;\n  }\n\n  .goods-list {\n    padding: 48rpx 0;\n    border-top: 2rpx solid rgba(0, 0, 0, 0.2);\n  }\n\n  .goods-item {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24rpx;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .goods-image {\n    border-radius: 8rpx;\n    width: 126rpx;\n    height: 124rpx;\n    margin-right: 24rpx;\n    flex-shrink: 0;\n  }\n\n  .goods-info {\n    flex: 1;\n    display: flex;\n    flex-direction: column;\n  }\n\n  .goods-name {\n    margin-left: 12rpx;\n    color: #333;\n    font-size: 28rpx;\n    font-weight: 600;\n    line-height: 26rpx;\n    letter-spacing: 3rpx;\n    margin-bottom: 24rpx;\n  }\n\n  .goods-specs {\n    margin-left: 12rpx;\n    color: #666;\n    font-size: 20rpx;\n    font-weight: 300;\n    line-height: 19rpx;\n    text-shadow: 0 0 #666;\n    margin-bottom: 24rpx;\n  }\n\n  .goods-quantity {\n    color: #666;\n    font-size: 26rpx;\n    line-height: 20rpx;\n  }\n\n  .fee-details {\n    padding-bottom: 36rpx;\n    border-bottom: 2rpx solid rgba(0, 0, 0, 0.2);\n  }\n\n  .fee-item {\n    margin-bottom: 48rpx;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .fee-label {\n    color: #333;\n    font-size: 24rpx;\n    font-weight: 300;\n    line-height: 22rpx;\n    letter-spacing: 2rpx;\n  }\n\n  // 地址信息卡片\n  .address-card {\n    margin: 24rpx 26rpx 0 34rpx;\n    padding: 40rpx 32rpx;\n    background-color: #fff;\n    border-radius: 18rpx;\n    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);\n  }\n\n  .address-header {\n    margin-bottom: 20rpx;\n  }\n\n  .address-type {\n    color: #333;\n    font-size: 28rpx;\n    font-weight: 600;\n  }\n\n  .address-info {\n    display: flex;\n    align-items: center;\n    margin-bottom: 24rpx;\n  }\n\n  .contact-name {\n    color: #999;\n    font-size: 26rpx;\n    margin-right: 20rpx;\n  }\n\n  .contact-phone {\n    color: #999;\n    font-size: 26rpx;\n  }\n\n  .address-detail {\n    color: #999;\n    font-size: 24rpx;\n  }\n\n  .address-region {\n    margin-right: 12rpx;\n  }\n\n  .address-full {\n    margin-left: 12rpx;\n  }\n\n  // 订单信息卡片\n  .order-info-card {\n    margin: 24rpx 24rpx 0 32rpx;\n    padding: 48rpx 24rpx 48rpx 32rpx;\n    background-color: #fff;\n    border-radius: 18rpx;\n    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);\n  }\n\n  .info-card-title {\n    color: #000;\n    font-size: 28rpx;\n    font-weight: 600;\n    line-height: 26rpx;\n    letter-spacing: 3rpx;\n  }\n\n  .info-divider {\n    margin-right: 20rpx;\n    margin-top: 28rpx;\n    background-color: rgba(0, 0, 0, 0.2);\n    height: 2rpx;\n  }\n\n  .info-list {\n    margin-top: 32rpx;\n  }\n\n  .info-row {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 40rpx;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n\n  .info-label {\n    color: rgba(51, 51, 51, 0.5);\n    font-size: 24rpx;\n    font-weight: 300;\n    line-height: 22rpx;\n    letter-spacing: 2rpx;\n    text-shadow: 0 0 rgba(51, 51, 51, 0.5);\n  }\n\n  .info-value {\n    color: #333;\n    font-size: 24rpx;\n    font-weight: 300;\n    line-height: 19rpx;\n    letter-spacing: 2rpx;\n    text-shadow: 0 0 #333;\n  }\n\n  .info-value-wrapper {\n    display: flex;\n    align-items: center;\n  }\n\n  .copy-button {\n    margin-left: 16rpx;\n    padding: 4rpx 16rpx;\n    border: 1rpx solid #c1c1c1;\n    border-radius: 12rpx;\n  }\n\n  .copy-text {\n    color: #666;\n    font-size: 20rpx;\n  }\n\n  .payment-info {\n    margin-right: 16rpx;\n    line-height: 22rpx;\n    height: 22rpx;\n  }\n\n  .payment-method {\n    color: #333;\n    font-size: 24rpx;\n    font-weight: 300;\n    line-height: 22rpx;\n    letter-spacing: 2rpx;\n  }\n\n  .payment-symbol {\n    color: #333;\n    font-size: 20rpx;\n    font-weight: 300;\n    line-height: 15rpx;\n  }\n\n  .payment-amount {\n    color: #333;\n    font-size: 24rpx;\n    font-weight: 300;\n    line-height: 19rpx;\n    letter-spacing: 2rpx;\n  }\n\n  // 底部提示\n  .bottom-notice {\n    margin-top: 26rpx;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n\n  .notice-text {\n    color: #999;\n    font-size: 22rpx;\n    line-height: 21rpx;\n    letter-spacing: 2rpx;\n  }\n\n  /* 底部操作栏 */\n  .footer-fixed {\n    position: fixed;\n    bottom: var(--window-bottom);\n    left: 0;\n    right: 0;\n    height: 180rpx;\n    padding-bottom: 30rpx;\n    z-index: 11;\n    box-shadow: 0 -4rpx 40rpx 0 rgba(97, 97, 97, 0.1);\n    background: #fff;\n\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      justify-content: flex-end;\n      padding: 0 30rpx;\n    }\n\n    .btn-item {\n      min-width: 164rpx;\n      border-radius: 8rpx;\n      padding: 20rpx 24rpx;\n      font-size: 28rpx;\n      color: #383838;\n      text-align: center;\n      border: 1rpx solid #a8a8a8;\n      margin-left: 10rpx;\n      \n      &.common {\n        color: #fff;\n        border: none;\n        background: linear-gradient(to right, #2b387e, #2b387e);\n      }\n\n      &.active {\n        color: #fff;\n        border: none;\n        background: linear-gradient(to right, #f9211c, #ff6335);\n      }\n    }\n  }\n\n  // 弹出层-支付方式\n  .pay-popup {\n    padding: 25rpx 25rpx 70rpx 25rpx;\n    \n    .title {\n      font-size: 30rpx;\n      margin-bottom: 50rpx;\n      font-weight: bold;\n      text-align: center;\n    }\n\n    .pop-content {\n      min-height: 120rpx;\n      padding: 0 20rpx;\n\n      .pay-item {\n        padding: 30rpx;\n        font-size: 30rpx;\n        background: #fff;\n        border: 1rpx solid #2b387e;\n        border-radius: 8rpx;\n        color: #888;\n        margin-bottom: 12rpx;\n        text-align: center;\n\n        .item-left_icon {\n          margin-right: 20rpx;\n          font-size: 48rpx;\n\n          &.wechat {\n            color: #00c800;\n          }\n\n          &.balance {\n            color: #2b387e;\n          }\n        }\n      }\n    }\n  }\n\n  // 预约信息样式\n  .reservation-info {\n    display: flex;\n    flex-direction: column;\n    align-items: flex-end;\n\n    .reservation-time {\n      color: #ff6600;\n      font-weight: bold;\n      font-size: 28rpx;\n      margin-bottom: 8rpx;\n    }\n\n    .reservation-status {\n      font-size: 24rpx;\n      padding: 4rpx 12rpx;\n      border-radius: 12rpx;\n\n      &.status-pending {\n        background-color: #fff3cd;\n        color: #856404;\n      }\n\n      &.status-processed {\n        background-color: #d4edda;\n        color: #155724;\n      }\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891607523\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=1&id=57d42baa&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=1&id=57d42baa&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891607519\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}