<template>
  <view class="page">
    <mescroll-body ref="mescrollRef" :sticky="true" @init="mescrollInit" :down="{ native: true }" @down="downCallback"
      :up="upOption" @up="upCallback">

      <!-- tab栏 -->
      <view class="tab-section">
        <u-tabs :list="tabs" :is-scroll="false" :current="curTab" active-color="#2b387e" :duration="0.2" @change="onChangeTab" />
      </view>

      <!-- 订单列表 -->
      <view class="order-list">
        <view class="order-card" v-for="(item, index) in list.content" :key="index">
          <!-- 订单头部 -->
          <view class="order-header" @click="handleTargetDetail(item.id)">
            <view class="flex-row items-center">
              <view class="order-type-tag">
                <text class="tag-text">{{ item.typeName }}</text>
              </view>
              <!-- 预约标识 -->
              <view v-if="item.isReservation === 'Y'" class="reservation-tag">
                <text class="reservation-icon">🕐</text>
                <text class="reservation-text">预约</text>
              </view>
              <!-- <text class="store-name">{{ item.storeName || '默认门店' }}</text> -->
            </view>
            <text class="status-text">{{ item.statusText }}</text>
          </view>
          
          <!-- 订单时间 -->
          <text class="order-time">{{ item.createTime }}</text>
          
          <!-- 商品列表 -->
          <view class="goods-section" v-if="item.goods" @click="handleTargetDetail(item.id)">
            <view class="goods-item" v-for="(goods, idx) in item.goods" :key="idx">
              <image class="goods-image" :src="goods.image"></image>
              <view class="goods-info">
                <view class="goods-content">
                  <view class="goods-title">{{ goods.name }}</view>
                  <view class="goods-specs">
                    <view class="spec-item" v-for="(props, idx) in goods.specList" :key="idx">
                      {{ props.specValue }}
                    </view>
                  </view>
                </view>
                <view class="goods-price-section">
                  <view class="price-group">
                    <text class="price-symbol">￥</text>
                    <text class="price-value">{{ goods.price }}</text>
                  </view>
                  <text class="goods-count">共{{ goods.num }}件</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 备注信息 -->
          <view v-if="item.remark" class="remark-section" @click="handleTargetDetail(item.id)">
            <text class="remark-label">备注：</text>
            <text class="remark-text">{{ item.remark || '--' }}</text>
          </view>

          <!-- 预约信息 -->
          <view v-if="item.isReservation === 'Y'" class="reservation-section" @click="handleTargetDetail(item.id)">
            <text class="reservation-label">预约取餐：</text>
            <text class="reservation-time-text">{{ item.reservationTime }}</text>
          </view>
        </view>
      </view>
    </mescroll-body>
  </view>
</template>

<script>
  import {
    DeliveryStatusEnum,
    DeliveryTypeEnum,
    OrderStatusEnum,
    PayStatusEnum,
    PayTypeEnum,
    ReceiptStatusEnum
  } from '@/common/enum/order'
  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'
  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'
  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'
  import * as OrderApi from '@/api/order'
  import { wxPayment } from '@/utils/app'

  // 每页记录数量
  const pageSize = 15

  // tab栏数据
  const tabs = [{
    name: `全部`,
    value: 'all'
  }, {
    name: `待支付`,
    value: 'toPay'
  }, {
    name: `已支付`,
    value: 'paid'
  }, {
    name: `已取消`,
    value: 'cancel'
  }]

  export default {
    components: {
      MescrollBody
    },
    mixins: [MescrollMixin],
    data() {
      return {
        // 枚举类
        DeliveryStatusEnum,
        DeliveryTypeEnum,
        OrderStatusEnum,
        PayStatusEnum,
        PayTypeEnum,
        ReceiptStatusEnum,

        // 当前页面参数
        options: { dataType: 'all' },
        // tab栏数据
        tabs,
        // 当前标签索引
        curTab: 0,
        // 订单列表数据
        list: getEmptyPaginateObj(),
        // 正在加载
        isLoading: false,
        // 上拉加载配置
        upOption: {
          // 首次自动执行
          auto: true,
          // 每页数据的数量; 默认10
          page: { size: pageSize },
          // 数量要大于12条才显示无更多数据
          noMoreSize: 12,
          // 空布局
          empty: {
            tip: '亲，暂无订单记录'
          }
        },
        // 支付方式弹窗
        showPayPopup: false,
        statusText: "payStatus"
      }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad(options) {
      // 初始化当前选中的标签
      this.initCurTab(options)
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
      this.onRefreshList();
    },

    methods: {

      // 初始化当前选中的标签
      initCurTab(options) {
        const app = this
        if (options.dataType) {
            console.log("options === ", options);
            const index = app.tabs.findIndex(item => item.value == options.dataType)
            app.curTab = index > -1 ? index : 0
        }
      },

      /**
       * 上拉加载的回调 (页面初始化时也会执行一次)
       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10
       * @param {Object} page
       */
      upCallback(page) {
        const app = this
        // 设置列表数据
        app.getOrderList(page.num)
          .then(list => {
            const curPageLen = list.content.length;
            const totalSize = list.totalElements;
            app.mescroll.endBySize(curPageLen, totalSize);
          })
          .catch(() => app.mescroll.endErr())
      },

      // 获取订单列表
      getOrderList(pageNo = 1) {
        const app = this
        return new Promise((resolve, reject) => {
          OrderApi.list({ dataType: app.getTabValue(), page: pageNo }, { load: false })
            .then(result => {
              // 合并新数据
              const newList = result.data;
              app.list.content = getMoreListData(newList, app.list, pageNo);
              resolve(newList);
            })
        })
      },
      
      // 点击跳转到首页
      onTargetIndex() {
        this.$navTo('pages/index/index')
      },

      // 获取当前标签项的值
      getTabValue() {
        return this.tabs[this.curTab].value
      },

      // 切换标签项
      onChangeTab(index) {
        const app = this
        // 设置当前选中的标签
        app.curTab = index
        // 刷新订单列表
        app.onRefreshList()
      },

      // 刷新订单列表
      onRefreshList() {
        this.list = getEmptyPaginateObj()
        setTimeout(() => {
            this.mescroll.resetUpScroll()
        }, 120)
      },

      // 跳转到订单详情页
      handleTargetDetail(orderId) {
        this.$navTo('pages/order/detail', { orderId })
      }
    }

  }
</script>

<style lang="scss" scoped>
  // 通用布局类
  .flex-row {
    display: flex;
    flex-direction: row;
  }
  
  .flex-col {
    display: flex;
    flex-direction: column;
  }
  
  .items-center {
    align-items: center;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  .self-stretch {
    align-self: stretch;
  }

  // 页面容器
  .page {
    background-color: #f2f2f2;
    min-height: 100vh;
  }

  // 标签页区域
  .tab-section {
    background-color: #fff;
    padding: 28rpx 92rpx 32rpx;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
  }

  // 订单列表
  .order-list {
    padding: 32rpx 28rpx;
  }

  // 订单卡片
  .order-card {
    margin-bottom: 32rpx;
    padding: 40rpx 32rpx;
    background-color: #fff;
    border-radius: 18rpx;
    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
  }

  // 订单头部
  .order-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .order-type-tag {
      padding: 8rpx 16rpx;
      background-color: #fff;
      border-radius: 18rpx;
      min-width: 72rpx;
      height: 36rpx;
      border: 1rpx solid #515881;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10rpx;

      .tag-text {
        color: #454d78;
        font-size: 20rpx;
        font-weight: 600;
        line-height: 19rpx;
        letter-spacing: 4rpx;
        white-space: nowrap;
      }
    }

    .store-name {
      color: #000;
      font-size: 28rpx;
      font-weight: 400;
      line-height: 26rpx;
      letter-spacing: 5rpx;
    }

    .status-text {
      color: #454d78;
      font-size: 26rpx;
      font-weight: 400;
      line-height: 24rpx;
      text-shadow: 0 0 #454d78;
    }
  }

  // 订单时间
  .order-time {
    color: #b7b7b7;
    font-size: 20rpx;
    font-weight: 300;
    line-height: 16rpx;
    margin-bottom: 44rpx;
    text-shadow: 0 0 #b7b7b7;
  }

  // 商品区域
  .goods-section {
    margin-bottom: 20rpx;
  }

  // 商品项
  .goods-item {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    .goods-image {
      width: 124rpx;
      height: 124rpx;
      border-radius: 6rpx;
      margin-right: 20rpx;
      flex-shrink: 0;
    }

    .goods-info {
      flex: 1;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .goods-content {
      flex: 1;

      .goods-title {
        color: #000;
        font-size: 28rpx;
        font-weight: 400;
        line-height: 28rpx;
        margin-bottom: 8rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .goods-specs {
        display: flex;
        flex-wrap: wrap;
        margin-top: 8rpx;

        .spec-item {
          color: #999;
          font-size: 24rpx;
          font-weight: 300;
          line-height: 22rpx;
          margin-right: 16rpx;
          padding: 4rpx 12rpx;
          background-color: #f5f5f5;
          border-radius: 8rpx;
        }
      }
    }

    .goods-price-section {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .price-group {
        line-height: 28rpx;
        margin-bottom: 18rpx;

        .price-symbol {
          color: #000;
          font-size: 20rpx;
          font-weight: 600;
          line-height: 16rpx;
        }

        .price-value {
          color: #000;
          font-size: 34rpx;
          font-weight: 600;
          line-height: 28rpx;
        }
      }

      .goods-count {
        color: #999;
        font-size: 20rpx;
        font-weight: 300;
        line-height: 18rpx;
      }
    }
  }

  // 备注区域
  .remark-section {
    padding: 24rpx 0;
    border-top: 1rpx solid rgba(0, 0, 0, 0.1);
    margin-top: 20rpx;

    .remark-label {
      color: #666;
      font-size: 26rpx;
      font-weight: 400;
    }

    .remark-text {
      color: #333;
      font-size: 26rpx;
      font-weight: 400;
    }
  }

  // 预约标识
  .reservation-tag {
    display: flex;
    align-items: center;
    background-color: #fff3cd;
    border: 1rpx solid #ffeaa7;
    border-radius: 12rpx;
    padding: 4rpx 8rpx;
    margin-right: 12rpx;

    .reservation-icon {
      font-size: 20rpx;
      margin-right: 4rpx;
    }

    .reservation-text {
      color: #856404;
      font-size: 20rpx;
      font-weight: 500;
    }
  }

  // 预约信息区域
  .reservation-section {
    padding: 24rpx 0;
    border-top: 1rpx solid rgba(0, 0, 0, 0.1);
    margin-top: 20rpx;

    .reservation-label {
      color: #666;
      font-size: 26rpx;
      font-weight: 400;
    }

    .reservation-time-text {
      color: #ff6600;
      font-size: 26rpx;
      font-weight: 600;
    }
  }
</style>
