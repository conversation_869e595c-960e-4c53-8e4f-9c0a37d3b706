<view class="container b-f p-b data-v-32f2f1fc"><view class="base data-v-32f2f1fc"><view class="merchant-name data-v-32f2f1fc"><view class="name data-v-32f2f1fc">{{storeInfo?storeInfo.name:systemName}}</view></view></view><view class="pay-form data-v-32f2f1fc"><u-form vue-id="2b6a4b78-1" model="{{form}}" label-width="100rpx" class="data-v-32f2f1fc" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item class="input data-v-32f2f1fc" vue-id="{{('2b6a4b78-2')+','+('2b6a4b78-1')}}" prop="payAmount" border-bottom="{{false}}" label="金额" rules="[{ required: true, message: '请输入支付金额', trigger: 'blur' }]" bind:__l="__l" vue-slots="{{['default']}}"><view class="amount-icon data-v-32f2f1fc">￥</view><view class="amount data-v-32f2f1fc">{{form.payAmount}}</view></u-form-item><block wx:if="{{form.remark}}"><u-form-item class="input data-v-32f2f1fc" vue-id="{{('2b6a4b78-3')+','+('2b6a4b78-1')}}" border-bottom="{{false}}" label="备注" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('2b6a4b78-4')+','+('2b6a4b78-3')}}" type="text" placeholder="请输入备注信息" value="{{form.remark}}" data-event-opts="{{[['^input',[['__set_model',['$0','remark','$event',[]],['form']]]]]}}" class="data-v-32f2f1fc" bind:__l="__l"></u-input></u-form-item></block><u-form-item vue-id="{{('2b6a4b78-5')+','+('2b6a4b78-1')}}" border-bottom="{{false}}" class="data-v-32f2f1fc" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['showRemarkPop']]]]}}" class="remark data-v-32f2f1fc" bindtap="__e"><text class="iconfont icon-edit data-v-32f2f1fc"></text>添加备注</view></u-form-item></u-form></view><neoceansoft-keyboard vue-id="2b6a4b78-6" keyboardType="payment" behaviorBgColor="#3f51b5" data-event-opts="{{[['^result',[['changeAmount']]],['^paymentClick',[['doPay']]]]}}" bind:result="__e" bind:paymentClick="__e" class="data-v-32f2f1fc" bind:__l="__l"></neoceansoft-keyboard><view class="remark-popup data-v-32f2f1fc"><uni-popup vue-id="2b6a4b78-7" type="dialog" data-ref="remarkPopup" class="data-v-32f2f1fc vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('2b6a4b78-8')+','+('2b6a4b78-7')}}" mode="input" focus="false" title="备注信息" type="info" placeholder="请输入备注信息" before-close="{{true}}" value="{{form.remark}}" data-event-opts="{{[['^close',[['cancelRemark']]],['^confirm',[['doRemark']]],['^input',[['__set_model',['$0','remark','$event',[]],['form']]]]]}}" bind:close="__e" bind:confirm="__e" bind:input="__e" class="data-v-32f2f1fc" bind:__l="__l"></uni-popup-dialog></uni-popup></view><block wx:if="{{!isLoading}}"><popup vue-id="2b6a4b78-9" payInfo="{{payInfo}}" value="{{showPopup}}" data-event-opts="{{[['^modifyChoice',[['modifyChoice']]],['^input',[['__set_model',['','showPopup','$event',[]]]]]]}}" bind:modifyChoice="__e" bind:input="__e" class="data-v-32f2f1fc" bind:__l="__l"></popup></block></view>