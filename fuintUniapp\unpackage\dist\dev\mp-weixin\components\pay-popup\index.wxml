<view class="{{['pay-popup','popup','data-v-65b5fac2',value&&complete?'show':'none']}}" catchtouchmove="__e" data-event-opts="{{[['touchmove',[['moveHandle',['$event']]]]]}}"><view data-event-opts="{{[['tap',[['close',['mask']]]]]}}" class="mask data-v-65b5fac2" bindtap="__e"></view><block wx:if="{{!showPayPopup}}"><view class="confirm data-v-65b5fac2"><view class="layer attr-content data-v-65b5fac2" style="{{('border-radius: 10rpx 10rpx 0 0;')}}"><view class="specification-wrapper data-v-65b5fac2"><scroll-view class="specification-wrapper-content data-v-65b5fac2" scroll-y="true"><view class="specification-header data-v-65b5fac2"><view class="specification-name data-v-65b5fac2">支付确认</view></view><view class="specification-content data-v-65b5fac2"><block wx:if="{{$root.m0}}"><view class="pay-item data-v-65b5fac2"><view class="item-point data-v-65b5fac2"><view class="title data-v-65b5fac2"><text class="iconfont icon-success data-v-65b5fac2"></text><block wx:if="{{payInfo.payDiscount<1}}"><text class="point-amount data-v-65b5fac2">{{"会员"+$root.g0+"折优惠"}}<text class="amount data-v-65b5fac2">{{"￥"+$root.g1}}</text></text></block></view></view></view></block><block wx:if="{{payInfo.isLogin&&payInfo.pointAmount>=0.01&&payInfo.canUsedAsMoney=='true'}}"><view class="pay-item data-v-65b5fac2"><view class="item-point data-v-65b5fac2"><view class="title data-v-65b5fac2"><text class="iconfont icon-success data-v-65b5fac2"></text><block wx:if="{{payInfo.usePoint>0}}"><text class="point-amount data-v-65b5fac2">{{"使用"+$root.g2+"积分抵扣"}}</text></block><block wx:if="{{payInfo.usePoint>0}}"><text class="amount data-v-65b5fac2">{{"￥"+$root.g3}}</text></block><block wx:if="{{payInfo.usePoint<1}}"><text class="point-amount data-v-65b5fac2">不使用积分抵扣</text></block><block wx:if="{{payInfo.maxPoint>0}}"><text data-event-opts="{{[['tap',[['modifyPoint',['$event']]]]]}}" class="modify data-v-65b5fac2" bindtap="__e">修改>></text></block></view></view></view></block><block wx:if="{{!payInfo.isLogin&&payInfo.maxPoint<1}}"><view class="pay-item data-v-65b5fac2"><view class="item-point data-v-65b5fac2"><view class="title data-v-65b5fac2"><text class="iconfont icon-success data-v-65b5fac2"></text><text class="data-v-65b5fac2">会员可使用积分进行抵扣哦~</text><text data-event-opts="{{[['tap',[['onGetLogin',['$event']]]]]}}" class="modify data-v-65b5fac2" bindtap="__e">去登录>></text></view></view></view></block><block wx:if="{{payInfo.isLogin&&payInfo.couponInfo!==null}}"><view class="pay-item data-v-65b5fac2"><view class="item-point data-v-65b5fac2"><view class="title data-v-65b5fac2"><text class="iconfont icon-success data-v-65b5fac2"></text><block wx:if="{{payInfo.couponAmount>0}}"><text class="point-amount data-v-65b5fac2">使用卡券抵扣</text></block><block wx:if="{{payInfo.couponAmount>0}}"><text class="amount data-v-65b5fac2">{{"￥"+$root.g4}}</text></block><block wx:if="{{payInfo.couponAmount<=0&&payInfo.couponInfo.amount}}"><text class="point-amount data-v-65b5fac2">不使用卡券抵扣？</text></block><block wx:if="{{payInfo.couponInfo.amount}}"><text data-event-opts="{{[['tap',[['modifyCoupon',['$event']]]]]}}" class="modify data-v-65b5fac2" bindtap="__e">修改>></text></block></view></view></view></block><view class="pay-item data-v-65b5fac2"><view class="item-amount data-v-65b5fac2"><view class="title data-v-65b5fac2"><text class="iconfont icon-success data-v-65b5fac2"></text>实付金额：<text class="amount data-v-65b5fac2">{{"￥"+$root.g5}}</text></view></view></view></view></scroll-view><block wx:if="{{showClose}}"><view data-event-opts="{{[['tap',[['close',['close']]]]]}}" class="close data-v-65b5fac2" bindtap="__e"><image class="close-item data-v-65b5fac2" src="{{closeImage}}"></image></view></block></view><view class="btn-wrapper data-v-65b5fac2"><view data-event-opts="{{[['tap',[['toPay',['$event']]]]]}}" class="sure data-v-65b5fac2" bindtap="__e">确认支付</view></view></view><view class="point-popup data-v-65b5fac2"><uni-popup vue-id="51085b72-1" type="dialog" data-ref="pointPopup" class="data-v-65b5fac2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('51085b72-2')+','+('51085b72-1')}}" mode="input" focus="false" title="修改积分数量" type="info" placeholder="请输入积分数量" before-close="{{true}}" value="{{$root.g6}}" data-event-opts="{{[['^close',[['closeDialog']]],['^confirm',[['doUsePoint']]],['^input',[['__set_model',['$0','toFixed(0)','$event',[]],['payInfo.usePoint']]]]]}}" bind:close="__e" bind:confirm="__e" bind:input="__e" class="data-v-65b5fac2" bind:__l="__l"></uni-popup-dialog></uni-popup></view><view class="coupon-popup data-v-65b5fac2"><uni-popup vue-id="51085b72-3" type="dialog" data-ref="couponPopup" class="data-v-65b5fac2 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{payInfo.couponAmount>0}}"><uni-popup-dialog vue-id="{{('51085b72-4')+','+('51085b72-3')}}" focus="false" title="确认信息" content="不使用卡券进行抵扣？" type="info" before-close="{{true}}" value="{{$root.g8}}" data-event-opts="{{[['^close',[['closeDialog']]],['^confirm',[['doUseCoupon']]],['^input',[['__set_model',['$0','toFixed(0)','$event',[]],['payInfo.usePoint']]]]]}}" bind:close="__e" bind:confirm="__e" bind:input="__e" class="data-v-65b5fac2" bind:__l="__l"></uni-popup-dialog></block><block wx:if="{{payInfo.couponAmount<=0&&payInfo.couponInfo!==null}}"><uni-popup-dialog vue-id="{{('51085b72-5')+','+('51085b72-3')}}" focus="false" title="确认信息" content="{{'使用卡券最多可抵扣￥'+payInfo.couponInfo.amount}}" type="info" before-close="{{true}}" data-event-opts="{{[['^close',[['closeDialog']]],['^confirm',[['doUseCoupon']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-65b5fac2" bind:__l="__l"></uni-popup-dialog></block></uni-popup></view></view></block><u-popup bind:input="__e" vue-id="51085b72-6" mode="bottom" closeable="{{true}}" value="{{showPayPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showPayPopup','$event',[]]]]]]}}" class="data-v-65b5fac2" bind:__l="__l" vue-slots="{{['default']}}"><view class="pay-type-popup data-v-65b5fac2"><view class="title data-v-65b5fac2">请选择支付方式</view><view class="pop-content data-v-65b5fac2"><view data-event-opts="{{[['tap',[['payNow',['$0'],['PayTypeEnum.WECHAT.value']]]]]}}" class="pay-item dis-flex flex-x-between data-v-65b5fac2" bindtap="__e"><view class="item-left dis-flex flex-y-center data-v-65b5fac2"><view class="item-left_icon wechat data-v-65b5fac2"><text class="iconfont icon-weixinzhifu data-v-65b5fac2"></text></view><view class="item-left_text data-v-65b5fac2"><text class="data-v-65b5fac2">{{PayTypeEnum.WECHAT.name}}</text></view></view></view></view></view></u-popup></view>