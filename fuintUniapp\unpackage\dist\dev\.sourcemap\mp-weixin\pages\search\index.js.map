{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/search/index.vue?a875", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/search/index.vue?49f5", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/search/index.vue?f3f4", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/search/index.vue?461d", "uni-app:///pages/search/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/search/index.vue?1a3c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/search/index.vue?7194"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "historySearch", "searchValue", "onLoad", "methods", "getHistorySearch", "onSearch", "search", "setHistory", "index", "clearSearch", "onUpdateStorage", "uni", "handleQuick"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiC9pB;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;QACA;QACA;QACA;QACA;UAAAC;QAAA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACAC;MACAT;MACA;MACA;IACA;IAEA;AACA;AACA;IACAU;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACAC;IACA;IAEA;AACA;AACA;IACAC;MACA;QAAAN;MAAA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/search/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/search/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=482e85b8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=482e85b8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"482e85b8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/search/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=482e85b8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.historySearch.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"search-wrapper\">\n      <view class=\"search-input\">\n        <view class=\"search-input-wrapper\">\n          <view class=\"left\">\n            <text class=\"search-icon iconfont icon-sousuo\"></text>\n          </view>\n          <view class=\"right\">\n            <input v-model=\"searchValue\" class=\"input\" focus=\"true\" placeholder=\"请输入搜索关键字\" type=\"text\"></input>\n          </view>\n        </view>\n      </view>\n      <view class=\"search-button\">\n        <button class=\"button\" @click=\"onSearch\" type=\"warn\"> 搜索 </button>\n      </view>\n    </view>\n    <view class=\"history\" v-if=\"historySearch.length\">\n      <view class=\"his-head\">\n        <text class=\"title\">最近搜索</text>\n        <text class=\"icon iconfont icon-lajixiang col-7\" @click=\"clearSearch\"></text>\n      </view>\n      <view class=\"his-list\">\n        <view class=\"his-item\" v-for=\"(val, index) in historySearch\" :key=\"index\">\n          <view class=\"history-button\" @click=\"handleQuick(val)\">{{ val }}</view>\n        </view>\n      </view>\n    </view>\n    <!-- </view> -->\n  </view>\n</template>\n\n<script>\n  const HISTORY_SEARCH = 'historySearch'\n\n  export default {\n    data() {\n      return {\n        historySearch: [],\n        searchValue: ''\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 获取历史搜索\n      this.historySearch = this.getHistorySearch()\n    },\n\n    methods: {\n\n      /**\n       * 获取历史搜索\n       */\n      getHistorySearch() {\n        return uni.getStorageSync(HISTORY_SEARCH) || []\n      },\n\n      /**\n       * 搜索提交\n       */\n      onSearch() {\n        const { searchValue } = this\n        if (searchValue) {\n          // 记录历史搜索\n          this.setHistory(searchValue)\n          // 跳转到商品列表页\n          this.$navTo('pages/goods/list', { search: searchValue })\n        }\n      },\n\n      /**\n       * 记录历史搜索\n       */\n      setHistory(searchValue) {\n        const data = this.getHistorySearch()\n        const index = data.indexOf(searchValue)\n        index > -1 && data.splice(index, 1)\n        data.unshift(searchValue)\n        this.historySearch = data\n        this.onUpdateStorage()\n      },\n\n      /**\n       * 清空最近搜索记录\n       */\n      clearSearch() {\n        this.historySearch = []\n        this.onUpdateStorage()\n      },\n\n      /**\n       * 更新历史搜索缓存\n       * @param {Object} data\n       */\n      onUpdateStorage(data) {\n        uni.setStorageSync(HISTORY_SEARCH, this.historySearch)\n      },\n\n      /**\n       * 跳转到最近搜索\n       */\n      handleQuick(search) {\n        this.$navTo('pages/goods/list', { search })\n      }\n\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    padding: 20rpx;\n    min-height: 100vh;\n    background: #f7f7f7;\n  }\n\n  .search-wrapper {\n    display: flex;\n    height: 78rpx;\n  }\n\n  // 搜索输入框\n  .search-input {\n    width: 80%;\n    background: #fff;\n    border-radius: 50rpx 0 0 50rpx;\n    box-sizing: border-box;\n    overflow: hidden;\n    border: solid 1px #cccccc;\n    .search-input-wrapper {\n      display: flex;\n\n      .left {\n        display: flex;\n        width: 60rpx;\n        justify-content: center;\n        align-items: center;\n        .search-icon {\n          display: block;\n          color: #b4b4b4;\n          font-size: 30rpx;\r\n          font-weight: bold;\n        }\n      }\n\n      .right {\n        flex: 1;\n\n        input {\n          font-size: 28rpx;\n          height: 78rpx;\n          line-height: 78rpx;\n\n          .input-placeholder {\n            color: #aba9a9;\n          }\n        }\n\n      }\n    }\n  }\n\n  // 搜索按钮\n  .search-button {\n    width: 20%;\n    box-sizing: border-box;\n\n    .button {\n      line-height: 78rpx;\n      height: 78rpx;\n      font-size: 28rpx;\n      border-radius: 0 20px 20px 0;\n      background: $fuint-theme;\n    }\n  }\n\n\n  // 最近搜索\n  .history {\n\n    .his-head {\n      font-size: 28rpx;\n      padding: 50rpx 0 0 0;\n      color: #777;\n\n      .icon {\n        float: right;\n      }\n\n    }\n\n    .his-list {\n      padding: 10px 0;\n      overflow: hidden;\n\n      .his-item {\n        width: 33.3%;\n        float: left;\n        padding: 10rpx;\n        box-sizing: border-box;\n\n        .history-button {\n          text-align: center;\n          padding: 14rpx;\n          line-height: 30rpx;\n          border-radius: 100rpx;\n          background: #fff;\n          font-size: 26rpx;\n          border: 1px solid #efefef;\n          overflow: hidden;\n          white-space: nowrap;\n          text-overflow: ellipsis;\n        }\n\n      }\n\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=482e85b8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=482e85b8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424089\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}