{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/components/SlideImage.vue?3b08", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/components/SlideImage.vue?5e06", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/components/SlideImage.vue?c6ba", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/components/SlideImage.vue?5b77", "uni-app:///pages/goods/components/SlideImage.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/components/SlideImage.vue?1b03", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/components/SlideImage.vue?8a68"], "names": ["props", "images", "type", "default", "data", "indicatorDots", "autoplay", "interval", "duration", "currentIndex", "methods", "setCurrent", "app", "onPreviewImages", "imageUrls", "uni", "current", "urls"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,6oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkBlrB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EAEAC;IAEA;IACAC;MACA;MACAC;IACA;IAEA;IACAC;MACA;MACA;MACAD;QACAE;MACA;MACAC;QACAC;QACAC;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAA6wC,CAAgB,wqCAAG,EAAC,C;;;;;;;;;;;ACAjyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods/components/SlideImage.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./SlideImage.vue?vue&type=template&id=25789601&scoped=true&\"\nvar renderjs\nimport script from \"./SlideImage.vue?vue&type=script&lang=js&\"\nexport * from \"./SlideImage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./SlideImage.vue?vue&type=style&index=0&id=25789601&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"25789601\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods/components/SlideImage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SlideImage.vue?vue&type=template&id=25789601&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.images.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SlideImage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SlideImage.vue?vue&type=script&lang=js&\"", "<template>\n  <!-- 商品图片 -->\n  <view class=\"images-swiper\">\n    <swiper class=\"swiper-box\" :autoplay=\"autoplay\" :duration=\"duration\" :indicator-dots=\"indicatorDots\"\n      :interval=\"interval\" :circular=\"true\" @change=\"setCurrent\">\n      <swiper-item v-for=\"(item, index) in images\" :key=\"index\" @click=\"onPreviewImages(index)\">\n        <image class=\"slide-image\" mode=\"aspectFill\" :src=\"item\"></image>\n      </swiper-item>\n    </swiper>\n    <view class=\"banner-num\">\n      <text>{{ currentIndex }}</text>\n      <text>/</text>\n      <text>{{ images.length }}</text>\n    </view>\n  </view>\n</template>\n\n<script>\n  export default {\n    props: {\n      images: {\n        type: Array,\n        default: []\n      }\n    },\n    data() {\n      return {\n        indicatorDots: true, // 是否显示面板指示点\n        autoplay: true, // 是否自动切换\n        interval: 3000, // 自动切换时间间隔\n        duration: 800, // 滑动动画时长\n        currentIndex: 1 // 轮播图指针\n      }\n    },\n\n    methods: {\n\n      // 设置轮播图当前指针 数字\n      setCurrent(e) {\n        const app = this\n        app.currentIndex = e.detail.current + 1\n      },\n\n      // 浏览商品图片\n      onPreviewImages(index) {\n        const app = this\n        const imageUrls = []\n        app.images.forEach(item => {\n          imageUrls.push(item);\n        });\n        uni.previewImage({\n          current: imageUrls[index],\n          urls: imageUrls\n        })\n      }\n\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  // swiper组件\n  .swiper-box {\n    width: 100%;\n    height: 100vw;\n\n    .slide-image {\n      width: 100%;\n      height: 100%;\n      margin: 0rpx;\n      padding: 0rpx;\n      display: block;\n      border-radius: 1rpx;\n    }\n  }\n\n  /* banner计数 */\n  .banner-num {\n    position: absolute;\n    right: 30rpx;\n    margin-top: -70rpx;\n    padding: 2rpx 18rpx;\n    background: rgba(0, 0, 0, 0.363);\n    border-radius: 50rpx;\n    color: #fff;\n    font-size: 26rpx;\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SlideImage.vue?vue&type=style&index=0&id=25789601&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SlideImage.vue?vue&type=style&index=0&id=25789601&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424030\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}