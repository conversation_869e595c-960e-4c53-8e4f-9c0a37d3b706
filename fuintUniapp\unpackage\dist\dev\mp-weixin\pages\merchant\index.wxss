@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.main-header.data-v-786a6504 {
  position: relative;
  height: 240rpx;
  background-size: 100% 100%;
  overflow: hidden;
  display: block;
  align-items: center;
  background: #3f51b5;
  padding: 10rpx;
  margin: 20rpx 20rpx 0rpx 20rpx;
  border-top-left-radius: 8rpx;
  border-top-right-radius: 8rpx;
}
.main-header .user-info.data-v-786a6504 {
  display: block;
  height: 100rpx;
  margin-top: 1rpx;
  text-align: center;
  width: 100%;
}
.main-header .user-info .user-content.data-v-786a6504 {
  display: block;
  margin-left: 0rpx;
  text-align: left;
  color: #ffffff;
}
.main-header .user-info .user-content .belong.data-v-786a6504 {
  font-size: 28rpx;
  color: #fff;
}
.main-header .user-info .user-content .belong .nick-name.data-v-786a6504 {
  padding-left: 15rpx;
}
.main-header .user-info .user-content .login-tips.data-v-786a6504 {
  margin-top: 12rpx;
  font-size: 28rpx;
}
.main-header .user-info .amount-info.data-v-786a6504 {
  margin-top: 25rpx;
  color: #fff;
  display: block;
  text-align: center;
}
.main-header .user-info .amount-info .amount-tip.data-v-786a6504 {
  font-size: 28rpx;
}
.main-header .user-info .amount-info .amount-num.data-v-786a6504 {
  margin-top: 10rpx;
  font-weight: bold;
  font-size: 58rpx;
}
.user-app.data-v-786a6504 {
  display: flex;
  height: 260rpx;
  text-align: center;
  color: #fff;
  background: #3f51b5;
  margin: 0 20rpx;
  border-bottom-left-radius: 8rpx;
  border-bottom-right-radius: 8rpx;
  padding-bottom: 60rpx;
}
.user-app .item.data-v-786a6504 {
  width: 50%;
  height: 100%;
  margin: 20rpx;
  padding: 20rpx;
  text-align: right;
}
.user-app .item .tool.data-v-786a6504 {
  width: 280rpx;
  clear: both;
  padding: 20rpx;
  border: 1rpx solid #fff;
  border-radius: 30rpx;
  text-align: center;
  margin: 0 auto;
}
.user-app .item .tool .icon .image.data-v-786a6504 {
  height: 68rpx;
  width: 68rpx;
  font-weight: bold;
}
.user-app .item .tool .text.data-v-786a6504 {
  margin-top: 10rpx;
  font-size: 30rpx;
  text-align: center;
  font-weight: bold;
}
.my-asset.data-v-786a6504 {
  display: flex;
  padding: 40rpx 0;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  border-radius: 5rpx;
  margin: 25rpx 20rpx 5rpx 20rpx;
  background: #ffffff;
}
.my-asset .asset-right.data-v-786a6504 {
  width: 200rpx;
  border-left: 1rpx solid #eee;
}
.my-asset .asset-right-item.data-v-786a6504 {
  text-align: center;
  color: #545454;
}
.my-asset .asset-right-item .item-icon.data-v-786a6504 {
  font-size: 60rpx;
}
.my-asset .asset-right-item .item-name.data-v-786a6504 {
  margin-top: 10rpx;
}
.my-asset .asset-right-item .item-name text.data-v-786a6504 {
  font-size: 20rpx;
}
.my-asset .asset-left-item.data-v-786a6504 {
  text-align: center;
  color: #666;
  padding: 0 72rpx;
}
.my-asset .asset-left-item .item-value.data-v-786a6504 {
  font-size: 36rpx;
  color: #f03c3c;
  font-weight: bold;
}
.my-asset .asset-left-item .item-name.data-v-786a6504 {
  margin-top: 6rpx;
}
.my-asset .asset-left-item .item-name.data-v-786a6504 {
  font-size: 24rpx;
}
.order-navbar.data-v-786a6504 {
  display: flex;
  margin: 20rpx auto 20rpx auto;
  padding: 20rpx 0;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  font-size: 30rpx;
  border-radius: 5rpx;
  background: #fff;
}
.order-navbar-item.data-v-786a6504 {
  position: relative;
  width: 25%;
}
.order-navbar-item .item-icon.data-v-786a6504 {
  text-align: center;
  margin: 0 auto;
  padding: 10rpx 0;
  color: #545454;
  font-size: 40rpx;
}
.order-navbar-item .item-name.data-v-786a6504 {
  font-size: 24rpx;
  color: #545454;
  text-align: center;
  margin-right: 10rpx;
}
.order-navbar-item .order-badge.data-v-786a6504 {
  position: absolute;
  top: 0;
  right: 55rpx;
  font-size: 22rpx;
  background: #fa2209;
  text-align: center;
  line-height: 28rpx;
  color: #fff;
  border-radius: 100%;
  min-height: 30rpx;
  min-width: 30rpx;
  padding: 1rpx;
}
.my-service.data-v-786a6504 {
  margin: 22rpx auto 22rpx auto;
  padding: 20rpx 0;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  border-radius: 5rpx;
  background: #fff;
}
.my-service .service-title.data-v-786a6504 {
  padding-left: 20rpx;
  margin-bottom: 30rpx;
  font-size: 28rpx;
}
.my-service .service-content .service-item.data-v-786a6504 {
  position: relative;
  width: 25%;
  float: left;
  margin-bottom: 30rpx;
}
.my-service .service-content .service-item .item-icon.data-v-786a6504 {
  text-align: center;
  margin: 0 auto;
  padding: 10rpx 0;
  color: #ff3800;
  font-size: 40rpx;
}
.my-service .service-content .service-item .item-name.data-v-786a6504 {
  font-size: 24rpx;
  color: #545454;
  text-align: center;
  margin-right: 10rpx;
}
