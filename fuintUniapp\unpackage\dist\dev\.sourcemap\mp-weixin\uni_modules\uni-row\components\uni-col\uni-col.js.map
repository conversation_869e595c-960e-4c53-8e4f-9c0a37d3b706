{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-col/uni-col.vue?9bfa", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-col/uni-col.vue?2b64", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-col/uni-col.vue?b3ca", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-col/uni-col.vue?bfe1", "uni-app:///uni_modules/uni-row/components/uni-col/uni-col.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-col/uni-col.vue?cdf7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-col/uni-col.vue?fadf"], "names": ["name", "options", "virtualHost", "props", "span", "type", "default", "offset", "pull", "push", "xs", "sm", "md", "lg", "xl", "data", "gutter", "sizeClass", "parentWidth", "nvueWidth", "marginLeft", "right", "left", "created", "parent", "computed", "sizeList", "pointClassList", "classList", "Object", "pointProp", "ComponentClass", "methods", "updateGutter", "parentGutter", "watch", "immediate", "handler", "size"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACiL;AACjL,gBAAgB,kLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC0B9rB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AAAA,eACA;EACAA;EAEAC;IACAC;EACA;;EAEAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IAAA;IACA;IACA;IAEA;MACAC;IACA;IAEA;IACAA;MACA;IACA;EAQA;EACAC;IACAC;MACA,IACAtB,OAIA,KAJAA;QACAG,SAGA,KAHAA;QACAC,OAEA,KAFAA;QACAC,OACA,KADAA;MAGA;QACAL;QACAG;QACAC;QACAC;MACA;IACA;IAEAkB;MAAA;MACA;MAEA;QACA;QACA;UACAC;QACA;UACAC;YACAD,eACAE,iCACAC,6EACAA,wFACA;UACA;QACA;MACA;;MAEA;MACA;IACA;EAEA;EACAC;IACAC;MACAC;MACA;QACA;MACA;IACA;EA4BA;EACAC;IACAT;MACAU;MACAC;QAEA;QACA;UACA;UACA;YACAT,eACAU,4BACAP,iDACAA,uDACA;UACA;QACA;QACA;QACA;MAKA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACnNA;AAAA;AAAA;AAAA;AAAqyC,CAAgB,qqCAAG,EAAC,C;;;;;;;;;;;ACAzzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-row/components/uni-col/uni-col.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-col.vue?vue&type=template&id=fff79656&scoped=true&\"\nvar renderjs\nimport script from \"./uni-col.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-col.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-col.vue?vue&type=style&index=0&id=fff79656&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fff79656\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-row/components/uni-col/uni-col.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-col.vue?vue&type=template&id=fff79656&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = Number(_vm.gutter)\n  var m1 = Number(_vm.gutter)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-col.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-col.vue?vue&type=script&lang=js&\"", "<template>\n    <!-- #ifndef APP-NVUE -->\n    <view :class=\"['uni-col', sizeClass, pointClassList]\" :style=\"{\n        paddingLeft:`${Number(gutter)}rpx`,\n        paddingRight:`${Number(gutter)}rpx`,\n    }\">\n        <slot></slot>\n    </view>\n    <!-- #endif -->\n    <!-- #ifdef APP-NVUE -->\n    <!-- 在nvue上，类名样式不生效，换为style -->\n    <!-- 设置right正值失效，设置 left 负值 -->\n    <view :class=\"['uni-col']\" :style=\"{\n        paddingLeft:`${Number(gutter)}rpx`,\n        paddingRight:`${Number(gutter)}rpx`,\n        width:`${nvueWidth}rpx`,\n        position:'relative',\n        marginLeft:`${marginLeft}rpx`,\n        left:`${right === 0 ? left : -right}rpx`\n    }\">\n        <slot></slot>\n    </view>\n    <!-- #endif -->\n</template>\n\n<script>\n    /**\n     * Col    布局-列\n     * @description    搭配uni-row使用，构建布局。\n     * @tutorial    https://ext.dcloud.net.cn/plugin?id=3958\n     *\n     * @property    {span} type = Number 栅格占据的列数\n     *                         默认 24\n     * @property    {offset} type = Number 栅格左侧的间隔格数\n     * @property    {push} type = Number 栅格向右移动格数\n     * @property    {pull} type = Number 栅格向左移动格数\n     * @property    {xs} type = [Number, Object] <768px 响应式栅格数或者栅格属性对象\n     *                         @description    Number时表示在此屏幕宽度下，栅格占据的列数。Object时可配置多个描述{span: 4, offset: 4}\n     * @property    {sm} type = [Number, Object] ≥768px 响应式栅格数或者栅格属性对象\n     *                         @description    Number时表示在此屏幕宽度下，栅格占据的列数。Object时可配置多个描述{span: 4, offset: 4}\n     * @property    {md} type = [Number, Object] ≥992px 响应式栅格数或者栅格属性对象\n     *                         @description    Number时表示在此屏幕宽度下，栅格占据的列数。Object时可配置多个描述{span: 4, offset: 4}\n     * @property    {lg} type = [Number, Object] ≥1200px 响应式栅格数或者栅格属性对象\n     *                         @description    Number时表示在此屏幕宽度下，栅格占据的列数。Object时可配置多个描述{span: 4, offset: 4}\n     * @property    {xl} type = [Number, Object] ≥1920px 响应式栅格数或者栅格属性对象\n     *                         @description    Number时表示在此屏幕宽度下，栅格占据的列数。Object时可配置多个描述{span: 4, offset: 4}\n     */\n    const ComponentClass = 'uni-col';\n\n    // -1 默认值，因为在微信小程序端只给Number会有默认值0\n    export default {\n        name: 'uniCol',\n        // #ifdef MP-WEIXIN\n        options: {\n            virtualHost: true // 在微信小程序中将组件节点渲染为虚拟节点，更加接近Vue组件的表现\n        },\n        // #endif\n        props: {\n            span: {\n                type: Number,\n                default: 24\n            },\n            offset: {\n                type: Number,\n                default: -1\n            },\n            pull: {\n                type: Number,\n                default: -1\n            },\n            push: {\n                type: Number,\n                default: -1\n            },\n            xs: [Number, Object],\n            sm: [Number, Object],\n            md: [Number, Object],\n            lg: [Number, Object],\n            xl: [Number, Object]\n        },\n        data() {\n            return {\n                gutter: 0,\n                sizeClass: '',\n                parentWidth: 0,\n                nvueWidth: 0,\n                marginLeft: 0,\n                right: 0,\n                left: 0\n            }\n        },\n        created() {\n            // 字节小程序中，在computed中读取$parent为undefined\n            let parent = this.$parent;\n\n            while (parent && parent.$options.componentName !== 'uniRow') {\n                parent = parent.$parent;\n            }\n\n            this.updateGutter(parent.gutter)\n            parent.$watch('gutter', (gutter) => {\n                this.updateGutter(gutter)\n            })\n\n            // #ifdef APP-NVUE\n            this.updateNvueWidth(parent.width)\n            parent.$watch('width', (width) => {\n                this.updateNvueWidth(width)\n            })\n            // #endif\n        },\n        computed: {\n            sizeList() {\n                let {\n                    span,\n                    offset,\n                    pull,\n                    push\n                } = this;\n\n                return {\n                    span,\n                    offset,\n                    pull,\n                    push\n                }\n            },\n            // #ifndef APP-NVUE\n            pointClassList() {\n                let classList = [];\n\n                ['xs', 'sm', 'md', 'lg', 'xl'].forEach(point => {\n                    const props = this[point];\n                    if (typeof props === 'number') {\n                        classList.push(`${ComponentClass}-${point}-${props}`)\n                    } else if (typeof props === 'object' && props) {\n                        Object.keys(props).forEach(pointProp => {\n                            classList.push(\n                                pointProp === 'span' ?\n                                `${ComponentClass}-${point}-${props[pointProp]}` :\n                                `${ComponentClass}-${point}-${pointProp}-${props[pointProp]}`\n                            )\n                        })\n                    }\n                });\n\n                // 支付宝小程序使用 :class=[ ['a','b'] ]，渲染错误\n                return classList.join(' ');\n            }\n            // #endif\n        },\n        methods: {\n            updateGutter(parentGutter) {\n                parentGutter = Number(parentGutter);\n                if (!isNaN(parentGutter)) {\n                    this.gutter = parentGutter / 2\n                }\n            },\n            // #ifdef APP-NVUE\n            updateNvueWidth(width) {\n                // 用于在nvue端，span，offset，pull，push的计算\n                this.parentWidth = width;\n                ['span', 'offset', 'pull', 'push'].forEach(size => {\n                    const curSize = this[size];\n                    if ((curSize || curSize === 0) && curSize !== -1) {\n                        let RPX = 1 / 24 * curSize * width\n                        RPX = Number(RPX);\n                        switch (size) {\n                            case 'span':\n                                this.nvueWidth = RPX\n                                break;\n                            case 'offset':\n                                this.marginLeft = RPX\n                                break;\n                            case 'pull':\n                                this.right = RPX\n                                break;\n                            case 'push':\n                                this.left = RPX\n                                break;\n                        }\n                    }\n                });\n            }\n            // #endif\n        },\n        watch: {\n            sizeList: {\n                immediate: true,\n                handler(newVal) {\n                    // #ifndef APP-NVUE\n                    let classList = [];\n                    for (let size in newVal) {\n                        const curSize = newVal[size];\n                        if ((curSize || curSize === 0) && curSize !== -1) {\n                            classList.push(\n                                size === 'span' ?\n                                `${ComponentClass}-${curSize}` :\n                                `${ComponentClass}-${size}-${curSize}`\n                            )\n                        }\n                    }\n                    // 支付宝小程序使用 :class=[ ['a','b'] ]，渲染错误\n                    this.sizeClass = classList.join(' ');\n                    // #endif\n                    // #ifdef APP-NVUE\n                    this.updateNvueWidth(this.parentWidth);\n                    // #endif\n                }\n            }\n        }\n    }\n</script>\n\n<style lang='scss' scoped>\n    /* breakpoints */\n    $--sm: 768px !default;\n    $--md: 992px !default;\n    $--lg: 1200px !default;\n    $--xl: 1920px !default;\n\n    $breakpoints: ('xs' : (max-width: $--sm - 1),\n    'sm' : (min-width: $--sm),\n    'md' : (min-width: $--md),\n    'lg' : (min-width: $--lg),\n    'xl' : (min-width: $--xl));\n\n    $layout-namespace: \".uni-\";\n    $col: $layout-namespace+\"col\";\n\n    @function getSize($size) {\n        @return 1 / 24 * $size * 100 * 1%;\n    }\n\n    @mixin res($key, $map:$breakpoints) {\n        @if map-has-key($map, $key) {\n            @media screen and #{inspect(map-get($map,$key))} {\n                @content;\n            }\n        }\n\n        @else {\n            @warn \"Undeinfed point: `#{$key}`\";\n        }\n    }\n\n    /* #ifndef APP-NVUE */\n    #{$col} {\n        float: left;\n        box-sizing: border-box;\n    }\n\n    #{$col}-0 {\n        /* #ifdef APP-NVUE */\n        width: 0;\n        height: 0;\n        margin-top: 0;\n        margin-right: 0;\n        margin-bottom: 0;\n        margin-left: 0;\n        /* #endif */\n        /* #ifndef APP-NVUE */\n        display: none;\n        /* #endif */\n    }\n\n    @for $i from 0 through 24 {\n        #{$col}-#{$i} {\n            width: getSize($i);\n        }\n\n        #{$col}-offset-#{$i} {\n            margin-left: getSize($i);\n        }\n\n        #{$col}-pull-#{$i} {\n            position: relative;\n            right: getSize($i);\n        }\n\n        #{$col}-push-#{$i} {\n            position: relative;\n            left: getSize($i);\n        }\n    }\n\n    @each $point in map-keys($breakpoints) {\n        @include res($point) {\n            #{$col}-#{$point}-0 {\n                display: none;\n            }\n\n            @for $i from 0 through 24 {\n                #{$col}-#{$point}-#{$i} {\n                    width: getSize($i);\n                }\n\n                #{$col}-#{$point}-offset-#{$i} {\n                    margin-left: getSize($i);\n                }\n\n                #{$col}-#{$point}-pull-#{$i} {\n                    position: relative;\n                    right: getSize($i);\n                }\n\n                #{$col}-#{$point}-push-#{$i} {\n                    position: relative;\n                    left: getSize($i);\n                }\n            }\n        }\n    }\n\n    /* #endif */\n</style>\n", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-col.vue?vue&type=style&index=0&id=fff79656&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-col.vue?vue&type=style&index=0&id=fff79656&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425316\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}