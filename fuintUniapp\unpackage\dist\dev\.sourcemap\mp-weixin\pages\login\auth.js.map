{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/auth.vue?52d0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/auth.vue?f7a8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/auth.vue?af56", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/auth.vue?0ccd", "uni-app:///pages/login/auth.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/auth.vue?b2da", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/auth.vue?8680"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "code", "onLoad", "methods", "do<PERSON><PERSON><PERSON>", "doAuth", "app", "setTimeout", "store", "then", "catch", "getQueryVariable", "handleCancel", "onNavigateBack", "uni", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyoB,CAAgB,uoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmB7pB;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;eACA;EAEAC;IACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;MACA;IACA;MACA;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAA;gBACA;gBACAC;kBACAD;gBACA;gBAAA,iCACA;cAAA;gBAGA;gBACAE;kBAAAP;gBAAA,GACAQ;kBACA;oBACA;oBACAH;oBACAA;kBACA;oBACAE;kBACA;gBACA,GACAE;kBACAJ;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAK;MACA;MACA;MACA;QACA;QACA;UAAA;QAAA;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACAJ;MACA;IACA;IAEA;AACA;AACA;IACAK;MAAA;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpGA;AAAA;AAAA;AAAA;AAA4uC,CAAgB,kqCAAG,EAAC,C;;;;;;;;;;;ACAhwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/auth.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/auth.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./auth.vue?vue&type=template&id=0a348a72&scoped=true&\"\nvar renderjs\nimport script from \"./auth.vue?vue&type=script&lang=js&\"\nexport * from \"./auth.vue?vue&type=script&lang=js&\"\nimport style0 from \"./auth.vue?vue&type=style&index=0&id=0a348a72&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0a348a72\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/auth.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=template&id=0a348a72&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"wechatapp\">\n      <view class=\"header\"></view>\n    </view>\n    <view class=\"auth-title\">申请获取以下权限</view>\n    <view>\n        <view class=\"auth-subtitle\">获得你的公开信息（昵称、头像等）</view>\n        <view class=\"login-btn\">\n          <button class=\"button btn-normal\" @click.stop=\"doLogin\">授权登录</button>\n        </view>\n    </view>\n    <view class=\"no-login-btn\">\n      <button class=\"button btn-normal\" @click=\"handleCancel\">暂不登录</button>\n    </view>\n  </view>\n</template>\n\n<script>\n  import store from '@/store'\n  import * as LoginApi from '@/api/login'\n  export default {\n\n    data() {\n      return {\n        code: ''\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onLoad(options) {\n       if (options.code) {\n           this.code = options.code;\n       } else {\n           this.code = this.getQueryVariable('code');\n       }\n    },\n\n    methods: {\n        doLogin() {\r\n          this.doAuth();\r\n        },\n        // 授权登录\n        async doAuth() {\n            const app = this;\n            if (!app.code) {\n                app.$toast(\"抱歉，授权失败！\");\n                // 跳转回原页面\n                setTimeout(() => {\n                  app.$navTo('pages/user/index')\n                }, 1000);\n                return false;\n            }\r\n            \r\n            // 提交到后端\r\n            store.dispatch('MpWxAuthLogin', { code: app.code })\r\n              .then(result => {\r\n                if (result.code == '200') {\r\n                    // 显示登录成功\r\n                    app.$toast(result.message);\r\n                    app.$navTo('pages/user/index');\r\n                } else {\r\n                    store.dispatch('Logout')\r\n                }\r\n              })\r\n              .catch(() => {\r\n                 app.$toast(\"抱歉，授权失败！\");\r\n              })\n          },\n          \n          getQueryVariable(variable) {\n            const query = window.location.search.substring(1);\n            const vars = query.split(\"&\");\n            for (let i = 0; i < vars.length; i++) {\n              let pair = vars[i].split(\"=\");\n              if (pair[0] == variable) { return pair[1]; }\n            }\n            return (false);\n          },\n\n          /**\n           * 暂不登录\n           */\n          handleCancel() {\n            // 跳转回原页面\r\n            store.dispatch('Logout');\n            this.$navTo('pages/user/index');\n          },\n\n          /**\n           * 授权成功 跳转回原页面\n           */\n          onNavigateBack(delta = 1) {\n            uni.navigateBack({\n              delta: Number(delta)\n            })\n          }\n        }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    padding: 0 60rpx;\n    font-size: 32rpx;\n    background: #fff;\n    min-height: 100vh;\n  }\n\n  .wechatapp {\n    padding: 80rpx 0 48rpx;\n    border-bottom: 1rpx solid #e3e3e3;\n    margin-bottom: 72rpx;\n    text-align: center;\n\n    .header {\n      width: 190rpx;\n      height: 190rpx;\n      border: 4rpx solid #fff;\n      margin: 0 auto 0;\n      border-radius: 50%;\n      overflow: hidden;\n      box-shadow: 2rpx 0 10rpx rgba(50, 50, 50, 0.3);\n    }\n  }\n\n  .auth-title {\n    color: #585858;\n    font-size: 34rpx;\n    margin-bottom: 40rpx;\n  }\n\n  .auth-subtitle {\n    color: #888;\n    margin-bottom: 88rpx;\n    font-size: 28rpx;\n  }\n\n  .login-btn {\n    padding: 0 20rpx;\n\n    .button {\n      height: 88rpx;\n      line-height: 88rpx;\n      background: $fuint-theme;\n      color: #fff;\n      font-size: 30rpx;\n      border-radius: 12rpx;\n      text-align: center;\n    }\n  }\n  .no-login-btn {\n    margin-top: 24rpx;\n    padding: 0 20rpx;\n    .button {\n      height: 88rpx;\n      line-height: 88rpx;\n      background: #dfdfdf;\n      color: #fff;\n      font-size: 30rpx;\n      border-radius: 12rpx;\n      text-align: center;\n    }\n  }\n</style>", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=style&index=0&id=0a348a72&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./auth.vue?vue&type=style&index=0&id=0a348a72&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424035\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}