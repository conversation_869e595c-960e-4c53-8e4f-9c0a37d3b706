{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/trees.vue?71b3", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/trees.vue?fbc5", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/trees.vue?f057", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/trees.vue?ca8d", "uni-app:///components/jyf-parser/libs/trees.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/trees.vue?395b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/trees.vue?e4d7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/handler.wxs?65c3", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/jyf-parser/libs/handler.wxs?0fbe"], "names": ["global", "components", "trees", "name", "data", "ctrl", "placeholder", "errorImg", "loadVideo", "c", "s", "props", "nodes", "lazyLoad", "loading", "mounted", "methods", "init", "ctx", "play", "contexts", "imgtap", "id", "src", "ignore", "current", "uni", "urls", "loadImg", "linkpress", "attrs", "appId", "path", "success", "title", "url", "fail", "error", "source", "i", "e", "target", "errMsg", "_loadVideo"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsT;AACtT;AACyD;AACL;AACa;;;AAGjE;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,oRAAM;AACR,EAAE,6RAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wRAAU;AACZ;AACA;;AAEA;AAC4M;AAC5M,WAAW,8NAAM,iBAAiB,sOAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4E7qBA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;IAEA;EACA;EACAC;IACAC;IACAC;IACAC;EAKA;EACAC;IACA;MAAA;IAAA;IACA;EACA;EAMAC;IACAC;MACA;QACA;UACA;QAmBA;UACA;UACA;YACAC,yCAEA,KAEA;UACA,mCACAA;UACA;YACAA;YACA;UACA;QACA;MACA;IAOA;IACAC;MACA;MACA,+CACA;QACA,kDACAC;MAAA;IACA;IACAC;MACA;MACA;QACA;UACAjB;YACAkB;YACAC;YACAC;cAAA;YAAA;UACA;QACAxB;QACA;QACA;UACA;YACAyB;UACAC;YACAD;YACAE;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;QAMA;MAKA;QAKA;MAIA;IACA;IACAC;MACA;QACAC;MACAA;QAAA;MAAA;MACA9B;MACA;MACA;QAEA;UACA;YACA+B;YACAC;UACA;QACA;QAEA;UACA;YACA,wBACA;cACAV;YACA;UACA;YAKAI;cACAtB;cACA6B;gBAAA,OACAP;kBACAQ;gBACA;cAAA;YACA;UAEA,OACAR;YACAS;YACAC;cACAV;gBACAS;cACA;YACA;UACA;QACA;MACA;IACA;IACAE;MACA;QACAC;QACAC;MACA;QACA;QACA;QACA,+CACA;QACA,uBACAC;MACA;QACA;QACA;MACA;MACA;QACAF;QACAG;QACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3RA;AAAA;AAAA;AAAA;AAAk8B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACAt9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAsV,CAAgB,oZAAG,EAAC,C;;;;;;;;;;;;ACA1W;AAAe;AACf;AACA;AACA;;AAEA,M", "file": "components/jyf-parser/libs/trees.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./trees.vue?vue&type=template&id=13da2543&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjYxNTAsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjYxNTB9fQ%3D%3D&\"\nvar renderjs\nimport script from \"./trees.vue?vue&type=script&lang=js&\"\nexport * from \"./trees.vue?vue&type=script&lang=js&\"\nimport style0 from \"./trees.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cworkspace%5CfuintFoodSystem%5CfuintUniapp%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/jyf-parser/libs/trees.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=template&id=13da2543&filter-modules=eyJoYW5kbGVyIjp7InR5cGUiOiJzY3JpcHQiLCJjb250ZW50IjoiIiwic3RhcnQiOjYxNTAsImF0dHJzIjp7Im1vZHVsZSI6ImhhbmRsZXIiLCJsYW5nIjoid3hzIiwic3JjIjoiLi9oYW5kbGVyLnd4cyJ9LCJlbmQiOjYxNTB9fQ%3D%3D&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=script&lang=js&\"", "<template>\n    <view :class=\"'interlayer '+(c||'')\" :style=\"s\">\n        <block v-for=\"(n, i) in nodes\" v-bind:key=\"i\">\n            <!--图片-->\n            <view v-if=\"n.name=='img'\" :class=\"'_img '+n.attrs.class\" :style=\"n.attrs.style\" :data-attrs=\"n.attrs\" @tap.stop=\"imgtap\">\n                <rich-text v-if=\"ctrl[i]!=0\" :nodes=\"[{attrs:{src:loading&&(ctrl[i]||0)<2?loading:(lazyLoad&&!ctrl[i]?placeholder:(ctrl[i]==3?errorImg:n.attrs.src||'')),alt:n.attrs.alt||'',width:n.attrs.width||'',style:'-webkit-touch-callout:none;max-width:100%;display:block'+(n.attrs.height?';height:'+n.attrs.height:'')},name:'img'}]\" />\n                <image class=\"_image\" :src=\"lazyLoad&&!ctrl[i]?placeholder:n.attrs.src\" :lazy-load=\"lazyLoad\"\n                 :show-menu-by-longpress=\"!n.attrs.ignore\" :data-i=\"i\" :data-index=\"n.attrs.i\" data-source=\"img\" @load=\"loadImg\"\n                 @error=\"error\" />\n            </view>\n            <!--文本-->\n            <text v-else-if=\"n.type=='text'\" decode>{{n.text}}</text>\n            <!--#ifndef MP-BAIDU-->\n            <text v-else-if=\"n.name=='br'\">\\n</text>\n            <!--#endif-->\n            <!--视频-->\n            <view v-else-if=\"((n.lazyLoad&&!n.attrs.autoplay)||(n.name=='video'&&!loadVideo))&&ctrl[i]==undefined\" :id=\"n.attrs.id\"\n             :class=\"'_video '+(n.attrs.class||'')\" :style=\"n.attrs.style\" :data-i=\"i\" @tap.stop=\"_loadVideo\" />\n            <video v-else-if=\"n.name=='video'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :autoplay=\"n.attrs.autoplay||ctrl[i]==0\"\n             :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :muted=\"n.attrs.muted\" :poster=\"n.attrs.poster\" :src=\"n.attrs.source[ctrl[i]||0]\"\n             :unit-id=\"n.attrs['unit-id']\" :data-id=\"n.attrs.id\" :data-i=\"i\" data-source=\"video\" @error=\"error\" @play=\"play\" />\n            <!--音频-->\n            <audio v-else-if=\"n.name=='audio'\" :ref=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :author=\"n.attrs.author\"\n             :autoplay=\"n.attrs.autoplay\" :controls=\"n.attrs.controls\" :loop=\"n.attrs.loop\" :name=\"n.attrs.name\" :poster=\"n.attrs.poster\"\n             :src=\"n.attrs.source[ctrl[i]||0]\" :data-i=\"i\" :data-id=\"n.attrs.id\" data-source=\"audio\" @error.native=\"error\"\n             @play.native=\"play\" />\n            <!--链接-->\n            <view v-else-if=\"n.name=='a'\" :id=\"n.attrs.id\" :class=\"'_a '+(n.attrs.class||'')\" hover-class=\"_hover\" :style=\"n.attrs.style\"\n             :data-attrs=\"n.attrs\" @tap.stop=\"linkpress\">\n                <trees class=\"_span\" c=\"_span\" :nodes=\"n.children\" />\n            </view>\n            <!--广告-->\n            <!--<ad v-else-if=\"n.name=='ad'\" :class=\"n.attrs.class\" :style=\"n.attrs.style\" :unit-id=\"n.attrs['unit-id']\" :appid=\"n.attrs.appid\" :apid=\"n.attrs.apid\" :type=\"n.attrs.type\" :adpid=\"n.attrs.adpid\" data-source=\"ad\" @error=\"error\" />-->\n            <!--列表-->\n            <view v-else-if=\"n.name=='li'\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:flex;flex-direction:row'\">\n                <view v-if=\"n.type=='ol'\" class=\"_ol-bef\">{{n.num}}</view>\n                <view v-else class=\"_ul-bef\">\n                    <view v-if=\"n.floor%3==0\" class=\"_ul-p1\">█</view>\n                    <view v-else-if=\"n.floor%3==2\" class=\"_ul-p2\" />\n                    <view v-else class=\"_ul-p1\" style=\"border-radius:50%\">█</view>\n                </view>\n                <trees class=\"_li\" c=\"_li\" :nodes=\"n.children\" :lazyLoad=\"lazyLoad\" :loading=\"loading\" />\n            </view>\n            <!--表格-->\n            <view v-else-if=\"n.name=='table'&&n.c&&n.flag\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:grid'\">\n                <trees v-for=\"(cell,n) in n.children\" v-bind:key=\"n\" :class=\"cell.attrs.class\" :c=\"cell.attrs.class\" :style=\"cell.attrs.style\"\n                 :s=\"cell.attrs.style\" :nodes=\"cell.children\" />\n            </view>\n            <view v-else-if=\"n.name=='table'&&n.c\" :id=\"n.attrs.id\" :class=\"n.attrs.class\" :style=\"(n.attrs.style||'')+';display:table'\">\n                <view v-for=\"(tbody, o) in n.children\" v-bind:key=\"o\" :class=\"tbody.attrs.class\" :style=\"(tbody.attrs.style||'')+(tbody.name[0]=='t'?';display:table-'+(tbody.name=='tr'?'row':'row-group'):'')\">\n                    <view v-for=\"(tr, p) in tbody.children\" v-bind:key=\"p\" :class=\"tr.attrs.class\" :style=\"(tr.attrs.style||'')+(tr.name[0]=='t'?';display:table-'+(tr.name=='tr'?'row':'cell'):'')\">\n                        <trees v-if=\"tr.name=='td'\" :nodes=\"tr.children\" />\n                        <trees v-else v-for=\"(td, q) in tr.children\" v-bind:key=\"q\" :class=\"td.attrs.class\" :c=\"td.attrs.class\" :style=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\"\n                         :s=\"(td.attrs.style||'')+(td.name[0]=='t'?';display:table-'+(td.name=='tr'?'row':'cell'):'')\" :nodes=\"td.children\" />\n                    </view>\n                </view>\n            </view>\n            <!--#ifdef APP-PLUS-->\n            <iframe v-else-if=\"n.name=='iframe'\" :style=\"n.attrs.style\" :allowfullscreen=\"n.attrs.allowfullscreen\" :frameborder=\"n.attrs.frameborder\"\n             :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\n            <embed v-else-if=\"n.name=='embed'\" :style=\"n.attrs.style\" :width=\"n.attrs.width\" :height=\"n.attrs.height\" :src=\"n.attrs.src\" />\n            <!--#endif-->\n            <!--富文本-->\n            <!--#ifdef MP-WEIXIN || MP-QQ || APP-PLUS-->\n            <rich-text v-else-if=\"handler.use(n)\" :id=\"n.attrs.id\" :class=\"'_p __'+n.name\" :nodes=\"[n]\" />\n            <!--#endif-->\n            <!--#ifndef MP-WEIXIN || MP-QQ || APP-PLUS-->\n            <rich-text v-else-if=\"!n.c\" :id=\"n.attrs.id\" :nodes=\"[n]\" style=\"display:inline\" />\n            <!--#endif-->\n            <trees v-else :class=\"(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')\" :c=\"(n.attrs.id||'')+' _'+n.name+' '+(n.attrs.class||'')\"\n             :style=\"n.attrs.style\" :s=\"n.attrs.style\" :nodes=\"n.children\" :lazyLoad=\"lazyLoad\" :loading=\"loading\" />\n        </block>\n    </view>\n</template>\n<script module=\"handler\" lang=\"wxs\" src=\"./handler.wxs\"></script>\n<script>\n    global.Parser = {};\n    import trees from './trees'\n    const errorImg = require('../libs/config.js').errorImg;\n    export default {\n        components: {\n            trees\n        },\n        name: 'trees',\n        data() {\n            return {\n                ctrl: [],\n                placeholder: 'data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"300\" height=\"225\"/>',\n                errorImg,\n                loadVideo: typeof plus == 'undefined',\n                // #ifndef MP-ALIPAY\n                c: '',\n                s: ''\n                // #endif\n            }\n        },\n        props: {\n            nodes: Array,\n            lazyLoad: Boolean,\n            loading: String,\n            // #ifdef MP-ALIPAY\n            c: String,\n            s: String\n            // #endif\n        },\n        mounted() {\n            for (this.top = this.$parent; this.top.$options.name != 'parser'; this.top = this.top.$parent);\n            this.init();\n        },\n        // #ifdef APP-PLUS\n        beforeDestroy() {\n            this.observer && this.observer.disconnect();\n        },\n        // #endif\n        methods: {\n            init() {\n                for (var i = this.nodes.length, n; n = this.nodes[--i];) {\n                    if (n.name == 'img') {\n                        this.top.imgList.setItem(n.attrs.i, n.attrs['original-src'] || n.attrs.src);\n                        // #ifdef APP-PLUS\n                        if (this.lazyLoad && !this.observer) {\n                            this.observer = uni.createIntersectionObserver(this).relativeToViewport({\n                                top: 500,\n                                bottom: 500\n                            });\n                            setTimeout(() => {\n                                this.observer.observe('._img', res => {\n                                    if (res.intersectionRatio) {\n                                        for (var j = this.nodes.length; j--;)\n                                            if (this.nodes[j].name == 'img')\n                                                this.$set(this.ctrl, j, 1);\n                                        this.observer.disconnect();\n                                    }\n                                })\n                            }, 0)\n                        }\n                        // #endif\n                    } else if (n.name == 'video' || n.name == 'audio') {\n                        var ctx;\n                        if (n.name == 'video') {\n                            ctx = uni.createVideoContext(n.attrs.id\n                                // #ifndef MP-BAIDU\n                                , this\n                                // #endif\n                            );\n                        } else if (this.$refs[n.attrs.id])\n                            ctx = this.$refs[n.attrs.id][0];\n                        if (ctx) {\n                            ctx.id = n.attrs.id;\n                            this.top.videoContexts.push(ctx);\n                        }\n                    }\n                }\n                // #ifdef APP-PLUS\n                // APP 上避免 video 错位需要延时渲染\n                setTimeout(() => {\n                    this.loadVideo = true;\n                }, 1000)\n                // #endif\n            },\n            play(e) {\n                var contexts = this.top.videoContexts;\n                if (contexts.length > 1 && this.top.autopause)\n                    for (var i = contexts.length; i--;)\n                        if (contexts[i].id != e.currentTarget.dataset.id)\n                            contexts[i].pause();\n            },\n            imgtap(e) {\n                var attrs = e.currentTarget.dataset.attrs;\n                if (!attrs.ignore) {\n                    var preview = true,\n                        data = {\n                            id: e.target.id,\n                            src: attrs.src,\n                            ignore: () => preview = false\n                        };\n                    global.Parser.onImgtap && global.Parser.onImgtap(data);\n                    this.top.$emit('imgtap', data);\n                    if (preview) {\n                        var urls = this.top.imgList,\n                            current = urls[attrs.i] ? parseInt(attrs.i) : (urls = [attrs.src], 0);\n                        uni.previewImage({\n                            current,\n                            urls\n                        })\n                    }\n                }\n            },\n            loadImg(e) {\n                var i = e.currentTarget.dataset.i;\n                if (this.lazyLoad && !this.ctrl[i]) {\n                    // #ifdef QUICKAPP-WEBVIEW\n                    this.$set(this.ctrl, i, 0);\n                    this.$nextTick(function() {\n                        // #endif\n                        // #ifndef APP-PLUS\n                        this.$set(this.ctrl, i, 1);\n                        // #endif\n                        // #ifdef QUICKAPP-WEBVIEW\n                    })\n                    // #endif\n                } else if (this.loading && this.ctrl[i] != 2) {\n                    // #ifdef QUICKAPP-WEBVIEW\n                    this.$set(this.ctrl, i, 0);\n                    this.$nextTick(function() {\n                        // #endif\n                        this.$set(this.ctrl, i, 2);\n                        // #ifdef QUICKAPP-WEBVIEW\n                    })\n                    // #endif\n                }\n            },\n            linkpress(e) {\n                var jump = true,\n                    attrs = e.currentTarget.dataset.attrs;\n                attrs.ignore = () => jump = false;\n                global.Parser.onLinkpress && global.Parser.onLinkpress(attrs);\n                this.top.$emit('linkpress', attrs);\n                if (jump) {\n                    // #ifdef MP\n                    if (attrs['app-id']) {\n                        return uni.navigateToMiniProgram({\n                            appId: attrs['app-id'],\n                            path: attrs.path\n                        })\n                    }\n                    // #endif\n                    if (attrs.href) {\n                        if (attrs.href[0] == '#') {\n                            if (this.top.useAnchor)\n                                this.top.navigateTo({\n                                    id: attrs.href.substring(1)\n                                })\n                        } else if (attrs.href.indexOf('http') == 0 || attrs.href.indexOf('//') == 0) {\n                            // #ifdef APP-PLUS\n                            plus.runtime.openWeb(attrs.href);\n                            // #endif\n                            // #ifndef APP-PLUS\n                            uni.setClipboardData({\n                                data: attrs.href,\n                                success: () =>\n                                    uni.showToast({\n                                        title: '链接已复制'\n                                    })\n                            })\n                            // #endif\n                        } else\n                            uni.navigateTo({\n                                url: attrs.href,\n                                fail() {\n                                    uni.switchTab({\n                                        url: attrs.href,\n                                    })\n                                }\n                            })\n                    }\n                }\n            },\n            error(e) {\n                var target = e.currentTarget,\n                    source = target.dataset.source,\n                    i = target.dataset.i;\n                if (source == 'video' || source == 'audio') {\n                    // 加载其他 source\n                    var index = this.ctrl[i] ? this.ctrl[i].i + 1 : 1;\n                    if (index < this.nodes[i].attrs.source.length)\n                        this.$set(this.ctrl, i, index);\n                    if (e.detail.__args__)\n                        e.detail = e.detail.__args__[0];\n                } else if (errorImg && source == 'img') {\n                    this.top.imgList.setItem(target.dataset.index, errorImg);\n                    this.$set(this.ctrl, i, 3);\n                }\n                this.top && this.top.$emit('error', {\n                    source,\n                    target,\n                    errMsg: e.detail.errMsg\n                });\n            },\n            _loadVideo(e) {\n                this.$set(this.ctrl, e.target.dataset.i, 0);\n            }\n        }\n    }\n</script>\n\n<style>\n    /* 在这里引入自定义样式 */\n\n    /* 链接和图片效果 */\n    ._a {\n        display: inline;\n        padding: 1.5px 0 1.5px 0;\n        color: #366092;\n        word-break: break-all;\n    }\n\n    ._hover {\n        text-decoration: underline;\n        opacity: 0.7;\n    }\n\n    ._img {\n        /* display: inline-block; */\n    display: block;\n        max-width: 100%;\n        overflow: hidden;\n    }\n\n    /* #ifdef MP-WEIXIN */\n    :host {\n        display: inline;\n    }\n\n    /* #endif */\n\n    /* #ifndef MP-ALIPAY || APP-PLUS */\n    .interlayer {\n        display: inherit;\n        flex-direction: inherit;\n        flex-wrap: inherit;\n        align-content: inherit;\n        align-items: inherit;\n        justify-content: inherit;\n        width: 100%;\n        white-space: inherit;\n    }\n\n    /* #endif */\n\n    ._b,\n    ._strong {\n        font-weight: bold;\n    }\n\n    /* #ifndef MP-ALIPAY */\n    ._blockquote,\n    ._div,\n    ._p,\n    ._ol,\n    ._ul,\n    ._li {\n        display: block;\n    }\n\n    /* #endif */\n\n    ._code {\n        font-family: monospace;\n    }\n\n    ._del {\n        text-decoration: line-through;\n    }\n\n    ._em,\n    ._i {\n        font-style: italic;\n    }\n\n    ._h1 {\n        font-size: 2em;\n    }\n\n    ._h2 {\n        font-size: 1.5em;\n    }\n\n    ._h3 {\n        font-size: 1.17em;\n    }\n\n    ._h5 {\n        font-size: 0.83em;\n    }\n\n    ._h6 {\n        font-size: 0.67em;\n    }\n\n    ._h1,\n    ._h2,\n    ._h3,\n    ._h4,\n    ._h5,\n    ._h6 {\n        display: block;\n        font-weight: bold;\n    }\n\n    ._image {\n        display: block;\n        width: 100%;\n        height: 360px;\n        margin-top: -360px;\n        opacity: 0;\n    }\n\n    ._ins {\n        text-decoration: underline;\n    }\n\n    ._li {\n        flex: 1;\n        width: 0;\n    }\n\n    ._ol-bef {\n        width: 36px;\n        margin-right: 5px;\n        text-align: right;\n    }\n\n    ._ul-bef {\n        display: block;\n        margin: 0 12px 0 23px;\n        line-height: normal;\n    }\n\n    ._ol-bef,\n    ._ul-bef {\n        flex: none;\n        user-select: none;\n    }\n\n    ._ul-p1 {\n        display: inline-block;\n        width: 0.3em;\n        height: 0.3em;\n        overflow: hidden;\n        line-height: 0.3em;\n    }\n\n    ._ul-p2 {\n        display: inline-block;\n        width: 0.23em;\n        height: 0.23em;\n        border: 0.05em solid black;\n        border-radius: 50%;\n    }\n\n    ._q::before {\n        content: '\"';\n    }\n\n    ._q::after {\n        content: '\"';\n    }\n\n    ._sub {\n        font-size: smaller;\n        vertical-align: sub;\n    }\n\n    ._sup {\n        font-size: smaller;\n        vertical-align: super;\n    }\n\n    /* #ifdef MP-ALIPAY || APP-PLUS || QUICKAPP-WEBVIEW */\n    ._abbr,\n    ._b,\n    ._code,\n    ._del,\n    ._em,\n    ._i,\n    ._ins,\n    ._label,\n    ._q,\n    ._span,\n    ._strong,\n    ._sub,\n    ._sup {\n        display: inline;\n    }\n\n    /* #endif */\n\n    /* #ifdef MP-WEIXIN || MP-QQ */\n    .__bdo,\n    .__bdi,\n    .__ruby,\n    .__rt {\n        display: inline-block;\n    }\n\n    /* #endif */\n    ._video {\n        position: relative;\n        display: inline-block;\n        width: 300px;\n        height: 225px;\n        background-color: black;\n    }\n\n    ._video::after {\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        margin: -15px 0 0 -15px;\n        content: '';\n        border-color: transparent transparent transparent white;\n        border-style: solid;\n        border-width: 15px 0 15px 30px;\n    }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./trees.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424857\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cworkspace%5CfuintFoodSystem%5CfuintUniapp%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./handler.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cworkspace%5CfuintFoodSystem%5CfuintUniapp%5Ccomponents%5Cjyf-parser%5Clibs%5Ctrees.vue&module=handler&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       \n     }"], "sourceRoot": ""}