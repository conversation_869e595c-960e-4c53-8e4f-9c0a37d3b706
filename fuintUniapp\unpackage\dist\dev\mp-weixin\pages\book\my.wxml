<mescroll-body vue-id="584af8fd-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-0b1b2d29 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="tabs-wrapper data-v-0b1b2d29"><scroll-view class="scroll-view data-v-0b1b2d29" scroll-x="{{true}}"><view data-event-opts="{{[['tap',[['onSwitchTab',['']]]]]}}" class="{{['tab-item','data-v-0b1b2d29',(curId=='')?'active':'']}}" bindtap="__e"><view class="value data-v-0b1b2d29"><text class="data-v-0b1b2d29">全部</text></view></view><block wx:for="{{statusList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onSwitchTab',['$0'],[[['statusList','',index,'key']]]]]]]}}" class="{{['tab-item','data-v-0b1b2d29',(curId==item.key)?'active':'']}}" bindtap="__e"><view class="value data-v-0b1b2d29"><text class="data-v-0b1b2d29">{{item.name}}</text></view></view></block></scroll-view></view><view class="book-list data-v-0b1b2d29"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onView',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="book-item data-v-0b1b2d29" bindtap="__e"><block class="data-v-0b1b2d29"><view class="flex-box data-v-0b1b2d29"><view class="book-item-title data-v-0b1b2d29"><text class="data-v-0b1b2d29">{{item.$orig.bookName}}</text></view><view class="book-content data-v-0b1b2d29"><view class="contacts data-v-0b1b2d29">{{"姓名："+item.$orig.contact}}</view><view class="time data-v-0b1b2d29">{{"时间："+item.$orig.serviceDate+" "+item.$orig.serviceTime}}</view></view><view class="book-item-footer m-top10 data-v-0b1b2d29"><text class="book-views f-24 col-8 data-v-0b1b2d29">{{item.f0}}</text><block wx:if="{{item.$orig.status=='A'}}"><view data-event-opts="{{[['tap',[['onView',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="btn-cancel data-v-0b1b2d29" bindtap="__e">取消</view></block><view data-event-opts="{{[['tap',[['onView',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="btn-view data-v-0b1b2d29" bindtap="__e">详情</view></view></view></block></view></block></view></mescroll-body>