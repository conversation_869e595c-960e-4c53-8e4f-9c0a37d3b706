<view data-event-opts="{{[['tap',[['onLink',['$0'],['params.link']]]]]}}" class="diy-notice data-v-6a66a530" style="{{'padding-top:'+(itemStyle.paddingTop+'px')+';'+('padding-bottom:'+(itemStyle.paddingTop+'px')+';')}}" bindtap="__e"><u-notice-bar vue-id="3b22fc54-1" padding="10rpx 24rpx" volume-icon="{{params.showIcon}}" autoplay="{{params.scrollable}}" bg-color="{{itemStyle.background}}" color="{{itemStyle.textColor}}" list="{{[params.text]}}" class="data-v-6a66a530" bind:__l="__l"></u-notice-bar></view>