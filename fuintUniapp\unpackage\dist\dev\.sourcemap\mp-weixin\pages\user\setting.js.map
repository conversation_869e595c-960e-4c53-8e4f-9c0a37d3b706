{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/setting.vue?d6c1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/setting.vue?f3be", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/setting.vue?9a1f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/setting.vue?416b", "uni-app:///pages/user/setting.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/setting.vue?75dc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/user/setting.vue?23df"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "key<PERSON>ords", "data", "options", "isLoading", "userInfo", "avatar", "name", "sex", "birthday", "hasPassword", "openCardPara", "code", "nickname", "onLoad", "methods", "getUserInfo", "app", "UserApi", "then", "bindDateChange", "that", "getnickname", "genderChange", "getPhoneNumber", "getCode", "uni", "provider", "success", "e", "resolve", "fail", "onAuthSuccess", "changePassword", "console", "chooseImage", "count", "sizeType", "sourceType", "UploadApi", "catch", "save", "logout", "store"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4oB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkEhqB;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;MACAC;MACAC;MACAP;IACA;EACA;EAEA;AACA;AACA;EACAQ;IACA;IACA;IACA;EACA;EAEAC;IACA;AACA;AACA;IACAC;MACA;MACAC;MACAC,eACAC;QACAF;QACA;UACAA;QACA;QACAA;QACAA;QACAA;MACA;IACA;IACAG;MACA;MACAC;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACAC;UACAC;UACAC;YACAC;YACAX,uBACAC;cACAF;YACA;YACAa;UACA;UACAC;QACA;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAC;IACA;IACA;IACAC;MACA;MACA;MACAT;QACAU;QACAC;QAAA;QACAC;QAAA;QACAV;UAAA;UACA;UACA;YACA;cACAW,2BACApB;gBACA;kBACAF;kBACAA;gBACA;gBACAa;cACA,GACAU;gBAAA;cAAA;YACA;cACAV;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAW;MACA;MACAxB;MACAC;QAAA;QAAA;QAAA;QAAA;MAAA,GACAC;QACAF;QACAA;QACAA;MACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACAyB;MACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClNA;AAAA;AAAA;AAAA;AAA+uC,CAAgB,qqCAAG,EAAC,C;;;;;;;;;;;ACAnwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/setting.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/setting.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./setting.vue?vue&type=template&id=52e26b05&scoped=true&\"\nvar renderjs\nimport script from \"./setting.vue?vue&type=script&lang=js&\"\nexport * from \"./setting.vue?vue&type=script&lang=js&\"\nimport style0 from \"./setting.vue?vue&type=style&index=0&id=52e26b05&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"52e26b05\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/setting.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=template&id=52e26b05&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"info-list\">\n      <view class=\"info-item\">\n          <view class=\"contacts avatar-warp\">\n            <text class=\"name\">头像</text>\n            <image class=\"avatar\" @click=\"chooseImage()\" :src=\"avatar\"></image>\n          </view>\n      </view>\n      <view class=\"info-item\">\n        <view class=\"contacts\">\n          <text class=\"name\">称呼</text>\n          <input class=\"weui-input value\" type=\"nickname\" @blur=\"getnickname\" v-model=\"nickname\" placeholder=\"请输入称呼\"/>\n        </view>\n      </view>\n      <view class=\"info-item\">\n        <view class=\"contacts\">\n          <text class=\"name\">手机</text>\n          <button class=\"button btn-normal value\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">\n              <text v-if=\"userInfo.mobile\">{{ userInfo.mobile }}</text>\n              <text style=\"color: #f9211c;margin-left: 2px;\">更换</text>\n          </button>\n        </view>\n      </view>\r\n    <!--  <view class=\"info-item\">\r\n        <view class=\"contacts\">\r\n          <text class=\"name\">密码</text>\r\n          <button class=\"button btn-normal value\" @click=\"changePassword\">\r\n              <text class=\"password\">********</text>\r\n              <text style=\"color: #f9211c;margin-left: 2px;\">修改</text>\r\n          </button>\r\n        </view>\r\n      </view> -->\n      <view class=\"info-item\">\n        <view class=\"contacts\">\n          <text class=\"name\">性别</text>\n          <view class=\"value\">\n             <radio-group @change=\"genderChange\">\n                <label class=\"radio\"><radio value=\"1\" color=\"#3f51b5\" :checked=\"userInfo.sex == '1' ? true : false\"/>男</label>\n                <label class=\"radio second\"><radio value=\"0\" color=\"#3f51b5\" :checked=\"userInfo.sex == '0' ? true: false\"/>女</label>\n             </radio-group>\n          </view>\n        </view>\n      </view>\n      <view class=\"info-item\">\n        <view class=\"contacts\">\n          <text class=\"name\">生日</text>\r\n          <picker class=\"value\" mode=\"date\" :value=\"userInfo.birthday\" start=\"1900-01-01\" @change=\"bindDateChange\">\r\n              <view class=\"picker\">{{ userInfo.birthday ? userInfo.birthday : '选择生日' }}</view>\r\n           </picker>\n        </view>\n      </view>\n    </view>\n    <!-- 底部操作按钮 -->\n    <view class=\"footer-fixed\" v-if=\"userInfo.id\">\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" @click=\"save()\">保存信息</view>\n      </view>\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-out\" @click=\"logout()\">退出登录</view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as UserApi from '@/api/user'\n  import * as UploadApi from '@/api/upload'\n  import store from '@/store'\r\n  import keyWords from \"@/components/bian-keywords/index.vue\"\n  export default {\r\n    components: {\r\n      keyWords\r\n    },\n    data() {\n      return {\n        //当前页面参数\n        options: {},\n        // 正在加载\n        isLoading: true,\n        userInfo: { avatar: '', name: '', sex: 0, birthday: '', hasPassword: '' },\r\n        openCardPara: null,\n        code: \"\",\n        nickname: \"\",\n        avatar: \"\"\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 当前页面参数\n      this.options = options;\n      this.getUserInfo();\n    },\n\n    methods: {\r\n      /**\n       * 用户信息\n       * */\n      getUserInfo() {\n        const app = this\n        app.isLoading = true\n        UserApi.info()\n          .then(result => {\n            app.userInfo = result.data.userInfo;\r\n            if (result.data.openCardPara) {\r\n                app.openCardPara = result.data.openCardPara; \r\n            }\n            app.nickname = app.userInfo.name;\n            app.avatar = app.userInfo.avatar;\n            app.isLoading = false;\n          })\n      },\r\n      bindDateChange (e) {\r\n        let that = this;\r\n        that.userInfo.birthday = e.detail.value;\r\n      },\n      getnickname(e) {\n          this.nickname = e.detail.value;  \n      }, \n      genderChange(e) {\n          this.userInfo.sex = e.detail.value\n      },\n      /**\n       * 获取会员手机\n       * */\n      getPhoneNumber(e) {\n          if (e.detail.errMsg == \"getPhoneNumber:ok\") {\n              this.onAuthSuccess(e)\n          }\n      },\n      getCode(e) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          uni.login({\n            provider: 'weixin',\n            success: res => {\n              e.detail.code = res.code\n              UserApi.save(e.detail)\n                 .then(result => {\n                 app.userInfo.mobile = result.data.mobile\n              })\n              resolve(res.code)\n            },\n            fail: reject\n          })\n        })\n      },\n      onAuthSuccess(e) {\n         this.getCode(e)\n      },\r\n      // 修改密码\r\n      changePassword() {\r\n         this.$navTo('pages/user/password?hasPassword=' + this.userInfo.hasPassword);\r\n         console.log(this.userInfo.hasPassword);\r\n      },\n      // 选择图片\n      chooseImage() {\n        const app = this\n        // 选择图片\n        uni.chooseImage({\n          count: 1,\n          sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有\n          sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有\n          success({ tempFiles }) {\n            const imageList = tempFiles;\n            return new Promise((resolve, reject) => {\n              if (imageList.length > 0) {\n                  UploadApi.image(imageList)\n                      .then(files => {\n                          if (files && files.length > 0) {\n                              app.userInfo.avatar = files[0].fileName;\n                              app.avatar = files[0].domain + app.userInfo.avatar;\n                          }\n                          resolve(files)\n                      })\n                      .catch(err => reject(err))\n              } else {\n                resolve()\n              }\n            })\n          }\n        });\n      },\n      /**\n       * 保存个人信息\n       */\n      save() {\n          const app = this\n          app.isLoading = true\n          UserApi.save({\"name\": app.nickname, \"avatar\": app.avatar, \"sex\": app.userInfo.sex, \"birthday\": app.userInfo.birthday})\n            .then(result => {\n              app.userInfo = result.data\n              app.isLoading = false\n              app.$success('保存成功！')\n         }).catch(err => {\n            app.isLoading = false;\n         })\n      },\r\n      \n      /**\n       * 退出登录\n       */\n      logout() {\n         store.dispatch('Logout');\n         this.$navTo('pages/user/index');\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .info-list {\n    padding-bottom: 100rpx;\n    margin-top: 25rpx;\n  }\n\n  // 项目内容\n  .info-item {\n    margin: 20rpx auto 20rpx auto;\n    padding: 30rpx 40rpx;\n    width: 94%;\n    box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);\n    border-radius: 16rpx;\n    background: #fff;\n    .avatar-warp {\n        line-height: 120rpx;\n    }\n  }\n\n  .contacts {\n    font-size: 30rpx;\n    .name {\n      margin-left:0px;\n    }\n    .value {\n        float:right;\n        color:#999999;\n        text-align: right;\n        .second {\n            margin-left: .6rem;\n        }\n    }\r\n    .password {\r\n        text-align: right;\r\n        float: left;\r\n        padding-right: 5rpx;\r\n    }\n    .avatar {\n        width: 120rpx;\n        height: 120rpx;\n        border-radius: 120rpx;\n        border: solid 1px #cccccc;\n        float: right;\n    }\n  }\n  .item-option {\n    display: flex;\n    justify-content: space-between;\n    height: 48rpx;\n  }\n\n  // 底部操作栏\n  .footer-fixed {\n    height: 100rpx;\n    z-index: 11;\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      padding: 0 20rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n    }\n    .btn-item-out {\n      margin-top: 20rpx;\n      background: #FFFFFF;\n      border: 1px solid $fuint-theme;\n      color: #666666;\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=style&index=0&id=52e26b05&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./setting.vue?vue&type=style&index=0&id=52e26b05&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420709\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}