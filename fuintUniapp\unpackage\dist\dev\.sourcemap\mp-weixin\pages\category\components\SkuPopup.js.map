{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/SkuPopup.vue?db19", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/SkuPopup.vue?8d2e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/SkuPopup.vue?46c0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/SkuPopup.vue?c1f6", "uni-app:///pages/category/components/SkuPopup.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/SkuPopup.vue?f85b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/SkuPopup.vue?e082"], "names": ["components", "GoodsSkuPopup", "model", "prop", "event", "props", "value", "Type", "default", "skuMode", "type", "goods", "gradeInfo", "hafanInfo", "computed", "hafanLevel", "isMemberPrice", "data", "goodsInfo", "_id", "name", "goods_thumb", "sku_list", "spec_list", "created", "methods", "onChangeValue", "getSkuList", "logo", "skuList", "sku<PERSON><PERSON>", "sku_id", "goods_id", "image", "price", "gradePrice", "stock", "spec_value_ids", "sku_name_arr", "getSkuNameArr", "specValueIds", "skuNameArr", "getSpecValueName", "specList", "getSpecList", "list", "group", "children", "specData", "openSkuPopup", "closeSkuPopup", "addCart", "console", "buy_num", "CartApi", "then", "app", "buyNow", "mode", "goodsId", "skuId", "buyNum", "onConfirm"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACOhrB;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;eAIA;EACAA;IACAC;EACA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAC;MACAF;IACA;IACA;IACAG;MACAD;MACAF;IACA;IACAI;MACAF;MACAF;IACA;IACAK;MACAH;MACAF;IACA;EACA;EAEAM;IACA;IACAC;MAAA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAC;IACA;MACA;QACAL;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EAEAE;IAEA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QAAAP;QAAA;QAAAQ;QAAA;QAAAC;MACA;MACA;QACAA;UACAC;YACAX;YACAY;YACAC;YACAZ;YACAa;YACAC;YACAC;YACAC;YACAC;YACAC;UACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;QACAC;UACA;UACAC;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;QAAAC;MAEA;QACA;UACA;QACA;QACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QAAA;QAAAD;MACA;QAAAvB;QAAAyB;UAAAzB;QAAA;MAAA;MACA;MACA;QACAuB;UACA;UACAG;YACAC;cAAA3B;YAAA;UACA;UACA4B;YACA5B;YACAyB;UACA;QACA;MACA;MACA;IACA;IAEA;IACAI;MACA;IAAA,CACA;IAEAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;MAEAC;MAEA;QAAArB;QAAAsB;MACAC,6CACAC;QACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;ACrNA;AAAA;AAAA;AAAA;AAA2wC,CAAgB,sqCAAG,EAAC,C;;;;;;;;;;;ACA/xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/category/components/SkuPopup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./SkuPopup.vue?vue&type=template&id=96f65520&scoped=true&\"\nvar renderjs\nimport script from \"./SkuPopup.vue?vue&type=script&lang=js&\"\nexport * from \"./SkuPopup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./SkuPopup.vue?vue&type=style&index=0&id=96f65520&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"96f65520\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/components/SkuPopup.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SkuPopup.vue?vue&type=template&id=96f65520&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SkuPopup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SkuPopup.vue?vue&type=script&lang=js&\"", "<template>\r\n  <goods-sku-popup :value=\"value\" @input=\"onChangeValue\" border-radius=\"20\" :goods=\"goodsInfo\"\r\n    :mode=\"skuMode\" :defaultPrice=\"(isMemberPrice && goods.gradePrice > 0) ? goods.gradePrice : goods.price\" :gradeInfo=\"gradeInfo\" :hafanInfo=\"hafanInfo\" :defaultStock=\"goods.stock\" :maskCloseAble=\"true\"\r\n    @open=\"openSkuPopup\" @close=\"closeSkuPopup\" @add-cart=\"addCart\" @buy-now=\"buyNow\" @confirm=\"onConfirm\" />\r\n</template>\r\n\r\n<script>\r\n  import { setCartTotalNum } from '@/utils/app'\r\n  import * as CartApi from '@/api/cart'\r\n  import * as GoodsApi from '@/api/goods'\r\n  import GoodsSkuPopup from '@/components/goods-sku-popup'\r\n \r\n\r\n  export default {\r\n    components: {\r\n      GoodsSkuPopup\r\n    },\r\n    model: {\r\n      prop: 'value',\r\n      event: 'input'\r\n    },\r\n    props: {\r\n      // true 组件显示 false 组件隐藏\r\n      value: {\r\n        Type: Boolean,\r\n        default: false\r\n      },\r\n      // 模式 1:都显示 2:只显示购物车 3:只显示立即购买\r\n      skuMode: {\r\n        type: Number,\r\n        default: 1\r\n      },\r\n      // 商品详情信息\r\n      goods: {\r\n        type: Object,\r\n        default: {}\r\n      },\r\n\t  gradeInfo: {\r\n\t    type: Object,\r\n\t    default: {}\r\n\t  },\r\n\t  hafanInfo: {\r\n\t    type: Object,\r\n\t    default: {}\r\n\t  }\r\n    },\r\n\r\n    computed: {\r\n      // 计算哈帆会员等级\r\n      hafanLevel() {\r\n        return this.hafanInfo?.premium?.level || 'free';\r\n      },\r\n      // 计算是否为会员价格\r\n      isMemberPrice() {\r\n        return (this.gradeInfo && this.gradeInfo.grade > 1) || (this.hafanLevel !== 'free');\r\n      }\r\n    },\r\n\r\n    data() {\r\n      return {\r\n        goodsInfo: {\r\n          _id: '',\r\n          name: '',\r\n          goods_thumb: '',\r\n          sku_list: [],\r\n          spec_list: []\r\n        }\r\n      }\r\n    },\r\n\r\n    created() {\r\n      if (this.goods) {\r\n        this.goodsInfo = {\r\n          _id: this.goods.goodsId || '',\r\n          name: this.goods.name || '',\r\n          goods_thumb: this.goods.logo || '',\r\n          sku_list: this.getSkuList(),\r\n          spec_list: this.getSpecList()\r\n        }\r\n      }\r\n    },\r\n\r\n    methods: {\r\n\r\n      // 监听组件显示隐藏\r\n      onChangeValue(val) {\r\n        this.$emit('input', val)\r\n      },\r\n      // 整理商品SKU列表\r\n      getSkuList() {\r\n        const app = this\r\n        const goods = app.goods || {}\r\n        const { name = '', logo = '', skuList = [] } = goods\r\n        const skuData = []\r\n        if (skuList && skuList.length) {\r\n          skuList.forEach(item => {\r\n              skuData.push({\r\n                _id: item.id,\r\n                sku_id: item.skuId,\r\n                goods_id: item.goodsId,\r\n                name: name,\r\n                image: item.logo ? item.logo : logo,\r\n                price: item.price,\r\n                gradePrice: item.gradePrice,\r\n                stock: item.stock,\r\n                spec_value_ids: item.specIds,\r\n                sku_name_arr: app.getSkuNameArr(item.specIds)\r\n              })\r\n            })\r\n        }\r\n        return skuData\r\n      },\r\n\r\n      // 获取sku记录的规格值列表\r\n      getSkuNameArr(specValueIds) {\r\n        const app = this\r\n        const defaultData = ['默认']\r\n        const skuNameArr = []\r\n        if (specValueIds) {\r\n          specValueIds.forEach((valueId, specId) => {\r\n            const specValueName = app.getSpecValueName(valueId, specId)\r\n            skuNameArr.push(specValueName)\r\n          })\r\n        }\r\n        return skuNameArr.length ? skuNameArr : defaultData\r\n      },\r\n\r\n      // 获取指定的规格值名称\r\n      getSpecValueName(valueId, specId) {\r\n        const app = this\r\n        const goods = app.goods || {}\r\n        const { specList = [] } = goods\r\n        \r\n        if (specList[specId] && specList[specId].valueList) {\r\n          const res = specList[specId].valueList.find(specValue => {\r\n            return specValue.specValueId == valueId\r\n          })\r\n          return res ? res.specValue : ''\r\n        }\r\n        return ''\r\n      },\r\n\r\n      // 整理规格数据\r\n      getSpecList() {\r\n        const { specList = [] } = this.goods || {}\r\n        const defaultData = [{ name: '默认', list: [{ name: '默认' }] }]\r\n        const specData = []\r\n        if (specList) {\r\n            specList.forEach(group => {\r\n              const children = []\r\n              group.valueList.forEach(specValue => {\r\n                children.push({ name: specValue.specValue })\r\n              })\r\n              specData.push({\r\n                name: group.name,\r\n                list: children\r\n              })\r\n            })\r\n        }\r\n        return specData.length ? specData : defaultData\r\n      },\r\n\r\n      // sku组件 开始-----------------------------------------------------------\r\n      openSkuPopup() {\r\n        // console.log(\"监听 - 打开sku组件\")\r\n      },\r\n\r\n      closeSkuPopup() {\r\n        // console.log(\"监听 - 关闭sku组件\")\r\n      },\r\n\r\n      // 加入购物车按钮\r\n      addCart(selectShop) {\r\n        const app = this\r\n        \r\n        console.log(selectShop)\r\n        \r\n        const { goods_id, sku_id, buy_num } = selectShop\r\n        CartApi.save(goods_id, '+', sku_id, buy_num)\r\n          .then(result => {\r\n            // 隐藏当前弹窗\r\n            app.onChangeValue(false);\r\n            // 购物车商品总数量\r\n            const cartTotal = result.data ? result.data.cartTotal : 0;\r\n            // 缓存购物车数量\r\n            setCartTotalNum(cartTotal);\r\n            // 传递给父级\r\n            app.$emit('addCart', cartTotal);\r\n          })\r\n      },\r\n\r\n      // 立即购买\r\n      buyNow(selectShop) {\r\n        // 跳转到订单结算页\r\n        this.$navTo('pages/settlement/goods', {\r\n          mode: 'buyNow',\r\n          goodsId: selectShop.goods_id,\r\n          skuId: selectShop.sku_id,\r\n          buyNum: selectShop.buy_num\r\n        })\r\n        // 隐藏当前弹窗\r\n        this.onChangeValue(false)\r\n      },\r\n\r\n      // 处理规格确认事件\r\n      onConfirm(selectShop) {\r\n        // 隐藏当前弹窗\r\n        this.onChangeValue(false);\r\n        // 传递给父级\r\n        this.$emit('confirm', selectShop);\r\n      }\r\n\r\n    }\r\n  }\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  .goods-sku-popup {\r\n  z-index: 9999999999999 !important;\r\n  \r\n  .mask {\r\n    z-index: 9999999999998 !important;\r\n  }\r\n  \r\n  .layer {\r\n    z-index: 9999999999999 !important;\r\n  }\r\n}\r\n\r\n</style>\r\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SkuPopup.vue?vue&type=style&index=0&id=96f65520&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./SkuPopup.vue?vue&type=style&index=0&id=96f65520&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424022\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}