@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.header.data-v-b227d520 {
  display: block;
  align-items: center;
  background-color: #fff;
  height: 103rpx;
}
.header .search.data-v-b227d520 {
  flex: 1;
}
.header .show-view.data-v-b227d520 {
  width: 60rpx;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 36rpx;
  color: #505050;
}
.empty-ipt.data-v-b227d520 {
  width: 220rpx;
  margin: 20rpx auto;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  color: #fff;
  border-radius: 5rpx;
  background: linear-gradient(to right, #3f51b5, #3f51b5);
}
.store-sort.data-v-b227d520 {
  position: -webkit-sticky;
  position: sticky;
  top: 0px;
  display: flex;
  padding: 20rpx 0;
  font-size: 28rpx;
  background: #fff;
  color: #000;
  z-index: 99;
}
.store-sort .sort-item.data-v-b227d520 {
  flex-basis: 33.3333%;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50rpx;
}
.store-sort .sort-item.active.data-v-b227d520 {
  color: #e49a3d;
}
.store-sort .sort-item-price .price-arrow.data-v-b227d520 {
  margin-left: 20rpx;
  font-size: 24rpx;
  color: #000;
}
.store-sort .sort-item-price .price-arrow .icon.active.data-v-b227d520 {
  color: #e49a3d;
}
.store-sort .sort-item-price .price-arrow .icon.up.data-v-b227d520 {
  margin-bottom: -16rpx;
}
.store-sort .sort-item-price .price-arrow .icon.down.data-v-b227d520 {
  margin-top: -16rpx;
}
.goods-list.data-v-b227d520 {
  padding: 4rpx;
  box-sizing: border-box;
}
.goods-list.column-1 .goods-item.data-v-b227d520 {
  width: 100%;
  height: 280rpx;
  margin-bottom: 12rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background: #fff;
  line-height: 1.6;
}
.goods-list.column-1 .goods-item.data-v-b227d520:last-child {
  margin-bottom: 0;
}
.goods-list.column-1 .goods-item_left.data-v-b227d520 {
  display: flex;
  width: 300rpx;
  background: #fff;
  align-items: center;
}
.goods-list.column-1 .goods-item_left .image.data-v-b227d520 {
  display: block;
  width: 220rpx;
  height: 200rpx;
  border-radius: 10rpx;
}
.goods-list.column-1 .goods-item_right.data-v-b227d520 {
  position: relative;
  flex: 1;
}
.goods-list.column-1 .goods-item_right .goods-name.data-v-b227d520 {
  margin-top: 10rpx;
  height: 64rpx;
  line-height: 1.3;
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
}
.goods-list.column-1 .goods-item_desc.data-v-b227d520 {
  margin-top: 8rpx;
}
.goods-list.column-1 .goods-item_desc .coupon-attr .attr-l.data-v-b227d520 {
  float: left;
  width: 60%;
}
.goods-list.column-1 .goods-item_desc .coupon-attr .attr-r.data-v-b227d520 {
  margin-top: 20rpx;
  float: left;
}
.goods-list.column-1 .desc-selling_point.data-v-b227d520 {
  width: 400rpx;
  font-size: 24rpx;
  color: #e49a3d;
}
.goods-list.column-1 .receive.data-v-b227d520 {
  color: #FFFFFF;
  float: right;
  margin-right: 20rpx;
  border: solid 1rpx #3f51b5;
  background: #3f51b5;
  padding: 8rpx 20rpx 8rpx 20rpx;
  border-radius: 5rpx;
  display: block;
}
.goods-list.column-1 .receive.state.data-v-b227d520 {
  border: none;
  color: #cccccc;
  background: #F5F5F5;
}
.goods-list.column-1 .desc-goods_sales.data-v-b227d520 {
  color: #999;
  font-size: 24rpx;
}
.goods-list.column-1 .desc_footer.data-v-b227d520 {
  font-size: 24rpx;
}
.goods-list.column-1 .desc_footer .price_x.data-v-b227d520 {
  margin-right: 16rpx;
  color: #f03c3c;
  font-size: 30rpx;
}
.goods-list.column-1 .desc_footer .price_y.data-v-b227d520 {
  text-decoration: line-through;
}
.goods-list.column-2 .goods-item.data-v-b227d520 {
  width: 50%;
}
.goods-item.data-v-b227d520 {
  float: left;
  box-sizing: border-box;
  padding: 6rpx;
}
.goods-item .goods-image.data-v-b227d520 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  background: #fff;
}
.goods-item .goods-image.data-v-b227d520:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.goods-item .goods-image .image.data-v-b227d520 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover;
}
.goods-item .detail.data-v-b227d520 {
  padding: 8rpx;
  background: #fff;
}
.goods-item .detail .goods-name.data-v-b227d520 {
  height: 64rpx;
  line-height: 32rpx;
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
}
.goods-item .detail .detail-price .goods-price.data-v-b227d520 {
  margin-right: 8rpx;
}
.goods-item .detail .detail-price .line-price.data-v-b227d520 {
  text-decoration: line-through;
}
