package com.fuint.module.schedule;

import com.fuint.common.dto.UserOrderDto;
import com.fuint.common.enums.OrderStatusEnum;
import com.fuint.common.enums.PayStatusEnum;
import com.fuint.common.enums.YesOrNoEnum;
import com.fuint.common.service.OrderService;
import com.fuint.common.service.PrinterService;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.repository.model.MtOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.core.env.Environment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预约取餐打印定时任务
 * 在预约时间前10分钟自动触发打印
 *
 */
@EnableScheduling
@Component("reservationPrintJob")
public class ReservationPrintJob {

    private Logger logger = LoggerFactory.getLogger(ReservationPrintJob.class);

    /**
     * 订单服务接口
     */
    @Autowired
    private OrderService orderService;

    /**
     * 打印服务接口
     */
    @Autowired
    private PrinterService printerService;

    @Autowired
    private Environment environment;

    /**
     * 一次最多处理订单数量
     **/
    private int MAX_PROCESS_NUM = 50;

    /**
     * 提前打印时间（分钟）
     * */
    private int ADVANCE_PRINT_MINUTES = 10;

    @Scheduled(cron = "${reservationPrint.job.time}")
    @Transactional(rollbackFor = Exception.class)
    public void processReservationOrders() throws BusinessCheckException {
        String theSwitch = environment.getProperty("reservationPrint.job.switch");
        if (theSwitch != null && theSwitch.equals("1")) {
            logger.info("ReservationPrintJob开始执行...");
            
            // 查询需要处理的预约订单
            Map<String, Object> param = new HashMap<>();
            param.put("STATUS", OrderStatusEnum.PAID.getKey()); // 已支付
            param.put("PAY_STATUS", PayStatusEnum.SUCCESS.getKey()); // 支付成功
            param.put("IS_RESERVATION", YesOrNoEnum.YES.getKey()); // 预约订单
            param.put("RESERVATION_STATUS", "A"); // 待处理状态
            
            List<MtOrder> reservationOrders = orderService.getOrderListByParams(param);
            
            if (reservationOrders.size() > 0) {
                int processedNum = 0;
                Date currentTime = new Date();
                
                for (MtOrder order : reservationOrders) {
                    if (processedNum >= MAX_PROCESS_NUM) {
                        break;
                    }
                    
                    // 检查是否到了打印时间（预约时间前10分钟）
                    if (order.getReservationTime() != null) {
                        Date printTime = new Date(order.getReservationTime().getTime() - (ADVANCE_PRINT_MINUTES * 60 * 1000));
                        
                        if (currentTime.getTime() >= printTime.getTime()) {
                            try {
                                // 获取订单详情并打印
                                UserOrderDto orderInfo = orderService.getOrderByOrderSn(order.getOrderSn());
                                if (orderInfo != null) {
                                    printerService.printOrder(orderInfo, true, false, true, null);
                                    
                                    // 更新预约状态为已处理
                                    order.setReservationStatus("B");
                                    orderService.updateOrder(order);
                                    
                                    processedNum++;
                                    logger.info("预约订单 {} 打印成功，预约时间：{}", order.getOrderSn(), order.getReservationTime());
                                }
                            } catch (Exception e) {
                                logger.error("预约订单 {} 打印失败：{}", order.getOrderSn(), e.getMessage());
                            }
                        }
                    }
                }
                
                if (processedNum > 0) {
                    logger.info("ReservationPrintJob执行完成，处理了 {} 个预约订单", processedNum);
                }
            }
        }
    }
}
