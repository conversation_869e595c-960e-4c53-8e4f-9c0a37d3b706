<view hidden="{{!(!isLoading)}}" class="container data-v-dcaad8e4"><block wx:if="{{!isLoading}}"><view class="coupon-info m-top20 data-v-dcaad8e4"><view class="info-item info-item__name dis-flex flex-y-center data-v-dcaad8e4"><view class="coupon-name flex-box data-v-dcaad8e4"><text class="twolist-hidden data-v-dcaad8e4">{{couponInfo.name}}</text></view><view class="coupon-share__line data-v-dcaad8e4"></view><view class="coupon-share data-v-dcaad8e4"><button class="share-btn dis-flex flex-dir-column data-v-dcaad8e4" open-type="share"><text class="share__icon iconfont icon-fenxiang-post data-v-dcaad8e4"></text><text class="f-24 data-v-dcaad8e4">分享</text></button></view></view><view class="store-rule data-v-dcaad8e4"><view class="title data-v-dcaad8e4">预存规则：</view><block wx:for="{{storeRule}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="item data-v-dcaad8e4"><text class="data-v-dcaad8e4">{{"预存￥"+item.store+" 到账 ￥"+item.upStore}}</text></view></block></view><view class="info-item data-v-dcaad8e4"><text class="data-v-dcaad8e4">已有<text class="number data-v-dcaad8e4">{{couponInfo.gotNum}}</text>人预存，剩余<text class="number data-v-dcaad8e4">{{couponInfo.limitNum}}</text>名额</text></view><view class="info-item data-v-dcaad8e4"><text class="data-v-dcaad8e4">{{"有效期："+couponInfo.effectiveDate}}</text></view></view></block><view data-event-opts="{{[['tap',[['onShowPopup']]]]}}" class="coupon-choice m-top20 b-f data-v-dcaad8e4" bindtap="__e"><view class="spec-list data-v-dcaad8e4"><view class="flex-box data-v-dcaad8e4"><text class="col-8 data-v-dcaad8e4">选择：</text><text class="spec-name data-v-dcaad8e4">预存金额、数量</text></view><view class="f-26 col-9 t-r data-v-dcaad8e4"><text class="iconfont icon-xiangyoujiantou data-v-dcaad8e4"></text></view></view></view><block wx:if="{{!isLoading}}"><popup bind:input="__e" vue-id="b8af4190-1" couponInfo="{{couponInfo}}" storeRule="{{storeRule}}" value="{{showPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showPopup','$event',[]]]]]]}}" class="data-v-dcaad8e4" bind:__l="__l"></popup></block><block wx:if="{{!isLoading}}"><view class="coupon-content m-top20 data-v-dcaad8e4"><view class="item-title b-f data-v-dcaad8e4"><text class="data-v-dcaad8e4">卡券描述</text></view><block wx:if="{{couponInfo.description!=''}}"><block class="data-v-dcaad8e4"><view class="coupon-content-detail b-f data-v-dcaad8e4"><jyf-parser vue-id="b8af4190-2" html="{{couponInfo.description}}" class="data-v-dcaad8e4" bind:__l="__l"></jyf-parser></view></block></block><block wx:else><empty vue-id="b8af4190-3" tips="亲，暂无卡券描述" class="data-v-dcaad8e4" bind:__l="__l"></empty></block></view></block><view class="footer-fixed data-v-dcaad8e4"><view class="footer-container data-v-dcaad8e4"><view class="foo-item-fast data-v-dcaad8e4"><button class="btn-normal data-v-dcaad8e4" open-type="contact"><view class="fast-item data-v-dcaad8e4"><view class="fast-icon data-v-dcaad8e4"><text class="iconfont icon-kefu1 data-v-dcaad8e4"></text></view><view class="fast-text data-v-dcaad8e4"><text class="data-v-dcaad8e4">客服</text></view></view></button></view><view class="foo-item-btn data-v-dcaad8e4"><view class="btn-wrapper data-v-dcaad8e4"><view data-event-opts="{{[['tap',[['onShowPopup']]]]}}" class="btn-item btn-item-main data-v-dcaad8e4" bindtap="__e"><text class="data-v-dcaad8e4">立即预存</text></view></view></view></view></view></view>