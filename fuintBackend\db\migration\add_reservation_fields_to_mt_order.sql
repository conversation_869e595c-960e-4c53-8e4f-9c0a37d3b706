-- 为mt_order表添加预约取餐相关字段
-- 执行时间：2025-01-06
-- 说明：添加预约取餐功能所需的数据库字段

-- 添加预约取餐时间字段
ALTER TABLE `mt_order` ADD COLUMN `RESERVATION_TIME` datetime DEFAULT NULL COMMENT '预约取餐时间';

-- 添加是否预约取餐订单字段
ALTER TABLE `mt_order` ADD COLUMN `IS_RESERVATION` char(1) DEFAULT 'N' COMMENT '是否预约取餐订单';

-- 添加预约状态字段
ALTER TABLE `mt_order` ADD COLUMN `RESERVATION_STATUS` char(1) DEFAULT 'A' COMMENT '预约状态：A-待处理，B-已处理';

-- 创建索引以提高查询性能
CREATE INDEX `idx_reservation_time` ON `mt_order` (`RESERVATION_TIME`);
CREATE INDEX `idx_is_reservation` ON `mt_order` (`IS_RESERVATION`);
CREATE INDEX `idx_reservation_status` ON `mt_order` (`RESERVATION_STATUS`);

-- 验证字段是否添加成功
-- SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = 'fuint-food' 
-- AND TABLE_NAME = 'mt_order' 
-- AND COLUMN_NAME IN ('RESERVATION_TIME', 'IS_RESERVATION', 'RESERVATION_STATUS');
