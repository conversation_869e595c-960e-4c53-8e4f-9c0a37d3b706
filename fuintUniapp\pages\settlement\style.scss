// 页面整体样式 - 基于模板设计
.container {
  background-color: #f2f2f2;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

// 通用卡片样式
.card-container {
  background-color: #ffffff;
  border-radius: 30rpx;
  box-shadow: 4rpx 4rpx 8rpx rgba(0, 0, 0, 0.25);
  margin: 20rpx 28rpx;
  padding: 40rpx;
}

// 弹出层-支付方式
.pay-type-popup {
  padding: 25rpx 25rpx 70rpx 25rpx;
 .title {
   font-size: 30rpx;
   margin-bottom: 50rpx;
   font-weight: bold;
   text-align: center;
 }
 
 // Flexbox 工具类
 .flex-row {
   display: flex;
   flex-direction: row;
 }
 
 .flex-col {
   display: flex;
   flex-direction: column;
 }
 
 .justify-start {
   justify-content: flex-start;
 }
 
 .justify-end {
   justify-content: flex-end;
 }
 
 .justify-center {
   justify-content: center;
 }
 
 .justify-between {
   justify-content: space-between;
 }
 
 .justify-around {
   justify-content: space-around;
 }
 
 .items-start {
   align-items: flex-start;
 }
 
 .items-end {
   align-items: flex-end;
 }
 
 .items-center {
   align-items: center;
 }
 
 .items-baseline {
   align-items: baseline;
 }
 
 .items-stretch {
   align-items: stretch;
 }
 
 .self-start {
   align-self: flex-start;
 }
 
 .self-end {
   align-self: flex-end;
 }
 
 .self-center {
   align-self: center;
 }
 
 .self-stretch {
   align-self: stretch;
 }

 .pop-content {
   min-height: 140rpx;
   padding: 0 20rpx;

   .pay-item {
     padding: 30rpx;
     font-size: 30rpx;
     background: #fff;
     border: 1rpx solid $fuint-theme;
     border-radius: 8rpx;
     color: #888;
     margin-bottom: 12rpx;
     text-align: center;

     .item-left_icon {
       margin-right: 20rpx;
       font-size: 48rpx;

       &.wechat {
         color: #00c800;
       }

       &.balance {
         color: $fuint-theme;
       }
     }
   }
 }
}

// 流程模式选择 - 基于模板样式
.flow-mode {
  @extend .card-container;
  margin-top: 0;
  padding: 48rpx 28rpx 60rpx;
  
  .store-name {
    font-size: 36rpx;
    color: #333;
    font-weight: bold;
    letter-spacing: 3rpx;
  }
}

// 配送信息 - 基于模板样式
.flow-delivery {
  @extend .card-container;
  
  .flow-delivery__detail {
    display: flex;
    align-items: center;
  }
  
  .detail-location {
    font-size: 36rpx;
    color: #333;
    margin-right: 20rpx;
  }
  
  .detail-content {
    flex: 1;
    
    .detail-content__title {
      margin-bottom: 12rpx;
      display: flex;
      align-items: center;
      
      .detail-content__title-phone {
        margin-left: 20rpx;
        color: #666;
        font-size: 28rpx;
      }
    }
    
    .detail-content__describe {
      font-size: 28rpx;
      color: #b7b7b7;
      line-height: 1.5;
    }
    
    .address-info {
      .icon {
        float: right;
        font-weight: bold;
        color: #333;
      }
    }
    
    .select-address {
      height: 90rpx;
      line-height: 90rpx;
      
      .icon {
        margin-left: 15rpx;
        color: #666;
      }
    }
    
    .store {
      .store-name {
        font-size: 36rpx;
        color: #333;
        font-weight: bold;
        margin-bottom: 8rpx;
      }
      
      .store-phone {
        font-size: 28rpx;
        color: #666;
        margin-bottom: 8rpx;
      }
      
      .store-address {
        font-size: 26rpx;
        color: #b7b7b7;
        line-height: 1.4;
      }
    }
  }
  
  .detail-arrow {
    font-size: 28rpx;
    color: #666;
  }
}

// 自取/外送切换样式 - 基于模板
.section_3 {
  background-color: #f2f2f2;
  border-radius: 50rpx;
  width: 162rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  position: relative;
  
  .text-wrapper_2 {
    background-color: #232e5d;
    border-radius: 50rpx;
    width: 80rpx;
    height: 50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    transition: left 0.3s ease;
    
    &.active-left {
      left: 0;
    }
    
    &.active-right {
      left: 82rpx;
    }
    
    .text_3 {
      color: #ffffff;
      font-size: 24rpx;
      font-weight: 500;
    }
  }
  
  .text_4 {
    color: #8c8d8f;
    font-size: 24rpx;
    margin-left: 16rpx;
  }
}

// 费用明细 - 基于模板样式
.flow-all-money {
  @extend .card-container;
  
  .detail-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
  }
  
  .flow-all-list {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f8f8f8;
    font-size: 24rpx;
    
    &:last-child {
      border-bottom: none;
    }
    
    .flex-five {
      color: #333;
    }
    
    .col-m {
      color: #e12526;
      font-weight: 500;
    }
    
    .col-gray {
      color: #999;
    }
  }
  
  .ipt-wrapper {
    textarea {
      font-size: 28rpx;
      width: 100%;
      padding: 20rpx;
      border: 1rpx solid #f4f4f4;
      border-radius: 8rpx;
      height: 120rpx;
      background: #fafafa;
      color: #333;
      
      &::placeholder {
        color: #999;
      }
    }
  }
}

// 商品列表 - 基于模板样式
.checkout_list {
  @extend .card-container;
  padding: 30rpx 40rpx;
  
  .flow-shopList {
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f8f8f8;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:first-child {
      padding-top: 0;
    }
  }
}

// 商品详情标题
.goods-detail-header {
  @extend .card-container;
  padding: 30rpx 40rpx 20rpx;
  
  .section-title {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 8rpx;
  }
  
  .section-subtitle {
    font-size: 24rpx;
    color: #666;
    line-height: 1.5;
  }
}

.flow-header-left {
  padding-left: 90rpx;
}

/* 商品列表项样式 - 基于模板 */
.flow-shopList {
  display: flex;
  align-items: flex-start;
  
  .flow-list-left {
    margin-right: 34rpx;
    
    image {
      width: 126rpx;
      height: 124rpx;
      border-radius: 8rpx;
      border: none;
      background: #fff;
    }
  }
  
  .flow-list-right {
    flex: 1;
    padding-top: 4rpx;
    
    .goods-name {
      font-size: 28rpx;
      color: #333;
      font-weight: 500;
      line-height: 1.4;
      margin-bottom: 10rpx;
    }
    
    .goods-props {
      margin-bottom: 12rpx;
      
      .goods-props-item {
        font-size: 26rpx;
        color: #666;
        line-height: 1.3;
        
        .group-name {
          color: #333;
        }
      }
    }
    
    .flow-list-cont {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .small {
        font-size: 26rpx;
        color: #666;
      }
      
      .flow-cont {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
        
        &.price-delete {
          font-size: 24rpx;
          color: #999;
          text-decoration: line-through;
        }
      }
    }
  }
}

/* 优惠券选择 */
.popup__coupon {
  width: 750rpx;
  background: #fff;
  box-sizing: border-box;
  padding: 30rpx;
  
  .coupon__do_not {
    .control {
      width: 90%;
      height: 82rpx;
      margin-top: 30rpx;
      margin-bottom: 1rpx;
      color: #fff;
      background: $fuint-theme;
      border: 1rpx solid $fuint-theme;
      border-radius: 8rpx;
    }
  }
  
  .coupon__title {
    text-align: center;
    margin-bottom: 30rpx;
  }
  
  .coupon-item {
    position: relative;
    overflow: hidden;
    margin-bottom: 22rpx;
  }
  
  .item-wrapper {
    width: 100%;
    display: flex;
    background: #fff;
    border-radius: 8rpx;
    color: #fff;
    height: 180rpx;
  
    .coupon-type {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 10;
      width: 128rpx;
      padding: 6rpx 0;
      background: #fa2209;
      font-size: 20rpx;
      text-align: center;
      color: #ffffff;
      transform: rotate(45deg);
      transform-origin: 64rpx 64rpx;
    }
  
    &.color-default {
      background: linear-gradient(-128deg, #6bf6f6, #008a8a);
    }
    
    &.color-gray {
      background: linear-gradient(-113deg, #bdbdbd, #a2a1a2);
      .coupon-type {
        background: #9e9e9e;
      }
    }
  
    .content {
      flex: 1;
      padding: 30rpx 20rpx;
      border-radius: 16rpx 0 0 16rpx;
  
      .title {
        font-size: 32rpx;
      }
  
      .bottom {
        .time {
          font-size: 24rpx;
        }
  
        .receive {
          height: 46rpx;
          width: 122rpx;
          line-height: 46rpx;
          text-align: center;
          border: 1rpx solid #fff;
          border-radius: 6rpx;
          color: #fff;
          font-size: 24rpx;
  
          &.state {
            border: none;
          }
        }
      }
    }
  
    .tip {
      position: relative;
      flex: 0 0 32%;
      text-align: center;
      border-radius: 0 16rpx 16rpx 0;
  
      .money {
        font-weight: bold;
        font-size: 52rpx;
      }
  
      .pay-line {
        font-size: 22rpx;
      }
    }
  
    .split-line {
      position: relative;
      flex: 0 0 0;
      border-left: 4rpx solid #fff;
      margin: 0 10rpx 0 6rpx;
      background: #fff;
  
      &:before {
        border-radius: 0 0 16rpx 16rpx;
        top: 0;
      }
  
      &:after {
        border-radius: 16rpx 16rpx 0 0;
        bottom: 0;
      }
  
      &:before,
      &:after {
        content: '';
        position: absolute;
        width: 24rpx;
        height: 12rpx;
        background: #f7f7f7;
        left: -14rpx;
        z-index: 1;
      }
    }
  }
  
}

/* 积分抵扣 */
.points {
  
  .title {
    margin-right: 5rpx;
  }
  
  .icon-help {
    font-size: 28rpx;
  }
  
  .points-money {
    margin-right: 20rpx;
  }
  
}

 // 积分说明
.points-content {
  padding: 30rpx 48rpx;
  font-size: 28rpx;
  line-height: 50rpx;
  text-align: left;
  color: #606266;
  height: 620rpx;
  box-sizing: border-box;
}

/* 支付方式 - 基于模板样式 */
.pay-method {
  @extend .card-container;
  
  .flow-all-list {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
    padding: 0 0 20rpx 0;
    border-bottom: none;
  }
}

.pay-method-list {
  .pay-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx;
    margin-bottom: 12rpx;
    background: #fff;
    border: 1rpx solid #f0f0f0;
    border-radius: 12rpx;
    transition: all 0.3s ease;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .item-left {
      display: flex;
      align-items: center;
      
      .item-left_icon {
        width: 48rpx;
        height: 48rpx;
        margin-right: 20rpx;
        font-size: 32rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        
        &.wechat {
          color: #00c800;
        }
        
        &.balance {
          color: #ff9700;
        }
      }
      
      .item-left_text {
        font-size: 28rpx;
        color: #333;
        
        .balance-amount {
          font-size: 24rpx;
          color: #666;
          margin-left: 8rpx;
        }
      }
    }
    
    .item-right {
      color: #e12526;
      font-size: 24rpx;
    }
  }
}

// 商品规格
.goods-props {
  padding-top: 10rpx;
  font-size: 24rpx;
  color: #999;

  .goods-props-item {
    // float: left;
    display: inline-block;
    .group-name {
      margin-right: 6rpx;
    }
  }
}

// 右侧箭头
.right-arrow {
  margin-left: 16rpx;
  // color: #777;
  font-size: 26rpx;
}

// 底部操作栏 - 基于模板样式
.flow-fixed-footer {
  position: fixed;
  bottom: var(--window-bottom);
  width: 100%;
  background: #fff;
  padding: 20rpx 28rpx 40rpx;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 11;
  
  .chackout-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  
  .chackout-left {
    flex: 1;
    
    .col-amount-do {
      font-size: 28rpx;
      color: #333;
      text-align: right;
      padding-right: 20rpx;
      
      .pay-amount {
        color: #e12526;
        font-size: 36rpx;
        font-weight: bold;
        margin-left: 8rpx;
      }
      
      .delivery-fee {
        margin-top: 8rpx;
        font-size: 24rpx;
        color: #666;
      }
    }
  }
  
  .chackout-right {
    flex: 0 0 auto;
  }
  
  // 提交按钮 - 基于模板样式
  .flow-btn {
    background: #333333;
    color: #ffffff;
    text-align: center;
    height: 76rpx;
    line-height: 76rpx;
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    border-radius: 38rpx;
    padding: 0 40rpx;
    letter-spacing: 10rpx;
    min-width: 348rpx;
    box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    
    // 禁用按钮
    &.disabled {
      background: #cccccc;
      box-shadow: none;
    }
  }
}

/* 商品统计框 - 基于模板样式 */
.flow-num-box {
  font-size: 24rpx;
  color: #333;
  padding: 40rpx 0 0;
  text-align: right;
  border-top: 1rpx solid rgba(153, 153, 153, 0.6);
  
  .flow-money {
    font-size: 36rpx;
    font-weight: bold;
    margin-left: 10rpx;
  }
}

/* app.scss */
.flow-shopList {
  padding: 18rpx 0;

  .flow-list-left {
    margin-right: 20rpx;

    image {
      width: 120rpx;
      height: 100rpx;
      border: 1rpx solid #eee;
      border-radius: 5rpx;
      background: #fff;
    }

  }

  .flow-list-right {

    .flow-cont {
      font-size: 28rpx;
      color: #fa2209;
    }

    .small {
      font-size: 26rpx;
      color: #777;
    }

    .flow-list-cont {
      padding-top: 10rpx;
    }

  }

}

.flow-all-money {
  padding: 0 24rpx;
  color: #444;

  .flow-all-list {
    font-size: 28rpx;
    padding: 20rpx 0;
    border-bottom: 1rpx solid rgb(248, 248, 248);
  }

  .flow-all-list:last-child {
    border-bottom: none;
  }

  .flow-all-list-cont {
    font-size: 28rpx;
    padding: 10rpx 0;
  }

  .flow-arrow {
    justify-content: flex-end;
    align-items: center;
  }

}

// 预约取餐样式
.reservation-time {
  color: #ff6600;
  font-weight: bold;
  font-size: 28rpx;
}

// 预约时间选择弹窗
.reservation-popup {
  padding: 40rpx 30rpx 60rpx 30rpx;
  background-color: #ffffff;

  .popup-header {
    text-align: center;
    margin-bottom: 40rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      display: block;
      margin-bottom: 10rpx;
    }

    .popup-subtitle {
      font-size: 26rpx;
      color: #666;
      display: block;
    }
  }

  .datetime-picker {
    margin-bottom: 40rpx;

    .picker-view {
      height: 400rpx;

      .picker-item {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 80rpx;
        font-size: 28rpx;
        color: #333;
      }
    }
  }

  .popup-buttons {
    display: flex;
    justify-content: space-between;

    .btn-cancel,
    .btn-confirm {
      flex: 1;
      height: 80rpx;
      line-height: 80rpx;
      text-align: center;
      font-size: 30rpx;
      border-radius: 40rpx;
      margin: 0 20rpx;
    }

    .btn-cancel {
      background-color: #f5f5f5;
      color: #666;
    }

    .btn-confirm {
      background-color: #3f51b5;
      color: #ffffff;
    }
  }
}
