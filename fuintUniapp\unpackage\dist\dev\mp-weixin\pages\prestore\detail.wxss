
@-webkit-keyframes _show {
0% {
        opacity: 0;
}
100% {
        opacity: 1;
}
}
@keyframes _show {
0% {
        opacity: 0;
}
100% {
        opacity: 1;
}
}
:host {
    display: block;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}



@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-6927eef9 {
  min-height: 100vh;
  padding: 20rpx;
  background: #fff;
}
.base.data-v-6927eef9 {
  border: dashed 5rpx #cccccc;
  padding: 15rpx 0rpx 15rpx 15rpx;
  border-radius: 10rpx;
  margin: 20rpx;
  height: auto;
  min-height: 420rpx;
}
.base .coupon-main.data-v-6927eef9 {
  clear: both;
  min-height: 164rpx;
  border: #ccc dashed 2rpx;
  border-radius: 5rpx;
  margin-bottom: 20rpx;
}
.base .coupon-main .left.data-v-6927eef9 {
  width: 215rpx;
  float: left;
}
.base .coupon-main .left .image.data-v-6927eef9 {
  width: 210rpx;
  height: 160rpx;
  border-radius: 8rpx;
  border-right: #cccccc dashed 2rpx;
}
.base .coupon-main .right.data-v-6927eef9 {
  width: 380rpx;
  float: left;
  overflow: hidden;
}
.base .coupon-main .right .name.data-v-6927eef9 {
  font-size: 38rpx;
}
.base .coupon-main .right .num.data-v-6927eef9 {
  font-size: 58rpx;
  color: red;
}
.base .item.data-v-6927eef9 {
  clear: both;
  margin-bottom: 10rpx;
  font-size: 30rpx;
  color: #666666;
}
.base .item .label.data-v-6927eef9 {
  float: left;
}
.base .item .amount.data-v-6927eef9 {
  font-weight: bold;
}
.base .item .name.data-v-6927eef9 {
  font-weight: bold;
}
.coupon-qr.data-v-6927eef9 {
  border: dashed 5rpx #cccccc;
  border-radius: 10rpx;
  margin: 20rpx;
  text-align: center;
  padding: 30rpx 15rpx 30rpx 15rpx;
}
.coupon-qr .image.data-v-6927eef9 {
  width: 360rpx;
  height: 360rpx;
  margin: 0 auto;
}
.coupon-qr .qr-code .code.data-v-6927eef9 {
  font-weight: bold;
  font-size: 30rpx;
  line-height: 50rpx;
}
.coupon-qr .qr-code .tips.data-v-6927eef9 {
  font-size: 25rpx;
  color: #C0C4CC;
}
.coupon-content.data-v-6927eef9 {
  font-size: 28rpx;
  padding: 30rpx;
  border: dashed 5rpx #cccccc;
  border-radius: 5rpx;
  margin: 20rpx;
  min-height: 400rpx;
}
.coupon-content .title.data-v-6927eef9 {
  margin-bottom: 15rpx;
}
.gift.data-v-6927eef9 {
  height: 46rpx;
  width: 120rpx;
  margin-top: 20rpx;
  line-height: 46rpx;
  text-align: center;
  border: 1px solid #f8df00;
  border-radius: 6rpx;
  color: #f86d48;
  background: #f8df98;
  font-size: 22rpx;
  float: right;
}
.gift.state.data-v-6927eef9 {
  border: none;
  color: #cccccc;
  background: #F5F5F5;
}
