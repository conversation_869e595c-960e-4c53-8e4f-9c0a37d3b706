<view class="page-items"><block wx:for="{{items}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block><block wx:if="{{item.type==='search'}}"><block><search vue-id="{{'571e72d9-1-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" bind:__l="__l"></search></block></block><block wx:if="{{item.type==='image'}}"><block><images vue-id="{{'571e72d9-2-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" dataList="{{item.data}}" bind:__l="__l"></images></block></block><block wx:if="{{item.type==='banner'}}"><block><banner vue-id="{{'571e72d9-3-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" dataList="{{item.data}}" bind:__l="__l"></banner></block></block><block wx:if="{{item.type==='window'}}"><block><window vue-id="{{'571e72d9-4-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" dataList="{{item.data}}" bind:__l="__l"></window></block></block><block wx:if="{{item.type==='video'}}"><block><videos vue-id="{{'571e72d9-5-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" bind:__l="__l"></videos></block></block><block wx:if="{{item.type==='article'}}"><block><article vue-id="{{'571e72d9-6-'+index}}" params="{{item.params}}" dataList="{{item.data}}" bind:__l="__l"></article></block></block><block wx:if="{{item.type==='notice'}}"><block><notice vue-id="{{'571e72d9-7-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" bind:__l="__l"></notice></block></block><block wx:if="{{item.type==='navBar'}}"><block><nav-bar vue-id="{{'571e72d9-8-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" dataList="{{item.data}}" bind:__l="__l"></nav-bar></block></block><block wx:if="{{item.type==='goods'}}"><block><goods vue-id="{{'571e72d9-9-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" dataList="{{item.data}}" bind:__l="__l"></goods></block></block><block wx:if="{{item.type==='service'}}"><block><service vue-id="{{'571e72d9-10-'+index}}" itemStyle="{{item.style}}" params="{{item.params}}" bind:__l="__l"></service></block></block><block wx:if="{{item.type==='blank'}}"><block><blank vue-id="{{'571e72d9-11-'+index}}" itemStyle="{{item.style}}" bind:__l="__l"></blank></block></block><block wx:if="{{item.type==='guide'}}"><block><guide vue-id="{{'571e72d9-12-'+index}}" itemStyle="{{item.style}}" bind:__l="__l"></guide></block></block><block wx:if="{{item.type==='richText'}}"><block><rich-text itemStyle="{{item.style}}" params="{{item.params}}"></rich-text></block></block></block></block></view>