
.uni-transition {
    transition-timing-function: ease;
    transition-duration: 0.3s;
    transition-property: opacity, -webkit-transform;
    transition-property: transform, opacity;
    transition-property: transform, opacity, -webkit-transform;
}
.fade-in {
    opacity: 0;
}
.fade-active {
    opacity: 1;
}
.slide-top-in {
    /* transition-property: transform, opacity; */
    -webkit-transform: translateY(-100%);
            transform: translateY(-100%);
}
.slide-top-active {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    /* opacity: 1; */
}
.slide-right-in {
    -webkit-transform: translateX(100%);
            transform: translateX(100%);
}
.slide-right-active {
    -webkit-transform: translateX(0);
            transform: translateX(0);
}
.slide-bottom-in {
    -webkit-transform: translateY(100%);
            transform: translateY(100%);
}
.slide-bottom-active {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
.slide-left-in {
    -webkit-transform: translateX(-100%);
            transform: translateX(-100%);
}
.slide-left-active {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 1;
}
.zoom-in-in {
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
}
.zoom-out-active {
    -webkit-transform: scale(1);
            transform: scale(1);
}
.zoom-out-in {
    -webkit-transform: scale(1.2);
            transform: scale(1.2);
}

