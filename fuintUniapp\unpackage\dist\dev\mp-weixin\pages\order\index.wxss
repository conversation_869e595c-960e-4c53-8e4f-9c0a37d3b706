@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.flex-row.data-v-0ca91b30 {
  display: flex;
  flex-direction: row;
}
.flex-col.data-v-0ca91b30 {
  display: flex;
  flex-direction: column;
}
.items-center.data-v-0ca91b30 {
  align-items: center;
}
.justify-between.data-v-0ca91b30 {
  justify-content: space-between;
}
.self-stretch.data-v-0ca91b30 {
  align-self: stretch;
}
.page.data-v-0ca91b30 {
  background-color: #f2f2f2;
  min-height: 100vh;
}
.tab-section.data-v-0ca91b30 {
  background-color: #fff;
  padding: 28rpx 92rpx 32rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.1);
}
.order-list.data-v-0ca91b30 {
  padding: 32rpx 28rpx;
}
.order-card.data-v-0ca91b30 {
  margin-bottom: 32rpx;
  padding: 40rpx 32rpx;
  background-color: #fff;
  border-radius: 18rpx;
  box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
}
.order-header.data-v-0ca91b30 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-header .order-type-tag.data-v-0ca91b30 {
  padding: 8rpx 16rpx;
  background-color: #fff;
  border-radius: 18rpx;
  min-width: 72rpx;
  height: 36rpx;
  border: 1rpx solid #515881;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 10rpx;
}
.order-header .order-type-tag .tag-text.data-v-0ca91b30 {
  color: #454d78;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 19rpx;
  letter-spacing: 4rpx;
  white-space: nowrap;
}
.order-header .store-name.data-v-0ca91b30 {
  color: #000;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 26rpx;
  letter-spacing: 5rpx;
}
.order-header .status-text.data-v-0ca91b30 {
  color: #454d78;
  font-size: 26rpx;
  font-weight: 400;
  line-height: 24rpx;
  text-shadow: 0 0 #454d78;
}
.order-time.data-v-0ca91b30 {
  color: #b7b7b7;
  font-size: 20rpx;
  font-weight: 300;
  line-height: 16rpx;
  margin-bottom: 44rpx;
  text-shadow: 0 0 #b7b7b7;
}
.goods-section.data-v-0ca91b30 {
  margin-bottom: 20rpx;
}
.goods-item.data-v-0ca91b30 {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.goods-item .goods-image.data-v-0ca91b30 {
  width: 124rpx;
  height: 124rpx;
  border-radius: 6rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.goods-item .goods-info.data-v-0ca91b30 {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.goods-item .goods-content.data-v-0ca91b30 {
  flex: 1;
}
.goods-item .goods-content .goods-title.data-v-0ca91b30 {
  color: #000;
  font-size: 28rpx;
  font-weight: 400;
  line-height: 28rpx;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.goods-item .goods-content .goods-specs.data-v-0ca91b30 {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8rpx;
}
.goods-item .goods-content .goods-specs .spec-item.data-v-0ca91b30 {
  color: #999;
  font-size: 24rpx;
  font-weight: 300;
  line-height: 22rpx;
  margin-right: 16rpx;
  padding: 4rpx 12rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
}
.goods-item .goods-price-section.data-v-0ca91b30 {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}
.goods-item .goods-price-section .price-group.data-v-0ca91b30 {
  line-height: 28rpx;
  margin-bottom: 18rpx;
}
.goods-item .goods-price-section .price-group .price-symbol.data-v-0ca91b30 {
  color: #000;
  font-size: 20rpx;
  font-weight: 600;
  line-height: 16rpx;
}
.goods-item .goods-price-section .price-group .price-value.data-v-0ca91b30 {
  color: #000;
  font-size: 34rpx;
  font-weight: 600;
  line-height: 28rpx;
}
.goods-item .goods-price-section .goods-count.data-v-0ca91b30 {
  color: #999;
  font-size: 20rpx;
  font-weight: 300;
  line-height: 18rpx;
}
.remark-section.data-v-0ca91b30 {
  padding: 24rpx 0;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  margin-top: 20rpx;
}
.remark-section .remark-label.data-v-0ca91b30 {
  color: #666;
  font-size: 26rpx;
  font-weight: 400;
}
.remark-section .remark-text.data-v-0ca91b30 {
  color: #333;
  font-size: 26rpx;
  font-weight: 400;
}
.reservation-tag.data-v-0ca91b30 {
  display: flex;
  align-items: center;
  background-color: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  margin-right: 12rpx;
}
.reservation-tag .reservation-icon.data-v-0ca91b30 {
  font-size: 20rpx;
  margin-right: 4rpx;
}
.reservation-tag .reservation-text.data-v-0ca91b30 {
  color: #856404;
  font-size: 20rpx;
  font-weight: 500;
}
.reservation-section.data-v-0ca91b30 {
  padding: 24rpx 0;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
  margin-top: 20rpx;
}
.reservation-section .reservation-label.data-v-0ca91b30 {
  color: #666;
  font-size: 26rpx;
  font-weight: 400;
}
.reservation-section .reservation-time-text.data-v-0ca91b30 {
  color: #ff6600;
  font-size: 26rpx;
  font-weight: 600;
}
