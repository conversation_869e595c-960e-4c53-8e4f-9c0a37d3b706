@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.widget-detail.data-v-3e30f78d {
  box-sizing: border-box;
  background: #fff;
  margin-bottom: 10rpx;
  border: solid 2px #f5f5f5;
  padding: 30rpx;
}
.widget-detail .row-block.data-v-3e30f78d {
  padding: 0 20rpx;
  min-height: 50rpx;
}
.widget-detail .detail-goods.data-v-3e30f78d {
  padding: 20rpx;
}
.widget-detail .detail-goods .goods-right.data-v-3e30f78d {
  padding: 15rpx 0;
}
.widget-detail .detail-goods .mobile.data-v-3e30f78d {
  color: #888888;
}
.widget-detail .detail-goods .goods-name.data-v-3e30f78d {
  margin-bottom: 10rpx;
  font-size: 34rpx;
}
.widget-detail .detail-goods .goods-props.data-v-3e30f78d {
  margin-top: 14rpx;
  color: #ababab;
  font-size: 24rpx;
  overflow: hidden;
}
.widget-detail .detail-goods .goods-props .goods-props-item.data-v-3e30f78d {
  display: inline-block;
  margin-right: 14rpx;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background-color: #F5F5F5;
  width: auto;
}
.widget-detail .detail-operate.data-v-3e30f78d {
  padding-bottom: 20rpx;
}
.widget-detail .detail-operate .detail-btn.data-v-3e30f78d {
  border-radius: 4px;
  border: 1rpx solid #ccc;
  padding: 8rpx 20rpx;
  font-size: 28rpx;
  color: #555;
  margin-left: 10rpx;
}
.widget-detail .detail-order.data-v-3e30f78d {
  padding: 10rpx 20rpx;
  font-size: 26rpx;
  line-height: 50rpx;
  height: 50rpx;
}
.widget-detail .detail-order .item.data-v-3e30f78d {
  margin-bottom: 10rpx;
}
.widget-detail .detail-order .item.data-v-3e30f78d:last-child {
  margin-bottom: 0;
}
.empty-ipt.data-v-3e30f78d {
  width: 220rpx;
  margin: 10px auto;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  color: #fff;
  border-radius: 5rpx;
  background: linear-gradient(to right, #3f51b5, #3f51b5);
}
