{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeUser.vue?1f29", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeUser.vue?0049", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeUser.vue?93a0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeUser.vue?0e94", "uni-app:///pages/index/components/HomeUser.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeUser.vue?6a5c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/components/HomeUser.vue?7b5c"], "names": ["props", "userInfo", "type", "default", "id", "avatar", "name", "balance", "point", "methods", "goLogin", "goMemberCode", "userId"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,2oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmBhrB;EACAA;IACAC;MACAC;MACAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;MAAA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QAAAC;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACpCA;AAAA;AAAA;AAAA;AAA2wC,CAAgB,sqCAAG,EAAC,C;;;;;;;;;;;ACA/xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/components/HomeUser.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./HomeUser.vue?vue&type=template&id=0c9bd25d&scoped=true&\"\nvar renderjs\nimport script from \"./HomeUser.vue?vue&type=script&lang=js&\"\nexport * from \"./HomeUser.vue?vue&type=script&lang=js&\"\nimport style0 from \"./HomeUser.vue?vue&type=style&index=0&id=0c9bd25d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c9bd25d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/components/HomeUser.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeUser.vue?vue&type=template&id=0c9bd25d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeUser.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeUser.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"com-user\">\r\n        <view class=\"user-main\">\r\n            <image class=\"avatar\" :src=\"userInfo && userInfo.avatar ? userInfo.avatar : '/static/default-avatar.png'\"></image>\r\n            <view class=\"uc\">\r\n                <view class=\"name\">Hi，你好！</view>\r\n                <view class=\"tip\" v-if=\"!userInfo || !userInfo.id\">为了向您提供更好的服务，请登录！</view>\r\n               <!-- <view class=\"tip\" v-if=\"userInfo && userInfo.id\">\r\n                    <view>余额：<text>{{ userInfo.balance }}</text></view>\r\n                    <view>积分：<text>{{ userInfo.point }}</text></view>\r\n                </view> -->\r\n            </view>\r\n            <view class=\"ur\" v-if=\"!userInfo || !userInfo.id\" @click=\"goLogin\">登录</view>\r\n            <view class=\"qr iconfont icon-qr-extract\" v-if=\"userInfo && userInfo.id\" @click=\"goMemberCode\"></view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        props: {\r\n            userInfo: {\r\n                type: Object,\r\n                default: { id: '', avatar: '', name: '', balance: '', point: '' }\r\n            }\r\n        },\r\n        methods: {\r\n            // 去登录\r\n            goLogin() {\r\n                this.$navTo('pages/login/index')\r\n            },\r\n            // 跳转会员码\r\n            goMemberCode(userId) {\r\n                this.$navTo('pages/user/code', { userId: this.userInfo.id })\r\n            },\r\n        }\r\n    }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.com-user {\r\n    width: 100%;\r\n    height: auto;\r\n    padding: 0 20rpx 20rpx;\r\n    margin-top: -60rpx;\r\n    position: relative;\r\n    z-index: 2;\r\n    .user-main{\r\n        width: 100%;\r\n        padding: 20rpx;\r\n        background: #f5f5f5;\r\n        border-radius: 20rpx;\r\n        border: #cccccc solid 1rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        .avatar{\r\n            width: 130rpx;\r\n            height: 130rpx;\r\n            border-radius: 50%;\r\n        }\r\n        .uc{\r\n            flex: 1;\r\n            padding-left: 10rpx;\r\n            .name{\r\n                font-size: 32rpx;\r\n                font-weight: 500;\r\n                color: #000;\r\n                \r\n            }\r\n            .tip{\r\n                font-size: 24rpx;\r\n                color: #666;\r\n                margin-top: 10rpx;\r\n            }\r\n        }\r\n        .ur{\r\n            width: 140rpx;\r\n            height: 60rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            border-radius: 60rpx;\r\n            justify-content: center;\r\n            color: #fff;\r\n            font-size: 26rpx;\r\n            background-color: $fuint-theme;\r\n        }\r\n        .qr{\r\n            width: 80rpx;\r\n            height: 80rpx;\r\n            display: flex;\r\n            align-items: center;\r\n            border-radius: 6rpx;\r\n            justify-content: center;\r\n            text-align: center;\r\n            color: $fuint-theme;\r\n            font-size: 68rpx;\r\n            background-color: #fff;\r\n            border: solid 1rpx $fuint-theme;\r\n            padding: 2rpx;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeUser.vue?vue&type=style&index=0&id=0c9bd25d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./HomeUser.vue?vue&type=style&index=0&id=0c9bd25d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425188\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}