@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.success.data-v-20765812 {
  width: 100%;
  text-align: center;
  margin-top: 60rpx;
  margin-bottom: 80rpx;
}
.success .result.data-v-20765812 {
  font-size: 35rpx;
  text-align: center;
  padding: 10rpx;
  height: 70rpx;
}
.success .result .icon.data-v-20765812 {
  width: 55rpx;
  height: 55rpx;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
}
.success .result .text.data-v-20765812 {
  text-align: center;
  height: 100%;
  display: inline-block;
  box-sizing: border-box;
  vertical-align: middle;
  color: #00B83F;
  margin-left: 10rpx;
  font-weight: bold;
}
.success .options.data-v-20765812 {
  margin-top: 0rpx;
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: row;
  padding: 50rpx 100rpx 60rpx 100rpx;
}
.success .options .to-home.data-v-20765812, .success .options .to-order.data-v-20765812 {
  margin: 0 auto;
  font-size: 28rpx;
  height: 72rpx;
  line-height: 72rpx;
  text-align: center;
  color: #888;
  border-radius: 72rpx;
  width: 240rpx;
  background: #fff;
  border: solid 1rpx #888;
  float: left;
}
.success .options .iconfont.data-v-20765812 {
  font-weight: bold;
  margin-right: 5rpx;
}
.attention.data-v-20765812 {
  width: 100%;
  text-align: center;
  margin-top: 14rpx;
}
