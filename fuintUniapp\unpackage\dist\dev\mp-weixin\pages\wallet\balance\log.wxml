<view class="container data-v-32787734"><mescroll-body vue-id="1c96f052-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-32787734 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="log-list data-v-32787734"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="log-item data-v-32787734"><view class="item-left flex-box data-v-32787734"><view class="rec-status data-v-32787734"><text class="data-v-32787734">{{item.$orig.description?item.$orig.description:''}}</text><block wx:if="{{!item.$orig.description}}"><text class="data-v-32787734">{{item.$orig.amount>0?'增加金额':'减少金额'}}</text></block></view><view class="rec-time data-v-32787734"><text class="data-v-32787734">{{item.f0}}</text></view></view><view class="item-right data-v-32787734"><text class="data-v-32787734">{{(item.$orig.amount>0?'+':'')+item.$orig.amount+"元"}}</text></view></view></block></view></mescroll-body></view>