<mescroll-body vue-id="8b26577e-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-99224d36 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="tabs-wrapper data-v-99224d36"><scroll-view class="scroll-view data-v-99224d36" scroll-x="{{true}}"><view data-event-opts="{{[['tap',[['onSwitchTab',[0]]]]]}}" class="{{['tab-item','data-v-99224d36',(curId==0)?'active':'']}}" bindtap="__e"><view class="value data-v-99224d36"><text class="data-v-99224d36">全部</text></view></view><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onSwitchTab',['$0'],[[['categoryList','',index,'id']]]]]]]}}" class="{{['tab-item','data-v-99224d36',(curId==item.id)?'active':'']}}" bindtap="__e"><view class="value data-v-99224d36"><text class="data-v-99224d36">{{item.name}}</text></view></view></block></scroll-view></view><view class="book-list data-v-99224d36"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="book-item show-type data-v-99224d36" bindtap="__e"><block class="data-v-99224d36"><view class="book-item-image data-v-99224d36"><image class="image data-v-99224d36" src="{{item.logo}}"></image></view><view class="book-item-left flex-box data-v-99224d36"><view class="book-item-title twolist-hidden data-v-99224d36"><text class="data-v-99224d36">{{item.name}}</text></view><view class="book-item-footer m-top10 data-v-99224d36"><text class="book-views data-v-99224d36">{{item.description}}</text></view></view></block></view></block></view></mescroll-body>