{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form/u-form.vue?5712", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form/u-form.vue?ce93", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form/u-form.vue?ef17", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form/u-form.vue?629f", "uni-app:///uview-ui/components/u-form/u-form.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form/u-form.vue?3d1d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form/u-form.vue?3872"], "names": ["name", "props", "model", "type", "default", "errorType", "borderBottom", "labelPosition", "labelWidth", "labelAlign", "labelStyle", "provide", "uForm", "data", "rules", "created", "methods", "setRules", "resetFields", "field", "validate", "valid", "errorArr", "resolve"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0pB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACK9qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,gBAeA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;EACA;EACAO;IACA;MACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;QACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;UACAD;YACA;YACA;cACAE;cACAC;YACA;YACA;YACA;cACAC;cACA;cACA;gBACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChIA;AAAA;AAAA;AAAA;AAAywC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACA7xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-form/u-form.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-form.vue?vue&type=template&id=5dd1f800&scoped=true&\"\nvar renderjs\nimport script from \"./u-form.vue?vue&type=script&lang=js&\"\nexport * from \"./u-form.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-form.vue?vue&type=style&index=0&id=5dd1f800&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5dd1f800\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-form/u-form.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=template&id=5dd1f800&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"u-form\"><slot /></view>\n</template>\n\n<script>\n    /**\n     * form 表单\n     * @description 此组件一般用于表单场景，可以配置Input输入框，Select弹出框，进行表单验证等。\n     * @tutorial http://uviewui.com/components/form.html\n     * @property {Object} model 表单数据对象\n     * @property {Boolean} border-bottom 是否显示表单域的下划线边框\n     * @property {String} label-position 表单域提示文字的位置，left-左侧，top-上方\n     * @property {String Number} label-width 提示文字的宽度，单位rpx（默认90）\n     * @property {Object} label-style lable的样式，对象形式\n     * @property {String} label-align lable的对齐方式\n     * @property {Object} rules 通过ref设置，见官网说明\n     * @property {Array} error-type 错误的提示方式，数组形式，见上方说明(默认['message'])\n     * @example <u-form :model=\"form\" ref=\"uForm\"></u-form>\n     */\n\nexport default {\n    name: 'u-form',\n    props: {\n        // 当前form的需要验证字段的集合\n        model: {\n            type: Object,\n            default() {\n                return {};\n            }\n        },\n        // 验证规则\n        // rules: {\n        //     type: [Object, Function, Array],\n        //     default() {\n        //         return {};\n        //     }\n        // },\n        // 有错误时的提示方式，message-提示信息，border-如果input设置了边框，变成呈红色，\n        // border-bottom-下边框呈现红色，none-无提示\n        errorType: {\n            type: Array,\n            default() {\n                return ['message', 'toast']\n            }\n        },\n        // 是否显示表单域的下划线边框\n        borderBottom: {\n            type: Boolean,\n            default: true\n        },\n        // label的位置，left-左边，top-上边\n        labelPosition: {\n            type: String,\n            default: 'left'\n        },\n        // label的宽度，单位rpx\n        labelWidth: {\n            type: [String, Number],\n            default: 90\n        },\n        // lable字体的对齐方式\n        labelAlign: {\n            type: String,\n            default: 'left'\n        },\n        // lable的样式，对象形式\n        labelStyle: {\n            type: Object,\n            default() {\n                return {}\n            }\n        },\n    },\n    provide() {\n        return {\n            uForm: this\n        };\n    },\n    data() {\n        return {\n            rules: {}\n        };\n    },\n    created() {\n        // 存储当前form下的所有u-form-item的实例\n        // 不能定义在data中，否则微信小程序会造成循环引用而报错\n        this.fields = [];\n    },\n    methods: {\n        setRules(rules) {\n            this.rules = rules;\n        },\n        // 清空所有u-form-item组件的内容，本质上是调用了u-form-item组件中的resetField()方法\n        resetFields() {\n            this.fields.map(field => {\n                field.resetField();\n            });\n        },\n        // 校验全部数据\n        validate(callback) {\n            return new Promise(resolve => {\n                // 对所有的u-form-item进行校验\n                let valid = true; // 默认通过\n                let count = 0; // 用于标记是否检查完毕\n                let errorArr = []; // 存放错误信息\n                this.fields.map(field => {\n                    // 调用每一个u-form-item实例的validation的校验方法\n                    field.validation('', error => {\n                        // 如果任意一个u-form-item校验不通过，就意味着整个表单不通过\n                        if (error) {\n                            valid = false;\n                            errorArr.push(error);\n                        }\n                        // 当历遍了所有的u-form-item时，调用promise的then方法\n                        if (++count === this.fields.length) {\n                            resolve(valid); // 进入promise的then方法\n                            // 判断是否设置了toast的提示方式，只提示最前面的表单域的第一个错误信息\n                            if(this.errorType.indexOf('none') === -1 && this.errorType.indexOf('toast') >= 0 && errorArr.length) {\n                                this.$u.toast(errorArr[0]);\n                            }\n                            // 调用回调方法\n                            if (typeof callback == 'function') callback(valid);\n                        }\n                    });\n                });\n            });\n        }\n    }\n};\n</script>\n\n<style scoped lang=\"scss\">\n@import \"../../libs/css/style.components.scss\";\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=style&index=0&id=5dd1f800&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form.vue?vue&type=style&index=0&id=5dd1f800&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425061\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}