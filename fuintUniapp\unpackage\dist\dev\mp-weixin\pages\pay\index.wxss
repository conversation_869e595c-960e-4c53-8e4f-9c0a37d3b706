
@-webkit-keyframes _show {
0% {
        opacity: 0;
}
100% {
        opacity: 1;
}
}
@keyframes _show {
0% {
        opacity: 0;
}
100% {
        opacity: 1;
}
}
:host {
    display: block;
    overflow: auto;
    -webkit-overflow-scrolling: touch;
}



@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-32f2f1fc {
  min-height: 100vh;
  padding: 20rpx;
  background: #fff;
  color: #666666;
}
.base.data-v-32f2f1fc {
  background: #3f51b5;
  padding: 30rpx;
  border-radius: 10rpx;
  color: #ffffff;
  margin: 20rpx;
  height: 100rpx;
}
.base .merchant-name.data-v-32f2f1fc {
  margin-left: 30rpx;
  overflow: hidden;
  text-align: center;
  font-weight: bold;
  font-size: 30rpx;
}
.pay-form.data-v-32f2f1fc {
  border: solid 3rpx #3f51b5;
  padding: 30rpx;
  border-radius: 10rpx;
  margin: 60rpx 20rpx 20rpx 20rpx;
}
.pay-form .remark-popup.data-v-32f2f1fc {
  border: #cccccc solid 1px;
  background: red;
}
.pay-form .input.data-v-32f2f1fc {
  padding-left: 20rpx;
  padding-right: 20rpx;
  margin-top: 30rpx;
  margin-bottom: 20rpx;
  width: 94%;
  display: inline-flex;
}
.pay-form .amount.data-v-32f2f1fc {
  font-weight: bold;
  font-size: 70rpx;
  float: left;
  min-width: 330rpx;
  display: block;
  height: 88rpx;
  border-bottom: solid 1rpx #CCCCCC;
}
.pay-form .amount-icon.data-v-32f2f1fc {
  font-size: 38rpx;
  font-weight: bold;
  float: left;
}
.pay-form .remark.data-v-32f2f1fc {
  width: 100%;
  text-align: right;
}
/* 底部操作栏 */
.footer-fixed.data-v-32f2f1fc {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  display: flex;
  height: 180rpx;
  padding-bottom: 30rpx;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);
  background: #fff;
}
.footer-container.data-v-32f2f1fc {
  width: 100%;
  display: flex;
}
.foo-item-btn.data-v-32f2f1fc {
  flex: 1;
}
.foo-item-btn .btn-wrapper.data-v-32f2f1fc {
  height: 100%;
  display: flex;
  align-items: center;
}
.foo-item-btn .btn-item.data-v-32f2f1fc {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  margin-right: 16rpx;
  margin-left: 16rpx;
  text-align: center;
  color: #fff;
  border-radius: 8rpx;
}
.foo-item-btn .btn-item-main.data-v-32f2f1fc {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
