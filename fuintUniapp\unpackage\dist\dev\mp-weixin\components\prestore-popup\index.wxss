@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.pre-store-popup.data-v-9768638a {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 21;
  overflow: hidden;
}
.pre-store-popup.show.data-v-9768638a {
  display: block;
}
.pre-store-popup.show .mask.data-v-9768638a {
  -webkit-animation: showPopup-data-v-9768638a 0.2s linear both;
          animation: showPopup-data-v-9768638a 0.2s linear both;
}
.pre-store-popup.show .layer.data-v-9768638a {
  -webkit-animation: showLayer-data-v-9768638a 0.2s linear both;
          animation: showLayer-data-v-9768638a 0.2s linear both;
  padding-bottom: 50rpx;
}
.pre-store-popup.hide .mask.data-v-9768638a {
  -webkit-animation: hidePopup-data-v-9768638a 0.2s linear both;
          animation: hidePopup-data-v-9768638a 0.2s linear both;
}
.pre-store-popup.hide .layer.data-v-9768638a {
  -webkit-animation: hideLayer-data-v-9768638a 0.2s linear both;
          animation: hideLayer-data-v-9768638a 0.2s linear both;
}
.pre-store-popup.none.data-v-9768638a {
  display: none;
}
.pre-store-popup .mask.data-v-9768638a {
  position: fixed;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  background-color: rgba(0, 0, 0, 0.65);
}
.pre-store-popup .layer.data-v-9768638a {
  display: flex;
  width: 100%;
  flex-direction: column;
  position: fixed;
  z-index: 99;
  bottom: 0;
  border-radius: 10rpx 10rpx 0 0;
  background-color: #fff;
}
.pre-store-popup .layer .specification-wrapper.data-v-9768638a {
  width: 100%;
  padding: 30rpx 25rpx;
  box-sizing: border-box;
  background: #ffffff;
}
.pre-store-popup .layer .specification-wrapper .specification-wrapper-content.data-v-9768638a {
  width: 100%;
  max-height: 900rpx;
  min-height: 300rpx;
}
.pre-store-popup .layer .specification-wrapper .specification-wrapper-content.data-v-9768638a::-webkit-scrollbar {
  /*隐藏滚轮*/
  display: none;
}
.pre-store-popup .layer .specification-wrapper .specification-wrapper-content .specification-header.data-v-9768638a {
  width: 100%;
  display: flex;
  flex-direction: row;
  position: relative;
  margin-bottom: 40rpx;
}
.pre-store-popup .layer .specification-wrapper .specification-wrapper-content .specification-header .specification-name.data-v-9768638a {
  font-weight: bold;
}
.pre-store-popup .layer .specification-wrapper .specification-wrapper-content .specification-content.data-v-9768638a {
  font-weight: 500;
  font-size: 26rpx;
}
.pre-store-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .store-item.data-v-9768638a {
  display: flex;
  height: 100rpx;
  padding-top: 30rpx;
  cursor: pointer;
}
.pre-store-popup .layer .specification-wrapper .specification-wrapper-content .specification-content .store-item .item-rule.data-v-9768638a {
  padding: 10rpx;
  border: solid 1px #f03c3c;
  border-radius: 10rpx;
  width: 400rpx;
  text-align: center;
  background: #f9211c;
  color: #ffffff;
}
.pre-store-popup .layer .specification-wrapper .close.data-v-9768638a {
  position: absolute;
  top: 30rpx;
  right: 25rpx;
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  line-height: 50rpx;
}
.pre-store-popup .layer .specification-wrapper .close .close-item.data-v-9768638a {
  width: 40rpx;
  height: 40rpx;
}
.pre-store-popup .layer .btn-wrapper.data-v-9768638a {
  display: flex;
  width: 100%;
  height: 120rpx;
  flex: 0 0 120rpx;
  align-items: center;
  justify-content: space-between;
  padding: 0 26rpx;
  box-sizing: border-box;
}
.pre-store-popup .layer .btn-wrapper .layer-btn.data-v-9768638a {
  width: 335rpx;
  height: 76rpx;
  border-radius: 38rpx;
  color: #fff;
  line-height: 76rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
}
.pre-store-popup .layer .btn-wrapper .layer-btn.add-cart.data-v-9768638a {
  background: #ffbe46;
}
.pre-store-popup .layer .btn-wrapper .layer-btn.buy.data-v-9768638a {
  background: #fe560a;
}
.pre-store-popup .layer .btn-wrapper .sure.data-v-9768638a {
  width: 698rpx;
  height: 80rpx;
  border-radius: 40rpx;
  color: #fff;
  line-height: 80rpx;
  text-align: center;
  font-weight: 500;
  font-size: 28rpx;
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.pre-store-popup .layer .btn-wrapper .sure.add-cart.data-v-9768638a {
  background: #ff9402;
}
@-webkit-keyframes showPopup-data-v-9768638a {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@keyframes showPopup-data-v-9768638a {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}
@-webkit-keyframes hidePopup-data-v-9768638a {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes hidePopup-data-v-9768638a {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@-webkit-keyframes showLayer-data-v-9768638a {
0% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
100% {
    -webkit-transform: translateY(0%);
            transform: translateY(0%);
}
}
@keyframes showLayer-data-v-9768638a {
0% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
100% {
    -webkit-transform: translateY(0%);
            transform: translateY(0%);
}
}
@-webkit-keyframes hideLayer-data-v-9768638a {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
100% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
}
@keyframes hideLayer-data-v-9768638a {
0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
}
100% {
    -webkit-transform: translateY(120%);
            transform: translateY(120%);
}
}
