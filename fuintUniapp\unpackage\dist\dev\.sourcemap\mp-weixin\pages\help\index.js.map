{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/help/index.vue?9f29", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/help/index.vue?bcfa", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/help/index.vue?efa1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/help/index.vue?40df", "uni-app:///pages/help/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/help/index.vue?b9ac", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/help/index.vue?e64d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MescrollBody", "mixins", "data", "list", "upOption", "auto", "page", "size", "noMoreSize", "empty", "tip", "onLoad", "methods", "upCallback", "app", "then", "catch", "getHelpList", "HelpApi", "resolve", "onTargetDetail", "articleId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkB9pB;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;QACA;QACAC;UACAC;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;EAEAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAC,0BACAC;QACA;QACA;QACAD;MACA,GACAE;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;UAAAZ;QAAA,GACAS;UACA;UACA;UACAD;UACAK;QACA;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;QAAAC;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC3FA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/help/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/help/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=7ee5dfbd&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=7ee5dfbd&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7ee5dfbd\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/help/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=7ee5dfbd&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ use: false }\" :up=\"upOption\"\n      @up=\"upCallback\">\n      <view class=\"help cont-box b-f\" v-for=\"(item, index) in list.content\" :key=\"index\" @click=\"onTargetDetail(item.id)\">\n        <view class=\"title\">\n          <text>{{ item.title }}</text>\n        </view>\n        <view class=\"content\">\n          <text>{{ item.brief }}</text>\n        </view>\n      </view>\n    </mescroll-body>\n  </view>\n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import * as HelpApi from '@/api/help'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n\n  const pageSize = 15;\n\n  export default {\n    components: {\n      MescrollBody\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        list: getEmptyPaginateObj(),\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于3条才显示无更多数据\n          noMoreSize: 3,\n          // 空布局\n          empty: {\n            tip: '亲，暂无相关数据'\n          }\n        }\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {},\n\n    methods: {\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this\n        // 设置列表数据\n        app.getHelpList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length;\r\n            const totalSize = list.content.totalElements;\r\n            app.mescroll.endBySize(curPageLen, totalSize);\n          })\n          .catch(() => app.mescroll.endErr())\n      },\n\n      // 获取帮助列表\n      getHelpList(pageNo = 1) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          HelpApi.list({ page: pageNo })\n            .then(result => {\n              // 合并新数据\r\n              const newList = result.data;\r\n              app.list.content = getMoreListData(newList, app.list, pageNo);\r\n              resolve(newList);\n            }).catch(() => app.mescroll.endErr());\n        })\n      },\r\n      \n      // 跳转文章详情页\n      onTargetDetail(articleId) {\n        this.$navTo('pages/article/detail', { articleId });\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .help {\n    border-bottom: 2rpx solid #f6f6f9;\n\n    .title {\n      font-size: 32rpx;\n      color: #333;\r\n      font-weight: bold;\n      margin-bottom: 10rpx;\n    }\n\n    .content {\n      font-size: 26rpx;\n      color: #666;\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=7ee5dfbd&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=7ee5dfbd&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424026\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}