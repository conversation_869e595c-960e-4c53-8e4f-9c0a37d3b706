<view class="page data-v-0ca91b30"><mescroll-body vue-id="3bc35b9e-1" sticky="{{true}}" down="{{({native:true})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^down',[['downCallback']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:down="__e" bind:up="__e" class="data-v-0ca91b30 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="tab-section data-v-0ca91b30"><u-tabs vue-id="{{('3bc35b9e-2')+','+('3bc35b9e-1')}}" list="{{tabs}}" is-scroll="{{false}}" current="{{curTab}}" active-color="#2b387e" duration="{{0.2}}" data-event-opts="{{[['^change',[['onChangeTab']]]]}}" bind:change="__e" class="data-v-0ca91b30" bind:__l="__l"></u-tabs></view><view class="order-list data-v-0ca91b30"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="order-card data-v-0ca91b30"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="order-header data-v-0ca91b30" bindtap="__e"><view class="flex-row items-center data-v-0ca91b30"><view class="order-type-tag data-v-0ca91b30"><text class="tag-text data-v-0ca91b30">{{item.typeName}}</text></view><block wx:if="{{item.isReservation==='Y'}}"><view class="reservation-tag data-v-0ca91b30"><text class="reservation-icon data-v-0ca91b30">🕐</text><text class="reservation-text data-v-0ca91b30">预约</text></view></block></view><text class="status-text data-v-0ca91b30">{{item.statusText}}</text></view><text class="order-time data-v-0ca91b30">{{item.createTime}}</text><block wx:if="{{item.goods}}"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="goods-section data-v-0ca91b30" bindtap="__e"><block wx:for="{{item.goods}}" wx:for-item="goods" wx:for-index="idx" wx:key="idx"><view class="goods-item data-v-0ca91b30"><image class="goods-image data-v-0ca91b30" src="{{goods.image}}"></image><view class="goods-info data-v-0ca91b30"><view class="goods-content data-v-0ca91b30"><view class="goods-title data-v-0ca91b30">{{goods.name}}</view><view class="goods-specs data-v-0ca91b30"><block wx:for="{{goods.specList}}" wx:for-item="props" wx:for-index="idx" wx:key="idx"><view class="spec-item data-v-0ca91b30">{{''+props.specValue+''}}</view></block></view></view><view class="goods-price-section data-v-0ca91b30"><view class="price-group data-v-0ca91b30"><text class="price-symbol data-v-0ca91b30">￥</text><text class="price-value data-v-0ca91b30">{{goods.price}}</text></view><text class="goods-count data-v-0ca91b30">{{"共"+goods.num+"件"}}</text></view></view></view></block></view></block><block wx:if="{{item.remark}}"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="remark-section data-v-0ca91b30" bindtap="__e"><text class="remark-label data-v-0ca91b30">备注：</text><text class="remark-text data-v-0ca91b30">{{item.remark||'--'}}</text></view></block><block wx:if="{{item.isReservation==='Y'}}"><view data-event-opts="{{[['tap',[['handleTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="reservation-section data-v-0ca91b30" bindtap="__e"><text class="reservation-label data-v-0ca91b30">预约取餐：</text><text class="reservation-time-text data-v-0ca91b30">{{item.reservationTime}}</text></view></block></view></block></view></mescroll-body></view>