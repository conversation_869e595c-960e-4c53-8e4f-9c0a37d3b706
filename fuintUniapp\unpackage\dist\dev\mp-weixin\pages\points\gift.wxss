
page {
  background: #f7f8fa;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.page-title.data-v-445924f3 {
  width: 94%;
  margin: 0 auto;
  padding-top: 40rpx;
  font-size: 28rpx;
  color: rgba(69, 90, 100, 0.6);
}
.form-wrapper.data-v-445924f3 {
  margin: 20rpx auto 20rpx auto;
  padding: 0 40rpx;
  width: 94%;
  box-shadow: 0 1rpx 5rpx 0px rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  background: #fff;
}
.form-wrapper .my-amount.data-v-445924f3 {
  height: 60rpx;
  margin-left: 160rpx;
  margin-top: 10rpx;
  color: #888888;
}
.form-wrapper .my-amount .amount.data-v-445924f3 {
  color: #f9211c;
}
.form-wrapper .my-amount .all.data-v-445924f3 {
  margin-left: 30rpx;
  color: #888888;
  background: #f5f5f5;
  padding: 5rpx 10rpx 5rpx 10rpx;
  border-radius: 10rpx;
}
/* 底部操作栏 */
.footer.data-v-445924f3 {
  margin-top: 60rpx;
}
.footer .btn-wrapper.data-v-445924f3 {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.footer .btn-item.data-v-445924f3 {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
}
.footer .btn-item-main.data-v-445924f3 {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.footer .btn-item-main.disabled.data-v-445924f3 {
  background: #ff9779;
}
