@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.level-detail-container.data-v-78a9bd0e {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
}
.level-bg-section.data-v-78a9bd0e {
  position: relative;
  height: 400rpx;
  overflow: hidden;
}
.level-bg-image.data-v-78a9bd0e {
  width: 100%;
  height: 42vw;
  object-fit: cover;
}
.level-info-overlay.data-v-78a9bd0e {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 40rpx;
}
.current-level-badge.data-v-78a9bd0e {
  padding: 16rpx 40rpx;
  margin-bottom: 170rpx;
}
.level-name.data-v-78a9bd0e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.level-progress-info.data-v-78a9bd0e {
  text-align: center;
  color: #fff;
}
.progress-label.data-v-78a9bd0e {
  font-size: 28rpx;
  display: block;
  margin-bottom: 16rpx;
}
.progress-numbers.data-v-78a9bd0e {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
}
.current-amount.data-v-78a9bd0e {
  font-size: 36rpx;
  font-weight: bold;
  color: #ffd700;
}
.separator.data-v-78a9bd0e {
  font-size: 28rpx;
  margin: 0 10rpx;
}
.target-amount.data-v-78a9bd0e {
  font-size: 28rpx;
}
.benefits-image.data-v-78a9bd0e {
  width: 100vw;
  object-fit: cover;
  height: 122vw;
}
.level-list-section.data-v-78a9bd0e {
  margin: 40rpx 30rpx;
}
.section-title.data-v-78a9bd0e {
  margin-bottom: 30rpx;
}
.title-text.data-v-78a9bd0e {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.level-items.data-v-78a9bd0e {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}
.level-item.data-v-78a9bd0e {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}
.level-item.data-v-78a9bd0e:last-child {
  border-bottom: none;
}
.level-item.current-level.data-v-78a9bd0e {
  background: linear-gradient(90deg, #fff7e6 0%, #ffffff 100%);
}
.level-info.data-v-78a9bd0e {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}
.level-title.data-v-78a9bd0e {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.level-requirement.data-v-78a9bd0e {
  font-size: 24rpx;
  color: #666;
}
.level-status .status-current.data-v-78a9bd0e {
  font-size: 24rpx;
  color: #ff9500;
  background: #fff7e6;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
}
.level-status .status-achieved.data-v-78a9bd0e {
  font-size: 24rpx;
  color: #52c41a;
  background: #f6ffed;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.level-status .status-pending.data-v-78a9bd0e {
  font-size: 24rpx;
  color: #999;
  background: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.back-button.data-v-78a9bd0e {
  position: fixed;
  bottom: 60rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  background: linear-gradient(90deg, #ff9500 0%, #ffab00 100%);
  color: white;
  padding: 24rpx 60rpx;
  border-radius: 50rpx;
  box-shadow: 0 8rpx 24rpx rgba(255, 149, 0, 0.3);
}
.back-text.data-v-78a9bd0e {
  font-size: 32rpx;
  font-weight: bold;
}
