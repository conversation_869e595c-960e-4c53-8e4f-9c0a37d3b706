<view class="container data-v-7ee5dfbd"><mescroll-body vue-id="5dd698e9-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-7ee5dfbd vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{list.content}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onTargetDetail',['$0'],[[['list.content','',index,'id']]]]]]]}}" class="help cont-box b-f data-v-7ee5dfbd" bindtap="__e"><view class="title data-v-7ee5dfbd"><text class="data-v-7ee5dfbd">{{item.title}}</text></view><view class="content data-v-7ee5dfbd"><text class="data-v-7ee5dfbd">{{item.brief}}</text></view></view></block></mescroll-body></view>