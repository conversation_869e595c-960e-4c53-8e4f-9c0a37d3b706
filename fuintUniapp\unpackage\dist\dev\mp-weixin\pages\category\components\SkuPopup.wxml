<goods-sku-popup vue-id="47d34cd4-1" value="{{value}}" border-radius="20" goods="{{goodsInfo}}" mode="{{skuMode}}" defaultPrice="{{isMemberPrice&&goods.gradePrice>0?goods.gradePrice:goods.price}}" gradeInfo="{{gradeInfo}}" hafanInfo="{{hafanInfo}}" defaultStock="{{goods.stock}}" maskCloseAble="{{true}}" data-event-opts="{{[['^input',[['onChangeValue']]],['^open',[['openSkuPopup']]],['^close',[['closeSkuPopup']]],['^addCart',[['addCart']]],['^buyNow',[['buyNow']]],['^confirm',[['onConfirm']]]]}}" bind:input="__e" bind:open="__e" bind:close="__e" bind:addCart="__e" bind:buyNow="__e" bind:confirm="__e" class="data-v-96f65520" bind:__l="__l"></goods-sku-popup>