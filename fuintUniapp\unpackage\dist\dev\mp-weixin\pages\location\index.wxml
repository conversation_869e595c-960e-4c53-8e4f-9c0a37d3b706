<view class="container data-v-f7e9129e"><view class="search-wrapper data-v-f7e9129e"><view class="search-input data-v-f7e9129e"><view class="search-input-wrapper data-v-f7e9129e"><view class="left data-v-f7e9129e"><text class="search-icon iconfont icon-sousuo data-v-f7e9129e"></text></view><view class="right data-v-f7e9129e"><input class="input data-v-f7e9129e" placeholder="请输入店铺关键字" type="text" data-event-opts="{{[['input',[['__set_model',['','searchValue','$event',[]]]]]]}}" value="{{searchValue}}" bindinput="__e"/></view></view></view><view class="search-button data-v-f7e9129e"><button class="button data-v-f7e9129e" type="warn" data-event-opts="{{[['tap',[['doSearch',['$event']]]]]}}" bindtap="__e">搜索</button></view></view><view class="store-list data-v-f7e9129e"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleQuick',['$0'],[[['storeList','',index,'id']]]]]]]}}" class="store-info data-v-f7e9129e" bindtap="__e"><view class="base-info data-v-f7e9129e"><view class="name data-v-f7e9129e">{{item.$orig.name}}</view><view class="hours data-v-f7e9129e">{{"营业时间："+item.$orig.hours}}</view><view class="address data-v-f7e9129e"><text class="location-icon iconfont icon-dingwei data-v-f7e9129e"></text>{{item.$orig.address}}</view><view class="tel data-v-f7e9129e">{{"联系电话："+item.$orig.phone}}</view></view><view class="loc-info data-v-f7e9129e"><text class="dis data-v-f7e9129e"><text class="distance data-v-f7e9129e">{{item.g0}}</text>公里</text></view></view></block></view><block wx:if="{{!$root.g1}}"><empty vue-id="05383f16-1" isLoading="{{isLoading}}" custom-style="{{({padding:'180rpx 50rpx'})}}" tips="暂无店铺~" class="data-v-f7e9129e" bind:__l="__l"></empty></block></view>