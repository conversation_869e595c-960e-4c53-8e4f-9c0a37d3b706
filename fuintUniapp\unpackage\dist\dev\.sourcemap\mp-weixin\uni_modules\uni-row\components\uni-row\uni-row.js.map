{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-row/uni-row.vue?7492", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-row/uni-row.vue?db5d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-row/uni-row.vue?8dfc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-row/uni-row.vue?4d57", "uni-app:///uni_modules/uni-row/components/uni-row/uni-row.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-row/uni-row.vue?3474", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-row/components/uni-row/uni-row.vue?e89c"], "names": ["name", "componentName", "options", "virtualHost", "props", "type", "gutter", "justify", "default", "align", "width", "created", "computed", "marginValue", "typeClass", "justifyClass", "alignClass"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACiL;AACjL,gBAAgB,kLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACU9rB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,eAiBA;EACAA;EACAC;EAEAC;IACAC;EACA;;EAEAC;IACAC;IACAC;IACAC;MACAF;MACAG;IACA;IACAC;MACAJ;MACAG;IACA;IACA;IACAE;MACAL;MACAG;IACA;EACA;EACAG,6BAIA;EACAC;IACAC;MAEA;QACA;MACA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAA6wC,CAAgB,6oCAAG,EAAC,C;;;;;;;;;;;ACAjyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-row/components/uni-row/uni-row.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-row.vue?vue&type=template&id=1d993189&\"\nvar renderjs\nimport script from \"./uni-row.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-row.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-row.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-row/components/uni-row/uni-row.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=template&id=1d993189&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = Number(_vm.marginValue)\n  var m1 = Number(_vm.marginValue)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=script&lang=js&\"", "<template>\n    <view :class=\"[ 'uni-row', typeClass , justifyClass, alignClass, ]\" :style=\"{\n        marginLeft:`${Number(marginValue)}rpx`,\n        marginRight:`${Number(marginValue)}rpx`,\n    }\">\n        <slot></slot>\n    </view>\n</template>\n\n<script>\n    const ComponentClass = 'uni-row';\n    const modifierSeparator = '--';\n    /**\n     * Row    布局-行\n     * @description    流式栅格系统，随着屏幕或视口分为 24 份，可以迅速简便地创建布局。\n     * @tutorial    https://ext.dcloud.net.cn/plugin?id=3958\n     *\n     * @property    {gutter} type = Number 栅格间隔\n     * @property    {justify} type = String flex 布局下的水平排列方式\n     *                         可选    start/end/center/space-around/space-between    start\n     *                         默认值    start\n     * @property    {align} type = String flex 布局下的垂直排列方式\n     *                         可选    top/middle/bottom\n     *                         默认值    top\n     * @property    {width} type = String|Number nvue下需要自行配置宽度用于计算\n     *                         默认值 750\n     */\n\n\n    export default {\n        name: 'uniRow',\n        componentName: 'uniRow',\n        // #ifdef MP-WEIXIN\n        options: {\n            virtualHost: true // 在微信小程序中将组件节点渲染为虚拟节点，更加接近Vue组件的表现，可使用flex布局\n        },\n        // #endif\n        props: {\n            type: String,\n            gutter: Number,\n            justify: {\n                type: String,\n                default: 'start'\n            },\n            align: {\n                type: String,\n                default: 'top'\n            },\n            // nvue如果使用span等属性，需要配置宽度\n            width: {\n                type: [String, Number],\n                default: 750\n            }\n        },\n        created() {\n            // #ifdef APP-NVUE\n            this.type = 'flex';\n            // #endif\n        },\n        computed: {\n            marginValue() {\n                // #ifndef APP-NVUE\n                if (this.gutter) {\n                    return -(this.gutter / 2);\n                }\n                // #endif\n                return 0;\n            },\n            typeClass() {\n                return this.type === 'flex' ? `${ComponentClass + modifierSeparator}flex` : '';\n            },\n            justifyClass() {\n                return this.justify !== 'start' ? `${ComponentClass + modifierSeparator}flex-justify-${this.justify}` : ''\n            },\n            alignClass() {\n                return this.align !== 'top' ? `${ComponentClass + modifierSeparator}flex-align-${this.align}` : ''\n            }\n        }\n    };\n</script>\n\n<style lang=\"scss\">\n    $layout-namespace: \".uni-\";\n    $row:$layout-namespace+\"row\";\n    $modifier-separator: \"--\";\n\n    @mixin utils-clearfix {\n        $selector: &;\n\n        @at-root {\n\n            /* #ifndef APP-NVUE */\n            #{$selector}::before,\n            #{$selector}::after {\n                display: table;\n                content: \"\";\n            }\n\n            #{$selector}::after {\n                clear: both;\n            }\n\n            /* #endif */\n        }\n\n    }\n\n    @mixin utils-flex ($direction: row) {\n        /* #ifndef APP-NVUE */\n        display: flex;\n        /* #endif */\n        flex-direction: $direction;\n    }\n\n    @mixin set-flex($state) {\n        @at-root &-#{$state} {\n            @content\n        }\n    }\n\n    #{$row} {\n        position: relative;\n        flex-direction: row;\n\n        /* #ifdef APP-NVUE */\n        flex: 1;\n        /* #endif */\n\n        /* #ifndef APP-NVUE */\n        box-sizing: border-box;\n        /* #endif */\n\n        // 非nvue使用float布局\n        @include utils-clearfix;\n\n        // 在QQ、字节、百度小程序平台，编译后使用shadow dom，不可使用flex布局，使用float\n        @at-root {\n\n            /* #ifndef MP-QQ || MP-TOUTIAO || MP-BAIDU */\n            &#{$modifier-separator}flex {\n                @include utils-flex;\n                flex-wrap: wrap;\n                flex: 1;\n\n                &:before,\n                &:after {\n                    /* #ifndef APP-NVUE */\n                    display: none;\n                    /* #endif */\n                }\n\n                @include set-flex(justify-center) {\n                    justify-content: center;\n                }\n\n                @include set-flex(justify-end) {\n                    justify-content: flex-end;\n                }\n\n                @include set-flex(justify-space-between) {\n                    justify-content: space-between;\n                }\n\n                @include set-flex(justify-space-around) {\n                    justify-content: space-around;\n                }\n\n                @include set-flex(align-middle) {\n                    align-items: center;\n                }\n\n                @include set-flex(align-bottom) {\n                    align-items: flex-end;\n                }\n            }\n\n            /* #endif */\n        }\n\n    }\n\n    // 字节、QQ配置后不生效\n    // 此处用法无法使用scoped\n    /* #ifdef MP-WEIXIN || MP-TOUTIAO || MP-QQ */\n    :host {\n        display: block;\n    }\n\n    /* #endif */\n</style>\n", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-row.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425322\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}