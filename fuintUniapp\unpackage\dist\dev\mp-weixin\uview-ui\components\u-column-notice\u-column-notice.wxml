<view class="{{['u-notice-bar','data-v-475fdbf0',type?'u-type-'+type+'-light-bg':'']}}" style="{{'background:'+(computeBgColor)+';'+('padding:'+(padding)+';')}}"><view class="u-icon-wrap data-v-475fdbf0"><block wx:if="{{volumeIcon}}"><u-icon class="u-left-icon data-v-475fdbf0" vue-id="b6c7b9d4-1" name="volume-fill" size="{{volumeSize}}" color="{{computeColor}}" bind:__l="__l"></u-icon></block></view><swiper class="u-swiper data-v-475fdbf0" disable-touch="{{disableTouch}}" autoplay="{{autoplay&&playState=='play'}}" vertical="{{vertical}}" circular="{{true}}" interval="{{duration}}" data-event-opts="{{[['change',[['change',['$event']]]]]}}" bindchange="__e"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item class="u-swiper-item data-v-475fdbf0"><view data-event-opts="{{[['tap',[['click',[index]]]]]}}" class="{{['u-news-item','u-line-1','data-v-475fdbf0','u-type-'+type]}}" style="{{$root.s0}}" bindtap="__e">{{''+item+''}}</view></swiper-item></block></swiper><view class="u-icon-wrap data-v-475fdbf0"><block wx:if="{{moreIcon}}"><u-icon class="u-right-icon data-v-475fdbf0" vue-id="b6c7b9d4-2" name="arrow-right" size="{{26}}" color="{{computeColor}}" data-event-opts="{{[['^click',[['getMore']]]]}}" bind:click="__e" bind:__l="__l"></u-icon></block><block wx:if="{{closeIcon}}"><u-icon class="u-right-icon data-v-475fdbf0" vue-id="b6c7b9d4-3" name="close" size="{{24}}" color="{{computeColor}}" data-event-opts="{{[['^click',[['close']]]]}}" bind:click="__e" bind:__l="__l"></u-icon></block></view></view>