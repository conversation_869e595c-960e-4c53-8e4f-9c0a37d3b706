{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/index.vue?2603", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/index.vue?9815", "uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/index.vue?6466", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/index.vue?c16d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/index.vue?db20", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/index/index.vue?c4b1", "uni-app:///pages/index/index.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "mixins", "components", "Empty", "HomeBanner", "HomeService", "HomeTakeCoupon", "HomeUser", "HomeNav", "HomeAds", "data", "banner", "ads", "storeInfo", "userInfo", "has<PERSON>lai<PERSON><PERSON><PERSON><PERSON>n", "isReflash", "isLoading", "onLoad", "storeId", "uni", "onShow", "app", "type", "success", "fail", "methods", "handleCloseCouponDialog", "goLogin", "goMemberCode", "userId", "goPickup", "storage", "goDelivery", "getPageData", "Api", "then", "finally", "getUserInfo", "UserApi", "rev1", "console", "CouponApi", "rev2", "onPullDownRefresh", "onGetStoreInfo", "<PERSON><PERSON><PERSON>", "onShareAppMessage", "title", "path", "onShareTimeline"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;;;;ACNL;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6D9pB;AAQA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAAA,eAEA;EACAC;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACAC;IACA;MACAC;MACAA;IACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;IACAC;IACAA;IACAF;MACAG;MACAC;QACAJ;QACAA;QACAE;MACA;MACAG;QACA;MAAA;IAEA;EACA;EAEAC;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAAC;QAAA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MACA;QAAAT;MAAA;IACA;IAEAU;MAEA;MACAD;MACA;QAAAT;MAAA;IACA;IACA;AACA;AACA;AACA;IACAW;MACA;MACAC,WACAC;QACAd;QACAA;QACAF;QACAE;MACA,GACAe;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACA;gBACAC;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAAAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IAEA;AACA;AACA;IACAC;MACA;MACA;MACA;QACAxB;MACA;IACA;IAEA;AACA;AACA;IACAyB;MACA;MACAC,0BACAV;QACAd;QACA;UACAF;UACAA;UACA;UACA;UACAE;UACA;YACAA;UACA;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAyB;IACA;IACA;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAF;MACAC;IACA;EACA;AAEA;AAAA,2B", "file": "pages/index/index.js", "sourcesContent": ["import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891423242\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container\">\r\n      <empty v-if=\"!storeInfo\" :isLoading=\"isLoading\" tips=\"数据加载中...\"></empty>\r\n      \r\n      <!-- 新的UI设计 -->\r\n      <view v-if=\"storeInfo\" class=\"new-home-layout\">\r\n          <!-- 顶部背景图 -->\r\n          <view class=\"top-bg-section\">\r\n              <image class=\"bg-image\" src=\"/static/home-bg.png\" mode=\"widthFix\"></image>\r\n          </view>\r\n          \r\n          <!-- 用户信息卡片 -->\r\n          <view class=\"user-info-card\">\r\n              <view class=\"greeting-section\">\r\n                  <view class=\"greeting-text\">\r\n                      <text class=\"hi-text\">Hi</text>\r\n                      <text class=\"username\">{{ userInfo && userInfo.name ? userInfo.name : '微信用户' }}</text>\r\n                  </view>\r\n                  <!-- 已登录显示会员码按钮 -->\r\n                  <view v-if=\"userInfo && userInfo.id\" class=\"member-section\">\r\n                      <view class=\"member-code-btn\" @click=\"goMemberCode\">查看会员码</view>\r\n                  </view>\r\n                  <!-- 未登录显示登录按钮 -->\r\n                  <view v-else class=\"login-section\">\r\n                      <view class=\"login-btn\" @click=\"goLogin\">登录</view>\r\n                  </view>\r\n                  <!-- 注释：优惠券积分信息\r\n                  <text v-if=\"userInfo && userInfo.id\" class=\"coupon-info\">优惠卷：{{ userInfo.couponNum ? userInfo.couponNum : '***' }}张 积分：{{ userInfo.point ? userInfo.point : '***' }}个</text>\r\n                  -->\r\n              </view>\r\n          </view>\r\n          \r\n          <!-- 服务选择区域 -->\r\n          <view class=\"service-selection\">\r\n              <view class=\"service-options\">\r\n                  <view class=\"service-item pickup-service\" @click=\"goPickup\">\r\n                      <text class=\"service-title\">到店自取</text>\r\n                      <text class=\"service-subtitle\">PICK UP</text>\r\n                      <view class=\"service-logo\">\r\n                          <text class=\"logo-text\">HATEA</text>\r\n                      </view>\r\n                      <text class=\"service-desc\">提前下单免排队</text>\r\n                  </view>\r\n                  <view class=\"service-item delivery-service\" @click=\"goDelivery\">\r\n                      <text class=\"service-title\">外送到家</text>\r\n                      <text class=\"service-subtitle\">DELIVERY</text>\r\n                      <view class=\"service-logo delivery-logo\">\r\n                          <text class=\"logo-text\">HATEA</text>\r\n                      </view>\r\n                      <text class=\"service-desc\">满￥50免配送费</text>\r\n                  </view>\r\n              </view>\r\n          </view>\r\n          \r\n      </view>\r\n      \r\n\t  <HomeTakeCoupon v-if=\"hasClaimedCoupon == false\" @close=\"handleCloseCouponDialog\" />\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import { setCartTabBadge, showMessage } from '@/utils/app'\r\n  import Empty from '@/components/empty'\r\n  import HomeBanner from \"./components/HomeBanner.vue\"\r\n  import HomeService from \"./components/HomeService.vue\"\r\n  import HomeTakeCoupon from \"./components/HomeTakeCoupon.vue\"\r\n  import HomeUser from \"./components/HomeUser.vue\"\r\n  import HomeNav from \"./components/HomeNav.vue\"\r\n  import HomeAds from \"./components/HomeAds.vue\"\r\n  import * as settingApi from '@/api/setting'\r\n  import * as Api from '@/api/page'\r\n  import * as UserApi from '@/api/user'\r\n  import * as CouponApi from '@/api/coupon'\r\n  import MescrollCompMixin from \"@/components/mescroll-uni/mixins/mescroll-comp.js\"\r\nimport storage from '@/utils/storage'\r\n\r\n  const App = getApp()\r\n  \r\n  export default {\r\n    mixins: [MescrollCompMixin],\r\n    components: {\r\n       Empty,\r\n       HomeBanner,\r\n       HomeService,\r\n       HomeTakeCoupon,\r\n       HomeUser,\r\n       HomeNav,\r\n       HomeAds\r\n    },\r\n    data() {\r\n      return {\r\n        banner: [],\r\n        ads: [],\r\n        storeInfo: null,\r\n        userInfo: {},\r\n\t\t    hasClaimedCoupon: true,\r\n        isReflash: false,\r\n        isLoading: false\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 生命周期函数--监听页面加载\r\n     */\r\n    onLoad({ storeId }) {\r\n      storeId = storeId ? parseInt(storeId) : 0;\r\n      if (storeId > 0) {\r\n          uni.setStorageSync('storeId', storeId);\r\n          uni.setStorageSync(\"reflashHomeData\", true);\r\n      } else {\r\n          this.getPageData();\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 生命周期函数--监听页面显示\r\n     */\r\n    onShow() {\r\n      const app = this;\r\n      showMessage();\r\n      setCartTabBadge();\r\n      app.onGetStoreInfo();\r\n      app.getUserInfo();\r\n      uni.getLocation({\r\n          type: 'gcj02',\r\n          success(res){ \r\n              uni.setStorageSync('latitude', res.latitude);\r\n              uni.setStorageSync('longitude', res.longitude);\r\n              app.onGetStoreInfo();\r\n          },\r\n          fail(e) {\r\n             // empty\r\n          }\r\n      })\r\n    },\r\n\r\n    methods: {\r\n        handleCloseCouponDialog() {\r\n   this.hasClaimedCoupon = true;\r\n  },\r\n        \r\n        // 登录方法\r\n        goLogin() {\r\n            this.$navTo('pages/login/index');\r\n        },\r\n        \r\n        // 查看会员码\r\n        goMemberCode() {\r\n            if (this.userInfo && this.userInfo.id) {\r\n                this.$navTo('pages/user/code', { userId: this.userInfo.id });\r\n            }\r\n        },\r\n        \r\n        // 服务导航方法\r\n        goPickup() {\r\n            // 保存当前的orderMode到本地存储\r\n            storage.set('current_order_mode', 'oneself')\r\n            this.$navTo('pages/category/index', { type: 'oneself' });\r\n        },\r\n        \r\n        goDelivery() {\r\n            \r\n            // 保存当前的orderMode到本地存储\r\n            storage.set('current_order_mode', 'express')\r\n            this.$navTo('pages/category/index', { type: 'express' });\r\n        },\r\n        /**\r\n         * 加载页面数据\r\n         * @param {Object} callback\r\n         */\r\n        getPageData(callback) {\r\n          const app = this;\r\n          Api.home()\r\n            .then(result => {\r\n                 app.banner = result.data.banner;\r\n                 app.ads = result.data.ads;\r\n                 uni.removeStorageSync(\"reflashHomeData\");\r\n                 app.isReflash = false;\r\n            })\r\n            .finally(() => callback && callback())\r\n        },\r\n        \r\n        /**\r\n         * 获取用户信息\r\n         * */\r\n        async getUserInfo() { \r\n          const rev1 = await UserApi.info()\r\n          this.userInfo = rev1.data?.userInfo ? rev1.data?.userInfo : {};\r\n          console.log(' this.userInfo :>> ',  this.userInfo);\r\n          if( this.userInfo.id){\r\n            \r\n            const rev2 = await CouponApi.hasClaimedCoupon()\r\n            this.hasClaimedCoupon = rev2.data ?? true;\r\n          }\r\n\t\t  \t\t  \r\n        },\r\n        \r\n        /**\r\n         * 下拉刷新\r\n         */\r\n        onPullDownRefresh() {\r\n          // 获取数据\r\n          this.getUserInfo();\r\n          this.getPageData(() => {\r\n             uni.stopPullDownRefresh()\r\n          })\r\n        },\r\n        \r\n        /**\r\n         * 获取默认店铺\r\n         * */\r\n         onGetStoreInfo() {\r\n            const app = this;\r\n            settingApi.systemConfig()\r\n             .then(result => {\r\n                 app.storeInfo = result.data.storeInfo;\r\n                 if (app.storeInfo) {\r\n                     uni.setStorageSync(\"storeId\", app.storeInfo.id);\r\n                     uni.setStorageSync(\"merchantNo\", app.storeInfo.merchantNo);\r\n                     // 判断是否需要更新页面\r\n                     let isReflash = uni.getStorageSync(\"reflashHomeData\");\r\n                     app.isReflash = isReflash;\r\n                     if (isReflash === true) {\r\n                         app.getPageData();\r\n                     }\r\n                 }\r\n             })\r\n         }\r\n    },\r\n\r\n    /**\r\n     * 分享当前页面\r\n     */\r\n    onShareAppMessage() {\r\n      const app = this\r\n      return {\r\n         title: \"fuint点餐系统\",\r\n         path: \"/pages/index/index?\" + app.$getShareUrlParams()\r\n      }\r\n    },\r\n\r\n    /**\r\n     * 分享到朋友圈\r\n     * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)\r\n     * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html\r\n     */\r\n    onShareTimeline() {\r\n      const app = this\r\n      const { page } = app\r\n      return {\r\n        title: page.params.share_title,\r\n        path: \"/pages/index/index?\" + app.$getShareUrlParams()\r\n      }\r\n    }\r\n\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.container {\r\n    background-color: #f7f7f7;\r\n    min-height: 100vh;\r\n    overflow: hidden;\r\n}\r\n\r\n.new-home-layout {\r\n    width: 100%;\r\n    min-height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n}\r\n\r\n.top-bg-section {\r\n    width: 100%;\r\n}\r\n\r\n.bg-image {\r\n    width: 100%;\r\n    height: auto;\r\n    display: block;\r\n}\r\n\r\n.user-info-card {\r\n    margin: -88rpx 20rpx 0;\r\n    padding: 28rpx 32rpx 40rpx 48rpx;\r\n    background-image: url('/static/user-info-bg.png');\r\n    background-size: 100% 100%;\r\n    background-repeat: no-repeat;\r\n    min-height: 120rpx;\r\n}\r\n\r\n.greeting-section {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: baseline;\r\n}\r\n\r\n.greeting-text {\r\n    display: flex;\r\n    align-items: baseline;\r\n}\r\n\r\n.hi-text {\r\n    color: #333333;\r\n    font-size: 38rpx;\r\n    font-weight: normal;\r\n    line-height: 29rpx;\r\n}\r\n\r\n.username {\r\n    color: #253557;\r\n    font-size: 30rpx;\r\n    margin-left: 22rpx;\r\n    line-height: 28rpx;\r\n}\r\n\r\n.coupon-info {\r\n    color: #DEA349;\r\n    font-size: 24rpx;\r\n    line-height: 24rpx;\r\n    width: 304rpx;\r\n}\r\n\r\n.member-section {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.member-code-btn {\r\n    background-color: #DEA349;\r\n    color: #ffffff;\r\n    font-size: 24rpx;\r\n    padding: 8rpx 24rpx;\r\n    border-radius: 20rpx;\r\n    text-align: center;\r\n}\r\n\r\n.login-section {\r\n    display: flex;\r\n    align-items: center;\r\n}\r\n\r\n.login-btn {\r\n    background-color: #DEA349;\r\n    color: #ffffff;\r\n    font-size: 24rpx;\r\n    padding: 8rpx 24rpx;\r\n    border-radius: 20rpx;\r\n    text-align: center;\r\n}\r\n\r\n.service-selection {\r\n    margin: 30rpx 20rpx;\r\n    padding: 30rpx 0;\r\n    background-image: url('/static/service-bg.png');\r\n    background-size: 100% 100%;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n    min-height: 300rpx;\r\n}\r\n\r\n.service-options {\r\n    display: flex;\r\n    padding: 0 20rpx;\r\n    height: 260rpx;\r\n}\r\n\r\n.service-item {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    align-items: center;\r\n    padding: 20rpx 0;\r\n}\r\n\r\n.pickup-service {\r\n    border-right: solid 2rpx rgba(153, 153, 153, 0.6);\r\n    padding-right: 20rpx;\r\n}\r\n\r\n.delivery-service {\r\n    padding-left: 20rpx;\r\n}\r\n\r\n.service-title {\r\n    font-size: 40rpx;\r\n    font-weight: bold;\r\n    line-height: 38rpx;\r\n    color: #333333;\r\n}\r\n\r\n.service-subtitle {\r\n    font-size: 18rpx;\r\n    line-height: 14rpx;\r\n    color: #000000;\r\n    margin-top: 18rpx;\r\n}\r\n\r\n.service-logo {\r\n    padding: 40rpx 0 20rpx;\r\n    background-image: url('/static/pickup-logo-bg.png');\r\n    background-size: 100% 100%;\r\n    background-repeat: no-repeat;\r\n    width: 110rpx;\r\n    height: 80rpx;\r\n    margin: 20rpx 0;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n}\r\n\r\n.delivery-logo {\r\n    background-image: url('/static/delivery-logo-bg.png');\r\n    width: 122rpx;\r\n}\r\n\r\n.logo-text {\r\n    font-size: 12rpx;\r\n    letter-spacing: 0;\r\n    line-height: 9rpx;\r\n    color: #223271;\r\n}\r\n\r\n.service-desc {\r\n    color: #000000;\r\n    font-size: 18rpx;\r\n    line-height: 17rpx;\r\n    margin-top: 15rpx;\r\n    text-align: center;\r\n}\r\n\r\n</style>\r\n"], "sourceRoot": ""}