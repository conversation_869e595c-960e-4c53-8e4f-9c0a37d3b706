@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.goods-container .diy-goods .goods-list.data-v-672a4bd8 {
  padding: 4rpx;
  box-sizing: border-box;
}
.goods-container .diy-goods .goods-list .goods-item.data-v-672a4bd8 {
  box-sizing: border-box;
  padding: 6rpx;
}
.goods-container .diy-goods .goods-list .goods-item .goods-image.data-v-672a4bd8 {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 100%;
  overflow: hidden;
  background: #fff;
}
.goods-container .diy-goods .goods-list .goods-item .goods-image.data-v-672a4bd8:after {
  content: "";
  display: block;
  margin-top: 100%;
}
.goods-container .diy-goods .goods-list .goods-item .goods-image .image.data-v-672a4bd8 {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  object-fit: cover;
}
.goods-container .diy-goods .goods-list .goods-item .detail.data-v-672a4bd8 {
  padding: 8rpx;
  background: #fff;
}
.goods-container .diy-goods .goods-list .goods-item .detail .goods-name.data-v-672a4bd8 {
  height: 64rpx;
  line-height: 1.3;
  white-space: normal;
  color: #484848;
  font-size: 26rpx;
  font-weight: bold;
}
.goods-container .diy-goods .goods-list .goods-item .detail .detail-price .goods-price.data-v-672a4bd8 {
  margin-right: 8rpx;
}
.goods-container .diy-goods .goods-list .goods-item .detail .detail-price .line-price.data-v-672a4bd8 {
  text-decoration: line-through;
}
.goods-container .diy-goods .goods-list.display__slide.data-v-672a4bd8 {
  white-space: nowrap;
  font-size: 0;
}
.goods-container .diy-goods .goods-list.display__slide .goods-item.data-v-672a4bd8 {
  display: inline-block;
}
.goods-container .diy-goods .goods-list.display__list .goods-item.data-v-672a4bd8 {
  float: left;
}
.goods-container .diy-goods .goods-list.column__2 .goods-item.data-v-672a4bd8 {
  width: 50%;
}
.goods-container .diy-goods .goods-list.column__3 .goods-item.data-v-672a4bd8 {
  width: 33.33333%;
}
.goods-container .diy-goods .goods-list.column__1 .goods-item.data-v-672a4bd8 {
  width: 100%;
  height: 250rpx;
  margin-bottom: 10rpx;
  padding: 20rpx;
  box-sizing: border-box;
  background: #fff;
  line-height: 1.6;
  border: 1rpx #F5f5f5 solid;
}
.goods-container .diy-goods .goods-list.column__1 .goods-item.data-v-672a4bd8:last-child {
  margin-bottom: 0;
}
.goods-container .diy-goods .goods-list.column__1 .goods-item_left.data-v-672a4bd8 {
  display: flex;
  width: 40%;
  background: #fff;
  align-items: center;
}
.goods-container .diy-goods .goods-list.column__1 .goods-item_left .image.data-v-672a4bd8 {
  display: block;
  width: 220rpx;
  height: 200rpx;
  border-radius: 10rpx;
  border: 1rpx #cccccc solid;
}
.goods-container .diy-goods .goods-list.column__1 .goods-item_right.data-v-672a4bd8 {
  position: relative;
  width: 60%;
}
.goods-container .diy-goods .goods-list.column__1 .goods-item_right .goods-name.data-v-672a4bd8 {
  margin-top: 20rpx;
  max-height: 69rpx;
  line-height: 1.3;
  white-space: normal;
  color: #484848;
  font-size: 30rpx;
  font-weight: bold;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.goods-container .diy-goods .goods-list.column__1 .goods-item_desc.data-v-672a4bd8 {
  margin-top: 0rpx;
}
.goods-container .diy-goods .goods-list.column__1 .desc-selling_point.data-v-672a4bd8 {
  width: 400rpx;
  font-size: 24rpx;
  color: #e49a3d;
}
.goods-container .diy-goods .goods-list.column__1 .desc-goods_sales.data-v-672a4bd8 {
  color: #999;
  font-size: 24rpx;
}
.goods-container .diy-goods .goods-list.column__1 .desc_footer.data-v-672a4bd8 {
  font-size: 24rpx;
}
.goods-container .diy-goods .goods-list.column__1 .desc_footer .price_x.data-v-672a4bd8 {
  margin-right: 16rpx;
  color: #f03c3c;
  font-size: 33rpx;
  font-weight: bold;
}
.goods-container .diy-goods .goods-list.column__1 .desc_footer .price_y.data-v-672a4bd8 {
  text-decoration: line-through;
}
.goods-container .diy-goods .goods-list.column__1 .desc_footer .buy-now.data-v-672a4bd8 {
  color: #FFFFFF;
  float: right;
  margin-right: 20rpx;
  border: solid 1rpx #3f51b5;
  background: #3f51b5;
  padding: 8rpx 20rpx 8rpx 20rpx;
  border-radius: 5rpx;
  display: block;
}
