{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/mescroll-body.vue?560f", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/mescroll-body.vue?404b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/mescroll-body.vue?22b7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/mescroll-body.vue?bcf2", "uni-app:///components/mescroll-uni/mescroll-body.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/mescroll-body.vue?e724", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/mescroll-body.vue?f1ad", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/wxs/wxs.wxs?0669", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/mescroll-uni/wxs/wxs.wxs?a41c"], "names": ["mixins", "components", "MescrollEmpty", "MescrollTop", "data", "mescroll", "optDown", "optUp", "downHight", "downRate", "downLoadType", "upLoadType", "isShowEmpty", "isShowToTop", "windowHeight", "windowBottom", "statusBarHeight", "props", "down", "up", "top", "topbar", "bottom", "safearea", "height", "bottombar", "type", "default", "sticky", "computed", "minHeight", "numTop", "padTop", "numBottom", "padBottom", "isDownReset", "transition", "translateY", "isDownLoading", "downRotate", "downText", "methods", "toPx", "num", "emptyClick", "toTopClick", "created", "inOffset", "vm", "outOffset", "onMoving", "showLoading", "beforeEndDownScroll", "endDownScroll", "clearTimeout", "callback", "showNoMore", "hideUpScroll", "empty", "onShow", "toTop", "MeScroll", "setTimeout", "selector", "uni", "scrollTop", "duration", "console"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0c;AAC1c;AACiE;AACL;AACa;;;AAGzE;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,waAAM;AACR,EAAE,ibAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4aAAU;AACZ;AACA;;AAEA;AAC8M;AAC9M,WAAW,gOAAM,iBAAiB,wOAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkpB,CAAgB,gpBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+EtqB;AAEA;AAMA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA;AAEA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eASA;EACAA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;QAAAC;QAAAC;MAAA;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;IAAA;IACAC;MAAA;MACAC;MACAC;IACA;IACAC;EACA;;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACA;QACA;UACA;YACA;YACAC;UACA;YACA;YACAA;UACA;YACA;YACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACA;EACAC;IACA;IAEA;MACA;MACA5B;QACA6B;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACA;UACAF;UACAA;QACA;QACAG;UACAH;UACAA;QACA;QACAI;UACAJ;UACA;QACA;QACAK;UACAL;UACAA;UACA;YAAAM;YAAAN;UAAA;UACAA;YAAA;YACA;UACA;QACA;QACA;QACAO;UACAP;QACA;MACA;MACA;MACA7B;QACA;QACAgC;UACAH;QACA;QACA;QACAQ;UACAR;QACA;QACA;QACAS;UACAT;QACA;QACA;QACAU;UACAC;YACA;YACAX;UACA;QACA;QACA;QACAY;UACAD;YACA;YACAX;UACA;QACA;QACA;QACAO;UACAP;QACA;MACA;IACA;IAEAa;IACA;MAAA3C;MAAAC;IAAA;IACA0C;;IAEA;IACAb;IACA;IACAA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACAA;;IAEA;IACAA;MACA;QACA;QACAc;UAAA;UACA;UACA;YACAC;UACA;YACAA;UAMA;UACAC;YACA;cACA;cACA5C;cACA4C;gBACAC;gBACAC;cACA;YACA;cACAC;YACA;UACA;QACA;MACA;QACA;QACAH;UACAC;UACAC;QACA;MACA;IACA;;IAEA;IACA;MACAlB;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpVA;AAAA;AAAA;AAAA;AAAq7B,CAAgB,+4BAAG,EAAC,C;;;;;;;;;;;ACAz8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAAoV,CAAgB,kZAAG,EAAC,C;;;;;;;;;;;;ACAxW;AAAe;AACf;AACA;AACA;AACA;AACA,M", "file": "components/mescroll-uni/mescroll-body.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mescroll-body.vue?vue&type=template&id=5eb4c084&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzU3MCwiYXR0cnMiOnsic3JjIjoiLi93eHMvd3hzLnd4cyIsIm1vZHVsZSI6Ind4c0JpeiIsImxhbmciOiJ3eHMifSwiZW5kIjozNTcwfSwicmVuZGVyQml6Ijp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzY5OCwiYXR0cnMiOnsibW9kdWxlIjoicmVuZGVyQml6IiwibGFuZyI6ImpzIn0sImVuZCI6MzgwMX19&\"\nvar renderjs\nimport script from \"./mescroll-body.vue?vue&type=script&lang=js&\"\nexport * from \"./mescroll-body.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mescroll-body.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./wxs/wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cworkspace%5CfuintFoodSystem%5CfuintUniapp%5Ccomponents%5Cmescroll-uni%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"components/mescroll-uni/mescroll-body.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=template&id=5eb4c084&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzU3MCwiYXR0cnMiOnsic3JjIjoiLi93eHMvd3hzLnd4cyIsIm1vZHVsZSI6Ind4c0JpeiIsImxhbmciOiJ3eHMifSwiZW5kIjozNTcwfSwicmVuZGVyQml6Ijp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzY5OCwiYXR0cnMiOnsibW9kdWxlIjoicmVuZGVyQml6IiwibGFuZyI6ImpzIn0sImVuZCI6MzgwMX19&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=script&lang=js&\"", "<template>\n    <view \n    class=\"mescroll-body mescroll-render-touch\" \n    :class=\"{'mescorll-sticky': sticky}\"\n    :style=\"{'minHeight':minHeight, 'padding-top': padTop, 'padding-bottom': padBottom}\" \n    @touchstart=\"wxsBiz.touchstartEvent\" \n    @touchmove=\"wxsBiz.touchmoveEvent\" \n    @touchend=\"wxsBiz.touchendEvent\" \n    @touchcancel=\"wxsBiz.touchendEvent\"\n    :change:prop=\"wxsBiz.propObserver\"\n    :prop=\"wxsProp\"\n    >\n        <!-- 状态栏 -->\n        <view v-if=\"topbar&&statusBarHeight\" class=\"mescroll-topbar\" :style=\"{height: statusBarHeight+'px', background: topbar}\"></view>\n        \n        <view class=\"mescroll-body-content mescroll-wxs-content\" :style=\"{ transform: translateY, transition: transition }\" :change:prop=\"wxsBiz.callObserver\" :prop=\"callProp\">\n            <!-- 下拉加载区域 (支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-down组件实现)-->\n            <!-- <mescroll-down :option=\"mescroll.optDown\" :type=\"downLoadType\" :rate=\"downRate\"></mescroll-down> -->\n            <view v-if=\"mescroll.optDown.use\" class=\"mescroll-downwarp\" :style=\"{'background':mescroll.optDown.bgColor,'color':mescroll.optDown.textColor}\">\n                <view class=\"downwarp-content\">\n                    <view class=\"downwarp-progress mescroll-wxs-progress\" :class=\"{'mescroll-rotate': isDownLoading}\" :style=\"{'border-color':mescroll.optDown.textColor, 'transform': downRotate}\"></view>\n                    <view class=\"downwarp-tip\">{{downText}}</view>\n                </view>\n            </view>\n    \n            <!-- 列表内容 -->\n            <slot></slot>\n\n            <!-- 空布局 -->\n            <mescroll-empty v-if=\"isShowEmpty\" :option=\"mescroll.optUp.empty\" @emptyclick=\"emptyClick\"></mescroll-empty>\n\n            <!-- 上拉加载区域 (下拉刷新时不显示, 支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-up组件实现)-->\n            <!-- <mescroll-up v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==3\" :option=\"mescroll.optUp\" :type=\"upLoadType\"></mescroll-up> -->\n            <view v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==3\" class=\"mescroll-upwarp\" :style=\"{'background':mescroll.optUp.bgColor,'color':mescroll.optUp.textColor}\">\n                <!-- 加载中 (此处不能用v-if,否则android小程序快速上拉可能会不断触发上拉回调) -->\n                <view v-show=\"upLoadType===1\">\n                    <view class=\"upwarp-progress mescroll-rotate\" :style=\"{'border-color':mescroll.optUp.textColor}\"></view>\n                    <view class=\"upwarp-tip\">{{ mescroll.optUp.textLoading }}</view>\n                </view>\n                <!-- 无数据 -->\n                <view v-if=\"upLoadType===2\" class=\"upwarp-nodata\">{{ mescroll.optUp.textNoMore }}</view>\n            </view>\n        </view>\n        \n        <!-- 底部是否偏移TabBar的高度(默认仅在H5端的tab页生效) -->\n        <!-- #ifdef H5 -->\n        <view v-if=\"bottombar && windowBottom>0\" class=\"mescroll-bottombar\" :style=\"{height: windowBottom+'px'}\"></view>\n        <!-- #endif -->\n        \n        <!-- 适配iPhoneX -->\n        <view v-if=\"safearea\" class=\"mescroll-safearea\"></view>\n        \n        <!-- 回到顶部按钮 (fixed元素需写在transform外面,防止降级为absolute)-->\n        <mescroll-top v-model=\"isShowToTop\" :option=\"mescroll.optUp.toTop\" @click=\"toTopClick\"></mescroll-top>\n        \n        <!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\n        <!-- renderjs的数据载体,不可写在mescroll-downwarp内部,避免use为false时,载体丢失,无法更新数据 -->\n        <view :change:prop=\"renderBiz.propObserver\" :prop=\"wxsProp\"></view>\n        <!-- #endif -->\n    </view>\n</template>\n\n<!-- 微信小程序, QQ小程序, app, h5使用wxs -->\n<!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\n<script src=\"./wxs/wxs.wxs\" module=\"wxsBiz\" lang=\"wxs\"></script>\n<!-- #endif -->\n\n<!-- app, h5使用renderjs -->\n<!-- #ifdef APP-PLUS || H5 -->\n<script module=\"renderBiz\" lang=\"renderjs\">\n    import renderBiz from './wxs/renderjs.js';\n    export default {\n        mixins: [renderBiz]\n    }\n</script>\n<!-- #endif -->\n\n<script>\n    // 引入mescroll-uni.js,处理核心逻辑\n    import MeScroll from './mescroll-uni.js';\n    // 引入全局配置\n    import GlobalOption from './mescroll-uni-option.js';\n    // 引入空布局组件\n    import MescrollEmpty from './components/mescroll-empty.vue';\n    // 引入回到顶部组件\n    import MescrollTop from './components/mescroll-top.vue';\n    // 引入兼容wxs(含renderjs)写法的mixins\n    import WxsMixin from './wxs/mixins.js';\n    \n    export default {\n        mixins: [WxsMixin],\n        components: {\n            MescrollEmpty,\n            MescrollTop\n        },\n        data() {\n            return {\n                mescroll: {optDown:{},optUp:{}}, // mescroll实例\n                downHight: 0, //下拉刷新: 容器高度\n                downRate: 0, // 下拉比率(inOffset: rate<1; outOffset: rate>=1)\n                downLoadType: 0, // 下拉刷新状态: 0(loading前), 1(inOffset), 2(outOffset), 3(showLoading), 4(endDownScroll)\n                upLoadType: 0, // 上拉加载状态：0（loading前），1（loading中），2（没有更多了,显示END文本提示），3（没有更多了,不显示END文本提示）\n                isShowEmpty: false, // 是否显示空布局\n                isShowToTop: false, // 是否显示回到顶部按钮\n                windowHeight: 0, // 可使用窗口的高度\n                windowBottom: 0, // 可使用窗口的底部位置\n                statusBarHeight: 0 // 状态栏高度\n            };\n        },\n        props: {\n            down: Object, // 下拉刷新的参数配置\n            up: Object, // 上拉加载的参数配置\n            top: [String, Number], // 下拉布局往下的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\n            topbar: [Boolean, String], // top的偏移量是否加上状态栏高度, 默认false (使用场景:取消原生导航栏时,配置此项可留出状态栏的占位, 支持传入字符串背景,如色值,背景图,渐变)\n            bottom: [String, Number], // 上拉布局往上的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\n            safearea: Boolean, // bottom的偏移量是否加上底部安全区的距离, 默认false (需要适配iPhoneX时使用)\n            height: [String, Number], // 指定mescroll最小高度,默认windowHeight,使列表不满屏仍可下拉\n            bottombar:{ // 底部是否偏移TabBar的高度(默认仅在H5端的tab页生效)\n                type: Boolean,\n                default: true\n            },\n            sticky: Boolean // 是否支持sticky,默认false; 当值配置true时,需避免在mescroll-body标签前面加非定位的元素,否则下拉区域无法会隐藏\n        },\n        computed: {\n            // mescroll最小高度,默认windowHeight,使列表不满屏仍可下拉\n            minHeight(){\n                return this.toPx(this.height || '100%') + 'px'\n            },\n            // 下拉布局往下偏移的距离 (px)\n            numTop() {\n                return this.toPx(this.top)\n            },\n            padTop() {\n                return this.numTop + 'px';\n            },\n            // 上拉布局往上偏移 (px)\n            numBottom() {\n                return this.toPx(this.bottom);\n            },\n            padBottom() {\n                return this.numBottom + 'px';\n            },\n            // 是否为重置下拉的状态\n            isDownReset() {\n                return this.downLoadType === 3 || this.downLoadType === 4;\n            },\n            // 过渡\n            transition() {\n                return this.isDownReset ? 'transform 300ms' : '';\n            },\n            translateY() {\n                return this.downHight > 0 ? 'translateY(' + this.downHight + 'px)' : ''; // transform会使fixed失效,需注意把fixed元素写在mescroll之外\n            },\n            // 是否在加载中\n            isDownLoading(){\n                return this.downLoadType === 3\n            },\n            // 旋转的角度\n            downRotate(){\n                return 'rotate(' + 360 * this.downRate + 'deg)'\n            },\n            // 文本提示\n            downText(){\n                if(!this.mescroll) return \"\"; // 避免头条小程序初始化时报错\n                switch (this.downLoadType){\n                    case 1: return this.mescroll.optDown.textInOffset;\n                    case 2: return this.mescroll.optDown.textOutOffset;\n                    case 3: return this.mescroll.optDown.textLoading;\n                    case 4: return this.mescroll.isDownEndSuccess ? this.mescroll.optDown.textSuccess : this.mescroll.isDownEndSuccess==false ? this.mescroll.optDown.textErr : this.mescroll.optDown.textInOffset;\n                    default: return this.mescroll.optDown.textInOffset;\n                }\n            }\n        },\n        methods: {\n            //number,rpx,upx,px,% --> px的数值\n            toPx(num) {\n                if (typeof num === 'string') {\n                    if (num.indexOf('px') !== -1) {\n                        if (num.indexOf('rpx') !== -1) {\n                            // \"10rpx\"\n                            num = num.replace('rpx', '');\n                        } else if (num.indexOf('upx') !== -1) {\n                            // \"10upx\"\n                            num = num.replace('upx', '');\n                        } else {\n                            // \"10px\"\n                            return Number(num.replace('px', ''));\n                        }\n                    } else if (num.indexOf('%') !== -1) {\n                        // 传百分比,则相对于windowHeight,传\"10%\"则等于windowHeight的10%\n                        let rate = Number(num.replace('%', '')) / 100;\n                        return this.windowHeight * rate;\n                    }\n                }\n                return num ? uni.upx2px(Number(num)) : 0;\n            },\n            // 点击空布局的按钮回调\n            emptyClick() {\n                this.$emit('emptyclick', this.mescroll);\n            },\n            // 点击回到顶部的按钮回调\n            toTopClick() {\n                this.mescroll.scrollTo(0, this.mescroll.optUp.toTop.duration); // 执行回到顶部\n                this.$emit('topclick', this.mescroll); // 派发点击回到顶部按钮的回调\n            }\n        },\n        // 使用created初始化mescroll对象; 如果用mounted部分css样式编译到H5会失效\n        created() {\n            let vm = this;\n\n            let diyOption = {\n                // 下拉刷新的配置\n                down: {\n                    inOffset() {\n                        vm.downLoadType = 1; // 下拉的距离进入offset范围内那一刻的回调 (自定义mescroll组件时,此行不可删)\n                    },\n                    outOffset() {\n                        vm.downLoadType = 2; // 下拉的距离大于offset那一刻的回调 (自定义mescroll组件时,此行不可删)\n                    },\n                    onMoving(mescroll, rate, downHight) {\n                        // 下拉过程中的回调,滑动过程一直在执行;\n                        vm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\n                        vm.downRate = rate; //下拉比率 (inOffset: rate<1; outOffset: rate>=1)\n                    },\n                    showLoading(mescroll, downHight) {\n                        vm.downLoadType = 3; // 显示下拉刷新进度的回调 (自定义mescroll组件时,此行不可删)\n                        vm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\n                    },\n                    beforeEndDownScroll(mescroll){\n                        vm.downLoadType = 4; \n                        return mescroll.optDown.beforeEndDelay // 延时结束的时长\n                    },\n                    endDownScroll() {\n                        vm.downLoadType = 4; // 结束下拉 (自定义mescroll组件时,此行不可删)\n                        vm.downHight = 0; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\n                        if(vm.downResetTimer) {clearTimeout(vm.downResetTimer); vm.downResetTimer = null} // 移除重置倒计时\n                        vm.downResetTimer = setTimeout(()=>{ // 过渡动画执行完毕后,需重置为0的状态,避免下次inOffset不及时显示textInOffset\n                            if(vm.downLoadType === 4) vm.downLoadType = 0\n                        },300)\n                    },\n                    // 派发下拉刷新的回调\n                    callback: function(mescroll) {\n                        vm.$emit('down', mescroll);\n                    }\n                },\n                // 上拉加载的配置\n                up: {\n                    // 显示加载中的回调\n                    showLoading() {\n                        vm.upLoadType = 1;\n                    },\n                    // 显示无更多数据的回调\n                    showNoMore() {\n                        vm.upLoadType = 2;\n                    },\n                    // 隐藏上拉加载的回调\n                    hideUpScroll(mescroll) {\n                        vm.upLoadType = mescroll.optUp.hasNext ? 0 : 3;\n                    },\n                    // 空布局\n                    empty: {\n                        onShow(isShow) {\n                            // 显示隐藏的回调\n                            vm.isShowEmpty = isShow;\n                        }\n                    },\n                    // 回到顶部\n                    toTop: {\n                        onShow(isShow) {\n                            // 显示隐藏的回调\n                            vm.isShowToTop = isShow;\n                        }\n                    },\n                    // 派发上拉加载的回调\n                    callback: function(mescroll) {\n                        vm.$emit('up', mescroll);\n                    }\n                }\n            };\n\n            MeScroll.extend(diyOption, GlobalOption); // 混入全局的配置\n            let myOption = JSON.parse(JSON.stringify({down: vm.down,up: vm.up})); // 深拷贝,避免对props的影响\n            MeScroll.extend(myOption, diyOption); // 混入具体界面的配置\n\n            // 初始化MeScroll对象\n            vm.mescroll = new MeScroll(myOption, true); // 传入true,标记body为滚动区域\n            // init回调mescroll对象\n            vm.$emit('init', vm.mescroll);\n\n            // 设置高度\n            const sys = uni.getSystemInfoSync();\n            if (sys.windowHeight) vm.windowHeight = sys.windowHeight;\n            if (sys.windowBottom) vm.windowBottom = sys.windowBottom;\n            if (sys.statusBarHeight) vm.statusBarHeight = sys.statusBarHeight;\n            // 使down的bottomOffset生效\n            vm.mescroll.setBodyHeight(sys.windowHeight);\n\n            // 因为使用的是page的scroll,这里需自定义scrollTo\n            vm.mescroll.resetScrollTo((y, t) => {\n                if(typeof y === 'string'){\n                    // 滚动到指定view (y为css选择器)\n                    setTimeout(()=>{ // 延时确保view已渲染; 不使用$nextTick\n                        let selector;\n                        if(y.indexOf('#')==-1 && y.indexOf('.')==-1){\n                            selector = '#'+y // 不带#和. 则默认为id选择器\n                        }else{\n                            selector = y\n                            // #ifdef APP-PLUS || H5 || MP-ALIPAY || MP-DINGTALK\n                            if(y.indexOf('>>>')!=-1){ // 不支持跨自定义组件的后代选择器 (转为普通的选择器即可跨组件查询)\n                                selector = y.split('>>>')[1].trim()\n                            }\n                            // #endif\n                        }\n                        uni.createSelectorQuery().select(selector).boundingClientRect(function(rect){\n                            if (rect) {\n                                let top = rect.top\n                                top += vm.mescroll.getScrollTop()\n                                uni.pageScrollTo({\n                                    scrollTop: top,\n                                    duration: t\n                                })\n                            } else{\n                                console.error(selector + ' does not exist');\n                            }\n                        }).exec()\n                    },30)\n                } else{\n                    // 滚动到指定位置 (y必须为数字)\n                    uni.pageScrollTo({\n                        scrollTop: y,\n                        duration: t\n                    })\n                }\n            });\n\n            // 具体的界面如果不配置up.toTop.safearea,则取本vue的safearea值\n            if (vm.up && vm.up.toTop && vm.up.toTop.safearea != null) {} else {\n                vm.mescroll.optUp.toTop.safearea = vm.safearea;\n            }\n        }\n    };\n</script>\n\n<style>\n    @import \"./mescroll-body.css\";\n    @import \"./components/mescroll-down.css\";\n    @import './components/mescroll-up.css';\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mescroll-body.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426712\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cworkspace%5CfuintFoodSystem%5CfuintUniapp%5Ccomponents%5Cmescroll-uni%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-filter-loader/index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=D%3A%5Cworkspace%5CfuintFoodSystem%5CfuintUniapp%5Ccomponents%5Cmescroll-uni%5Cmescroll-body.vue&module=wxsBiz&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       Component.options.wxsCallMethods.push('wxsCall')\n     }"], "sourceRoot": ""}