{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-package-popup/index.vue?de60", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-package-popup/index.vue?9b3c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-package-popup/index.vue?c293", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-package-popup/index.vue?0d5f", "uni-app:///components/goods-package-popup/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-package-popup/index.vue?38c7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/goods-package-popup/index.vue?5977"], "names": ["name", "components", "NumberBox", "props", "value", "Type", "default", "goodsId", "mode", "maskCloseAble", "borderRadius", "goodsThumbName", "minBuyNum", "maxBuy<PERSON>um", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "goods", "priceColor", "buyNowText", "buyNowColor", "buyNowBackgroundColor", "addCartText", "addCartColor", "addCartBackgroundColor", "showClose", "closeImage", "defaultStock", "defaultPrice", "gradeInfo", "grade", "hafanInfo", "selected<PERSON><PERSON>", "data", "complete", "goodsInfo", "isShow", "selectNum", "selectedItems", "showSkuPopup", "mounted", "that", "computed", "hafanLevel", "isMemberPrice", "getSelectedSkuText", "isRequiredItemsSelected", "extraPrice", "total", "methods", "isItemSelected", "getItemQuantity", "selectOptionalItem", "selected", "reduce", "groupId", "groupName", "itemId", "itemName", "price", "quantity", "increaseItemQuantity", "decreaseItemQuantity", "init", "group", "open", "close", "moveHandle", "addCart", "success", "buyNow", "checkSelectComplete", "console", "baseItem", "goods_id", "package_items", "buy_num", "isPackage", "findItemGoods", "onShowSkuPopup", "findSkuItem", "toast", "uni", "title", "icon", "filters", "priceFilter", "n", "watch"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClFA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiJ9pB;AAAA,gBACA;EACAA;EACAC;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IAEAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACAqB;MACAtB;MACAC;QAAA;UACAsB;QACA;MAAA;IACA;IACAC;MACAxB;MACAC;IACA;IACAwB;MACAzB;MACAC;QAAA;MAAA;IACA;EACA;EACAyB;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACAC;IACA;MACAA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;;MAEA;MAAA,2CACA;QAAA;MAAA;QAAA;UAAA;UACA;YACA;YACA;cAAA;YAAA;;YAEA;YACA;cACA;gBAAA;cAAA;YACA;UACA;QAAA;QATA;UAAA;UAAA;QAUA;MAAA;QAAA;MAAA;QAAA;MAAA;MAEA;IACA;IAEA;IACAC;MACA;MAAA,4CACA;QAAA;MAAA;QAAA;UAAA;UACAC;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QAAA;MAAA;MACA;IACA;IAEA;IACAC;MAEA;MACA;QAAA,OACAC;MAAA,EACA;MAEA;QACA;QACA;MACA;QACA;QACA;UAAA;QAAA,GACAC;UAAA;QAAA;;QAEA;QACA;UACA;YACAC;YACAC;YACA/C;YACAgD;YACAC;YACAC;YACAC;UACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QAAA,OACAR;MAAA,EACA;MAEA;QACA;QACA;UAAA;QAAA,GACAC;UAAA;QAAA;;QAEA;QACA;UACA;QACA;UACA;QACA;MACA;IACA;IAEA;IACAQ;MACA;QAAA,OACAT;MAAA,EACA;MAEA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IAGA;IACAU;MACA;MACAtB;MACAA;;MAEA;MACA;QACAA;UACA;YACAuB;cACAvB;gBACAc;gBACAC;gBACA/C;gBACAgD;gBACAC;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;MAEAnB;MACAA;MACAA;IACA;IAEAwB;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAxB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAyB;MACA;QACAzB;QACAA;MACA;QACA;UACAA;UACAA;QACA;MACA;IACA;IACA0B;MACA;IAAA,CACA;IAEA;IACAC;MACA3B;QACA4B;UACA5B;QACA;MACA;IACA;IACA;IACA6B;MACA7B;QACA4B;UACA5B;QACA;MACA;IACA;IAEA;IACA8B;MAAA;MAAA;MACA;MACA;QACA;QACA;MACA;;MAEA;MACA;QAAA;MAAA;MACA;QACA;UAAA;QAAA;QACA;MACA;MACA;QACA;QACA;MACA;;MAEA;MAAA,4CACA;QAAA;MAAA;QAAA;UAAA;UAAA,4CACAP;YAAA;UAAA;YAAA;cAAA;cACA;cACA,2CACA;gBAAA;cAAA;gBACA;gBACA;gBACA;kBACA;kBACA;oBAAA;kBAAA;gBACA;cACA;YAAA;YAVA;cAAA;cAAA;YAWA;UAAA;YAAA;UAAA;YAAA;UAAA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACAQ;MACA;MACA;QACA;UACAjB;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;;QAEA;QACA;QACA;UACA;UACA;YACAa;YACAA;YACAA;UACA;QACA;QAEA;MACA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACAL;MAEA;IACA;IAEA;IACAM;MAAA,4CACA;QAAA;MAAA;QAAA;UAAA;UACA;YAAA;UAAA;UACA;YACA;UACA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA,4CACA;QAAA;MAAA;QAAA;UAAA;UAAA,4CACAhB;YAAA;UAAA;YAAA;cAAA;cACA;gBACA;cACA;YACA;UAAA;YAAA;UAAA;YAAA;UAAA;QACA;MAAA;QAAA;MAAA;QAAA;MAAA;MACA;IACA;IAEA;IACAiB;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACAC;MACA;MACA;IACA;EACA;EACAC;IACAlF;MACA;QACAmC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AClmBA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/goods-package-popup/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=6c4cc486&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=6c4cc486&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6c4cc486\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/goods-package-popup/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=6c4cc486&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var f0 = _vm._f(\"priceFilter\")(\n    (_vm.isMemberPrice ? _vm.goodsInfo.gradePrice : _vm.goodsInfo.price) ||\n      _vm.defaultPrice\n  )\n  var g0 =\n    !_vm.goodsInfo.packageGroups || _vm.goodsInfo.packageGroups.length === 0\n  var l1 = _vm.__map(_vm.goodsInfo.packageGroups, function (group, groupIndex) {\n    var $orig = _vm.__get_orig(group)\n    var l0 =\n      group.groupType === \"R\"\n        ? _vm.__map(group.items, function (item, itemIndex) {\n            var $orig = _vm.__get_orig(item)\n            var m0 = _vm.getSelectedSkuText(item.itemGoods.id)\n            var m1 = m0 ? _vm.getSelectedSkuText(item.itemGoods.id) : null\n            return {\n              $orig: $orig,\n              m0: m0,\n              m1: m1,\n            }\n          })\n        : null\n    return {\n      $orig: $orig,\n      l0: l0,\n    }\n  })\n  var l3 = _vm.__map(_vm.goodsInfo.packageGroups, function (group, groupIndex) {\n    var $orig = _vm.__get_orig(group)\n    var l2 =\n      group.groupType === \"O\"\n        ? _vm.__map(group.items, function (item, itemIndex) {\n            var $orig = _vm.__get_orig(item)\n            var m2 = _vm.isItemSelected(group.id, item.id)\n            var m3 = _vm.getSelectedSkuText(item.itemGoods.id)\n            var m4 = m3 ? _vm.getSelectedSkuText(item.itemGoods.id) : null\n            var m5 =\n              item.itemGoods.isSingleSpec === \"Y\"\n                ? _vm.isItemSelected(group.id, item.id)\n                : null\n            var m6 =\n              item.itemGoods.isSingleSpec === \"Y\" && m5\n                ? _vm.getItemQuantity(group.id, item.id)\n                : null\n            var m7 =\n              item.itemGoods.isSingleSpec === \"Y\" && !m5\n                ? _vm.isItemSelected(group.id, item.id)\n                : null\n            return {\n              $orig: $orig,\n              m2: m2,\n              m3: m3,\n              m4: m4,\n              m5: m5,\n              m6: m6,\n              m7: m7,\n            }\n          })\n        : null\n    return {\n      $orig: $orig,\n      l2: l2,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        f0: f0,\n        g0: g0,\n        l1: l1,\n        l3: l3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"goods-package-popup popup\" catchtouchmove=\"true\" :class=\"(value && complete) ? 'show' : 'none'\"\n    @touchmove.stop.prevent=\"moveHandle\">\n    <!-- 页面内容开始 -->\n    <view class=\"mask\" @click=\"close('mask')\"></view>\n    <!-- 页面开始 -->\n    <view class=\"layer attr-content\" :style=\"'border-radius: '+borderRadius+'rpx '+borderRadius+'rpx 0 0;'\">\n      <view class=\"specification-wrapper\">\n        <scroll-view class=\"specification-wrapper-content\" scroll-y=\"true\" style=\"height:70vh;\">\n          <view class=\"specification-header\">\n            <view class=\"specification-left\">\n              <image class=\"product-img\" :src=\"goodsInfo.goods_thumb\"\n                mode=\"aspectFill\"></image>\n            </view>\n            <view class=\"specification-right\">\n              <view class=\"price-content\" :style=\"'color: '+priceColor+' ;'\">\n                <text class=\"sign\">¥</text>\n                <text class=\"price\">{{ (isMemberPrice ? goodsInfo.gradePrice : goodsInfo.price) || defaultPrice | priceFilter }}</text>\n              </view>\n              <view class=\"choose\">\n                <text>{{ goodsInfo.name }}</text>\n              </view>\n            </view>\n          </view>\n\n          <view class=\"specification-content\">\n            <view v-if=\"!goodsInfo.packageGroups || goodsInfo.packageGroups.length === 0\" style=\"padding:20px; text-align:center; color:#999;\">\n              加载套餐信息中...\n            </view>\n            <!-- 套餐必选分组 -->\n            <view class=\"package-group\" v-for=\"(group, groupIndex) in goodsInfo.packageGroups\" :key=\"groupIndex\" v-if=\"group.groupType === 'R'\">\n              <view class=\"group-title\">\n                <text class=\"title-text\">{{ group.groupName }}</text>\n                <text class=\"required-tag\">(已包含)</text>\n              </view>\n              <view class=\"item-wrapper\">\n                <view class=\"package-item active\"\n                  v-for=\"(item, itemIndex) in group.items\"\n                  :key=\"itemIndex\">\n                  <view class=\"item-image\">\n                    <image :src=\"item.itemGoods.logo\" mode=\"aspectFill\"></image>\n                  </view>\n                  <view class=\"item-info\">\n                    <view class=\"item-name\">{{ item.itemGoods.name }}</view>\n                    <view class=\"item-spec-text\" v-if=\"getSelectedSkuText(item.itemGoods.id)\">\n                      <text>已选：{{ getSelectedSkuText(item.itemGoods.id) }}</text>\n                    </view>\n                    <view class=\"item-price\" v-if=\"item.extraPrice > 0\">\n                      <text>¥{{ item.extraPrice }}</text>\n                    </view>\n                  </view>\n                  <view v-if=\"item.itemGoods.isSingleSpec === 'Y'\" class=\"item-checkbox\">\n                    <text class=\"checkbox checked\"></text>\n                  </view>\n                  <view v-else class=\"item-spec\">\n                    <view class=\"select-spec\" @click.stop=\"onShowSkuPopup(item.itemGoods,group, item)\">选规格</view>\n                  </view>\n                </view>\n              </view>\n            </view>\n            \n            <!-- 套餐可选分组 -->\n            <view class=\"package-group\" v-for=\"(group, groupIndex) in goodsInfo.packageGroups\" :key=\"groupIndex\" v-if=\"group.groupType === 'O'\">\n              <view class=\"group-title\">\n                <text class=\"title-text\">{{ group.groupName }}</text>\n                <text class=\"optional-tag\">(可选)</text>\n                <text class=\"selection-rule\">{{ group.groupName ? '最多可选' + group.selectCount + '份' : '' }}</text>\n              </view>\n              <view class=\"item-wrapper\">\n                <view class=\"package-item\" \n                  v-for=\"(item, itemIndex) in group.items\" \n                  :key=\"itemIndex\"\n                  :class=\"[isItemSelected(group.id, item.id) ? 'active' : '']\"\n                  @tap=\"selectOptionalItem(group, item)\">\n                  <view class=\"item-image\">\n                    <image :src=\"item.itemGoods.logo\" mode=\"aspectFill\"></image>\n                  </view>\n                  <view class=\"item-info\">\n                    <view class=\"item-name\">{{ item.itemGoods.name }}</view>\n                    <view class=\"item-spec-text\" v-if=\"getSelectedSkuText(item.itemGoods.id)\">\n                      <text>已选：{{ getSelectedSkuText(item.itemGoods.id) }}</text>\n                    </view>\n                    <view class=\"item-price\" v-if=\"item.extraPrice > 0\">\n                      <text>+¥{{ item.extraPrice }}</text>\n                    </view>\n                  </view>\n                  <template v-if=\"item.itemGoods.isSingleSpec === 'Y'\">\n                    <view class=\"item-quantity\" v-if=\"isItemSelected(group.id, item.id)\">\n                      <view class=\"quantity-control\">\n                        <text class=\"minus\" @tap.stop=\"decreaseItemQuantity(group, item)\">-</text>\n                        <text class=\"value\">{{ getItemQuantity(group.id, item.id) }}</text>\n                        <text class=\"plus\" @tap.stop=\"increaseItemQuantity(group, item, group.selectCount)\">+</text>\n                      </view>\n                    </view>\n                    <view class=\"item-checkbox\" v-else>\n                      <text class=\"checkbox\" :class=\"[isItemSelected(group.id, item.id) ? 'checked' : '']\"></text>\n                    </view>\n                  </template>\n                  <view v-else class=\"item-spec\">\n                    <view class=\"select-spec\" @click.stop=\"onShowSkuPopup(item.itemGoods,group, item)\">选规格</view>\n                  </view>\n                </view>\n              </view>\n            </view>\n            \n            <view style=\"display: flex;\">\n              <view style=\"flex: 1;\">\n                <text style=\"font-size: 26rpx; color: #333; line-height: 50rpx;\">数量</text>\n              </view>\n              <view style=\"flex: 4;text-align: right;\">\n                <number-box :min=\"minBuyNum\" :max=\"maxBuyNum\" :step=\"stepBuyNum\" v-model=\"selectNum\"\n                  :positive-integer=\"true\">\n                </number-box>\n              </view>\n            </view>\n          </view>\n        </scroll-view>\n        <view class=\"close\" @click=\"close('close')\" v-if=\"showClose\">\n          <image class=\"close-item\" :src=\"closeImage\"></image>\n        </view>\n      </view>\n      <view class=\"btn-option\">\n          <view class=\"btn-wrapper\" v-if=\"!isRequiredItemsSelected || mode == 4\">\n            <view class=\"sure\" style=\"color:#ffffff;background-color:#cccccc\">请选择必选套餐项</view>\n          </view>\n          <view class=\"btn-wrapper\" v-else-if=\"mode == 1\">\n            <view class=\"sure add-cart\" style=\"border-radius:38rpx 0rpx 0rpx 38rpx;\" @click=\"addCart\" :style=\"'color:'+addCartColor+';background:'+addCartBackgroundColor\">{{ addCartText }}</view>\n            <view class=\"sure\" style=\"border-radius:0rpx 38rpx 38rpx 0rpx;\" @click=\"buyNow\" :style=\"'color:'+buyNowColor+';background-color:'+buyNowBackgroundColor\">{{ buyNowText }}</view>\n          </view>\n          <view class=\"btn-wrapper\" v-else-if=\"mode == 2\">\n            <view class=\"sure add-cart\" @click=\"addCart\" :style=\"'color:'+addCartColor+';background:'+addCartBackgroundColor\">{{ addCartText }}</view>\n          </view>\n          <view class=\"btn-wrapper\" v-else-if=\"mode == 3\">\n            <view class=\"sure\" @click=\"buyNow\" :style=\"'color:'+buyNowColor+';background:'+buyNowBackgroundColor\">{{ buyNowText }}</view>\n          </view>\n      </view>\n      <!-- 页面结束 -->\n    </view>\n    <!-- 页面内容结束 -->\n  </view>\n</template>\n\n<script>\n  import NumberBox from '../goods-sku-popup/number-box'\n\n  var that; // 当前页面对象\n  export default {\n    name: 'GoodsPackagePopup',\n    components: {\n      NumberBox\n    },\n    props: {\n      // true 组件显示 false 组件隐藏\n      value: {\n        Type: Boolean,\n        default: false\n      },\n      /* 商品id */\n      goodsId: {\n        Type: String,\n        default: \"\"\n      },\n      /* 模式 1:都显示  2:只显示购物车 3:只显示立即购买 4:显示缺货按钮 默认 1 */\n      mode: {\n        Type: Number,\n        default: 1\n      },\n      /* 点击遮罩是否关闭组件 true 关闭 false 不关闭 默认true */\n      maskCloseAble: {\n        Type: Boolean,\n        default: true\n      },\n      /* 顶部圆角值 */\n      borderRadius: {\n        Type: [String, Number],\n        default: 0\n      },\n      /* 商品缩略图字段名(未选择sku时) */\n      goodsThumbName: {\n        Type: [String],\n        default: \"goods_thumb\"\n      },\n      /* 最小购买数量 */\n      minBuyNum: {\n        Type: Number,\n        default: 1\n      },\n      /* 最大购买数量 */\n      maxBuyNum: {\n        Type: Number,\n        default: 100000\n      },\n      /* 每次点击后的数量 */\n      stepBuyNum: {\n        Type: Number,\n        default: 1\n      },\n      \n      goods: {\n        Type: Object,\n        default: null\n      },\n      /* 价格的字体颜色 */\n      priceColor: {\n        Type: String,\n        default: \"#fe560a\"\n      },\n      /* 立即购买按钮的文字 */\n      buyNowText: {\n        Type: String,\n        default: \"立即购买\"\n      },\n      /* 立即购买按钮的字体颜色 */\n      buyNowColor: {\n        Type: String,\n        default: \"#ffffff\"\n      },\n      /* 立即购买按钮的背景颜色 */\n      buyNowBackgroundColor: {\n        Type: String,\n       default: \"linear-gradient(to right, $fuint-theme, $fuint-theme)\"\n      },\n      /* 加入购物车按钮的文字 */\n      addCartText: {\n        Type: String,\n        default: \"加入购物车\"\n      },\n      /* 加入购物车按钮的字体颜色 */\n      addCartColor: {\n        Type: String,\n        default: \"#ffffff\"\n      },\n      /* 加入购物车按钮的背景颜色 */\n      addCartBackgroundColor: {\n        Type: String,\n        default: \"linear-gradient(to right, $fuint-theme, $fuint-theme)\"\n      },\n      /* 是否显示右上角关闭按钮 */\n      showClose: {\n        Type: Boolean,\n        default: true\n      },\n      /* 关闭按钮的图片地址 */\n      closeImage: {\n        Type: String,\n        default: \"https://img.alicdn.com/imgextra/i1/121022687/O1CN01ImN0O11VigqwzpLiK_!!121022687.png\"\n      },\n      /* 默认库存数量 (未选择sku时) */\n      defaultStock: {\n        Type: Number,\n        default: 0\n      },\n      /* 默认显示的价格 (未选择sku时) */\n      defaultPrice: {\n        Type: Number,\n        default: 0\n      },\n\t     gradeInfo: {\n\t        Type: Object,\n\t        default: () => ({\n\t          grade: 1\n\t        })\n\t      },\n\t      hafanInfo: {\n\t        Type: Object,\n\t        default: {}\n\t      },\n\t     selectedSkus: {\n\t        Type: Object,\n\t        default: () => ({})\n\t      }\n    },\n    data() {\n      return {\n        complete: false, // 组件是否加载完成\n        goodsInfo: {...this.goods}, // 商品信息\n        isShow: false, // true 显示 false 隐藏\n        selectNum: this.minBuyNum, // 选中数量\n        selectedItems: [], // 已选择的套餐项\n        showSkuPopup: false, // 是否显示规格弹窗\n      };\n    },\n    mounted() {\n      that = this;\n      if (that.value) {\n        that.open();\n      }\n    },\n    computed: {\n      // 计算哈帆会员等级\n      hafanLevel() {\n        return this.hafanInfo?.premium?.level || 'free';\n      },\n      // 计算是否为会员价格\n      isMemberPrice() {\n        return (this.gradeInfo && this.gradeInfo.grade > 1) || (this.hafanLevel !== 'free');\n      },\n      // 根据商品ID获取已选规格文本\n      getSelectedSkuText() {\n        return (goodsId) => {\n          const selectedSku = this.selectedSkus[goodsId];\n          if (selectedSku) {\n            return `${selectedSku.sku_name_arr.join('，')}`;\n          }\n          return '';\n        };\n      },\n      // 检查是否已选择了所有必选项\n      isRequiredItemsSelected() {\n        if (!this.goodsInfo.packageGroups) return false;\n        \n        /* 检查每个必选分组是否满足规则 */\n        for (const group of this.goodsInfo.packageGroups) {\n          if (group.groupType === 'R') {\n            /* 查找当前分组已选择的项目数量 */\n            const selectedCount = this.selectedItems.filter(item => item.groupId === group.id).length;\n            \n            /* 必选分组需要满足选择数量要求 */\n            if (selectedCount < group.selectCount) {\n              return false;\n            }\n          }\n        }\n        \n        return true;\n      },\n      \n      // 计算套餐额外价格\n      extraPrice() {\n        let total = 0;\n        for (const selectedItem of this.selectedItems) {\n          total += selectedItem.price * selectedItem.quantity;\n        }\n        return total;\n      }\n    },\n    methods: {\n      // 检查项目是否已选中\n      isItemSelected(groupId, itemId) {\n        return this.selectedItems.some(item => item.groupId === groupId && item.itemId === itemId);\n      },\n      \n      // 获取指定项目的选择数量\n      getItemQuantity(groupId, itemId) {\n        const item = this.selectedItems.find(item => item.groupId === groupId && item.itemId === itemId);\n        return item ? item.quantity : 0;\n      }, \n      \n      // 选择可选项\n      selectOptionalItem(group, item) {\n        \n        // 检查当前项目是否已选中\n        const index = this.selectedItems.findIndex(selected => \n          selected.groupId === group.id && selected.itemId === item.id\n        );\n        \n        if (index !== -1) {\n          // 如果已选中，则取消选择\n          this.selectedItems.splice(index, 1);\n        } else {\n          /* 检查当前分组选择的项目总数 */\n          const selectedCount = this.selectedItems.filter(selected => selected.groupId === group.id)\n            .reduce((total, item) => total + item.quantity, 0);\n          \n          // 如果没有超过限制，添加选择\n          if (selectedCount < group.selectCount) {\n            this.selectedItems.push({\n              groupId: group.id,\n              groupName: group.groupName,\n              goodsId: item.itemGoodsId,\n              itemId: item.id,\n              itemName: item.itemGoods.name,\n              price: parseFloat(item.price) || 0,\n              quantity: 1\n            });\n          } else {\n            this.$toast(`最多可选${group.selectCount}份`);\n          }\n        }\n      },\n      \n      // 增加可选项数量\n      increaseItemQuantity(group, item, maxQuantity) {\n        const index = this.selectedItems.findIndex(selected => \n          selected.groupId === group.id && selected.itemId === item.id\n        );\n        \n        if (index !== -1) {\n          /* 检查当前分组选择的项目总数 */\n          const selectedCount = this.selectedItems.filter(selected => selected.groupId === group.id)\n            .reduce((total, item) => total + item.quantity, 0);\n          \n          // 如果没有超过限制，增加数量\n          if (selectedCount < maxQuantity) {\n            this.selectedItems[index].quantity += 1;\n          } else {\n            this.$toast(`最多可选${maxQuantity}份`);\n          }\n        }\n      },\n      \n      // 减少可选项数量\n      decreaseItemQuantity(group, item) {\n        const index = this.selectedItems.findIndex(selected => \n          selected.groupId === group.id && selected.itemId === item.id\n        );\n        \n        if (index !== -1) {\n          if (this.selectedItems[index].quantity > 1) {\n            this.selectedItems[index].quantity -= 1;\n          } else {\n            // 如果数量为1，则移除项目\n            this.selectedItems.splice(index, 1);\n          }\n        }\n      },\n\n\n      // 初始化\n      init() { \n        // 清空之前的数据\n        that.selectedItems = [];\n        that.selectNum = that.minBuyNum;\n        \n        // 自动选择所有必选项\n        if (that.goodsInfo.packageGroups) {\n          that.goodsInfo.packageGroups.forEach(group => {\n            if (group.groupType === 'R') {\n              group.items.forEach(item => {\n                that.selectedItems.push({\n                  groupId: group.id,\n                  groupName: group.groupName,\n                  goodsId: item.itemGoodsId,\n                  itemId: item.id,\n                  itemName: item.itemGoods.name,\n                  price: parseFloat(item.price) || 0,\n                  quantity: 1\n                });\n              });\n            }\n          });\n        }\n        \n        that.complete = true;\n        that.$emit(\"open\", true);\n        that.$emit(\"input\", true);\n      },\n\n      async open() {  \n        that.init();\n      },\n      // 监听 - 弹出层收起\n      close(s) {\n        if (s == \"close\") {\n          that.$emit(\"input\", false);\n          that.$emit(\"close\", \"close\");\n        } else if (s == \"mask\") {\n          if (that.maskCloseAble) {\n            that.$emit(\"input\", false);\n            that.$emit(\"close\", \"mask\");\n          }\n        }\n      },\n      moveHandle() {\n        //禁止父元素滑动\n      },\n\n      // 加入购物车\n      addCart() {\n        that.checkSelectComplete({\n          success: function(selectPackage) {\n            that.$emit(\"add-cart\", selectPackage);\n          }\n        });\n      },\n      // 立即购买\n      buyNow() {\n        that.checkSelectComplete({\n          success: function(selectPackage) {\n            that.$emit(\"buy-now\", selectPackage);\n          }\n        });\n      },\n      \n      // 检测套餐选择是否完成\n     checkSelectComplete(obj = {}) {\n       // 1. 检查必选项\n       if (!this.isRequiredItemsSelected) {\n         this.toast(\"请选择必选套餐项\");\n         return;\n       }\n\n       // 2. 检查可选组是否至少选择一个\n       const hasOptionalGroup = this.goodsInfo.packageGroups.some(group => group.groupType === 'O');\n       const hasSelectedOptional = this.selectedItems.some(item => {\n         const group = this.goodsInfo.packageGroups.find(g => g.id === item.groupId);\n         return group && group.groupType === 'O';\n       });\n       if (hasOptionalGroup && !hasSelectedOptional) {\n         this.toast(\"请至少选择一个可选项\");\n         return;\n       }\n\n       // 3. 检查多规格商品的规格选择\n       for (const group of this.goodsInfo.packageGroups) {\n         for (const item of group.items) {\n           // 如果是多规格商品且被选中\n           if (item.itemGoods.isSingleSpec === 'N' &&\n               this.selectedItems.some(selected => selected.itemId === item.id)) {\n             // 检查是否已选择规格\n             const hasSelectedSku = this.selectedSkus[item.itemGoods.id];\n             if (!hasSelectedSku) {\n               this.toast(`请选择商品${item.itemGoods.name}的规格`);\n               return;\n             }\n           }\n         }\n       }\n       console.log('this.selectedItems :>> ', this.selectedItems);\n       // 4. 构建选择结果\n       const packageItems = this.selectedItems.map(item => {\n         const baseItem = {\n           groupId: item.groupId,\n           groupName: item.groupName,\n           itemId: item.itemId,\n           itemName: item.itemName,\n           price: item.price,\n           quantity: item.quantity\n         };\n         \n         // 获取商品信息\n         const goods = this.findItemGoods(item.itemId);\n         if (goods && goods.isSingleSpec === 'N') {\n           const sku = this.selectedSkus[goods.id];\n           if (sku) {\n             baseItem.selectedSkuId = sku.sku_id;\n             baseItem.selectedSkuText = sku.sku_name_arr.join('，');\n             baseItem.spec_value_ids = sku.spec_value_ids;\n           }\n         }\n         \n         return baseItem;\n       }); \n       const selectPackage = {\n         goods_id: this.goodsInfo.goodsId,\n         package_items: JSON.stringify(packageItems),\n         buy_num: this.selectNum,\n         isPackage: true\n       };\n       console.log('selectPackage :>> ', packageItems);\n       \n       if (typeof obj.success == \"function\") obj.success(selectPackage);\n     },\n\n     // 根据itemId查找商品信息\n     findItemGoods(itemId) {\n       for (const group of this.goodsInfo.packageGroups) {\n         const item = group.items.find(i => i.id === itemId);\n         if (item) {\n           return item.itemGoods;\n         }\n       }\n       return null;\n     },\n      // 显示规格选择弹窗\n      onShowSkuPopup(goods,group, item) {\n        if(!this.isItemSelected(group.id, item.id) && group.groupType === 'O') {\n          this.selectOptionalItem(group, item);\n        } \n        this.currentSkuItem = this.findSkuItem(goods);\n        this.$emit('show-sku-popup', goods);\n      },\n\n      // 根据商品查找套餐项\n      findSkuItem(goods) {\n        for (const group of this.goodsInfo.packageGroups) {\n          for (const item of group.items) {\n            if (item.itemGoods.id === goods.id) {\n              return item;\n            }\n          }\n        }\n        return null;\n      },\n      \n      // 弹窗\n      toast(title, icon) {\n        uni.showToast({\n          title: title,\n          icon: icon || 'none'\n        });\n      }\n    },\n    // 过滤器\n    filters: {\n      // 金额显示过滤器\n      priceFilter(n = 0) {\n        if (typeof n == \"string\") {\n            n = parseFloat(n)\n        }\n        return n ? n.toFixed(2) : n\n      }\n    },\n    watch: {\n      value: function(val) {\n        if (val) {\n            that.open();\n        }\n      },\n    }\n  };\n</script>\n\n<style lang=\"scss\" scoped>\n  /*  套餐弹出层样式 */\n  .goods-package-popup {\n    position: fixed;\n    left: 0;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    z-index: 999999999999;\n    overflow: hidden;\n    &.show {\n      display: block;\n\n      .mask {\n        animation: showPopup 0.2s linear both;\n      }\n\n      .layer {\n        animation: showLayer 0.2s linear both;\n      }\n    }\n\n    &.hide {\n      .mask {\n        animation: hidePopup 0.2s linear both;\n      }\n\n      .layer {\n        animation: hideLayer 0.2s linear both;\n      }\n    }\n\n    &.none {\n      display: none;\n    }\n\n    .mask {\n      position: fixed;\n      top: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 1;\n      background-color: rgba(0, 0, 0, 0.65);\n    }\n\n    .layer {\n      display: flex;\n      width: 100%;\n      max-height: 1200rpx;\n      flex-direction: column;\n      position: fixed;\n      z-index: 999999;\n      bottom: 0;\n      border-radius: 10rpx 10rpx 0 0;\n      background-color: #ffffff;\n      margin-top: 10rpx;\n      overflow-y: scroll;\n      .btn-option {\n          padding: 1rpx;\n          display: block;\n          clear: both;\n          margin-bottom: 60rpx;\n      }\n      .specification-wrapper {\n        width: 100%;\n        margin-top: 20rpx;\n        padding: 30rpx 25rpx;\n        box-sizing: border-box;\n        .specification-wrapper-content {\n          width: 100%;\n          min-height: 300rpx;\n\n          &::-webkit-scrollbar {\n            /*隐藏滚轮*/\n            display: none;\n          }\n\n          .specification-header {\n            width: 100%;\n            display: flex;\n            flex-direction: row;\n            position: relative;\n            margin-bottom: 40rpx;\n\n            .specification-left {\n              width: 180rpx;\n              height: 180rpx;\n              flex: 0 0 180rpx;\n\n              .product-img {\n                width: 180rpx;\n                height: 180rpx;\n                background-color: #999999;\n              }\n            }\n\n            .specification-right {\n              flex: 1;\n              padding: 0 35rpx 10rpx 28rpx;\n              box-sizing: border-box;\n              display: flex;\n              flex-direction: column;\n              justify-content: flex-end;\n              font-weight: 500;\n\n              .price-content {\n                color: #fe560a;\n                margin-bottom: 10rpx;\n\n                .sign {\n                  font-size: 28rpx;\n                  margin-right: 4rpx;\n                }\n\n                .price {\n                  font-size: 44rpx;\n                }\n              }\n\n              .choose {\n                font-size: 24rpx;\n                color: #333;\n                min-height: 32rpx;\n                font-weight: bold;\n              }\n            }\n          }\n\n          .specification-content {\n            font-weight: 500;\n            \n            .package-group {\n              margin-bottom: 40rpx;\n              \n              .group-title {\n                font-size: 30rpx;\n                font-weight: bold;\n                color: #333;\n                margin-bottom: 20rpx;\n                \n                .required-tag {\n                  color: #f03c3c;\n                  font-size: 26rpx;\n                  margin-left: 10rpx;\n                  font-weight: normal;\n                }\n                \n                .optional-tag {\n                  color: #666;\n                  font-size: 26rpx;\n                  margin-left: 10rpx;\n                  font-weight: normal;\n                }\n                \n                .selection-rule {\n                  color: #999;\n                  font-size: 24rpx;\n                  margin-left: 20rpx;\n                  font-weight: normal;\n                }\n              }\n              \n              .item-wrapper {\n                .package-item {\n                  display: flex;\n                  padding: 20rpx;\n                  border-radius: 12rpx;\n                  margin-bottom: 16rpx;\n                  background-color: #f8f8f8;\n                  align-items: center;\n                  \n                  &.active {\n                    background-color: #fff4f4;\n                    border: 1px solid #ffdddd;\n                  }\n                  \n                  .item-image {\n                    width: 100rpx;\n                    height: 100rpx;\n                    margin-right: 20rpx;\n                    \n                    image {\n                      width: 100%;\n                      height: 100%;\n                      border-radius: 8rpx;\n                    }\n                  }\n                  \n                  .item-info {\n                    flex: 1;\n                    \n                    .item-name {\n                      font-size: 28rpx;\n                      font-weight: bold;\n                      color: #333;\n                      margin-bottom: 6rpx;\n                    }\n\n                    .item-spec-text {\n                      font-size: 24rpx;\n                      color: #666;\n                      margin-bottom: 6rpx;\n                    }\n                    \n                    .item-price {\n                      font-size: 24rpx;\n                      color: #f03c3c;\n                    }\n                  }\n                  \n                  .item-checkbox {\n                    padding: 0 10rpx;\n                    \n                    .checkbox {\n                      display: inline-block;\n                      width: 40rpx;\n                      height: 40rpx;\n                      border: 2rpx solid #ddd;\n                      border-radius: 50%;\n                      position: relative;\n                      \n                      &.checked {\n                        background-color: #f03c3c;\n                        border-color: #f03c3c;\n                        \n                        &:after {\n                          content: '';\n                          position: absolute;\n                          width: 20rpx;\n                          height: 10rpx;\n                          border-left: 4rpx solid #fff;\n                          border-bottom: 4rpx solid #fff;\n                          transform: rotate(-45deg);\n                          top: 10rpx;\n                          left: 8rpx;\n                        }\n                      }\n                    }\n                  }\n\n                  .item-spec {\n                    padding: 0 10rpx;\n                    \n                    .select-spec {\n                      height: 50rpx;\n                      line-height: 48rpx;\n                      padding: 0 20rpx;\n                      border-radius: 25rpx;\n                      background-color: #f8f8f8;\n                      color: #333;\n                      font-size: 24rpx;\n                      border: 1rpx solid #eee;\n                    }\n                  }\n                  \n                  .item-quantity {\n                    .quantity-control {\n                      display: flex;\n                      align-items: center;\n                      \n                      .minus, .plus {\n                        display: inline-block;\n                        width: 50rpx;\n                        height: 50rpx;\n                        background-color: #f5f5f5;\n                        color: #333;\n                        text-align: center;\n                        line-height: 48rpx;\n                        font-size: 36rpx;\n                        border-radius: 50%;\n                        font-weight: bold;\n                      }\n                      \n                      .value {\n                        margin: 0 15rpx;\n                        font-size: 28rpx;\n                        min-width: 40rpx;\n                        text-align: center;\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n\n        .close {\n          position: absolute;\n          top: 30rpx;\n          right: 25rpx;\n          width: 50rpx;\n          height: 50rpx;\n          text-align: center;\n          line-height: 50rpx;\n\n          .close-item {\n            width: 40rpx;\n            height: 40rpx;\n          }\n        }\n      }\n\n      .btn-wrapper {\n        display: flex;\n        width: 100%;\n        height: 120rpx;\n        flex: 0 0 120rpx;\n        align-items: center;\n        justify-content: space-between;\n        padding: 0 26rpx;\n        box-sizing: border-box;\n\n        .layer-btn {\n          width: 335rpx;\n          height: 80rpx;\n          border-radius: 40rpx;\n          color: #fff;\n          line-height: 80rpx;\n          text-align: center;\n          font-weight: 500;\n          font-size: 28rpx;\n\n          &.add-cart {\n            background: #ffbe46;\n          }\n\n          &.buy {\n            background: #fe560a;\n          }\n        }\n\n        .sure {\n          width: 698rpx;\n          height: 80rpx;\n          border-radius: 38rpx;\n          color: #fff;\n          line-height: 80rpx;\n          text-align: center;\n          font-weight: 500;\n          font-size: 28rpx;\n          background: #fe560a;\n        }\n\n        .sure.add-cart {\n          background: #ff9402;\n        }\n      }\n    }\n\n    @keyframes showPopup {\n      0% {\n        opacity: 0;\n      }\n\n      100% {\n        opacity: 1;\n      }\n    }\n\n    @keyframes hidePopup {\n      0% {\n        opacity: 1;\n      }\n\n      100% {\n        opacity: 0;\n      }\n    }\n\n    @keyframes showLayer {\n      0% {\n        transform: translateY(120%);\n      }\n\n      100% {\n        transform: translateY(0%);\n      }\n    }\n\n    @keyframes hideLayer {\n      0% {\n        transform: translateY(0);\n      }\n\n      100% {\n        transform: translateY(120%);\n      }\n    }\n  }\n</style>", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6c4cc486&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=6c4cc486&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426051\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}