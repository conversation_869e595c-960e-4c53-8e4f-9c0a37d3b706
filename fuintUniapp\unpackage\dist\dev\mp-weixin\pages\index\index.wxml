<view class="container data-v-57280228"><block wx:if="{{!storeInfo}}"><empty vue-id="8dd740cc-1" isLoading="{{isLoading}}" tips="数据加载中..." class="data-v-57280228" bind:__l="__l"></empty></block><block wx:if="{{storeInfo}}"><view class="new-home-layout data-v-57280228"><view class="top-bg-section data-v-57280228"><image class="bg-image data-v-57280228" src="/static/home-bg.png" mode="widthFix"></image></view><view class="user-info-card data-v-57280228"><view class="greeting-section data-v-57280228"><view class="greeting-text data-v-57280228"><text class="hi-text data-v-57280228">Hi</text><text class="username data-v-57280228">{{userInfo&&userInfo.name?userInfo.name:'微信用户'}}</text></view><block wx:if="{{userInfo&&userInfo.id}}"><view class="member-section data-v-57280228"><view data-event-opts="{{[['tap',[['goMemberCode',['$event']]]]]}}" class="member-code-btn data-v-57280228" bindtap="__e">查看会员码</view></view></block><block wx:else><view class="login-section data-v-57280228"><view data-event-opts="{{[['tap',[['goLogin',['$event']]]]]}}" class="login-btn data-v-57280228" bindtap="__e">登录</view></view></block></view></view><view class="service-selection data-v-57280228"><view class="service-options data-v-57280228"><view data-event-opts="{{[['tap',[['goPickup',['$event']]]]]}}" class="service-item pickup-service data-v-57280228" bindtap="__e"><text class="service-title data-v-57280228">到店自取</text><text class="service-subtitle data-v-57280228">PICK UP</text><view class="service-logo data-v-57280228"><text class="logo-text data-v-57280228">HATEA</text></view><text class="service-desc data-v-57280228">提前下单免排队</text></view><view data-event-opts="{{[['tap',[['goDelivery',['$event']]]]]}}" class="service-item delivery-service data-v-57280228" bindtap="__e"><text class="service-title data-v-57280228">外送到家</text><text class="service-subtitle data-v-57280228">DELIVERY</text><view class="service-logo delivery-logo data-v-57280228"><text class="logo-text data-v-57280228">HATEA</text></view><text class="service-desc data-v-57280228">满￥50免配送费</text></view></view></view></view></block><block wx:if="{{hasClaimedCoupon==false}}"><home-take-coupon bind:close="__e" vue-id="8dd740cc-2" data-event-opts="{{[['^close',[['handleCloseCouponDialog']]]]}}" class="data-v-57280228" bind:__l="__l"></home-take-coupon></block></view>