<view class="container data-v-38cab12e"><block wx:if="{{orderMode==1}}"><view class="flow-mode data-v-38cab12e"><view class="flex-row justify-between items-center data-v-38cab12e"></view><view style="margin-top:20rpx;text-align:center;" class="data-v-38cab12e"><select-switch vue-id="e08a7286-1" switchList="{{['堂食','外带']}}" checked_bj_color="#232e5d" data-event-opts="{{[['^change',[['switchTakeMode']]]]}}" bind:change="__e" class="data-v-38cab12e" bind:__l="__l"></select-switch></view></view></block><block wx:if="{{orderMode==false}}"><view data-event-opts="{{[['tap',[['onSelectAddress',['$event']]]]]}}" class="flow-delivery data-v-38cab12e" bindtap="__e"><view class="flow-delivery__detail data-v-38cab12e"><view class="detail-location data-v-38cab12e"><text class="iconfont icon-dingwei data-v-38cab12e"></text></view><view class="detail-content data-v-38cab12e"><block wx:if="{{address}}"><block class="data-v-38cab12e"><view class="detail-content__title data-v-38cab12e"><text class="data-v-38cab12e">{{address.name}}</text><text class="detail-content__title-phone data-v-38cab12e">{{address.mobile}}</text></view><view class="address-info detail-content__describe data-v-38cab12e"><text class="region data-v-38cab12e">{{address.provinceName+address.cityName+address.regionName}}</text><text class="detail data-v-38cab12e">{{address.detail}}</text><text class="icon data-v-38cab12e">»</text></view></block></block><block wx:else><block class="data-v-38cab12e"><view class="detail-content__describe select-address data-v-38cab12e"><text class="data-v-38cab12e">请选择配送地址<text class="icon data-v-38cab12e">></text></text></view></block></block></view><view class="detail-arrow data-v-38cab12e"><text class="iconfont icon-arrow-right data-v-38cab12e"></text></view></view></view></block><block wx:if="{{orderMode==true}}"><view class="flow-delivery data-v-38cab12e"><view class="flow-delivery__detail data-v-38cab12e"><view class="detail-location data-v-38cab12e"><text class="iconfont icon-dingwei data-v-38cab12e"></text></view><view class="detail-content data-v-38cab12e"><view class="store data-v-38cab12e"><view class="store-name data-v-38cab12e">{{storeInfo?storeInfo.name:''}}</view><view class="store-phone data-v-38cab12e">{{storeInfo?storeInfo.phone:''}}</view><text class="store-address data-v-38cab12e">{{storeInfo?storeInfo.address:''}}</text></view></view><view class="detail-arrow data-v-38cab12e"><text class="iconfont icon-arrow-right data-v-38cab12e"></text></view></view></view></block><view class="goods-detail-header data-v-38cab12e"><view class="section-title data-v-38cab12e">商品详情</view><view class="section-subtitle data-v-38cab12e">注意选对温度和甜度</view></view><view class="checkout_list data-v-38cab12e"><block wx:for="{{goodsCart}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="flow-shopList data-v-38cab12e"><view class="flow-list-left data-v-38cab12e"><image mode="scaleToFill" src="{{item.goodsInfo.logo}}" class="data-v-38cab12e"></image></view><view class="flow-list-right data-v-38cab12e"><text class="goods-name data-v-38cab12e">{{item.goodsInfo.name}}</text><block wx:if="{{!item.isPackage||item.isPackage!=='Y'}}"><view class="goods-props data-v-38cab12e"><block wx:for="{{item.specList}}" wx:for-item="props" wx:for-index="idx" wx:key="idx"><view class="goods-props-item data-v-38cab12e"><text class="group-name data-v-38cab12e">{{props.specName+':'}}</text><text class="data-v-38cab12e">{{props.specValue+"；"}}</text></view></block></view></block><block wx:if="{{item.isPackage==='Y'&&item.packageGroups}}"><view class="goods-props data-v-38cab12e"><block wx:for="{{item.packageGroups}}" wx:for-item="group" wx:for-index="groupIndex" wx:key="groupIndex"><view class="goods-props-item data-v-38cab12e"><text class="group-name data-v-38cab12e">{{group.groupName+':'}}</text><view class="group-items data-v-38cab12e"><block wx:for="{{group.items}}" wx:for-item="packageItem" wx:for-index="itemIndex" wx:key="itemIndex"><view class="goods-props-item data-v-38cab12e"><text class="group-name data-v-38cab12e">{{packageItem.itemName}}</text><block wx:if="{{packageItem.selectedSkuText}}"><text class="item-spec data-v-38cab12e">{{packageItem.selectedSkuText}}</text></block><text class="item-quantity data-v-38cab12e">{{"x"+packageItem.quantity}}</text></view></block></view></view></block></view></block><view class="flow-list-cont data-v-38cab12e"><text class="small data-v-38cab12e">{{"x"+item.num}}</text><text class="flow-cont data-v-38cab12e">{{"￥"+item.goodsInfo.price}}</text></view></view></view></block><view class="flow-num-box data-v-38cab12e"><text class="data-v-38cab12e">{{"共"+totalNum+"件商品，"}}</text><text class="data-v-38cab12e">合计</text><text class="flow-money data-v-38cab12e">{{"￥"+totalPrice}}</text></view></view><view class="flow-all-money data-v-38cab12e"><view class="detail-title data-v-38cab12e">费用明细</view><view class="flow-all-list data-v-38cab12e"><text class="data-v-38cab12e">优惠券</text><view class="data-v-38cab12e"><block wx:if="{{curPayType==='HAFAN'}}"><view class="data-v-38cab12e"><text class="col-gray data-v-38cab12e">Hafan支付不可使用卡券</text></view></block><block wx:else><block wx:if="{{$root.g0>0}}"><view data-event-opts="{{[['tap',[['handleShowPopup']]]]}}" bindtap="__e" class="data-v-38cab12e"><block wx:if="{{useCouponInfo}}"><text class="col-m data-v-38cab12e">{{"-￥"+useCouponInfo.amount}}</text></block><block wx:else><text class="data-v-38cab12e">{{"共有卡券"+$root.g1+"张"}}</text></block><text class="right-arrow iconfont icon-arrow-right data-v-38cab12e"></text></view></block><block wx:else><text class="data-v-38cab12e">暂无可用</text></block></block></view></view><block wx:if="{{usePoint>0}}"><view class="flow-all-list data-v-38cab12e"><view data-event-opts="{{[['tap',[['handleShowPoints']]]]}}" bindtap="__e" class="data-v-38cab12e"><text class="data-v-38cab12e">{{"使用"+usePoint+"积分抵扣"}}</text><text class="iconfont icon-help data-v-38cab12e"></text></view><view class="data-v-38cab12e"><text class="col-m data-v-38cab12e">{{"-￥"+usePointAmount}}</text><u-switch vue-id="e08a7286-2" size="48" active-color="#3f51b5" value="{{isUsePoints}}" data-event-opts="{{[['^change',[['getCartList']]],['^input',[['__set_model',['','isUsePoints','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-38cab12e" bind:__l="__l"></u-switch></view></view></block><view class="flow-all-list data-v-38cab12e"><text class="data-v-38cab12e">备注</text><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" bindtap="__e" class="data-v-38cab12e"><block wx:if="{{remark}}"><text class="data-v-38cab12e">{{remark}}</text></block><block wx:else><text class="col-gray data-v-38cab12e">口味、包装等要求</text></block><text class="right-arrow iconfont icon-arrow-right data-v-38cab12e"></text></view></view><block wx:if="{{orderMode==true}}"><view class="flow-all-list data-v-38cab12e"><text class="data-v-38cab12e">预约取餐</text><view class="data-v-38cab12e"><block wx:if="{{isReservation&&reservationTime}}"><text class="reservation-time data-v-38cab12e">{{reservationTime}}</text></block><block wx:else><text class="col-gray data-v-38cab12e">立即制作</text></block><u-switch vue-id="e08a7286-3" size="48" active-color="#3f51b5" value="{{isReservation}}" data-event-opts="{{[['^change',[['handleReservationChange']]],['^input',[['__set_model',['','isReservation','$event',[]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-38cab12e" bind:__l="__l"></u-switch></view></view></block><view class="flow-all-list data-v-38cab12e"><text class="data-v-38cab12e">会员支付折扣</text><text class="data-v-38cab12e">{{memberDiscount>0?'￥'+memberDiscount:'无折扣'}}</text></view><block wx:if="{{deliveryFee>0&&orderMode==false}}"><view class="flow-all-list data-v-38cab12e"><text class="data-v-38cab12e">配送费用</text><text class="col-m data-v-38cab12e">{{"￥"+deliveryFee}}</text></view></block></view><block wx:if="{{showRemarkInput||remark}}"><view class="flow-all-money data-v-38cab12e"><view class="ipt-wrapper data-v-38cab12e"><textarea rows="3" maxlength="100" placeholder="买家留言 (选填,100字以内)" data-event-opts="{{[['input',[['__set_model',['','remark','$event',[]]]]]]}}" value="{{remark}}" bindinput="__e" class="data-v-38cab12e"></textarea></view></view></block><view class="flow-fixed-footer data-v-38cab12e"><view class="chackout-box data-v-38cab12e"><view class="chackout-left data-v-38cab12e"><view class="col-amount-do data-v-38cab12e">支付金额<text class="pay-amount data-v-38cab12e">{{"￥"+(payPrice?$root.g2:'0.00')}}</text></view></view><view data-event-opts="{{[['tap',[['onSubmitOrder']]]]}}" class="chackout-right data-v-38cab12e" bindtap="__e"><view class="{{['flow-btn','data-v-38cab12e',(disabled)?'disabled':'']}}">支付</view></view></view></view><u-modal bind:input="__e" vue-id="e08a7286-4" title="积分说明" value="{{showPoints}}" data-event-opts="{{[['^input',[['__set_model',['','showPoints','$event',[]]]]]]}}" class="data-v-38cab12e" bind:__l="__l" vue-slots="{{['default']}}"><scroll-view class="points-content data-v-38cab12e" scroll-y="{{true}}"><text class="data-v-38cab12e">积分兑换金额</text></scroll-view></u-modal><u-popup bind:input="__e" vue-id="e08a7286-5" mode="bottom" value="{{showPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showPopup','$event',[]]]]]]}}" class="data-v-38cab12e" bind:__l="__l" vue-slots="{{['default']}}"><view class="popup__coupon data-v-38cab12e"><view class="coupon__title f-30 data-v-38cab12e">选择卡券</view><view class="coupon-list data-v-38cab12e"><scroll-view style="height:565rpx;" scroll-y="{{true}}" class="data-v-38cab12e"><block wx:for="{{couponList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="coupon-item data-v-38cab12e"><view data-event-opts="{{[['tap',[['handleSelectCoupon',[index]]]]]}}" class="{{['item-wrapper','data-v-38cab12e',item.status=='A'?'color-default':'color-gray']}}" bindtap="__e"><view class="coupon-type data-v-38cab12e">{{item.type}}</view><view class="tip dis-flex flex-dir-column flex-x-center data-v-38cab12e"><text class="money data-v-38cab12e">{{"￥"+item.amount}}</text><text class="pay-line data-v-38cab12e">{{item.description}}</text></view><view class="split-line data-v-38cab12e"></view><view class="content dis-flex flex-dir-column flex-x-between data-v-38cab12e"><view class="title data-v-38cab12e">{{item.name}}</view><view class="bottom dis-flex flex-y-center data-v-38cab12e"><view class="time flex-box data-v-38cab12e"><block class="data-v-38cab12e">{{item.effectiveDate}}</block></view></view></view></view></view></block></scroll-view></view><view class="coupon__do_not dis-flex flex-y-center flex-x-center data-v-38cab12e"><view data-event-opts="{{[['tap',[['handleNotUseCoupon']]]]}}" class="control dis-flex flex-y-center flex-x-center data-v-38cab12e" bindtap="__e"><text class="f-26 data-v-38cab12e">不使用卡券</text></view></view></view></u-popup><u-popup bind:input="__e" vue-id="e08a7286-6" mode="bottom" closeable="{{true}}" value="{{showPayPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showPayPopup','$event',[]]]]]]}}" class="data-v-38cab12e" bind:__l="__l" vue-slots="{{['default']}}"><view class="pay-type-popup data-v-38cab12e"><view class="title data-v-38cab12e">请选择支付方式</view><view class="pop-content data-v-38cab12e"><view data-event-opts="{{[['tap',[['doSubmitOrder',['$0'],['PayTypeEnum.WECHAT.value']]]]]}}" class="pay-item dis-flex flex-x-between data-v-38cab12e" bindtap="__e"><view class="item-left dis-flex flex-y-center data-v-38cab12e"><view class="item-left_icon wechat data-v-38cab12e"><text class="iconfont icon-weixinzhifu data-v-38cab12e"></text></view><view class="item-left_text data-v-38cab12e"><text class="data-v-38cab12e">{{PayTypeEnum.WECHAT.name}}</text></view></view></view><view data-event-opts="{{[['tap',[['doSubmitOrder',['HAFAN']]]]]}}" class="pay-item dis-flex flex-x-between data-v-38cab12e" bindtap="__e"><view class="item-left dis-flex flex-y-center data-v-38cab12e"><view class="item-left_icon balance data-v-38cab12e"><text class="iconfont icon-qiandai data-v-38cab12e"></text></view><view class="item-left_text data-v-38cab12e"><text class="data-v-38cab12e">火炬币</text><block wx:if="{{hafanBalance!==null}}"><text class="balance-amount data-v-38cab12e">{{'(可用: ¥'+$root.g3+')'}}</text></block></view></view></view></view></view></u-popup><u-popup bind:input="__e" vue-id="e08a7286-7" mode="bottom" closeable="{{true}}" value="{{showReservationPopup}}" data-event-opts="{{[['^input',[['__set_model',['','showReservationPopup','$event',[]]]]]]}}" class="data-v-38cab12e" bind:__l="__l" vue-slots="{{['default']}}"><view class="reservation-popup data-v-38cab12e"><view class="popup-header data-v-38cab12e"><text class="popup-title data-v-38cab12e">选择预约时间</text><text class="popup-subtitle data-v-38cab12e">请选择24小时内的取餐时间</text></view><view class="datetime-picker data-v-38cab12e"><picker-view class="picker-view data-v-38cab12e" value="{{pickerValue}}" data-event-opts="{{[['change',[['onPickerChange',['$event']]]]]}}" bindchange="__e"><picker-view-column class="data-v-38cab12e"><block wx:for="{{dateList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picker-item data-v-38cab12e">{{''+item.label+''}}</view></block></picker-view-column><picker-view-column class="data-v-38cab12e"><block wx:for="{{hourList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picker-item data-v-38cab12e">{{''+item.label+''}}</view></block></picker-view-column><picker-view-column class="data-v-38cab12e"><block wx:for="{{minuteList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="picker-item data-v-38cab12e">{{''+item.label+''}}</view></block></picker-view-column></picker-view></view><view class="popup-buttons data-v-38cab12e"><view data-event-opts="{{[['tap',[['cancelReservation',['$event']]]]]}}" class="btn-cancel data-v-38cab12e" bindtap="__e">取消</view><view data-event-opts="{{[['tap',[['confirmReservation',['$event']]]]]}}" class="btn-confirm data-v-38cab12e" bindtap="__e">确定</view></view></view></u-popup></view>