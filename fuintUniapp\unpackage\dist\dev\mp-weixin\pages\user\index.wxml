<view class="container data-v-137d5072"><image class="container-bg-img data-v-137d5072" src="https://hatea.zhijuchina.com/static/my_bg.png"></image><block wx:if="{{!isLoading}}"><view class="new-user-layout data-v-137d5072"><view class="member-card-wrapper data-v-137d5072"><block wx:if="{{isLogin&&userInfo&&userInfo.id}}"><block class="data-v-137d5072"><view class="member-info-card data-v-137d5072"><view class="member-content data-v-137d5072"><text class="user-greeting data-v-137d5072">{{"Hi "+(userInfo.name?userInfo.name:'微信用户')}}</text><view class="avatar-group data-v-137d5072"><image class="avatar-logo data-v-137d5072" src="https://hatea.zhijuchina.com/static/logo.png?_t=1"></image></view><view class="member-stats data-v-137d5072"><view class="member-grade-section data-v-137d5072"><view data-event-opts="{{[['tap',[['goToLevelDetail',['$event']]]]]}}" class="grade-badge data-v-137d5072" bindtap="__e"><text class="grade-text data-v-137d5072">{{gradeInfo&&gradeInfo.name?gradeInfo.name:'普通会员'}}</text></view><view class="stats-row data-v-137d5072"><view data-event-opts="{{[['tap',[['onTargetMyCoupon',['$event']]]]]}}" class="stat-item data-v-137d5072" bindtap="__e"><text class="stat-number data-v-137d5072">{{assets.coupon?assets.coupon:'0'}}</text><text class="stat-label data-v-137d5072">优惠券</text></view><image class="stats-divider data-v-137d5072" src="/static/user-divider.png"></image><view data-event-opts="{{[['tap',[['onNavigateToTorchMiniProgram',['$event']]]]]}}" class="stat-item data-v-137d5072" bindtap="__e"><text class="stat-number data-v-137d5072">{{isLogin&&hafanInfo&&hafanInfo.wallet?$root.g0:'0.00'}}</text><text class="stat-label data-v-137d5072">火炬币</text></view></view></view></view><view data-event-opts="{{[['tap',[['goToLevelDetail',['$event']]]]]}}" class="experience-section data-v-137d5072" bindtap="__e"><view class="experience-label data-v-137d5072"><text class="label-text data-v-137d5072">当前消费金额</text><view class="experience-numbers data-v-137d5072"><text class="current-exp data-v-137d5072">{{userYearlyPaymentAmount?userYearlyPaymentAmount:'0'}}</text><text class="exp-separator data-v-137d5072">/</text><text class="max-exp data-v-137d5072">{{memberGrade[0]?memberGrade[0].catchValue||'Max':'Max'}}</text></view></view><view class="progress-bar data-v-137d5072"><view class="progress-track data-v-137d5072"><view class="progress-fill data-v-137d5072" style="{{'width:'+(userLevelProgress+'px')+';'}}"></view></view></view></view></view></view><view class="member-number-bg data-v-137d5072"><text class="member-number data-v-137d5072">{{"会员号："+(userInfo.userNo?userInfo.userNo:'8576539564120')}}</text></view></block></block><block wx:else><block class="data-v-137d5072"><view class="login-prompt-card data-v-137d5072"><view class="login-content data-v-137d5072"><text class="login-greeting data-v-137d5072">Hi 游客</text><text class="login-message data-v-137d5072">请登录查看会员信息</text><view data-event-opts="{{[['tap',[['goLogin',['$event']]]]]}}" class="login-button data-v-137d5072" bindtap="__e"><text class="login-btn-text data-v-137d5072">立即登录</text></view></view></view></block></block></view><view class="order-section data-v-137d5072"><view class="order-grid data-v-137d5072"><view data-event-opts="{{[['tap',[['onTargetOrder',[['o',['id','all']]]]]]]}}" class="order-item data-v-137d5072" bindtap="__e"><image class="order-icon data-v-137d5072" src="/static/icon-all-orders.png"></image><text class="order-text data-v-137d5072">全部订单</text></view><view data-event-opts="{{[['tap',[['onTargetOrder',[['o',['id','toPay']]]]]]]}}" class="order-item data-v-137d5072" bindtap="__e"><image class="order-icon data-v-137d5072" src="/static/icon-pending-payment.png"></image><text class="order-text data-v-137d5072">待支付</text></view><view data-event-opts="{{[['tap',[['onTargetOrder',[['o',['id','paid']]]]]]]}}" class="order-item data-v-137d5072" bindtap="__e"><image class="order-icon data-v-137d5072" src="/static/icon-paid.png"></image><text class="order-text data-v-137d5072">已支付</text></view></view></view><view class="function-section data-v-137d5072"><text class="function-title data-v-137d5072">我的功能</text><view class="function-grid data-v-137d5072"><view data-event-opts="{{[['tap',[['handleService',[['o',['url','pages/user/setting']]]]]]]}}" class="function-item data-v-137d5072" bindtap="__e"><image class="function-icon data-v-137d5072" src="/static/icon-personal-info.png"></image><text class="function-text data-v-137d5072">个人信息</text></view></view></view></view></block></view>