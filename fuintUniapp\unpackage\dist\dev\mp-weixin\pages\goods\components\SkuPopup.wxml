<goods-sku-popup vue-id="b4f629c0-1" value="{{value}}" border-radius="20" custom-action="{{findGoodsInfo}}" mode="{{skuMode}}" defaultPrice="{{isMemberPrice&&goods.gradePrice>0?goods.gradePrice:goods.price}}" gradeInfo="{{gradeInfo}}" hafanInfo="{{hafanInfo}}" defaultStock="{{goods.stock}}" maskCloseAble="{{true}}" data-event-opts="{{[['^input',[['onChangeValue']]],['^open',[['openSkuPopup']]],['^close',[['closeSkuPopup']]],['^addCart',[['addCart']]],['^buyNow',[['buyNow']]]]}}" bind:input="__e" bind:open="__e" bind:close="__e" bind:addCart="__e" bind:buyNow="__e" class="data-v-0d98eaa6" bind:__l="__l"></goods-sku-popup>