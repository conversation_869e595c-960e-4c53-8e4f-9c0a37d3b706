@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.order-item.data-v-3a20e7fa {
  margin: 10rpx auto 10rpx auto;
  padding: 20rpx 20rpx;
  width: 94%;
  border: 3rpx solid #e8e8e8;
  box-shadow: 5rpx 5rpx 5rpx 5rpx rgba(0, 0, 0, 0.05);
  border-radius: 16rpx;
  background: #fff;
}
.order-item .A.data-v-3a20e7fa {
  color: #fa2209;
}
.order-item .B.data-v-3a20e7fa {
  color: #333;
}
.item-top.data-v-3a20e7fa {
  display: flex;
  justify-content: space-between;
  font-size: 26rpx;
  margin-bottom: 40rpx;
}
.item-top .order-type.data-v-3a20e7fa {
  font-weight: bold;
  margin-left: 20rpx;
}
.item-top .state-text.data-v-3a20e7fa {
  color: #fa2209;
}
.goods-list .goods-item.data-v-3a20e7fa {
  display: flex;
  margin-bottom: 10rpx;
  border-bottom: 3rpx solid #e8e8e8;
  padding: 20rpx;
}
.goods-list .goods-item .goods-image.data-v-3a20e7fa {
  width: 180rpx;
  height: 143rpx;
}
.goods-list .goods-item .goods-image .image.data-v-3a20e7fa {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}
.goods-list .goods-item .goods-content.data-v-3a20e7fa {
  flex: 1;
  padding-left: 16rpx;
  padding-top: 16rpx;
}
.goods-list .goods-item .goods-content .goods-title.data-v-3a20e7fa {
  font-size: 26rpx;
  max-height: 76rpx;
}
.goods-list .goods-item .goods-content .goods-props.data-v-3a20e7fa {
  margin-top: 14rpx;
  color: #ababab;
  font-size: 24rpx;
  overflow: hidden;
}
.goods-list .goods-item .goods-content .goods-props .goods-props-item.data-v-3a20e7fa {
  display: inline-block;
  margin-right: 14rpx;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background-color: #F5F5F5;
  width: auto;
}
.goods-list .goods-item .goods-trade.data-v-3a20e7fa {
  padding-top: 16rpx;
  width: 150rpx;
  text-align: right;
  color: #999;
  font-size: 26rpx;
}
.goods-list .goods-item .goods-trade .goods-price.data-v-3a20e7fa {
  vertical-align: bottom;
  margin-bottom: 16rpx;
}
.goods-list .goods-item .goods-trade .goods-price .unit.data-v-3a20e7fa {
  margin-right: -2rpx;
  font-size: 24rpx;
}
.remark.data-v-3a20e7fa {
  padding: 12rpx 0 12rpx 20rpx;
  border-radius: 5rpx;
  height: 60rpx;
}
.order-total.data-v-3a20e7fa {
  font-size: 26rpx;
  vertical-align: bottom;
  text-align: right;
  height: 40rpx;
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}
.order-total .unit.data-v-3a20e7fa {
  margin-left: 8rpx;
  margin-right: -2rpx;
  font-size: 26rpx;
}
.order-total .money.data-v-3a20e7fa {
  font-size: 28rpx;
}
.order-handle.data-v-3a20e7fa {
  height: 50rpx;
}
.order-handle .order-time.data-v-3a20e7fa {
  color: #777;
  float: left;
  margin-left: 20rpx;
}
.order-handle .btn-group .btn-item.data-v-3a20e7fa {
  border-radius: 10rpx;
  padding: 8rpx 24rpx;
  font-size: 28rpx;
  float: right;
  color: #ffffff;
  background: #f9211c;
  border: 1rpx solid #f9211c;
  margin-left: 25rpx;
}
