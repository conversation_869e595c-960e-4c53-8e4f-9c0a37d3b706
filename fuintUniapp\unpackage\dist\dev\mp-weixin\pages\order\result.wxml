<view class="container data-v-20765812"><view class="success data-v-20765812"><block wx:if="{{isSuccess}}"><view class="result data-v-20765812"><image class="icon data-v-20765812" src="/static/pay/success.png"></image><block wx:if="{{message&&message!=undefined}}"><text class="text data-v-20765812">{{message}}</text></block><block wx:if="{{!message||message==undefined}}"><text class="text data-v-20765812">恭喜，支付成功！</text></block></view></block><block wx:if="{{!isSuccess}}"><view class="result data-v-20765812"><image class="icon data-v-20765812" src="/static/pay/fail.png"></image><block wx:if="{{message&&message!=undefined}}"><text class="text data-v-20765812" style="color:#888888;">{{"支付失败："+message}}</text></block><block wx:if="{{!message||message==undefined}}"><text class="text data-v-20765812" style="color:#888888;">哎呀，支付失败啦~</text></block></view></block><view class="options data-v-20765812"><view data-event-opts="{{[['tap',[['toHome']]]]}}" class="to-home data-v-20765812" bindtap="__e"><text class="iconfont icon-home data-v-20765812"></text>返回首页</view><view data-event-opts="{{[['tap',[['toOrderInfo']]]]}}" class="to-order data-v-20765812" bindtap="__e"><text class="iconfont icon-form data-v-20765812"></text>查看订单</view></view></view><block class="data-v-20765812"><goods vue-id="08d62bef-1" itemStyle="{{goodsStyle}}" params="{{goodsParams}}" data-ref="mescrollItem" class="data-v-20765812 vue-ref" bind:__l="__l"></goods></block></view>