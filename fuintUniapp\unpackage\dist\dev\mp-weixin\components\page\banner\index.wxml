<view class="diy-banner data-v-2c2596f8" style="{{'height:'+(imgHeights[imgCurrent]+'rpx')+';'}}"><swiper class="swiper-box data-v-2c2596f8" autoplay="{{autoplay}}" duration="{{duration}}" circular="{{true}}" interval="{{itemStyle.interval*1000}}" data-event-opts="{{[['change',[['_bindChange',['$event']]]]]}}" bindchange="__e"><block wx:for="{{dataList}}" wx:for-item="dataItem" wx:for-index="index" wx:key="index"><swiper-item class="data-v-2c2596f8"><image class="slide-image data-v-2c2596f8" lazy-load="{{true}}" lazy-load-margin="{{0}}" src="{{dataItem.image}}" data-event-opts="{{[['tap',[['onLink',['$0'],[[['dataList','',index,'url']]]]]],['load',[['_imagesHeight',['$event']]]]]}}" bindtap="__e" bindload="__e"></image></swiper-item></block></swiper><view class="{{['indicator-dots','data-v-2c2596f8',itemStyle.btnShape]}}"><block wx:for="{{dataList}}" wx:for-item="dataItem" wx:for-index="index" wx:key="index"><view class="{{['dots-item','data-v-2c2596f8',(imgCurrent==index)?'active':'']}}" style="{{'background-color:'+(itemStyle.btnColor)+';'}}"></view></block></view></view>