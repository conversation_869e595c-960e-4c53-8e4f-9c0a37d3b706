<view class="diy-window data-v-1e096c48" style="{{'background:'+(itemStyle.background)+';'+('padding:'+(itemStyle.paddingTop+'px '+itemStyle.paddingLeft+'px')+';')}}"><block wx:if="{{itemStyle.layout>-1}}"><view class="{{['data-list','data-v-1e096c48','avg-sm-'+itemStyle.layout]}}"><block wx:for="{{dataList}}" wx:for-item="dataItem" wx:for-index="index" wx:key="index"><view class="data-item data-v-1e096c48" style="{{'padding:'+(itemStyle.paddingTop+'px '+itemStyle.paddingLeft+'px')+';'}}"><view data-event-opts="{{[['tap',[['onLink',['$0'],[[['dataList','',index,'link']]]]]]]}}" class="item-image data-v-1e096c48" bindtap="__e"><image class="image data-v-1e096c48" mode="widthFix" src="{{dataItem.imgUrl}}"></image></view></view></block></view></block><block wx:else><view class="display data-v-1e096c48"><view class="display-left data-v-1e096c48" style="{{'padding:'+(itemStyle.paddingTop+'px '+itemStyle.paddingLeft+'px')+';'}}"><image class="image data-v-1e096c48" src="{{dataList[0].imgUrl}}" data-event-opts="{{[['tap',[['onLink',['$0'],['dataItem.link']]]]]}}" bindtap="__e"></image></view><view class="display-right data-v-1e096c48"><block wx:if="{{$root.g0>=2}}"><view class="display-right1 data-v-1e096c48" style="{{'padding:'+(itemStyle.paddingTop+'px '+itemStyle.paddingLeft+'px')+';'}}"><image class="image data-v-1e096c48" src="{{dataList[1].imgUrl}}" data-event-opts="{{[['tap',[['onLink',['$0'],['dataItem.link']]]]]}}" bindtap="__e"></image></view></block><view class="display-right2 data-v-1e096c48"><block wx:if="{{$root.g1>=3}}"><view class="left data-v-1e096c48" style="{{'padding:'+(itemStyle.paddingTop+'px '+itemStyle.paddingLeft+'px')+';'}}"><image class="image data-v-1e096c48" src="{{dataList[2].imgUrl}}" data-event-opts="{{[['tap',[['onLink',['$0'],['dataItem.link']]]]]}}" bindtap="__e"></image></view></block><block wx:if="{{$root.g2>=4}}"><view class="right data-v-1e096c48" style="{{'padding:'+(itemStyle.paddingTop+'px '+itemStyle.paddingLeft+'px')+';'}}"><image class="image data-v-1e096c48" src="{{dataList[3].imgUrl}}" data-event-opts="{{[['tap',[['onLink',['$0'],['dataItem.link']]]]]}}" bindtap="__e"></image></view></block></view></view></view></block></view>