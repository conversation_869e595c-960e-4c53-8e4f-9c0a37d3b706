<mescroll-body vue-id="af8991ea-1" sticky="{{true}}" down="{{({use:false})}}" up="{{upOption}}" data-ref="mescrollRef" data-event-opts="{{[['^init',[['mescrollInit']]],['^up',[['upCallback']]]]}}" bind:init="__e" bind:up="__e" class="data-v-6043f55b vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="tabs-wrapper data-v-6043f55b"><scroll-view class="scroll-view data-v-6043f55b" scroll-x="{{true}}"><view data-event-opts="{{[['tap',[['onSwitchTab',[0]]]]]}}" class="{{['tab-item','data-v-6043f55b',(curId==0)?'active':'']}}" bindtap="__e"><view class="value data-v-6043f55b"><text class="data-v-6043f55b">全部</text></view></view><block wx:for="{{categoryList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onSwitchTab',['$0'],[[['categoryList','',index,'id']]]]]]]}}" class="{{['tab-item','data-v-6043f55b',(curId==item.id)?'active':'']}}" bindtap="__e"><view class="value data-v-6043f55b"><text class="data-v-6043f55b">{{item.name}}</text></view></view></block></scroll-view></view><view class="member-list data-v-6043f55b"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onTargetDetail',['$0'],[[['memberList.content','',index,'id']]]]]]]}}" class="member-item data-v-6043f55b" bindtap="__e"><block class="data-v-6043f55b"><view class="left flex-box data-v-6043f55b"><image class="image data-v-6043f55b" src="{{item.$orig.avatar}}"></image></view><view class="right data-v-6043f55b"><view class="base data-v-6043f55b"><text class="name data-v-6043f55b">{{item.$orig.name}}</text><text class="grade data-v-6043f55b">{{item.$orig.gradeName?item.$orig.gradeName:''}}</text></view><view class="amount data-v-6043f55b"><view class="balance data-v-6043f55b">{{"余额：￥"+item.g0}}</view><view class="point data-v-6043f55b">{{"积分："+item.$orig.point}}</view></view><view class="footer data-v-6043f55b"><text class="member-views f-24 col-8 data-v-6043f55b">{{item.$orig.lastLoginTime+"活跃"}}</text></view></view></block></view></block></view></mescroll-body>