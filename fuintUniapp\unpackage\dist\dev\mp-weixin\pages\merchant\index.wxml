<block wx:if="{{!isLoading}}"><view class="container data-v-786a6504"><view class="main-header data-v-786a6504"><block wx:if="{{isLogin}}"><view class="user-info data-v-786a6504"><view class="user-content data-v-786a6504"><block wx:if="{{dataInfo.confirmInfo.storeInfo}}"><view class="belong data-v-786a6504">{{dataInfo.confirmInfo.storeInfo.name}}<text class="nick-name data-v-786a6504">{{dataInfo.confirmInfo.realName}}</text></view></block><block wx:else><view class="belong data-v-786a6504">{{dataInfo.confirmInfo.merchantInfo.name}}<text class="nick-name data-v-786a6504">{{dataInfo.confirmInfo.realName}}</text></view></block></view><view class="amount-info data-v-786a6504"><view class="amount-tip data-v-786a6504">今日交易金额（元）</view><block wx:if="{{dataInfo.payMoney}}"><view class="amount-num data-v-786a6504">{{$root.g0}}</view></block><block wx:if="{{!dataInfo.payMoney}}"><view class="amount-num data-v-786a6504">0.00</view></block></view></view></block><block wx:if="{{!isLogin}}"><view data-event-opts="{{[['tap',[['handleLogin',['$event']]]]]}}" class="user-info data-v-786a6504" bindtap="__e"><view class="user-content data-v-786a6504"><view class="nick-name data-v-786a6504">未登录</view><view class="login-tips data-v-786a6504">点击登录账号</view></view></view></block></view><block wx:if="{{isLogin}}"><view class="user-app data-v-786a6504"><view class="item data-v-786a6504"><view data-event-opts="{{[['tap',[['scanCodeConfirm',['$event']]]]]}}" class="tool data-v-786a6504" bindtap="__e"><view class="icon data-v-786a6504"><image class="image data-v-786a6504" src="/static/icon/saoyisao.png" mode="scaleToFill"></image></view><view class="text data-v-786a6504">核销卡券</view></view></view><view class="item data-v-786a6504"><view data-event-opts="{{[['tap',[['scanCodeCashier',['$event']]]]]}}" class="tool data-v-786a6504" bindtap="__e"><view class="icon data-v-786a6504"><image class="image data-v-786a6504" src="/static/icon/saoma.png" mode="scaleToFill"></image></view><view class="text data-v-786a6504">扫码收款</view></view></view></view></block><view class="my-asset data-v-786a6504"><view class="asset-left flex-box dis-flex flex-x-center data-v-786a6504"><view data-event-opts="{{[['tap',[['onTargetMember',['all']]]]]}}" class="asset-left-item data-v-786a6504" bindtap="__e"><view class="item-value dis-flex flex-x-center data-v-786a6504"><text class="data-v-786a6504">{{dataInfo.userCount}}</text></view><view class="item-name dis-flex flex-x-center data-v-786a6504"><text class="data-v-786a6504">总会员</text></view></view><view data-event-opts="{{[['tap',[['onTargetMember',['todayActive']]]]]}}" class="asset-left-item data-v-786a6504" bindtap="__e"><view class="item-value dis-flex flex-x-center data-v-786a6504"><text class="data-v-786a6504">{{dataInfo.todayUser?dataInfo.todayUser:0}}</text></view><view class="item-name dis-flex flex-x-center data-v-786a6504"><text class="data-v-786a6504">今日活跃</text></view></view><view data-event-opts="{{[['tap',[['onTargetOrder',['today']]]]]}}" class="asset-left-item data-v-786a6504" bindtap="__e"><view class="item-value dis-flex flex-x-center data-v-786a6504"><text class="data-v-786a6504">{{dataInfo.orderCount}}</text></view><view class="item-name dis-flex flex-x-center data-v-786a6504"><text class="data-v-786a6504">今日订单</text></view></view></view></view><view class="order-navbar data-v-786a6504"><block wx:for="{{orderNavbar}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['onTargetTodo',['$0'],[[['orderNavbar','',index]]]]]]]}}" class="order-navbar-item data-v-786a6504" bindtap="__e"><view class="item-icon data-v-786a6504"><text class="{{['iconfont','data-v-786a6504','icon-'+item.icon]}}"></text></view><view class="item-name data-v-786a6504">{{item.name}}</view><block wx:if="{{item.count&&item.count>0}}"><text class="order-badge data-v-786a6504">{{item.count}}</text></block></view></block></view><view class="my-service data-v-786a6504"><view class="service-title data-v-786a6504">我的管理</view><view class="service-content clearfix data-v-786a6504"><block wx:for="{{service}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block class="data-v-786a6504"><block wx:if="{{item.type=='link'}}"><view data-event-opts="{{[['tap',[['handleService',['$0'],[[['service','',index]]]]]]]}}" class="service-item data-v-786a6504" bindtap="__e"><view class="item-icon data-v-786a6504"><text class="{{['iconfont','data-v-786a6504','icon-'+item.icon]}}"></text></view><view class="item-name data-v-786a6504">{{item.name}}</view></view></block><block wx:if="{{item.type=='button'&&$platform=='MP-WEIXIN'}}"><view class="service-item data-v-786a6504"><button class="btn-normal data-v-786a6504" open-type="{{item.openType}}"><view class="item-icon data-v-786a6504"><text class="{{['iconfont','data-v-786a6504','icon-'+item.icon]}}"></text></view><view class="item-name data-v-786a6504">{{item.name}}</view></button></view></block></block></block></view></view></view></block>