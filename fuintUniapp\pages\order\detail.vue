<template>
  <view v-if="!isLoading" class="page">
    <!-- 订单状态卡片 -->
    <view class="status-card">
      <text class="status-title">
        <text v-if="order.status == OrderStatusEnum.CREATED.value">{{OrderStatusEnum.CREATED.name}}</text>
        <text v-else-if="order.status == OrderStatusEnum.PAID.value">{{OrderStatusEnum.PAID.name}}</text>
        <text v-else-if="order.status == OrderStatusEnum.DELIVERY.value">{{OrderStatusEnum.DELIVERY.name}}</text>
        <text v-else-if="order.status == OrderStatusEnum.DELIVERED.value">{{OrderStatusEnum.DELIVERED.name}}</text>
        <text v-else-if="order.status == OrderStatusEnum.RECEIVED.value">{{OrderStatusEnum.RECEIVED.name}}</text>
        <text v-else-if="order.status == OrderStatusEnum.CANCEL.value">{{OrderStatusEnum.CANCEL.name}}</text>
        <text v-else-if="order.status == OrderStatusEnum.REFUND.value">{{OrderStatusEnum.REFUND.name}}</text>
      </text>
      
      <view class="status-content">
        <text class="status-message">
          <text v-if="order.status == OrderStatusEnum.RECEIVED.value">欢迎光顾，期待您下次光临</text>
          <text v-else-if="order.status == OrderStatusEnum.PAID.value">订单已支付，正在为您准备</text>
          <text v-else-if="order.status == OrderStatusEnum.CREATED.value">请尽快完成支付</text>
          <text v-else>感谢您的支持</text>
        </text>
        
        <!-- 评价按钮（仅已完成订单显示） -->
        <view v-if="order.status == OrderStatusEnum.RECEIVED.value" class="rate-button" @click="handleRate">
          <text class="rate-text">评价一下</text>
        </view>
      </view>
      
      <!-- 取餐码 -->
      <view v-if="order.orderMode == 'oneself' && order.type == 'goods' && order.pickupCode && order.payStatus == 'B' && !order.tableInfo && ( !['C', 'H', 'G'].includes(order.status))" class="pickup-code">
        <text class="pickup-label">取餐码：</text>
        <text class="pickup-number">{{ order.pickupCode }}</text>
      </view>
    </view>

    <!-- 积分奖励提示 -->
    <view v-if="order.status == OrderStatusEnum.RECEIVED.value && order.pointAmount > 0" class="points-card">
      <text class="points-text">获得{{ order.pointAmount }}积分</text>
    </view>

    <!-- 商品详情卡片 -->
    <view class="goods-detail-card" v-if="order.goods && order.goods.length > 0">
      <view class="goods-header">
        <text class="goods-header-title">商品详情</text>
      </view>
      
      <view class="goods-list">
        <view class="goods-item" v-for="(goods, idx) in order.goods" :key="idx" v-if="goods.num > 0">
          <image class="goods-image" :src="goods.image" mode="aspectFill" @click="handleTargetGoods(goods.goodsId, goods.type)"></image>
          <view class="goods-info">
            <text class="goods-name">{{goods.name}}</text>
            <text class="goods-specs">
              <text v-for="(props, idx) in goods.specList" :key="idx">{{ props.specValue }}{{ idx < goods.specList.length - 1 ? '，' : '' }}</text>
            </text>
            <text class="goods-quantity">x{{goods.num}}</text>
          </view>
        </view>
      </view>

      <!-- 费用明细 -->
      <view class="fee-details">
        <view v-if="order.deliveryFee > 0" class="fee-item">
          <text class="fee-label">配送费</text>
        </view>
        <view v-if="order.discount > 0" class="fee-item">
          <text class="fee-label">优惠券</text>
        </view>
      </view>
    </view>

    <!-- 快递配送：配送地址 -->
    <view v-if="order.address" class="address-card">
      <view class="address-header">
        <text class="address-type">[快递配送]</text>
      </view>
      <view class="address-info">
        <text class="contact-name">{{ order.address.name }}</text>
        <text class="contact-phone">{{ order.address.mobile }}</text>
      </view>
      <view class="address-detail">
        <text class="address-region">{{ order.address.provinceName }}{{ order.address.cityName }}{{ order.address.regionName }}</text>
        <text class="address-full">{{ order.address.detail }}</text>
      </view>
    </view>
    
    <!-- 门店自提：自提地址 -->
    <view v-if="order.orderMode == 'oneself' && order.type == 'goods'" class="address-card">
      <view class="address-header">
        <text class="address-type" v-if="!order.tableInfo">[门店自提]</text>
        <text class="address-type" v-if="order.tableInfo">[桌台点餐] {{order.tableInfo.code}}</text>
      </view>
      <view class="address-info">
        <text class="contact-name">{{ order.storeInfo.name }}</text>
        <text class="contact-phone">{{ order.storeInfo.phone }}</text>
      </view>
      <view class="address-detail">
        <text class="address-full">{{ order.storeInfo.address }}</text>
      </view>
    </view>

    <!-- 订单信息卡片 -->
    <view class="order-info-card">
      <text class="info-card-title">订单信息</text>
      <view class="info-divider"></view>
      
      <view class="info-list">
        <view class="info-row">
          <text class="info-label">订单编号</text>
          <view class="info-value-wrapper">
            <text class="info-value">{{order.orderSn}}</text>
            <view class="copy-button" @click="handleCopy(order.orderSn)">
              <text class="copy-text">复制</text>
            </view>
          </view>
        </view>
        
        <view class="info-row">
          <text class="info-label">下单时间</text>
          <text class="info-value">{{order.createTime}}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">支付方式</text>
          <text class="info-value">{{ PayTypeEnum.getNameByValue(order.payType) }}</text>
        </view>
        
        <view class="info-row">
          <text class="info-label">支付金额</text>
          <view class="payment-info">
            <text class="payment-method">{{ PayTypeEnum.getNameByValue(order.payType) }}：</text>
            <text class="payment-symbol">￥</text>
            <text class="payment-amount">{{order.payAmount ? order.payAmount.toFixed(2) : '0.00'}}</text>
          </view>
        </view>
        
        <view class="info-row">
          <text class="info-label">备注信息</text>
          <text class="info-value">{{order.remark ? order.remark : '无'}}</text>
        </view>

        <!-- 预约取餐信息 -->
        <view class="info-row" v-if="order.isReservation === 'Y'">
          <text class="info-label">预约取餐</text>
          <view class="reservation-info">
            <text class="reservation-time">{{order.reservationTime}}</text>
            <text class="reservation-status" :class="reservationStatusClass">
              {{reservationStatusText}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部提示 -->
    <view class="bottom-notice">
      <text class="notice-text">如有疑问，请联系店员或致电门店</text>
    </view>

    <!-- 底部操作按钮 -->
    <view class="footer-fixed" v-if="order.status == OrderStatusEnum.CREATED.value">
      <view class="btn-wrapper">
        <block v-if="!order.tableInfo">
          <view class="btn-item" @click="onCancel(order.id)">取消订单</view>
        </block>
        <block v-if="order.tableInfo">
          <view class="btn-item" @click="onContinue(order.id, order.tableInfo.id)">继续点单</view>
        </block>
        <block>
          <view class="btn-item active" @click="onPay(order.id)">去支付</view>
        </block>
      </view>
    </view>
    
	
   <uni-popup ref="fapiaoPopup" background-color="#fff">
	   <view style="padding: 30px;">
	   <uni-link :href="fapiaoUrl"  fontSize="26"  showUnderLine text="点击前往获取发票"></uni-link>
	   </view>
   </uni-popup>
    <!-- 已支付的订单 -->
   <view class="footer-fixed" v-if="(order.payStatus == OrderStatusEnum.PAID.value) && !order.tableInfo"> 
      <view class="btn-wrapper">
		  <block v-if="order.hfOrder">
			 <view class="btn-item active" @click="handleApplyFapiao(order.orderSn)">申请发票</view>
		   </block>
       <!-- <block v-if="!order.refundInfo">
          <view class="btn-item active" @click="handleApplyRefund(order.id)">申请售后</view>
        </block>
        <block v-if="order.refundInfo">
          <view class="btn-item common" @click="handleRefundDetail(order.refundInfo.id)">售后详情</view>
        </block> -->
      </view>
    </view>
    
    <view class="footer-fixed" v-if="order.status == OrderStatusEnum.DELIVERED.value">
      <view class="btn-wrapper">
        <block>
          <view class="btn-item active" @click="onReceipt(order.id)">确认收货</view>
        </block>
      </view>
    </view>

    <!-- 支付方式弹窗 -->
    <u-popup v-model="showPayPopup" mode="bottom" :closeable="true">
      <view class="pay-popup">
        <view class="title">请选择支付方式</view>
        <view class="pop-content">
          <!-- 微信支付 -->
          <view class="pay-item dis-flex flex-x-between" @click="onSelectPayType(PayTypeEnum.WECHAT.value)">
            <view class="item-left dis-flex flex-y-center">
              <view class="item-left_icon wechat">
                <text class="iconfont icon-weixinzhifu"></text>
              </view>
              <view class="item-left_text">
                <text>{{ PayTypeEnum.WECHAT.name }}</text>
              </view>
            </view>
          </view>
          <!-- 余额支付 -->
         <!-- <view v-if="order.type != 'recharge' && order.type != 'prestore'" class="pay-item dis-flex flex-x-between" @click="onSelectPayType(PayTypeEnum.BALANCE.value)">
            <view class="item-left dis-flex flex-y-center">
              <view class="item-left_icon balance">
                <text class="iconfont icon-qiandai"></text>
              </view>
              <view class="item-left_text">
                <text>{{ PayTypeEnum.BALANCE.name }}</text>
              </view>
            </view>
          </view> -->
        </view>
      </view>
    </u-popup>
    
    <!-- 快捷导航 -->
    <shortcut/>
  </view>
</template>

<script>
  import {
    DeliveryStatusEnum,
    DeliveryTypeEnum,
    OrderStatusEnum,
    PayStatusEnum,
    PayTypeEnum,
    ReceiptStatusEnum
  } from '@/common/enum/order'
  import * as OrderApi from '@/api/order'
  import { wxPayment } from '@/utils/app'
  import Shortcut from '@/components/shortcut'

  export default {
    components: {
       Shortcut
    },
    data() {
      return {
        // 枚举类
        DeliveryStatusEnum,
        DeliveryTypeEnum,
        OrderStatusEnum,
        PayStatusEnum,
        PayTypeEnum,
        ReceiptStatusEnum,
        // 当前订单ID
        orderId: null,
		fapiaoUrl: '',
        // 桌码ID
        tableId: 0,
        // 加载中
        isLoading: true,
        // 当前订单详情
        order: {},
        // 当前设置
        setting: {},
        // 支付方式弹窗
        showPayPopup: false,
        // 刷新页面
        reflash: false
      }
    },

    computed: {
      // 预约状态文本
      reservationStatusText() {
        if (!this.order.reservationStatus) {
          return '待处理';
        }
        switch (this.order.reservationStatus) {
          case 'A':
            return '待处理';
          case 'B':
            return '已处理';
          default:
            return '待处理';
        }
      },

      // 预约状态样式类
      reservationStatusClass() {
        if (!this.order.reservationStatus) {
          return 'status-pending';
        }
        switch (this.order.reservationStatus) {
          case 'A':
            return 'status-pending';
          case 'B':
            return 'status-processed';
          default:
            return 'status-pending';
        }
      }
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad({ orderId }) {
      // 当前订单ID
      this.orderId = orderId;
      this.tableId = uni.getStorageSync("tableId") ? uni.getStorageSync("tableId") : 0;
    },

    /**
     * 生命周期函数--监听页面显示
     */
    onShow() {
      // 获取当前订单信息
      this.getOrderDetail();
    },

    methods: {

      // 获取当前订单信息
      getOrderDetail() {
        const app = this
        app.isLoading = true
        OrderApi.detail(app.orderId)
          .then(result => {
            app.order = result.data
            app.setting = result.data
            app.isLoading = false
          })
      },

      // 复制指定内容
      handleCopy(value) {
        const app = this
        uni.setClipboardData({
          data: value,
          success() {
            app.$toast('复制成功')
          }
        })
      },

      // 跳转到商品详情页面
      handleTargetGoods(goodsId, type) {
        if (goodsId && parseInt(goodsId) > 0) {
            this.$navTo('pages/goods/detail', { goodsId })
        }
      },

      // 跳转到申请售后页面
      handleApplyRefund(orderId) {
        this.$navTo('pages/refund/apply', { orderId })
      },
	  
	  async handleApplyFapiao(orderSn) {
	    const rev = await OrderApi.getInvoiceQrCode(orderSn);
		const url =rev.data;
		this.fapiaoUrl = url
		
		this.$refs.fapiaoPopup.open('center')
	  },
      
      // 售后详情
      handleRefundDetail(refundId) {
        this.$navTo('pages/refund/detail', { refundId })
      },

      // 取消订单
      onCancel() {
        const app = this
        uni.showModal({
          title: '友情提示',
          content: '确认要取消该订单吗？',
          success(o) {
            if (o.confirm) {
              OrderApi.cancel(app.orderId)
                .then(result => {
                  // 显示成功信息
                  app.$success(result.message);
                  // 刷新当前订单数据
                  app.getOrderDetail();
                })
            }
          }
        });
      },
      // 继续点单
      onContinue(orderId, tableId) {
         this.$navTo('pages/category/index');
         uni.setStorageSync('orderId', orderId);
         uni.setStorageSync('tableId', tableId);
      },

      // 点击去支付
      onPay() {
        // 显示支付方式弹窗
        this.showPayPopup = true;
      },
      
      // 确认收货
      onReceipt(orderId) {
          const app = this
          uni.showModal({
            title: '友情提示',
            content: '确认收到商品了吗？',
            success(o) {
              if (o.confirm) {
                OrderApi.receipt(orderId)
                  .then(result => {
                    // 显示成功信息
                    app.$success(result.message)
                    // 刷新当前订单数据
                    app.getOrderDetail()
                  })
              }
            }
          });
       },

      // 选择支付方式
      onSelectPayType(payType) {
        const app = this
        // 隐藏支付方式弹窗
        this.showPayPopup = false
        // 发起支付请求
        OrderApi.pay(app.orderId, payType)
          .then(result => app.onSubmitCallback(result))
          .catch(err => err)
      },

      // 订单提交成功后回调
      onSubmitCallback(result) {
        const app = this;
        if (!result.data) {
            if (result.message) {
                app.$error(result.message);
            } else {
                app.$error('支付失败');
            }
            return false;
        }
        
        // 发起微信支付
        if (result.data.payType == PayTypeEnum.WECHAT.value) {
            wxPayment(result.data.payment)
              .then(() => {
                app.$success('支付成功');
                setTimeout(() => {
                   app.getOrderDetail();
                }, 1500)
              })
              .catch(err => {
                 app.$error('订单未支付');
              })
              .finally(() => {
                 app.disabled = false;
              })
         }
         // 余额支付
         if (result.data.payType == PayTypeEnum.BALANCE.value) {
            if (result.data.orderInfo.payStatus == 'B') {
                app.$success('支付成功');
                app.disabled = false;
                setTimeout(() => {
                    // 刷新当前订单数据
                    app.getOrderDetail();
                }, 1500)
            } else {
                app.$error('支付失败');
            }
         }
       },

       // 处理评价
       handleRate() {
         const app = this;
         // 跳转到评价页面或显示评价弹窗
         app.$toast('评价功能待开发');
       }
    }
  }
</script>

<style>
  page {
    background: #f2f2f2;
  }
</style>
<style lang="scss" scoped>
  // 通用布局类
  .flex-row {
    display: flex;
    flex-direction: row;
  }
  
  .flex-col {
    display: flex;
    flex-direction: column;
  }
  
  .items-center {
    align-items: center;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  
  .justify-center {
    justify-content: center;
  }
  
  .self-stretch {
    align-self: stretch;
  }
  
  .self-center {
    align-self: center;
  }

  // 页面容器
  .page {
    padding-bottom: 204rpx;
    background-color: #f2f2f2;
    min-height: 100vh;
  }

  .header-wrapper {
    padding: 16rpx 0 24rpx;
    background-color: #fff;
    position: relative;
  }

  .header-title {
    color: #000;
    font-size: 40rpx;
    font-weight: 600;
    line-height: 38rpx;
    letter-spacing: 4rpx;
    text-align: center;
  }

  .header-icon {
    position: absolute;
    right: 204rpx;
    bottom: 23rpx;
    border-radius: 50%;
    width: 70rpx;
    height: 72rpx;
  }

  // 订单状态卡片
  .status-card {
    margin: 6rpx 26rpx 0 34rpx;
    padding: 56rpx 0 42rpx;
    background-color: #fff;
    border-radius: 18rpx;
    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .status-title {
    color: #000;
    font-size: 36rpx;
    font-weight: 300;
    line-height: 33rpx;
    letter-spacing: 4rpx;
    text-shadow: 0 0 #000;
  }

  .status-content {
    margin-top: 42rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .status-message {
    color: #404040;
    font-size: 24rpx;
    font-weight: 300;
    line-height: 25rpx;
  }

  .rate-button {
    margin-top: 42rpx;
    padding: 24rpx 0 28rpx;
    background-color: #fff;
    border-radius: 39rpx;
    width: 348rpx;
    border: 1rpx solid #333;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .rate-text {
    color: #404040;
    font-size: 24rpx;
    font-weight: 600;
    line-height: 23rpx;
    letter-spacing: 4rpx;
  }

  .pickup-code {
    margin-top: 30rpx;
    color: #404040;
    font-size: 24rpx;
    display: flex;
    align-items: center;

    .pickup-label {
      margin-right: 10rpx;
    }

    .pickup-number {
      font-size: 32rpx;
      font-weight: 600;
      color: #2b387e;
    }
  }

  // 积分奖励卡片
  .points-card {
    margin: 24rpx 26rpx 0 34rpx;
    padding: 28rpx 0;
    background-color: #fff;
    border-radius: 18rpx;
    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .points-text {
    margin-left: 32rpx;
    color: #404040;
    font-size: 24rpx;
    font-weight: 300;
    line-height: 22rpx;
  }

  // 商品详情卡片
  .goods-detail-card {
    margin: 24rpx 26rpx 0 34rpx;
    padding: 0 32rpx 116rpx;
    background-color: #fff;
    border-radius: 18rpx;
    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
  }

  .goods-header {
    padding-top: 40rpx;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.2);
  }

  .goods-header-title {
    color: #000;
    font-size: 28rpx;
    font-weight: 600;
    line-height: 26rpx;
    letter-spacing: 3rpx;
  }

  .goods-list {
    padding: 48rpx 0;
    border-top: 2rpx solid rgba(0, 0, 0, 0.2);
  }

  .goods-item {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .goods-image {
    border-radius: 8rpx;
    width: 126rpx;
    height: 124rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
  }

  .goods-info {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .goods-name {
    margin-left: 12rpx;
    color: #333;
    font-size: 28rpx;
    font-weight: 600;
    line-height: 26rpx;
    letter-spacing: 3rpx;
    margin-bottom: 24rpx;
  }

  .goods-specs {
    margin-left: 12rpx;
    color: #666;
    font-size: 20rpx;
    font-weight: 300;
    line-height: 19rpx;
    text-shadow: 0 0 #666;
    margin-bottom: 24rpx;
  }

  .goods-quantity {
    color: #666;
    font-size: 26rpx;
    line-height: 20rpx;
  }

  .fee-details {
    padding-bottom: 36rpx;
    border-bottom: 2rpx solid rgba(0, 0, 0, 0.2);
  }

  .fee-item {
    margin-bottom: 48rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .fee-label {
    color: #333;
    font-size: 24rpx;
    font-weight: 300;
    line-height: 22rpx;
    letter-spacing: 2rpx;
  }

  // 地址信息卡片
  .address-card {
    margin: 24rpx 26rpx 0 34rpx;
    padding: 40rpx 32rpx;
    background-color: #fff;
    border-radius: 18rpx;
    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
  }

  .address-header {
    margin-bottom: 20rpx;
  }

  .address-type {
    color: #333;
    font-size: 28rpx;
    font-weight: 600;
  }

  .address-info {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;
  }

  .contact-name {
    color: #999;
    font-size: 26rpx;
    margin-right: 20rpx;
  }

  .contact-phone {
    color: #999;
    font-size: 26rpx;
  }

  .address-detail {
    color: #999;
    font-size: 24rpx;
  }

  .address-region {
    margin-right: 12rpx;
  }

  .address-full {
    margin-left: 12rpx;
  }

  // 订单信息卡片
  .order-info-card {
    margin: 24rpx 24rpx 0 32rpx;
    padding: 48rpx 24rpx 48rpx 32rpx;
    background-color: #fff;
    border-radius: 18rpx;
    box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
  }

  .info-card-title {
    color: #000;
    font-size: 28rpx;
    font-weight: 600;
    line-height: 26rpx;
    letter-spacing: 3rpx;
  }

  .info-divider {
    margin-right: 20rpx;
    margin-top: 28rpx;
    background-color: rgba(0, 0, 0, 0.2);
    height: 2rpx;
  }

  .info-list {
    margin-top: 32rpx;
  }

  .info-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-label {
    color: rgba(51, 51, 51, 0.5);
    font-size: 24rpx;
    font-weight: 300;
    line-height: 22rpx;
    letter-spacing: 2rpx;
    text-shadow: 0 0 rgba(51, 51, 51, 0.5);
  }

  .info-value {
    color: #333;
    font-size: 24rpx;
    font-weight: 300;
    line-height: 19rpx;
    letter-spacing: 2rpx;
    text-shadow: 0 0 #333;
  }

  .info-value-wrapper {
    display: flex;
    align-items: center;
  }

  .copy-button {
    margin-left: 16rpx;
    padding: 4rpx 16rpx;
    border: 1rpx solid #c1c1c1;
    border-radius: 12rpx;
  }

  .copy-text {
    color: #666;
    font-size: 20rpx;
  }

  .payment-info {
    margin-right: 16rpx;
    line-height: 22rpx;
    height: 22rpx;
  }

  .payment-method {
    color: #333;
    font-size: 24rpx;
    font-weight: 300;
    line-height: 22rpx;
    letter-spacing: 2rpx;
  }

  .payment-symbol {
    color: #333;
    font-size: 20rpx;
    font-weight: 300;
    line-height: 15rpx;
  }

  .payment-amount {
    color: #333;
    font-size: 24rpx;
    font-weight: 300;
    line-height: 19rpx;
    letter-spacing: 2rpx;
  }

  // 底部提示
  .bottom-notice {
    margin-top: 26rpx;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .notice-text {
    color: #999;
    font-size: 22rpx;
    line-height: 21rpx;
    letter-spacing: 2rpx;
  }

  /* 底部操作栏 */
  .footer-fixed {
    position: fixed;
    bottom: var(--window-bottom);
    left: 0;
    right: 0;
    height: 180rpx;
    padding-bottom: 30rpx;
    z-index: 11;
    box-shadow: 0 -4rpx 40rpx 0 rgba(97, 97, 97, 0.1);
    background: #fff;

    .btn-wrapper {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding: 0 30rpx;
    }

    .btn-item {
      min-width: 164rpx;
      border-radius: 8rpx;
      padding: 20rpx 24rpx;
      font-size: 28rpx;
      color: #383838;
      text-align: center;
      border: 1rpx solid #a8a8a8;
      margin-left: 10rpx;
      
      &.common {
        color: #fff;
        border: none;
        background: linear-gradient(to right, #2b387e, #2b387e);
      }

      &.active {
        color: #fff;
        border: none;
        background: linear-gradient(to right, #f9211c, #ff6335);
      }
    }
  }

  // 弹出层-支付方式
  .pay-popup {
    padding: 25rpx 25rpx 70rpx 25rpx;
    
    .title {
      font-size: 30rpx;
      margin-bottom: 50rpx;
      font-weight: bold;
      text-align: center;
    }

    .pop-content {
      min-height: 120rpx;
      padding: 0 20rpx;

      .pay-item {
        padding: 30rpx;
        font-size: 30rpx;
        background: #fff;
        border: 1rpx solid #2b387e;
        border-radius: 8rpx;
        color: #888;
        margin-bottom: 12rpx;
        text-align: center;

        .item-left_icon {
          margin-right: 20rpx;
          font-size: 48rpx;

          &.wechat {
            color: #00c800;
          }

          &.balance {
            color: #2b387e;
          }
        }
      }
    }
  }

  // 预约信息样式
  .reservation-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .reservation-time {
      color: #ff6600;
      font-weight: bold;
      font-size: 28rpx;
      margin-bottom: 8rpx;
    }

    .reservation-status {
      font-size: 24rpx;
      padding: 4rpx 12rpx;
      border-radius: 12rpx;

      &.status-pending {
        background-color: #fff3cd;
        color: #856404;
      }

      &.status-processed {
        background-color: #d4edda;
        color: #155724;
      }
    }
  }
</style>
