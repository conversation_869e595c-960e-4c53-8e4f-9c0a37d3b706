<view class="number-box data-v-4a90999e"><view data-event-opts="{{[['touchstart',[['btnTouchStart',['minus']]]],['touchend',[['clearTimer',['$event']]]]]}}" class="{{['u-icon-minus','data-v-4a90999e',(disabled||inputVal<=min)?'u-icon-disabled':'']}}" style="{{'background:'+(bgColor)+';'+('height:'+(inputHeight+'rpx')+';')+('color:'+(color)+';')+('font-size:'+(size+'rpx')+';')+('min-height:'+('1.4em')+';')}}" bindtouchstart="__e" catchtouchend="__e"><view class="num-btn data-v-4a90999e" style="{{('font-size:'+($root.m0+10)+'rpx')}}">-</view></view><input class="{{['u-number-input','data-v-4a90999e',(disabled)?'u-input-disabled':'']}}" style="{{'color:'+(color)+';'+('font-size:'+(size+'rpx')+';')+('background:'+(bgColor)+';')+('height:'+(inputHeight+'rpx')+';')+('width:'+(inputWidth+'rpx')+';')}}" disabled="{{disabledInput||disabled}}" cursor-spacing="{{getCursorSpacing}}" type="number" data-event-opts="{{[['blur',[['onBlur',['$event']]]],['input',[['__set_model',['','inputVal','$event',[]]]]]]}}" value="{{inputVal}}" bindblur="__e" bindinput="__e"/><view data-event-opts="{{[['touchstart',[['btnTouchStart',['plus']]]],['touchend',[['clearTimer',['$event']]]]]}}" class="{{['u-icon-plus','data-v-4a90999e',(disabled||inputVal>=max)?'u-icon-disabled':'']}}" style="{{'background:'+(bgColor)+';'+('height:'+(inputHeight+'rpx')+';')+('color:'+(color)+';')+('font-size:'+(size+'rpx')+';')+('min-height:'+('1.4em')+';')}}" bindtouchstart="__e" catchtouchend="__e"><view class="num-btn data-v-4a90999e" style="{{('font-size:'+($root.m1+10)+'rpx')}}">＋</view></view></view>