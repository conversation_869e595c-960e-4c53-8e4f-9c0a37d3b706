@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
/* 引入uView库样式 */
.u-relative,
.u-rela {
  position: relative;
}
.u-absolute,
.u-abso {
  position: absolute;
}
image {
  display: inline-block;
}
view,
text {
  box-sizing: border-box;
}
.u-font-xs {
  font-size: 22rpx;
}
.u-font-sm {
  font-size: 26rpx;
}
.u-font-md {
  font-size: 28rpx;
}
.u-font-lg {
  font-size: 30rpx;
}
.u-font-xl {
  font-size: 34rpx;
}
.u-flex {

  display: flex;

  flex-direction: row;
  align-items: center;
}
.u-flex-wrap {
  flex-wrap: wrap;
}
.u-flex-nowrap {
  flex-wrap: nowrap;
}
.u-col-center {
  align-items: center;
}
.u-col-top {
  align-items: flex-start;
}
.u-col-bottom {
  align-items: flex-end;
}
.u-row-center {
  justify-content: center;
}
.u-row-left {
  justify-content: flex-start;
}
.u-row-right {
  justify-content: flex-end;
}
.u-row-between {
  justify-content: space-between;
}
.u-row-around {
  justify-content: space-around;
}
.u-text-left {
  text-align: left;
}
.u-text-center {
  text-align: center;
}
.u-text-right {
  text-align: right;
}
.u-flex-col {

  display: flex;

  flex-direction: column;
}
.u-flex-0 {
  flex: 0;
}
.u-flex-1 {
  flex: 1;
}
.u-flex-2 {
  flex: 2;
}
.u-flex-3 {
  flex: 3;
}
.u-flex-4 {
  flex: 4;
}
.u-flex-5 {
  flex: 5;
}
.u-flex-6 {
  flex: 6;
}
.u-flex-7 {
  flex: 7;
}
.u-flex-8 {
  flex: 8;
}
.u-flex-9 {
  flex: 9;
}
.u-flex-10 {
  flex: 10;
}
.u-flex-11 {
  flex: 11;
}
.u-flex-12 {
  flex: 12;
}
.u-font-9 {
  font-size: 9px;
}
.u-font-10 {
  font-size: 10px;
}
.u-font-11 {
  font-size: 11px;
}
.u-font-12 {
  font-size: 12px;
}
.u-font-13 {
  font-size: 13px;
}
.u-font-14 {
  font-size: 14px;
}
.u-font-15 {
  font-size: 15px;
}
.u-font-16 {
  font-size: 16px;
}
.u-font-17 {
  font-size: 17px;
}
.u-font-18 {
  font-size: 18px;
}
.u-font-19 {
  font-size: 19px;
}
.u-font-20 {
  font-size: 20rpx;
}
.u-font-21 {
  font-size: 21rpx;
}
.u-font-22 {
  font-size: 22rpx;
}
.u-font-23 {
  font-size: 23rpx;
}
.u-font-24 {
  font-size: 24rpx;
}
.u-font-25 {
  font-size: 25rpx;
}
.u-font-26 {
  font-size: 26rpx;
}
.u-font-27 {
  font-size: 27rpx;
}
.u-font-28 {
  font-size: 28rpx;
}
.u-font-29 {
  font-size: 29rpx;
}
.u-font-30 {
  font-size: 30rpx;
}
.u-font-31 {
  font-size: 31rpx;
}
.u-font-32 {
  font-size: 32rpx;
}
.u-font-33 {
  font-size: 33rpx;
}
.u-font-34 {
  font-size: 34rpx;
}
.u-font-35 {
  font-size: 35rpx;
}
.u-font-36 {
  font-size: 36rpx;
}
.u-font-37 {
  font-size: 37rpx;
}
.u-font-38 {
  font-size: 38rpx;
}
.u-font-39 {
  font-size: 39rpx;
}
.u-font-40 {
  font-size: 40rpx;
}
.u-margin-0, .u-m-0 {
  margin: 0rpx !important;
}
.u-padding-0, .u-p-0 {
  padding: 0rpx !important;
}
.u-m-l-0 {
  margin-left: 0rpx !important;
}
.u-p-l-0 {
  padding-left: 0rpx !important;
}
.u-margin-left-0 {
  margin-left: 0rpx !important;
}
.u-padding-left-0 {
  padding-left: 0rpx !important;
}
.u-m-t-0 {
  margin-top: 0rpx !important;
}
.u-p-t-0 {
  padding-top: 0rpx !important;
}
.u-margin-top-0 {
  margin-top: 0rpx !important;
}
.u-padding-top-0 {
  padding-top: 0rpx !important;
}
.u-m-r-0 {
  margin-right: 0rpx !important;
}
.u-p-r-0 {
  padding-right: 0rpx !important;
}
.u-margin-right-0 {
  margin-right: 0rpx !important;
}
.u-padding-right-0 {
  padding-right: 0rpx !important;
}
.u-m-b-0 {
  margin-bottom: 0rpx !important;
}
.u-p-b-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-bottom-0 {
  margin-bottom: 0rpx !important;
}
.u-padding-bottom-0 {
  padding-bottom: 0rpx !important;
}
.u-margin-2, .u-m-2 {
  margin: 2rpx !important;
}
.u-padding-2, .u-p-2 {
  padding: 2rpx !important;
}
.u-m-l-2 {
  margin-left: 2rpx !important;
}
.u-p-l-2 {
  padding-left: 2rpx !important;
}
.u-margin-left-2 {
  margin-left: 2rpx !important;
}
.u-padding-left-2 {
  padding-left: 2rpx !important;
}
.u-m-t-2 {
  margin-top: 2rpx !important;
}
.u-p-t-2 {
  padding-top: 2rpx !important;
}
.u-margin-top-2 {
  margin-top: 2rpx !important;
}
.u-padding-top-2 {
  padding-top: 2rpx !important;
}
.u-m-r-2 {
  margin-right: 2rpx !important;
}
.u-p-r-2 {
  padding-right: 2rpx !important;
}
.u-margin-right-2 {
  margin-right: 2rpx !important;
}
.u-padding-right-2 {
  padding-right: 2rpx !important;
}
.u-m-b-2 {
  margin-bottom: 2rpx !important;
}
.u-p-b-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-bottom-2 {
  margin-bottom: 2rpx !important;
}
.u-padding-bottom-2 {
  padding-bottom: 2rpx !important;
}
.u-margin-4, .u-m-4 {
  margin: 4rpx !important;
}
.u-padding-4, .u-p-4 {
  padding: 4rpx !important;
}
.u-m-l-4 {
  margin-left: 4rpx !important;
}
.u-p-l-4 {
  padding-left: 4rpx !important;
}
.u-margin-left-4 {
  margin-left: 4rpx !important;
}
.u-padding-left-4 {
  padding-left: 4rpx !important;
}
.u-m-t-4 {
  margin-top: 4rpx !important;
}
.u-p-t-4 {
  padding-top: 4rpx !important;
}
.u-margin-top-4 {
  margin-top: 4rpx !important;
}
.u-padding-top-4 {
  padding-top: 4rpx !important;
}
.u-m-r-4 {
  margin-right: 4rpx !important;
}
.u-p-r-4 {
  padding-right: 4rpx !important;
}
.u-margin-right-4 {
  margin-right: 4rpx !important;
}
.u-padding-right-4 {
  padding-right: 4rpx !important;
}
.u-m-b-4 {
  margin-bottom: 4rpx !important;
}
.u-p-b-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-bottom-4 {
  margin-bottom: 4rpx !important;
}
.u-padding-bottom-4 {
  padding-bottom: 4rpx !important;
}
.u-margin-5, .u-m-5 {
  margin: 5rpx !important;
}
.u-padding-5, .u-p-5 {
  padding: 5rpx !important;
}
.u-m-l-5 {
  margin-left: 5rpx !important;
}
.u-p-l-5 {
  padding-left: 5rpx !important;
}
.u-margin-left-5 {
  margin-left: 5rpx !important;
}
.u-padding-left-5 {
  padding-left: 5rpx !important;
}
.u-m-t-5 {
  margin-top: 5rpx !important;
}
.u-p-t-5 {
  padding-top: 5rpx !important;
}
.u-margin-top-5 {
  margin-top: 5rpx !important;
}
.u-padding-top-5 {
  padding-top: 5rpx !important;
}
.u-m-r-5 {
  margin-right: 5rpx !important;
}
.u-p-r-5 {
  padding-right: 5rpx !important;
}
.u-margin-right-5 {
  margin-right: 5rpx !important;
}
.u-padding-right-5 {
  padding-right: 5rpx !important;
}
.u-m-b-5 {
  margin-bottom: 5rpx !important;
}
.u-p-b-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-bottom-5 {
  margin-bottom: 5rpx !important;
}
.u-padding-bottom-5 {
  padding-bottom: 5rpx !important;
}
.u-margin-6, .u-m-6 {
  margin: 6rpx !important;
}
.u-padding-6, .u-p-6 {
  padding: 6rpx !important;
}
.u-m-l-6 {
  margin-left: 6rpx !important;
}
.u-p-l-6 {
  padding-left: 6rpx !important;
}
.u-margin-left-6 {
  margin-left: 6rpx !important;
}
.u-padding-left-6 {
  padding-left: 6rpx !important;
}
.u-m-t-6 {
  margin-top: 6rpx !important;
}
.u-p-t-6 {
  padding-top: 6rpx !important;
}
.u-margin-top-6 {
  margin-top: 6rpx !important;
}
.u-padding-top-6 {
  padding-top: 6rpx !important;
}
.u-m-r-6 {
  margin-right: 6rpx !important;
}
.u-p-r-6 {
  padding-right: 6rpx !important;
}
.u-margin-right-6 {
  margin-right: 6rpx !important;
}
.u-padding-right-6 {
  padding-right: 6rpx !important;
}
.u-m-b-6 {
  margin-bottom: 6rpx !important;
}
.u-p-b-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-bottom-6 {
  margin-bottom: 6rpx !important;
}
.u-padding-bottom-6 {
  padding-bottom: 6rpx !important;
}
.u-margin-8, .u-m-8 {
  margin: 8rpx !important;
}
.u-padding-8, .u-p-8 {
  padding: 8rpx !important;
}
.u-m-l-8 {
  margin-left: 8rpx !important;
}
.u-p-l-8 {
  padding-left: 8rpx !important;
}
.u-margin-left-8 {
  margin-left: 8rpx !important;
}
.u-padding-left-8 {
  padding-left: 8rpx !important;
}
.u-m-t-8 {
  margin-top: 8rpx !important;
}
.u-p-t-8 {
  padding-top: 8rpx !important;
}
.u-margin-top-8 {
  margin-top: 8rpx !important;
}
.u-padding-top-8 {
  padding-top: 8rpx !important;
}
.u-m-r-8 {
  margin-right: 8rpx !important;
}
.u-p-r-8 {
  padding-right: 8rpx !important;
}
.u-margin-right-8 {
  margin-right: 8rpx !important;
}
.u-padding-right-8 {
  padding-right: 8rpx !important;
}
.u-m-b-8 {
  margin-bottom: 8rpx !important;
}
.u-p-b-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-bottom-8 {
  margin-bottom: 8rpx !important;
}
.u-padding-bottom-8 {
  padding-bottom: 8rpx !important;
}
.u-margin-10, .u-m-10 {
  margin: 10rpx !important;
}
.u-padding-10, .u-p-10 {
  padding: 10rpx !important;
}
.u-m-l-10 {
  margin-left: 10rpx !important;
}
.u-p-l-10 {
  padding-left: 10rpx !important;
}
.u-margin-left-10 {
  margin-left: 10rpx !important;
}
.u-padding-left-10 {
  padding-left: 10rpx !important;
}
.u-m-t-10 {
  margin-top: 10rpx !important;
}
.u-p-t-10 {
  padding-top: 10rpx !important;
}
.u-margin-top-10 {
  margin-top: 10rpx !important;
}
.u-padding-top-10 {
  padding-top: 10rpx !important;
}
.u-m-r-10 {
  margin-right: 10rpx !important;
}
.u-p-r-10 {
  padding-right: 10rpx !important;
}
.u-margin-right-10 {
  margin-right: 10rpx !important;
}
.u-padding-right-10 {
  padding-right: 10rpx !important;
}
.u-m-b-10 {
  margin-bottom: 10rpx !important;
}
.u-p-b-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-bottom-10 {
  margin-bottom: 10rpx !important;
}
.u-padding-bottom-10 {
  padding-bottom: 10rpx !important;
}
.u-margin-12, .u-m-12 {
  margin: 12rpx !important;
}
.u-padding-12, .u-p-12 {
  padding: 12rpx !important;
}
.u-m-l-12 {
  margin-left: 12rpx !important;
}
.u-p-l-12 {
  padding-left: 12rpx !important;
}
.u-margin-left-12 {
  margin-left: 12rpx !important;
}
.u-padding-left-12 {
  padding-left: 12rpx !important;
}
.u-m-t-12 {
  margin-top: 12rpx !important;
}
.u-p-t-12 {
  padding-top: 12rpx !important;
}
.u-margin-top-12 {
  margin-top: 12rpx !important;
}
.u-padding-top-12 {
  padding-top: 12rpx !important;
}
.u-m-r-12 {
  margin-right: 12rpx !important;
}
.u-p-r-12 {
  padding-right: 12rpx !important;
}
.u-margin-right-12 {
  margin-right: 12rpx !important;
}
.u-padding-right-12 {
  padding-right: 12rpx !important;
}
.u-m-b-12 {
  margin-bottom: 12rpx !important;
}
.u-p-b-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-bottom-12 {
  margin-bottom: 12rpx !important;
}
.u-padding-bottom-12 {
  padding-bottom: 12rpx !important;
}
.u-margin-14, .u-m-14 {
  margin: 14rpx !important;
}
.u-padding-14, .u-p-14 {
  padding: 14rpx !important;
}
.u-m-l-14 {
  margin-left: 14rpx !important;
}
.u-p-l-14 {
  padding-left: 14rpx !important;
}
.u-margin-left-14 {
  margin-left: 14rpx !important;
}
.u-padding-left-14 {
  padding-left: 14rpx !important;
}
.u-m-t-14 {
  margin-top: 14rpx !important;
}
.u-p-t-14 {
  padding-top: 14rpx !important;
}
.u-margin-top-14 {
  margin-top: 14rpx !important;
}
.u-padding-top-14 {
  padding-top: 14rpx !important;
}
.u-m-r-14 {
  margin-right: 14rpx !important;
}
.u-p-r-14 {
  padding-right: 14rpx !important;
}
.u-margin-right-14 {
  margin-right: 14rpx !important;
}
.u-padding-right-14 {
  padding-right: 14rpx !important;
}
.u-m-b-14 {
  margin-bottom: 14rpx !important;
}
.u-p-b-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-bottom-14 {
  margin-bottom: 14rpx !important;
}
.u-padding-bottom-14 {
  padding-bottom: 14rpx !important;
}
.u-margin-15, .u-m-15 {
  margin: 15rpx !important;
}
.u-padding-15, .u-p-15 {
  padding: 15rpx !important;
}
.u-m-l-15 {
  margin-left: 15rpx !important;
}
.u-p-l-15 {
  padding-left: 15rpx !important;
}
.u-margin-left-15 {
  margin-left: 15rpx !important;
}
.u-padding-left-15 {
  padding-left: 15rpx !important;
}
.u-m-t-15 {
  margin-top: 15rpx !important;
}
.u-p-t-15 {
  padding-top: 15rpx !important;
}
.u-margin-top-15 {
  margin-top: 15rpx !important;
}
.u-padding-top-15 {
  padding-top: 15rpx !important;
}
.u-m-r-15 {
  margin-right: 15rpx !important;
}
.u-p-r-15 {
  padding-right: 15rpx !important;
}
.u-margin-right-15 {
  margin-right: 15rpx !important;
}
.u-padding-right-15 {
  padding-right: 15rpx !important;
}
.u-m-b-15 {
  margin-bottom: 15rpx !important;
}
.u-p-b-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-bottom-15 {
  margin-bottom: 15rpx !important;
}
.u-padding-bottom-15 {
  padding-bottom: 15rpx !important;
}
.u-margin-16, .u-m-16 {
  margin: 16rpx !important;
}
.u-padding-16, .u-p-16 {
  padding: 16rpx !important;
}
.u-m-l-16 {
  margin-left: 16rpx !important;
}
.u-p-l-16 {
  padding-left: 16rpx !important;
}
.u-margin-left-16 {
  margin-left: 16rpx !important;
}
.u-padding-left-16 {
  padding-left: 16rpx !important;
}
.u-m-t-16 {
  margin-top: 16rpx !important;
}
.u-p-t-16 {
  padding-top: 16rpx !important;
}
.u-margin-top-16 {
  margin-top: 16rpx !important;
}
.u-padding-top-16 {
  padding-top: 16rpx !important;
}
.u-m-r-16 {
  margin-right: 16rpx !important;
}
.u-p-r-16 {
  padding-right: 16rpx !important;
}
.u-margin-right-16 {
  margin-right: 16rpx !important;
}
.u-padding-right-16 {
  padding-right: 16rpx !important;
}
.u-m-b-16 {
  margin-bottom: 16rpx !important;
}
.u-p-b-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-bottom-16 {
  margin-bottom: 16rpx !important;
}
.u-padding-bottom-16 {
  padding-bottom: 16rpx !important;
}
.u-margin-18, .u-m-18 {
  margin: 18rpx !important;
}
.u-padding-18, .u-p-18 {
  padding: 18rpx !important;
}
.u-m-l-18 {
  margin-left: 18rpx !important;
}
.u-p-l-18 {
  padding-left: 18rpx !important;
}
.u-margin-left-18 {
  margin-left: 18rpx !important;
}
.u-padding-left-18 {
  padding-left: 18rpx !important;
}
.u-m-t-18 {
  margin-top: 18rpx !important;
}
.u-p-t-18 {
  padding-top: 18rpx !important;
}
.u-margin-top-18 {
  margin-top: 18rpx !important;
}
.u-padding-top-18 {
  padding-top: 18rpx !important;
}
.u-m-r-18 {
  margin-right: 18rpx !important;
}
.u-p-r-18 {
  padding-right: 18rpx !important;
}
.u-margin-right-18 {
  margin-right: 18rpx !important;
}
.u-padding-right-18 {
  padding-right: 18rpx !important;
}
.u-m-b-18 {
  margin-bottom: 18rpx !important;
}
.u-p-b-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-bottom-18 {
  margin-bottom: 18rpx !important;
}
.u-padding-bottom-18 {
  padding-bottom: 18rpx !important;
}
.u-margin-20, .u-m-20 {
  margin: 20rpx !important;
}
.u-padding-20, .u-p-20 {
  padding: 20rpx !important;
}
.u-m-l-20 {
  margin-left: 20rpx !important;
}
.u-p-l-20 {
  padding-left: 20rpx !important;
}
.u-margin-left-20 {
  margin-left: 20rpx !important;
}
.u-padding-left-20 {
  padding-left: 20rpx !important;
}
.u-m-t-20 {
  margin-top: 20rpx !important;
}
.u-p-t-20 {
  padding-top: 20rpx !important;
}
.u-margin-top-20 {
  margin-top: 20rpx !important;
}
.u-padding-top-20 {
  padding-top: 20rpx !important;
}
.u-m-r-20 {
  margin-right: 20rpx !important;
}
.u-p-r-20 {
  padding-right: 20rpx !important;
}
.u-margin-right-20 {
  margin-right: 20rpx !important;
}
.u-padding-right-20 {
  padding-right: 20rpx !important;
}
.u-m-b-20 {
  margin-bottom: 20rpx !important;
}
.u-p-b-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-bottom-20 {
  margin-bottom: 20rpx !important;
}
.u-padding-bottom-20 {
  padding-bottom: 20rpx !important;
}
.u-margin-22, .u-m-22 {
  margin: 22rpx !important;
}
.u-padding-22, .u-p-22 {
  padding: 22rpx !important;
}
.u-m-l-22 {
  margin-left: 22rpx !important;
}
.u-p-l-22 {
  padding-left: 22rpx !important;
}
.u-margin-left-22 {
  margin-left: 22rpx !important;
}
.u-padding-left-22 {
  padding-left: 22rpx !important;
}
.u-m-t-22 {
  margin-top: 22rpx !important;
}
.u-p-t-22 {
  padding-top: 22rpx !important;
}
.u-margin-top-22 {
  margin-top: 22rpx !important;
}
.u-padding-top-22 {
  padding-top: 22rpx !important;
}
.u-m-r-22 {
  margin-right: 22rpx !important;
}
.u-p-r-22 {
  padding-right: 22rpx !important;
}
.u-margin-right-22 {
  margin-right: 22rpx !important;
}
.u-padding-right-22 {
  padding-right: 22rpx !important;
}
.u-m-b-22 {
  margin-bottom: 22rpx !important;
}
.u-p-b-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-bottom-22 {
  margin-bottom: 22rpx !important;
}
.u-padding-bottom-22 {
  padding-bottom: 22rpx !important;
}
.u-margin-24, .u-m-24 {
  margin: 24rpx !important;
}
.u-padding-24, .u-p-24 {
  padding: 24rpx !important;
}
.u-m-l-24 {
  margin-left: 24rpx !important;
}
.u-p-l-24 {
  padding-left: 24rpx !important;
}
.u-margin-left-24 {
  margin-left: 24rpx !important;
}
.u-padding-left-24 {
  padding-left: 24rpx !important;
}
.u-m-t-24 {
  margin-top: 24rpx !important;
}
.u-p-t-24 {
  padding-top: 24rpx !important;
}
.u-margin-top-24 {
  margin-top: 24rpx !important;
}
.u-padding-top-24 {
  padding-top: 24rpx !important;
}
.u-m-r-24 {
  margin-right: 24rpx !important;
}
.u-p-r-24 {
  padding-right: 24rpx !important;
}
.u-margin-right-24 {
  margin-right: 24rpx !important;
}
.u-padding-right-24 {
  padding-right: 24rpx !important;
}
.u-m-b-24 {
  margin-bottom: 24rpx !important;
}
.u-p-b-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-bottom-24 {
  margin-bottom: 24rpx !important;
}
.u-padding-bottom-24 {
  padding-bottom: 24rpx !important;
}
.u-margin-25, .u-m-25 {
  margin: 25rpx !important;
}
.u-padding-25, .u-p-25 {
  padding: 25rpx !important;
}
.u-m-l-25 {
  margin-left: 25rpx !important;
}
.u-p-l-25 {
  padding-left: 25rpx !important;
}
.u-margin-left-25 {
  margin-left: 25rpx !important;
}
.u-padding-left-25 {
  padding-left: 25rpx !important;
}
.u-m-t-25 {
  margin-top: 25rpx !important;
}
.u-p-t-25 {
  padding-top: 25rpx !important;
}
.u-margin-top-25 {
  margin-top: 25rpx !important;
}
.u-padding-top-25 {
  padding-top: 25rpx !important;
}
.u-m-r-25 {
  margin-right: 25rpx !important;
}
.u-p-r-25 {
  padding-right: 25rpx !important;
}
.u-margin-right-25 {
  margin-right: 25rpx !important;
}
.u-padding-right-25 {
  padding-right: 25rpx !important;
}
.u-m-b-25 {
  margin-bottom: 25rpx !important;
}
.u-p-b-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-bottom-25 {
  margin-bottom: 25rpx !important;
}
.u-padding-bottom-25 {
  padding-bottom: 25rpx !important;
}
.u-margin-26, .u-m-26 {
  margin: 26rpx !important;
}
.u-padding-26, .u-p-26 {
  padding: 26rpx !important;
}
.u-m-l-26 {
  margin-left: 26rpx !important;
}
.u-p-l-26 {
  padding-left: 26rpx !important;
}
.u-margin-left-26 {
  margin-left: 26rpx !important;
}
.u-padding-left-26 {
  padding-left: 26rpx !important;
}
.u-m-t-26 {
  margin-top: 26rpx !important;
}
.u-p-t-26 {
  padding-top: 26rpx !important;
}
.u-margin-top-26 {
  margin-top: 26rpx !important;
}
.u-padding-top-26 {
  padding-top: 26rpx !important;
}
.u-m-r-26 {
  margin-right: 26rpx !important;
}
.u-p-r-26 {
  padding-right: 26rpx !important;
}
.u-margin-right-26 {
  margin-right: 26rpx !important;
}
.u-padding-right-26 {
  padding-right: 26rpx !important;
}
.u-m-b-26 {
  margin-bottom: 26rpx !important;
}
.u-p-b-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-bottom-26 {
  margin-bottom: 26rpx !important;
}
.u-padding-bottom-26 {
  padding-bottom: 26rpx !important;
}
.u-margin-28, .u-m-28 {
  margin: 28rpx !important;
}
.u-padding-28, .u-p-28 {
  padding: 28rpx !important;
}
.u-m-l-28 {
  margin-left: 28rpx !important;
}
.u-p-l-28 {
  padding-left: 28rpx !important;
}
.u-margin-left-28 {
  margin-left: 28rpx !important;
}
.u-padding-left-28 {
  padding-left: 28rpx !important;
}
.u-m-t-28 {
  margin-top: 28rpx !important;
}
.u-p-t-28 {
  padding-top: 28rpx !important;
}
.u-margin-top-28 {
  margin-top: 28rpx !important;
}
.u-padding-top-28 {
  padding-top: 28rpx !important;
}
.u-m-r-28 {
  margin-right: 28rpx !important;
}
.u-p-r-28 {
  padding-right: 28rpx !important;
}
.u-margin-right-28 {
  margin-right: 28rpx !important;
}
.u-padding-right-28 {
  padding-right: 28rpx !important;
}
.u-m-b-28 {
  margin-bottom: 28rpx !important;
}
.u-p-b-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-bottom-28 {
  margin-bottom: 28rpx !important;
}
.u-padding-bottom-28 {
  padding-bottom: 28rpx !important;
}
.u-margin-30, .u-m-30 {
  margin: 30rpx !important;
}
.u-padding-30, .u-p-30 {
  padding: 30rpx !important;
}
.u-m-l-30 {
  margin-left: 30rpx !important;
}
.u-p-l-30 {
  padding-left: 30rpx !important;
}
.u-margin-left-30 {
  margin-left: 30rpx !important;
}
.u-padding-left-30 {
  padding-left: 30rpx !important;
}
.u-m-t-30 {
  margin-top: 30rpx !important;
}
.u-p-t-30 {
  padding-top: 30rpx !important;
}
.u-margin-top-30 {
  margin-top: 30rpx !important;
}
.u-padding-top-30 {
  padding-top: 30rpx !important;
}
.u-m-r-30 {
  margin-right: 30rpx !important;
}
.u-p-r-30 {
  padding-right: 30rpx !important;
}
.u-margin-right-30 {
  margin-right: 30rpx !important;
}
.u-padding-right-30 {
  padding-right: 30rpx !important;
}
.u-m-b-30 {
  margin-bottom: 30rpx !important;
}
.u-p-b-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-bottom-30 {
  margin-bottom: 30rpx !important;
}
.u-padding-bottom-30 {
  padding-bottom: 30rpx !important;
}
.u-margin-32, .u-m-32 {
  margin: 32rpx !important;
}
.u-padding-32, .u-p-32 {
  padding: 32rpx !important;
}
.u-m-l-32 {
  margin-left: 32rpx !important;
}
.u-p-l-32 {
  padding-left: 32rpx !important;
}
.u-margin-left-32 {
  margin-left: 32rpx !important;
}
.u-padding-left-32 {
  padding-left: 32rpx !important;
}
.u-m-t-32 {
  margin-top: 32rpx !important;
}
.u-p-t-32 {
  padding-top: 32rpx !important;
}
.u-margin-top-32 {
  margin-top: 32rpx !important;
}
.u-padding-top-32 {
  padding-top: 32rpx !important;
}
.u-m-r-32 {
  margin-right: 32rpx !important;
}
.u-p-r-32 {
  padding-right: 32rpx !important;
}
.u-margin-right-32 {
  margin-right: 32rpx !important;
}
.u-padding-right-32 {
  padding-right: 32rpx !important;
}
.u-m-b-32 {
  margin-bottom: 32rpx !important;
}
.u-p-b-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-bottom-32 {
  margin-bottom: 32rpx !important;
}
.u-padding-bottom-32 {
  padding-bottom: 32rpx !important;
}
.u-margin-34, .u-m-34 {
  margin: 34rpx !important;
}
.u-padding-34, .u-p-34 {
  padding: 34rpx !important;
}
.u-m-l-34 {
  margin-left: 34rpx !important;
}
.u-p-l-34 {
  padding-left: 34rpx !important;
}
.u-margin-left-34 {
  margin-left: 34rpx !important;
}
.u-padding-left-34 {
  padding-left: 34rpx !important;
}
.u-m-t-34 {
  margin-top: 34rpx !important;
}
.u-p-t-34 {
  padding-top: 34rpx !important;
}
.u-margin-top-34 {
  margin-top: 34rpx !important;
}
.u-padding-top-34 {
  padding-top: 34rpx !important;
}
.u-m-r-34 {
  margin-right: 34rpx !important;
}
.u-p-r-34 {
  padding-right: 34rpx !important;
}
.u-margin-right-34 {
  margin-right: 34rpx !important;
}
.u-padding-right-34 {
  padding-right: 34rpx !important;
}
.u-m-b-34 {
  margin-bottom: 34rpx !important;
}
.u-p-b-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-bottom-34 {
  margin-bottom: 34rpx !important;
}
.u-padding-bottom-34 {
  padding-bottom: 34rpx !important;
}
.u-margin-35, .u-m-35 {
  margin: 35rpx !important;
}
.u-padding-35, .u-p-35 {
  padding: 35rpx !important;
}
.u-m-l-35 {
  margin-left: 35rpx !important;
}
.u-p-l-35 {
  padding-left: 35rpx !important;
}
.u-margin-left-35 {
  margin-left: 35rpx !important;
}
.u-padding-left-35 {
  padding-left: 35rpx !important;
}
.u-m-t-35 {
  margin-top: 35rpx !important;
}
.u-p-t-35 {
  padding-top: 35rpx !important;
}
.u-margin-top-35 {
  margin-top: 35rpx !important;
}
.u-padding-top-35 {
  padding-top: 35rpx !important;
}
.u-m-r-35 {
  margin-right: 35rpx !important;
}
.u-p-r-35 {
  padding-right: 35rpx !important;
}
.u-margin-right-35 {
  margin-right: 35rpx !important;
}
.u-padding-right-35 {
  padding-right: 35rpx !important;
}
.u-m-b-35 {
  margin-bottom: 35rpx !important;
}
.u-p-b-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-bottom-35 {
  margin-bottom: 35rpx !important;
}
.u-padding-bottom-35 {
  padding-bottom: 35rpx !important;
}
.u-margin-36, .u-m-36 {
  margin: 36rpx !important;
}
.u-padding-36, .u-p-36 {
  padding: 36rpx !important;
}
.u-m-l-36 {
  margin-left: 36rpx !important;
}
.u-p-l-36 {
  padding-left: 36rpx !important;
}
.u-margin-left-36 {
  margin-left: 36rpx !important;
}
.u-padding-left-36 {
  padding-left: 36rpx !important;
}
.u-m-t-36 {
  margin-top: 36rpx !important;
}
.u-p-t-36 {
  padding-top: 36rpx !important;
}
.u-margin-top-36 {
  margin-top: 36rpx !important;
}
.u-padding-top-36 {
  padding-top: 36rpx !important;
}
.u-m-r-36 {
  margin-right: 36rpx !important;
}
.u-p-r-36 {
  padding-right: 36rpx !important;
}
.u-margin-right-36 {
  margin-right: 36rpx !important;
}
.u-padding-right-36 {
  padding-right: 36rpx !important;
}
.u-m-b-36 {
  margin-bottom: 36rpx !important;
}
.u-p-b-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-bottom-36 {
  margin-bottom: 36rpx !important;
}
.u-padding-bottom-36 {
  padding-bottom: 36rpx !important;
}
.u-margin-38, .u-m-38 {
  margin: 38rpx !important;
}
.u-padding-38, .u-p-38 {
  padding: 38rpx !important;
}
.u-m-l-38 {
  margin-left: 38rpx !important;
}
.u-p-l-38 {
  padding-left: 38rpx !important;
}
.u-margin-left-38 {
  margin-left: 38rpx !important;
}
.u-padding-left-38 {
  padding-left: 38rpx !important;
}
.u-m-t-38 {
  margin-top: 38rpx !important;
}
.u-p-t-38 {
  padding-top: 38rpx !important;
}
.u-margin-top-38 {
  margin-top: 38rpx !important;
}
.u-padding-top-38 {
  padding-top: 38rpx !important;
}
.u-m-r-38 {
  margin-right: 38rpx !important;
}
.u-p-r-38 {
  padding-right: 38rpx !important;
}
.u-margin-right-38 {
  margin-right: 38rpx !important;
}
.u-padding-right-38 {
  padding-right: 38rpx !important;
}
.u-m-b-38 {
  margin-bottom: 38rpx !important;
}
.u-p-b-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-bottom-38 {
  margin-bottom: 38rpx !important;
}
.u-padding-bottom-38 {
  padding-bottom: 38rpx !important;
}
.u-margin-40, .u-m-40 {
  margin: 40rpx !important;
}
.u-padding-40, .u-p-40 {
  padding: 40rpx !important;
}
.u-m-l-40 {
  margin-left: 40rpx !important;
}
.u-p-l-40 {
  padding-left: 40rpx !important;
}
.u-margin-left-40 {
  margin-left: 40rpx !important;
}
.u-padding-left-40 {
  padding-left: 40rpx !important;
}
.u-m-t-40 {
  margin-top: 40rpx !important;
}
.u-p-t-40 {
  padding-top: 40rpx !important;
}
.u-margin-top-40 {
  margin-top: 40rpx !important;
}
.u-padding-top-40 {
  padding-top: 40rpx !important;
}
.u-m-r-40 {
  margin-right: 40rpx !important;
}
.u-p-r-40 {
  padding-right: 40rpx !important;
}
.u-margin-right-40 {
  margin-right: 40rpx !important;
}
.u-padding-right-40 {
  padding-right: 40rpx !important;
}
.u-m-b-40 {
  margin-bottom: 40rpx !important;
}
.u-p-b-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-bottom-40 {
  margin-bottom: 40rpx !important;
}
.u-padding-bottom-40 {
  padding-bottom: 40rpx !important;
}
.u-margin-42, .u-m-42 {
  margin: 42rpx !important;
}
.u-padding-42, .u-p-42 {
  padding: 42rpx !important;
}
.u-m-l-42 {
  margin-left: 42rpx !important;
}
.u-p-l-42 {
  padding-left: 42rpx !important;
}
.u-margin-left-42 {
  margin-left: 42rpx !important;
}
.u-padding-left-42 {
  padding-left: 42rpx !important;
}
.u-m-t-42 {
  margin-top: 42rpx !important;
}
.u-p-t-42 {
  padding-top: 42rpx !important;
}
.u-margin-top-42 {
  margin-top: 42rpx !important;
}
.u-padding-top-42 {
  padding-top: 42rpx !important;
}
.u-m-r-42 {
  margin-right: 42rpx !important;
}
.u-p-r-42 {
  padding-right: 42rpx !important;
}
.u-margin-right-42 {
  margin-right: 42rpx !important;
}
.u-padding-right-42 {
  padding-right: 42rpx !important;
}
.u-m-b-42 {
  margin-bottom: 42rpx !important;
}
.u-p-b-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-bottom-42 {
  margin-bottom: 42rpx !important;
}
.u-padding-bottom-42 {
  padding-bottom: 42rpx !important;
}
.u-margin-44, .u-m-44 {
  margin: 44rpx !important;
}
.u-padding-44, .u-p-44 {
  padding: 44rpx !important;
}
.u-m-l-44 {
  margin-left: 44rpx !important;
}
.u-p-l-44 {
  padding-left: 44rpx !important;
}
.u-margin-left-44 {
  margin-left: 44rpx !important;
}
.u-padding-left-44 {
  padding-left: 44rpx !important;
}
.u-m-t-44 {
  margin-top: 44rpx !important;
}
.u-p-t-44 {
  padding-top: 44rpx !important;
}
.u-margin-top-44 {
  margin-top: 44rpx !important;
}
.u-padding-top-44 {
  padding-top: 44rpx !important;
}
.u-m-r-44 {
  margin-right: 44rpx !important;
}
.u-p-r-44 {
  padding-right: 44rpx !important;
}
.u-margin-right-44 {
  margin-right: 44rpx !important;
}
.u-padding-right-44 {
  padding-right: 44rpx !important;
}
.u-m-b-44 {
  margin-bottom: 44rpx !important;
}
.u-p-b-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-bottom-44 {
  margin-bottom: 44rpx !important;
}
.u-padding-bottom-44 {
  padding-bottom: 44rpx !important;
}
.u-margin-45, .u-m-45 {
  margin: 45rpx !important;
}
.u-padding-45, .u-p-45 {
  padding: 45rpx !important;
}
.u-m-l-45 {
  margin-left: 45rpx !important;
}
.u-p-l-45 {
  padding-left: 45rpx !important;
}
.u-margin-left-45 {
  margin-left: 45rpx !important;
}
.u-padding-left-45 {
  padding-left: 45rpx !important;
}
.u-m-t-45 {
  margin-top: 45rpx !important;
}
.u-p-t-45 {
  padding-top: 45rpx !important;
}
.u-margin-top-45 {
  margin-top: 45rpx !important;
}
.u-padding-top-45 {
  padding-top: 45rpx !important;
}
.u-m-r-45 {
  margin-right: 45rpx !important;
}
.u-p-r-45 {
  padding-right: 45rpx !important;
}
.u-margin-right-45 {
  margin-right: 45rpx !important;
}
.u-padding-right-45 {
  padding-right: 45rpx !important;
}
.u-m-b-45 {
  margin-bottom: 45rpx !important;
}
.u-p-b-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-bottom-45 {
  margin-bottom: 45rpx !important;
}
.u-padding-bottom-45 {
  padding-bottom: 45rpx !important;
}
.u-margin-46, .u-m-46 {
  margin: 46rpx !important;
}
.u-padding-46, .u-p-46 {
  padding: 46rpx !important;
}
.u-m-l-46 {
  margin-left: 46rpx !important;
}
.u-p-l-46 {
  padding-left: 46rpx !important;
}
.u-margin-left-46 {
  margin-left: 46rpx !important;
}
.u-padding-left-46 {
  padding-left: 46rpx !important;
}
.u-m-t-46 {
  margin-top: 46rpx !important;
}
.u-p-t-46 {
  padding-top: 46rpx !important;
}
.u-margin-top-46 {
  margin-top: 46rpx !important;
}
.u-padding-top-46 {
  padding-top: 46rpx !important;
}
.u-m-r-46 {
  margin-right: 46rpx !important;
}
.u-p-r-46 {
  padding-right: 46rpx !important;
}
.u-margin-right-46 {
  margin-right: 46rpx !important;
}
.u-padding-right-46 {
  padding-right: 46rpx !important;
}
.u-m-b-46 {
  margin-bottom: 46rpx !important;
}
.u-p-b-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-bottom-46 {
  margin-bottom: 46rpx !important;
}
.u-padding-bottom-46 {
  padding-bottom: 46rpx !important;
}
.u-margin-48, .u-m-48 {
  margin: 48rpx !important;
}
.u-padding-48, .u-p-48 {
  padding: 48rpx !important;
}
.u-m-l-48 {
  margin-left: 48rpx !important;
}
.u-p-l-48 {
  padding-left: 48rpx !important;
}
.u-margin-left-48 {
  margin-left: 48rpx !important;
}
.u-padding-left-48 {
  padding-left: 48rpx !important;
}
.u-m-t-48 {
  margin-top: 48rpx !important;
}
.u-p-t-48 {
  padding-top: 48rpx !important;
}
.u-margin-top-48 {
  margin-top: 48rpx !important;
}
.u-padding-top-48 {
  padding-top: 48rpx !important;
}
.u-m-r-48 {
  margin-right: 48rpx !important;
}
.u-p-r-48 {
  padding-right: 48rpx !important;
}
.u-margin-right-48 {
  margin-right: 48rpx !important;
}
.u-padding-right-48 {
  padding-right: 48rpx !important;
}
.u-m-b-48 {
  margin-bottom: 48rpx !important;
}
.u-p-b-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-bottom-48 {
  margin-bottom: 48rpx !important;
}
.u-padding-bottom-48 {
  padding-bottom: 48rpx !important;
}
.u-margin-50, .u-m-50 {
  margin: 50rpx !important;
}
.u-padding-50, .u-p-50 {
  padding: 50rpx !important;
}
.u-m-l-50 {
  margin-left: 50rpx !important;
}
.u-p-l-50 {
  padding-left: 50rpx !important;
}
.u-margin-left-50 {
  margin-left: 50rpx !important;
}
.u-padding-left-50 {
  padding-left: 50rpx !important;
}
.u-m-t-50 {
  margin-top: 50rpx !important;
}
.u-p-t-50 {
  padding-top: 50rpx !important;
}
.u-margin-top-50 {
  margin-top: 50rpx !important;
}
.u-padding-top-50 {
  padding-top: 50rpx !important;
}
.u-m-r-50 {
  margin-right: 50rpx !important;
}
.u-p-r-50 {
  padding-right: 50rpx !important;
}
.u-margin-right-50 {
  margin-right: 50rpx !important;
}
.u-padding-right-50 {
  padding-right: 50rpx !important;
}
.u-m-b-50 {
  margin-bottom: 50rpx !important;
}
.u-p-b-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-bottom-50 {
  margin-bottom: 50rpx !important;
}
.u-padding-bottom-50 {
  padding-bottom: 50rpx !important;
}
.u-margin-52, .u-m-52 {
  margin: 52rpx !important;
}
.u-padding-52, .u-p-52 {
  padding: 52rpx !important;
}
.u-m-l-52 {
  margin-left: 52rpx !important;
}
.u-p-l-52 {
  padding-left: 52rpx !important;
}
.u-margin-left-52 {
  margin-left: 52rpx !important;
}
.u-padding-left-52 {
  padding-left: 52rpx !important;
}
.u-m-t-52 {
  margin-top: 52rpx !important;
}
.u-p-t-52 {
  padding-top: 52rpx !important;
}
.u-margin-top-52 {
  margin-top: 52rpx !important;
}
.u-padding-top-52 {
  padding-top: 52rpx !important;
}
.u-m-r-52 {
  margin-right: 52rpx !important;
}
.u-p-r-52 {
  padding-right: 52rpx !important;
}
.u-margin-right-52 {
  margin-right: 52rpx !important;
}
.u-padding-right-52 {
  padding-right: 52rpx !important;
}
.u-m-b-52 {
  margin-bottom: 52rpx !important;
}
.u-p-b-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-bottom-52 {
  margin-bottom: 52rpx !important;
}
.u-padding-bottom-52 {
  padding-bottom: 52rpx !important;
}
.u-margin-54, .u-m-54 {
  margin: 54rpx !important;
}
.u-padding-54, .u-p-54 {
  padding: 54rpx !important;
}
.u-m-l-54 {
  margin-left: 54rpx !important;
}
.u-p-l-54 {
  padding-left: 54rpx !important;
}
.u-margin-left-54 {
  margin-left: 54rpx !important;
}
.u-padding-left-54 {
  padding-left: 54rpx !important;
}
.u-m-t-54 {
  margin-top: 54rpx !important;
}
.u-p-t-54 {
  padding-top: 54rpx !important;
}
.u-margin-top-54 {
  margin-top: 54rpx !important;
}
.u-padding-top-54 {
  padding-top: 54rpx !important;
}
.u-m-r-54 {
  margin-right: 54rpx !important;
}
.u-p-r-54 {
  padding-right: 54rpx !important;
}
.u-margin-right-54 {
  margin-right: 54rpx !important;
}
.u-padding-right-54 {
  padding-right: 54rpx !important;
}
.u-m-b-54 {
  margin-bottom: 54rpx !important;
}
.u-p-b-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-bottom-54 {
  margin-bottom: 54rpx !important;
}
.u-padding-bottom-54 {
  padding-bottom: 54rpx !important;
}
.u-margin-55, .u-m-55 {
  margin: 55rpx !important;
}
.u-padding-55, .u-p-55 {
  padding: 55rpx !important;
}
.u-m-l-55 {
  margin-left: 55rpx !important;
}
.u-p-l-55 {
  padding-left: 55rpx !important;
}
.u-margin-left-55 {
  margin-left: 55rpx !important;
}
.u-padding-left-55 {
  padding-left: 55rpx !important;
}
.u-m-t-55 {
  margin-top: 55rpx !important;
}
.u-p-t-55 {
  padding-top: 55rpx !important;
}
.u-margin-top-55 {
  margin-top: 55rpx !important;
}
.u-padding-top-55 {
  padding-top: 55rpx !important;
}
.u-m-r-55 {
  margin-right: 55rpx !important;
}
.u-p-r-55 {
  padding-right: 55rpx !important;
}
.u-margin-right-55 {
  margin-right: 55rpx !important;
}
.u-padding-right-55 {
  padding-right: 55rpx !important;
}
.u-m-b-55 {
  margin-bottom: 55rpx !important;
}
.u-p-b-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-bottom-55 {
  margin-bottom: 55rpx !important;
}
.u-padding-bottom-55 {
  padding-bottom: 55rpx !important;
}
.u-margin-56, .u-m-56 {
  margin: 56rpx !important;
}
.u-padding-56, .u-p-56 {
  padding: 56rpx !important;
}
.u-m-l-56 {
  margin-left: 56rpx !important;
}
.u-p-l-56 {
  padding-left: 56rpx !important;
}
.u-margin-left-56 {
  margin-left: 56rpx !important;
}
.u-padding-left-56 {
  padding-left: 56rpx !important;
}
.u-m-t-56 {
  margin-top: 56rpx !important;
}
.u-p-t-56 {
  padding-top: 56rpx !important;
}
.u-margin-top-56 {
  margin-top: 56rpx !important;
}
.u-padding-top-56 {
  padding-top: 56rpx !important;
}
.u-m-r-56 {
  margin-right: 56rpx !important;
}
.u-p-r-56 {
  padding-right: 56rpx !important;
}
.u-margin-right-56 {
  margin-right: 56rpx !important;
}
.u-padding-right-56 {
  padding-right: 56rpx !important;
}
.u-m-b-56 {
  margin-bottom: 56rpx !important;
}
.u-p-b-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-bottom-56 {
  margin-bottom: 56rpx !important;
}
.u-padding-bottom-56 {
  padding-bottom: 56rpx !important;
}
.u-margin-58, .u-m-58 {
  margin: 58rpx !important;
}
.u-padding-58, .u-p-58 {
  padding: 58rpx !important;
}
.u-m-l-58 {
  margin-left: 58rpx !important;
}
.u-p-l-58 {
  padding-left: 58rpx !important;
}
.u-margin-left-58 {
  margin-left: 58rpx !important;
}
.u-padding-left-58 {
  padding-left: 58rpx !important;
}
.u-m-t-58 {
  margin-top: 58rpx !important;
}
.u-p-t-58 {
  padding-top: 58rpx !important;
}
.u-margin-top-58 {
  margin-top: 58rpx !important;
}
.u-padding-top-58 {
  padding-top: 58rpx !important;
}
.u-m-r-58 {
  margin-right: 58rpx !important;
}
.u-p-r-58 {
  padding-right: 58rpx !important;
}
.u-margin-right-58 {
  margin-right: 58rpx !important;
}
.u-padding-right-58 {
  padding-right: 58rpx !important;
}
.u-m-b-58 {
  margin-bottom: 58rpx !important;
}
.u-p-b-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-bottom-58 {
  margin-bottom: 58rpx !important;
}
.u-padding-bottom-58 {
  padding-bottom: 58rpx !important;
}
.u-margin-60, .u-m-60 {
  margin: 60rpx !important;
}
.u-padding-60, .u-p-60 {
  padding: 60rpx !important;
}
.u-m-l-60 {
  margin-left: 60rpx !important;
}
.u-p-l-60 {
  padding-left: 60rpx !important;
}
.u-margin-left-60 {
  margin-left: 60rpx !important;
}
.u-padding-left-60 {
  padding-left: 60rpx !important;
}
.u-m-t-60 {
  margin-top: 60rpx !important;
}
.u-p-t-60 {
  padding-top: 60rpx !important;
}
.u-margin-top-60 {
  margin-top: 60rpx !important;
}
.u-padding-top-60 {
  padding-top: 60rpx !important;
}
.u-m-r-60 {
  margin-right: 60rpx !important;
}
.u-p-r-60 {
  padding-right: 60rpx !important;
}
.u-margin-right-60 {
  margin-right: 60rpx !important;
}
.u-padding-right-60 {
  padding-right: 60rpx !important;
}
.u-m-b-60 {
  margin-bottom: 60rpx !important;
}
.u-p-b-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-bottom-60 {
  margin-bottom: 60rpx !important;
}
.u-padding-bottom-60 {
  padding-bottom: 60rpx !important;
}
.u-margin-62, .u-m-62 {
  margin: 62rpx !important;
}
.u-padding-62, .u-p-62 {
  padding: 62rpx !important;
}
.u-m-l-62 {
  margin-left: 62rpx !important;
}
.u-p-l-62 {
  padding-left: 62rpx !important;
}
.u-margin-left-62 {
  margin-left: 62rpx !important;
}
.u-padding-left-62 {
  padding-left: 62rpx !important;
}
.u-m-t-62 {
  margin-top: 62rpx !important;
}
.u-p-t-62 {
  padding-top: 62rpx !important;
}
.u-margin-top-62 {
  margin-top: 62rpx !important;
}
.u-padding-top-62 {
  padding-top: 62rpx !important;
}
.u-m-r-62 {
  margin-right: 62rpx !important;
}
.u-p-r-62 {
  padding-right: 62rpx !important;
}
.u-margin-right-62 {
  margin-right: 62rpx !important;
}
.u-padding-right-62 {
  padding-right: 62rpx !important;
}
.u-m-b-62 {
  margin-bottom: 62rpx !important;
}
.u-p-b-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-bottom-62 {
  margin-bottom: 62rpx !important;
}
.u-padding-bottom-62 {
  padding-bottom: 62rpx !important;
}
.u-margin-64, .u-m-64 {
  margin: 64rpx !important;
}
.u-padding-64, .u-p-64 {
  padding: 64rpx !important;
}
.u-m-l-64 {
  margin-left: 64rpx !important;
}
.u-p-l-64 {
  padding-left: 64rpx !important;
}
.u-margin-left-64 {
  margin-left: 64rpx !important;
}
.u-padding-left-64 {
  padding-left: 64rpx !important;
}
.u-m-t-64 {
  margin-top: 64rpx !important;
}
.u-p-t-64 {
  padding-top: 64rpx !important;
}
.u-margin-top-64 {
  margin-top: 64rpx !important;
}
.u-padding-top-64 {
  padding-top: 64rpx !important;
}
.u-m-r-64 {
  margin-right: 64rpx !important;
}
.u-p-r-64 {
  padding-right: 64rpx !important;
}
.u-margin-right-64 {
  margin-right: 64rpx !important;
}
.u-padding-right-64 {
  padding-right: 64rpx !important;
}
.u-m-b-64 {
  margin-bottom: 64rpx !important;
}
.u-p-b-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-bottom-64 {
  margin-bottom: 64rpx !important;
}
.u-padding-bottom-64 {
  padding-bottom: 64rpx !important;
}
.u-margin-65, .u-m-65 {
  margin: 65rpx !important;
}
.u-padding-65, .u-p-65 {
  padding: 65rpx !important;
}
.u-m-l-65 {
  margin-left: 65rpx !important;
}
.u-p-l-65 {
  padding-left: 65rpx !important;
}
.u-margin-left-65 {
  margin-left: 65rpx !important;
}
.u-padding-left-65 {
  padding-left: 65rpx !important;
}
.u-m-t-65 {
  margin-top: 65rpx !important;
}
.u-p-t-65 {
  padding-top: 65rpx !important;
}
.u-margin-top-65 {
  margin-top: 65rpx !important;
}
.u-padding-top-65 {
  padding-top: 65rpx !important;
}
.u-m-r-65 {
  margin-right: 65rpx !important;
}
.u-p-r-65 {
  padding-right: 65rpx !important;
}
.u-margin-right-65 {
  margin-right: 65rpx !important;
}
.u-padding-right-65 {
  padding-right: 65rpx !important;
}
.u-m-b-65 {
  margin-bottom: 65rpx !important;
}
.u-p-b-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-bottom-65 {
  margin-bottom: 65rpx !important;
}
.u-padding-bottom-65 {
  padding-bottom: 65rpx !important;
}
.u-margin-66, .u-m-66 {
  margin: 66rpx !important;
}
.u-padding-66, .u-p-66 {
  padding: 66rpx !important;
}
.u-m-l-66 {
  margin-left: 66rpx !important;
}
.u-p-l-66 {
  padding-left: 66rpx !important;
}
.u-margin-left-66 {
  margin-left: 66rpx !important;
}
.u-padding-left-66 {
  padding-left: 66rpx !important;
}
.u-m-t-66 {
  margin-top: 66rpx !important;
}
.u-p-t-66 {
  padding-top: 66rpx !important;
}
.u-margin-top-66 {
  margin-top: 66rpx !important;
}
.u-padding-top-66 {
  padding-top: 66rpx !important;
}
.u-m-r-66 {
  margin-right: 66rpx !important;
}
.u-p-r-66 {
  padding-right: 66rpx !important;
}
.u-margin-right-66 {
  margin-right: 66rpx !important;
}
.u-padding-right-66 {
  padding-right: 66rpx !important;
}
.u-m-b-66 {
  margin-bottom: 66rpx !important;
}
.u-p-b-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-bottom-66 {
  margin-bottom: 66rpx !important;
}
.u-padding-bottom-66 {
  padding-bottom: 66rpx !important;
}
.u-margin-68, .u-m-68 {
  margin: 68rpx !important;
}
.u-padding-68, .u-p-68 {
  padding: 68rpx !important;
}
.u-m-l-68 {
  margin-left: 68rpx !important;
}
.u-p-l-68 {
  padding-left: 68rpx !important;
}
.u-margin-left-68 {
  margin-left: 68rpx !important;
}
.u-padding-left-68 {
  padding-left: 68rpx !important;
}
.u-m-t-68 {
  margin-top: 68rpx !important;
}
.u-p-t-68 {
  padding-top: 68rpx !important;
}
.u-margin-top-68 {
  margin-top: 68rpx !important;
}
.u-padding-top-68 {
  padding-top: 68rpx !important;
}
.u-m-r-68 {
  margin-right: 68rpx !important;
}
.u-p-r-68 {
  padding-right: 68rpx !important;
}
.u-margin-right-68 {
  margin-right: 68rpx !important;
}
.u-padding-right-68 {
  padding-right: 68rpx !important;
}
.u-m-b-68 {
  margin-bottom: 68rpx !important;
}
.u-p-b-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-bottom-68 {
  margin-bottom: 68rpx !important;
}
.u-padding-bottom-68 {
  padding-bottom: 68rpx !important;
}
.u-margin-70, .u-m-70 {
  margin: 70rpx !important;
}
.u-padding-70, .u-p-70 {
  padding: 70rpx !important;
}
.u-m-l-70 {
  margin-left: 70rpx !important;
}
.u-p-l-70 {
  padding-left: 70rpx !important;
}
.u-margin-left-70 {
  margin-left: 70rpx !important;
}
.u-padding-left-70 {
  padding-left: 70rpx !important;
}
.u-m-t-70 {
  margin-top: 70rpx !important;
}
.u-p-t-70 {
  padding-top: 70rpx !important;
}
.u-margin-top-70 {
  margin-top: 70rpx !important;
}
.u-padding-top-70 {
  padding-top: 70rpx !important;
}
.u-m-r-70 {
  margin-right: 70rpx !important;
}
.u-p-r-70 {
  padding-right: 70rpx !important;
}
.u-margin-right-70 {
  margin-right: 70rpx !important;
}
.u-padding-right-70 {
  padding-right: 70rpx !important;
}
.u-m-b-70 {
  margin-bottom: 70rpx !important;
}
.u-p-b-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-bottom-70 {
  margin-bottom: 70rpx !important;
}
.u-padding-bottom-70 {
  padding-bottom: 70rpx !important;
}
.u-margin-72, .u-m-72 {
  margin: 72rpx !important;
}
.u-padding-72, .u-p-72 {
  padding: 72rpx !important;
}
.u-m-l-72 {
  margin-left: 72rpx !important;
}
.u-p-l-72 {
  padding-left: 72rpx !important;
}
.u-margin-left-72 {
  margin-left: 72rpx !important;
}
.u-padding-left-72 {
  padding-left: 72rpx !important;
}
.u-m-t-72 {
  margin-top: 72rpx !important;
}
.u-p-t-72 {
  padding-top: 72rpx !important;
}
.u-margin-top-72 {
  margin-top: 72rpx !important;
}
.u-padding-top-72 {
  padding-top: 72rpx !important;
}
.u-m-r-72 {
  margin-right: 72rpx !important;
}
.u-p-r-72 {
  padding-right: 72rpx !important;
}
.u-margin-right-72 {
  margin-right: 72rpx !important;
}
.u-padding-right-72 {
  padding-right: 72rpx !important;
}
.u-m-b-72 {
  margin-bottom: 72rpx !important;
}
.u-p-b-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-bottom-72 {
  margin-bottom: 72rpx !important;
}
.u-padding-bottom-72 {
  padding-bottom: 72rpx !important;
}
.u-margin-74, .u-m-74 {
  margin: 74rpx !important;
}
.u-padding-74, .u-p-74 {
  padding: 74rpx !important;
}
.u-m-l-74 {
  margin-left: 74rpx !important;
}
.u-p-l-74 {
  padding-left: 74rpx !important;
}
.u-margin-left-74 {
  margin-left: 74rpx !important;
}
.u-padding-left-74 {
  padding-left: 74rpx !important;
}
.u-m-t-74 {
  margin-top: 74rpx !important;
}
.u-p-t-74 {
  padding-top: 74rpx !important;
}
.u-margin-top-74 {
  margin-top: 74rpx !important;
}
.u-padding-top-74 {
  padding-top: 74rpx !important;
}
.u-m-r-74 {
  margin-right: 74rpx !important;
}
.u-p-r-74 {
  padding-right: 74rpx !important;
}
.u-margin-right-74 {
  margin-right: 74rpx !important;
}
.u-padding-right-74 {
  padding-right: 74rpx !important;
}
.u-m-b-74 {
  margin-bottom: 74rpx !important;
}
.u-p-b-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-bottom-74 {
  margin-bottom: 74rpx !important;
}
.u-padding-bottom-74 {
  padding-bottom: 74rpx !important;
}
.u-margin-75, .u-m-75 {
  margin: 75rpx !important;
}
.u-padding-75, .u-p-75 {
  padding: 75rpx !important;
}
.u-m-l-75 {
  margin-left: 75rpx !important;
}
.u-p-l-75 {
  padding-left: 75rpx !important;
}
.u-margin-left-75 {
  margin-left: 75rpx !important;
}
.u-padding-left-75 {
  padding-left: 75rpx !important;
}
.u-m-t-75 {
  margin-top: 75rpx !important;
}
.u-p-t-75 {
  padding-top: 75rpx !important;
}
.u-margin-top-75 {
  margin-top: 75rpx !important;
}
.u-padding-top-75 {
  padding-top: 75rpx !important;
}
.u-m-r-75 {
  margin-right: 75rpx !important;
}
.u-p-r-75 {
  padding-right: 75rpx !important;
}
.u-margin-right-75 {
  margin-right: 75rpx !important;
}
.u-padding-right-75 {
  padding-right: 75rpx !important;
}
.u-m-b-75 {
  margin-bottom: 75rpx !important;
}
.u-p-b-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-bottom-75 {
  margin-bottom: 75rpx !important;
}
.u-padding-bottom-75 {
  padding-bottom: 75rpx !important;
}
.u-margin-76, .u-m-76 {
  margin: 76rpx !important;
}
.u-padding-76, .u-p-76 {
  padding: 76rpx !important;
}
.u-m-l-76 {
  margin-left: 76rpx !important;
}
.u-p-l-76 {
  padding-left: 76rpx !important;
}
.u-margin-left-76 {
  margin-left: 76rpx !important;
}
.u-padding-left-76 {
  padding-left: 76rpx !important;
}
.u-m-t-76 {
  margin-top: 76rpx !important;
}
.u-p-t-76 {
  padding-top: 76rpx !important;
}
.u-margin-top-76 {
  margin-top: 76rpx !important;
}
.u-padding-top-76 {
  padding-top: 76rpx !important;
}
.u-m-r-76 {
  margin-right: 76rpx !important;
}
.u-p-r-76 {
  padding-right: 76rpx !important;
}
.u-margin-right-76 {
  margin-right: 76rpx !important;
}
.u-padding-right-76 {
  padding-right: 76rpx !important;
}
.u-m-b-76 {
  margin-bottom: 76rpx !important;
}
.u-p-b-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-bottom-76 {
  margin-bottom: 76rpx !important;
}
.u-padding-bottom-76 {
  padding-bottom: 76rpx !important;
}
.u-margin-78, .u-m-78 {
  margin: 78rpx !important;
}
.u-padding-78, .u-p-78 {
  padding: 78rpx !important;
}
.u-m-l-78 {
  margin-left: 78rpx !important;
}
.u-p-l-78 {
  padding-left: 78rpx !important;
}
.u-margin-left-78 {
  margin-left: 78rpx !important;
}
.u-padding-left-78 {
  padding-left: 78rpx !important;
}
.u-m-t-78 {
  margin-top: 78rpx !important;
}
.u-p-t-78 {
  padding-top: 78rpx !important;
}
.u-margin-top-78 {
  margin-top: 78rpx !important;
}
.u-padding-top-78 {
  padding-top: 78rpx !important;
}
.u-m-r-78 {
  margin-right: 78rpx !important;
}
.u-p-r-78 {
  padding-right: 78rpx !important;
}
.u-margin-right-78 {
  margin-right: 78rpx !important;
}
.u-padding-right-78 {
  padding-right: 78rpx !important;
}
.u-m-b-78 {
  margin-bottom: 78rpx !important;
}
.u-p-b-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-bottom-78 {
  margin-bottom: 78rpx !important;
}
.u-padding-bottom-78 {
  padding-bottom: 78rpx !important;
}
.u-margin-80, .u-m-80 {
  margin: 80rpx !important;
}
.u-padding-80, .u-p-80 {
  padding: 80rpx !important;
}
.u-m-l-80 {
  margin-left: 80rpx !important;
}
.u-p-l-80 {
  padding-left: 80rpx !important;
}
.u-margin-left-80 {
  margin-left: 80rpx !important;
}
.u-padding-left-80 {
  padding-left: 80rpx !important;
}
.u-m-t-80 {
  margin-top: 80rpx !important;
}
.u-p-t-80 {
  padding-top: 80rpx !important;
}
.u-margin-top-80 {
  margin-top: 80rpx !important;
}
.u-padding-top-80 {
  padding-top: 80rpx !important;
}
.u-m-r-80 {
  margin-right: 80rpx !important;
}
.u-p-r-80 {
  padding-right: 80rpx !important;
}
.u-margin-right-80 {
  margin-right: 80rpx !important;
}
.u-padding-right-80 {
  padding-right: 80rpx !important;
}
.u-m-b-80 {
  margin-bottom: 80rpx !important;
}
.u-p-b-80 {
  padding-bottom: 80rpx !important;
}
.u-margin-bottom-80 {
  margin-bottom: 80rpx !important;
}
.u-padding-bottom-80 {
  padding-bottom: 80rpx !important;
}
.u-reset-nvue {
  flex-direction: row;
  align-items: center;
}
.u-type-primary-light {
  color: #ecf5ff;
}
.u-type-warning-light {
  color: #fdf6ec;
}
.u-type-success-light {
  color: #dbf1e1;
}
.u-type-error-light {
  color: #fef0f0;
}
.u-type-info-light {
  color: #f4f4f5;
}
.u-type-primary-light-bg {
  background-color: #ecf5ff;
}
.u-type-warning-light-bg {
  background-color: #fdf6ec;
}
.u-type-success-light-bg {
  background-color: #dbf1e1;
}
.u-type-error-light-bg {
  background-color: #fef0f0;
}
.u-type-info-light-bg {
  background-color: #f4f4f5;
}
.u-type-primary-dark {
  color: #2b85e4;
}
.u-type-warning-dark {
  color: #f29100;
}
.u-type-success-dark {
  color: #18b566;
}
.u-type-error-dark {
  color: #dd6161;
}
.u-type-info-dark {
  color: #82848a;
}
.u-type-primary-dark-bg {
  background-color: #2b85e4;
}
.u-type-warning-dark-bg {
  background-color: #f29100;
}
.u-type-success-dark-bg {
  background-color: #18b566;
}
.u-type-error-dark-bg {
  background-color: #dd6161;
}
.u-type-info-dark-bg {
  background-color: #82848a;
}
.u-type-primary-disabled {
  color: #a0cfff;
}
.u-type-warning-disabled {
  color: #fcbd71;
}
.u-type-success-disabled {
  color: #71d5a1;
}
.u-type-error-disabled {
  color: #fab6b6;
}
.u-type-info-disabled {
  color: #c8c9cc;
}
.u-type-primary {
  color: #2979ff;
}
.u-type-warning {
  color: #ff9900;
}
.u-type-success {
  color: #19be6b;
}
.u-type-error {
  color: #fa3534;
}
.u-type-info {
  color: #909399;
}
.u-type-primary-bg {
  background-color: #2979ff;
}
.u-type-warning-bg {
  background-color: #ff9900;
}
.u-type-success-bg {
  background-color: #19be6b;
}
.u-type-error-bg {
  background-color: #fa3534;
}
.u-type-info-bg {
  background-color: #909399;
}
.u-main-color {
  color: #303133;
}
.u-content-color {
  color: #606266;
}
.u-tips-color {
  color: #909399;
}
.u-light-color {
  color: #c0c4cc;
}
page {
  color: #303133;
  font-size: 28rpx;
}
/* start--去除webkit的默认样式--start */
.u-fix-ios-appearance {
  -webkit-appearance: none;
}
/* end--去除webkit的默认样式--end */
/* start--icon图标外层套一个view，让其达到更好的垂直居中的效果--start */
.u-icon-wrap {
  display: flex;
  align-items: center;
}
/* end-icon图标外层套一个view，让其达到更好的垂直居中的效果--end */
/* start--iPhoneX底部安全区定义--start */
.safe-area-inset-bottom {
  padding-bottom: 0;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}
/* end-iPhoneX底部安全区定义--end */
/* start--各种hover点击反馈相关的类名-start */
.u-hover-class {
  opacity: 0.6;
}
.u-cell-hover {
  background-color: #f7f8f9 !important;
}
/* end--各种hover点击反馈相关的类名--end */
/* start--文本行数限制--start */
.u-line-1 {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.u-line-2 {
  -webkit-line-clamp: 2;
}
.u-line-3 {
  -webkit-line-clamp: 3;
}
.u-line-4 {
  -webkit-line-clamp: 4;
}
.u-line-5 {
  -webkit-line-clamp: 5;
}
.u-line-2, .u-line-3, .u-line-4, .u-line-5 {
  overflow: hidden;
  word-break: break-all;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}
/* end--文本行数限制--end */
/* start--Retina 屏幕下的 1px 边框--start */
.u-border,
.u-border-bottom,
.u-border-left,
.u-border-right,
.u-border-top,
.u-border-top-bottom {
  position: relative;
}
.u-border-bottom:after,
.u-border-left:after,
.u-border-right:after,
.u-border-top-bottom:after,
.u-border-top:after,
.u-border:after {

  content: " ";

  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  box-sizing: border-box;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  width: 199.8%;
  height: 199.7%;
  -webkit-transform: scale(0.5, 0.5);
          transform: scale(0.5, 0.5);
  border: 0 solid #e4e7ed;
  z-index: 2;
}
.u-border-top:after {
  border-top-width: 1px;
}
.u-border-left:after {
  border-left-width: 1px;
}
.u-border-right:after {
  border-right-width: 1px;
}
.u-border-bottom:after {
  border-bottom-width: 1px;
}
.u-border-top-bottom:after {
  border-width: 1px 0;
}
.u-border:after {
  border-width: 1px;
}
/* end--Retina 屏幕下的 1px 边框--end */
/* start--clearfix--start */
.u-clearfix:after,
.clearfix:after {

  content: "";

  display: table;
  clear: both;
}
/* end--clearfix--end */
/* start--高斯模糊tabbar底部处理--start */
.u-blur-effect-inset {
  width: 750rpx;
  height: 0px;
  background-color: #FFFFFF;
}
/* end--高斯模糊tabbar底部处理--end */
/* start--提升H5端uni.toast()的层级，避免被uView的modal等遮盖--start */








/* end--提升H5端uni.toast()的层级，避免被uView的modal等遮盖--end */
/* start--去除button的所有默认样式--start */
.u-reset-button {
  padding: 0;
  font-size: inherit;
  line-height: inherit;
  background-color: transparent;
  color: inherit;
}
.u-reset-button::after {
  border: none;
}
/* end--去除button的所有默认样式--end */
/* start--微信小程序编译后页面有组件名的元素，特别处理--start */
u-td, u-th {
  flex: 1;
  align-self: stretch;
}
.u-td {
  height: 100%;
}
u-icon {
  display: inline-flex;
  align-items: center;
}
u-grid {
  width: 100%;
  flex: 0 0 100%;
}
u-line {
  flex: 1;
}
u-switch {
  display: inline-flex;
  align-items: center;
}
u-dropdown {
  flex: 1;
}

/* end-微信小程序编译后页面有组件名的元素，特别处理--end */





/* start--头条小程序编译后页面有组件名的元素，特别处理--start */
















/* end-头条小程序编译后页面有组件名的元素，特别处理--end */






























































/* 项目基础样式 */
/* utils.scss */
/* iconfont */
@font-face {font-family: "iconfont";
  src: url('https://at.alicdn.com/t/font_2282770_mixnq71bwq.eot?t=1610188587716'); /* IE9 */
  src: url('https://at.alicdn.com/t/font_2282770_mixnq71bwq.eot?t=1610188587716#iefix') format('embedded-opentype'), /* IE6-IE8 */
  url('data:application/x-font-woff2;charset=utf-8;base64,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') format('woff2'),
  url('https://at.alicdn.com/t/font_2282770_mixnq71bwq.woff?t=1610188587716') format('woff'),
  url('https://at.alicdn.com/t/font_2282770_mixnq71bwq.ttf?t=1610188587716') format('truetype'), /* chrome, firefox, opera, Safari, Android, iOS 4.2+ */
  url('https://at.alicdn.com/t/font_2282770_mixnq71bwq.svg?t=1610188587716#iconfont') format('svg'); /* iOS 4.1- */
}
.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-delete:before {
  content: "\e668";
}
.icon-edit:before {
  content: "\e7f8";
}
.icon-daifukuan:before {
  content: "\e65c";
}
.icon-qpdingdan:before {
  content: "\e667";
}
.icon-daifahuo:before {
  content: "\e60c";
}
.icon-daishouhuo:before {
  content: "\e771";
}
.icon-jifen:before {
  content: "\e628";
}
.icon-shouhou:before {
  content: "\e788";
}
.icon-youhuiquan:before {
  content: "\e623";
}
.icon-shouhuodizhi:before {
  content: "\e67e";
}
.icon-kefu:before {
  content: "\e654";
}
.icon-lingquan:before {
  content: "\e60b";
}
.icon-bangzhu:before {
  content: "\e63a";
}
.icon-bianji:before {
  content: "\e6a4";
}
.icon-fuwu:before {
  content: "\e732";
}
.icon-gouwuche:before {
  content: "\e61b";
}
.icon-view-tile:before {
  content: "\e6c8";
}
.icon-view-list:before {
  content: "\e637";
}
.icon-arrow-down:before {
  content: "\e603";
}
.icon-arrow-up:before {
  content: "\e604";
}
.icon-add:before {
  content: "\e68b";
}
.icon-cuxiao:before {
  content: "\e68e";
}
.icon-chaping:before {
  content: "\e68f";
}
.icon-dianji:before {
  content: "\e692";
}
.icon-dizhi:before {
  content: "\e695";
}
.icon-dianpu:before {
  content: "\e697";
}
.icon-dingwei:before {
  content: "\e698";
}
.icon-fanhui:before {
  content: "\e69a";
}
.icon-fanhuidingbu:before {
  content: "\e69b";
}
.icon-favorites:before {
  content: "\e69c";
}
.icon-fenlei:before {
  content: "\e69d";
}
.icon-favorite:before {
  content: "\e69e";
}
.icon-gengduo:before {
  content: "\e6a0";
}
.icon-guanbi:before {
  content: "\e6a1";
}
.icon-fapiaoguanli:before {
  content: "\e6a2";
}
.icon-form:before {
  content: "\e6a3";
}
.icon-haoping:before {
  content: "\e6a5";
}
.icon-home:before {
  content: "\e6a6";
}
.icon-help:before {
  content: "\e6a7";
}
.icon-jiantou:before {
  content: "\e6a8";
}
.icon-huo:before {
  content: "\e6a9";
}
.icon-arrow-bottom:before {
  content: "\e6aa";
}
.icon-kanjia:before {
  content: "\e6ac";
}
.icon-iconxx:before {
  content: "\e6ae";
}
.icon-delete2:before {
  content: "\e6af";
}
.icon-mjiantou-copy:before {
  content: "\e6b1";
}
.icon-msnui-rightmini:before {
  content: "\e6b2";
}
.icon-map:before {
  content: "\e6b3";
}
.icon-menu-product:before {
  content: "\e6b4";
}
.icon-profile:before {
  content: "\e6b6";
}
.icon-shanchu:before {
  content: "\e6b7";
}
.icon-shaixuan:before {
  content: "\e6b8";
}
.icon-renshu:before {
  content: "\e6b9";
}
.icon-shehuituanti:before {
  content: "\e6ba";
}
.icon-shoucang:before {
  content: "\e6bc";
}
.icon-shezhi:before {
  content: "\e6bd";
}
.icon-shouji:before {
  content: "\e6be";
}
.icon-sousuo:before {
  content: "\e6bf";
}
.icon-shezhi1:before {
  content: "\e6c0";
}
.icon-templatedefault:before {
  content: "\e6c1";
}
.icon-tableshare:before {
  content: "\e6c2";
}
.icon-tubiao_xiangji:before {
  content: "\e6c4";
}
.icon-unif:before {
  content: "\e6c5";
}
.icon-wancheng:before {
  content: "\e6c6";
}
.icon-xiajiantous:before {
  content: "\e6ca";
}
.icon-xiangyoujiantou:before {
  content: "\e6cb";
}
.icon-x:before {
  content: "\e6cc";
}
.icon-xiaoxi:before {
  content: "\e6cd";
}
.icon-wushuju:before {
  content: "\e6ce";
}
.icon-xihuan:before {
  content: "\e6cf";
}
.icon-zhongping:before {
  content: "\e6d9";
}
.icon-z-new:before {
  content: "\e6da";
}
.icon-radio:before {
  content: "\e600";
}
.icon-zijinmingxi:before {
  content: "\e622";
}
.icon-dingdan:before {
  content: "\e60f";
}
.icon-erweima:before {
  content: "\e601";
}
.icon-tuandui:before {
  content: "\e6d1";
}
.icon-zhangben:before {
  content: "\ea2c";
}
.icon-shenhezhong:before {
  content: "\e723";
}
.icon-arrow-top:before {
  content: "\ea2d";
}
.icon-fenxiang:before {
  content: "\e63e";
}
.icon-close:before {
  content: "\e629";
}
.icon-zhuye:before {
  content: "\ee76";
}
.icon-pintuan:before {
  content: "\e804";
}
.icon-wenhao:before {
  content: "\e69f";
}
.icon-shibai:before {
  content: "\e6b5";
}
.icon-success:before {
  content: "\e644";
}
.icon-pintuan_huaban:before {
  content: "\e6c3";
}
.icon-cate:before {
  content: "\e6c7";
}
.icon-daohang:before {
  content: "\e650";
}
.icon-iconfontduihaocopy:before {
  content: "\e609";
}
.icon-duihao:before {
  content: "\e60a";
}
.icon-locate:before {
  content: "\e652";
}
.icon-dianhua:before {
  content: "\e670";
}
.icon-shijian:before {
  content: "\e661";
}
.icon-qr-extract:before {
  content: "\e635";
}
.icon-qianbao:before {
  content: "\e632";
}
.icon-weixinzhifu:before {
  content: "\e669";
}
.icon-qiandai:before {
  content: "\e656";
}
.icon-shangcheng:before {
  content: "\e605";
}
.icon-sy-yh:before {
  content: "\e7b1";
}
.icon-naozhong:before {
  content: "\e76e";
}
.icon-shouye:before {
  content: "\e73d";
}
.icon-xiaoxi1:before {
  content: "\e740";
}
.icon-kefu1:before {
  content: "\e75c";
}
.icon-artboard:before {
  content: "\e7b5";
}
.icon-miaosha-b:before {
  content: "\e646";
}
.icon-jia:before {
  content: "\e663";
}
.icon-shijian-s:before {
  content: "\e6eb";
}
.icon-shipin:before {
  content: "\e67d";
}
.icon-zhibozhong:before {
  content: "\e837";
}
@font-face {
  font-family: 'iconfont';  /* Project id 4514721 */
  src: url('https://at.alicdn.com/t/c/font_4514721_cvr3z4lc04l.woff2?t=1722241706974') format('woff2'),
       url('https://at.alicdn.com/t/c/font_4514721_cvr3z4lc04l.woff?t=1722241706974') format('woff'),
       url('https://at.alicdn.com/t/c/font_4514721_cvr3z4lc04l.ttf?t=1722241706974') format('truetype'),
       url('https://at.alicdn.com/t/c/font_4514721_cvr3z4lc04l.svg?t=1722241706974#iconfont') format('svg');
}
.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.icon-yanzhengma:before {
  content: "\e660";
}
.icon-shoujihao:before {
  content: "\e60f";
}
.icon-gouwu:before {
  content: "\e899";
}
.icon-fukuan:before {
  content: "\e62d";
}
.icon-suo:before {
  content: "\e63e";
}
.icon-tuxingyanzhengma:before {
  content: "\e799";
}
.icon-fenxiang-post:before {
  content: "\e7c5";
}
.icon-download-post:before {
  content: "\e63f";
}
.icon-gift:before {
  content: "\e682";
}
.icon-copy:before {
  content: "\e61c";
}
.container, input {
  font-family: PingFang-Medium,
                 PingFangSC-Regular,
                 Heiti,
                 Heiti SC,
                 DroidSans,
                 DroidSansFallback,
                 "Microsoft YaHei",
                 sans-serif;
  -webkit-font-smoothing: antialiased;
}
.b-f {
  background: #fff;
}
.tf-180 {
  -webkit-transform: rotate(-180deg);
          transform: rotate(-180deg);
}
.tf-90 {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}
.dis-block {
  display: block;
}
.dis-flex {
  display: flex !important;
  /* flex-wrap: wrap; */
}
.flex-box {
  flex: 1;
}
.flex-dir-row {
  flex-direction: row;
}
.flex-dir-column {
  flex-direction: column;
}
.flex-x-center {
  /* display: flex; */
  justify-content: center;
}
.flex-x-between {
  justify-content: space-between;
}
.flex-x-around {
  justify-content: space-around;
}
.flex-x-end {
  justify-content: flex-end;
}
.flex-y-center {
  align-items: center;
}
.flex-y-end {
  align-items: flex-end;
}
.flex-five {
  box-sizing: border-box;
  flex: 0 0 50%;
}
.flex-three {
  float: left;
  width: 33.3%;
}
.flex-two {
  float: left;
  width: 50%;
}
.flex-four {
  box-sizing: border-box;
  flex: 0 0 25%;
}
.t-l {
  text-align: left;
}
.t-c {
  text-align: center;
}
.t-r {
  text-align: right;
}
.p-a {
  position: absolute;
}
.p-r {
  position: relative;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.clearfix::after {
  clear: both;
  content: " ";
  display: table;
}
.oh {
  overflow: hidden;
}
.tb-lr-center {
  display: flex !important;
  justify-content: center;
  align-items: center;
}
.f-36 {
  font-size: 36rpx;
}
.f-34 {
  font-size: 34rpx;
}
.f-32 {
  font-size: 32rpx;
}
.f-31 {
  font-size: 31rpx;
}
.f-30 {
  font-size: 30rpx;
}
.f-29 {
  font-size: 29rpx;
}
.f-28 {
  font-size: 28rpx;
}
.f-26 {
  font-size: 26rpx;
}
.f-25 {
  font-size: 25rpx;
}
.f-24 {
  font-size: 24rpx;
}
.f-22 {
  font-size: 22rpx;
}
.f-w {
  font-weight: 700;
}
.f-n {
  font-weight: 400;
}
.col-f {
  color: #fff;
}
.col-3 {
  color: #333;
}
.col-6 {
  color: #666;
}
.col-7 {
  color: #777;
}
.col-8 {
  color: #888;
}
.col-9 {
  color: #999;
}
.col-m {
  color: #fa2209 !important;
}
.col-s {
  color: #be0117 !important;
}
.col-green {
  color: #0ed339 !important;
}
.cont-box {
  padding: 20rpx;
}
.cont-bot {
  margin-bottom: 120rpx;
}
.padding-box {
  padding: 0 24rpx;
  box-sizing: border-box;
}
.pl-12 {
  padding-left: 12px;
}
.pr-12 {
  padding-right: 12px;
}
.pr-6 {
  padding-right: 6px;
}
.m-top4 {
  margin-top: 4rpx;
}
.m-top10 {
  margin-top: 10rpx;
}
.m-top20 {
  margin-top: 25rpx;
}
.m-top30 {
  margin-top: 30rpx;
}
.m-l-10 {
  margin-left: 10rpx;
}
.m-l-20 {
  margin-left: 20rpx;
}
.p-bottom {
  padding-bottom: 112rpx;
}
.onelist-hidden {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.twolist-hidden {
  display: -webkit-box;
  word-break: break-all;
  text-overflow: ellipsis;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.b-r {
  border-right: 1rpx solid #eee;
}
.b-b {
  border-bottom: 1rpx solid #eee;
}
.b-t {
  border-top: 1rpx solid #eee;
}
.ts-1 {
  transition: all 0.1s;
}
.ts-2 {
  transition: all 0.2s;
}
.ts-3 {
  transition: all 0.3s;
}
.ts-5 {
  transition: all 0.5s;
}
/* 无样式button (用于伪submit) */
.btn-normal {
  display: block;
  margin: 0;
  padding: 0;
  line-height: normal;
  background: none;
  border-radius: 0;
  box-shadow: none;
  border: none;
  font-size: unset;
  text-align: unset;
  overflow: visible;
  color: inherit;
}
.btn-normal:after {
  border: none;
}
.btn-normal.button-hover {
  color: inherit;
}
button:after {
  content: none;
  border: none;
}
page {
  background: #fafafa;
}
uni-modal {
  z-index: 99999999999999 !important;
}
@-webkit-keyframes rotate {
0% {
    -webkit-transform: rotate(0deg) scale(1);
            transform: rotate(0deg) scale(1);
}
100% {
    -webkit-transform: rotate(360deg) scale(1);
            transform: rotate(360deg) scale(1);
}
}
@keyframes rotate {
0% {
    -webkit-transform: rotate(0deg) scale(1);
            transform: rotate(0deg) scale(1);
}
100% {
    -webkit-transform: rotate(360deg) scale(1);
            transform: rotate(360deg) scale(1);
}
}
/* 新增UI模板的全局样式 */
.flex-row {
  display: flex;
  flex-direction: row;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.justify-around {
  justify-content: space-around;
}
.justify-evenly {
  justify-content: space-evenly;
}
.items-start {
  align-items: flex-start;
}
.items-end {
  align-items: flex-end;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.items-stretch {
  align-items: stretch;
}
.self-start {
  align-self: flex-start;
}
.self-end {
  align-self: flex-end;
}
.self-center {
  align-self: center;
}
.self-baseline {
  align-self: baseline;
}
.self-stretch {
  align-self: stretch;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-auto {
  flex: 1 1 auto;
}
.grow {
  flex-grow: 1;
}
.grow-0 {
  flex-grow: 0;
}
.shrink {
  flex-shrink: 1;
}
.shrink-0 {
  flex-shrink: 0;
}
.relative {
  position: relative;
}

