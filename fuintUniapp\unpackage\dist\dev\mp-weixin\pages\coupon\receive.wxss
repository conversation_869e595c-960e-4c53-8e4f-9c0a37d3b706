@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-264d149a {
  padding: 20rpx;
  min-height: 100vh;
  background: #f7f7f7;
}
.search-wrapper.data-v-264d149a {
  display: flex;
  height: 100rpx;
  margin-top: 80rpx;
  padding: 0 5rpx;
}
.search-input.data-v-264d149a {
  width: 100%;
  background: #fff;
  border-radius: 10rpx 0 0 10rpx;
  box-sizing: border-box;
  overflow: hidden;
}
.search-input .search-input-wrapper.data-v-264d149a {
  display: flex;
}
.search-input .search-input-wrapper .right.data-v-264d149a {
  flex: 1;
}
.search-input .search-input-wrapper .right input.data-v-264d149a {
  font-size: 30rpx;
  height: 100rpx;
  line-height: 100rpx;
  padding-left: 30rpx;
}
.search-input .search-input-wrapper .right input .input-placeholder.data-v-264d149a {
  color: #aba9a9;
}
.search-input .search-input-wrapper .scan.data-v-264d149a {
  display: flex;
  width: 60rpx;
  justify-content: center;
  align-items: center;
}
.search-input .search-input-wrapper .scan .icon.data-v-264d149a {
  display: block;
  color: #b4b4b4;
  font-size: 48rpx;
}
/* 底部操作栏 */
.footer.data-v-264d149a {
  margin-top: 100rpx;
}
.footer .btn-wrapper.data-v-264d149a {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 5rpx;
}
.footer .btn-item.data-v-264d149a {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
}
.footer .btn-item-main.data-v-264d149a {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.footer .btn-item-main.disabled.data-v-264d149a {
  background: #ff9779;
}
