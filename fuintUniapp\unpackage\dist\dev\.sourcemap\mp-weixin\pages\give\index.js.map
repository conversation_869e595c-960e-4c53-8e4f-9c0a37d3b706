{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/give/index.vue?be08", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/give/index.vue?39a1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/give/index.vue?43f8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/give/index.vue?91d3", "uni-app:///pages/give/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/give/index.vue?7aa5", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/give/index.vue?5b80"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "value", "components", "MescrollBody", "Empty", "mixins", "data", "list", "isLoading", "tabs", "curTab", "upOption", "auto", "page", "size", "noMoreSize", "empty", "tip", "canReset", "onLoad", "onShow", "methods", "upCallback", "app", "then", "catch", "getGiveLogList", "<PERSON><PERSON><PERSON>", "type", "load", "resolve", "onChangeTab", "onRefreshList", "setTimeout", "getTabValue"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC+C9pB;AAEA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AACA;;AAEA;AACA;EACAC;EACAC;AACA;EACAD;EACAC;AACA;AAAA,eAEA;EACAC;IACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;QACA;QACAC;UACAC;QACA;MACA;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EAAA,CACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAC,6BACAC;QACA;QACA;QACAD;MACA,GACAE;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;UAAAC;UAAAf;QAAA;UAAAgB;QAAA,GACAL;UACA;UACA;UACAD;UACAO;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAR;MACA;MACAA;IACA;IAEA;IACAS;MAAA;MACA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACxKA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/give/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/give/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3e30f78d&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3e30f78d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3e30f78d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/give/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3e30f78d&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-tabs/u-tabs\" */ \"@/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.list.content, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g0 = item.mobile.substr(0, 3)\n    var g1 = item.mobile.substr(7)\n    return {\n      $orig: $orig,\n      g0: g0,\n      g1: g1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ native: true }\" @down=\"downCallback\"\n      :up=\"upOption\" @up=\"upCallback\">\n\n      <!-- tab栏 -->\n      <u-tabs :list=\"tabs\" :is-scroll=\"false\" :current=\"curTab\" active-color=\"#FA2209\" :duration=\"0.2\"\n        @change=\"onChangeTab\" />\n\n      <!-- 转赠记录 -->\n      <view class=\"widget-list\">\n        <view class=\"widget-detail\" v-for=\"(item, index) in list.content\" :key=\"index\">\n          <view class=\"row-block dis-flex flex-y-center\">\n            <view class=\"flex-box\">{{ item.createTime }}</view>\n            <view class=\"flex-box t-r\">\n              <text class=\"mobile\">{{ item.mobile.substr(0, 3) + '****' + item.mobile.substr(7) }}</text>\n            </view>\n          </view>\n          <view class=\"detail-goods row-block dis-flex\">\n            <view class=\"goods-right flex-box\">\n              <view class=\"goods-name\">\n                <text class=\"twolist-hidden\">{{ item.couponNames }}</text>\n              </view>\n              <view class=\"goods-props clearfix\">\n                <view class=\"goods-props-item\">\n                  <text>￥{{ item.money }}</text>\n                </view>\n              </view>\n              <view class=\"goods-num t-r\">\n                <text class=\"f-26 col-8\">×{{ item.num }}</text>\n              </view>\n            </view>\n          </view>\n          <view class=\"detail-order row-block\">\n            <view class=\"item dis-flex flex-x-end flex-y-center\">\n              <text class=\"\">转赠总金额：</text>\n              <text class=\"col-m\">￥{{ item.money * item.num }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </mescroll-body>\n  </view>\n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import Empty from '@/components/empty'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n  import * as giveApi from '@/api/give'\n\n  // 每页记录数量\n  const pageSize = 15\n\n  // tab栏数据\n  const tabs = [{\n    name: '收到',\n    value: 'gived'\n  }, {\n    name: '赠出',\n    value: 'give'\n  }]\n\n  export default {\n    components: {\n      MescrollBody,\n      Empty\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        // 订单列表数据\n        list: getEmptyPaginateObj(),\n        // 正在加载\n        isLoading: false,\n        // tabs栏数据\n        tabs,\n        // 当前标签索引\n        curTab: 0,\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于2条才显示无更多数据\n          noMoreSize: 2,\n          // 空布局\n          empty: {\n            tip: '亲，暂无转赠记录'\n          }\n        },\n        // 控制首次触发onShow事件时不刷新列表\n        canReset: false,\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n       // empty\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n      this.canReset && this.onRefreshList()\n      this.canReset = true\n    },\n\n    methods: {\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this\n        // 设置列表数据\n        app.getGiveLogList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length\n            const totalSize = list.totalElements\n            app.mescroll.endBySize(curPageLen, totalSize)\n          })\n          .catch(() => app.mescroll.endErr())\n      },\n\n      // 获取转赠列表\n      getGiveLogList(pageNo = 1) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          giveApi.giveLog({ type: app.getTabValue(), page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data\n              app.list.content = getMoreListData(newList, app.list, pageNo)\n              resolve(newList)\n            })\n        })\n      },\n\n      // 切换标签项\n      onChangeTab(index) {\n        const app = this\n        // 设置当前选中的标签\n        app.curTab = index\n        // 刷新列表\n        app.onRefreshList()\n      },\n\n      // 刷新订单列表\n      onRefreshList() {\n        this.list = getEmptyPaginateObj()\n        setTimeout(() => {\n          this.mescroll.resetUpScroll()\n        }, 120)\n      },\n\n      // 获取当前标签项的值\n      getTabValue() {\n        return this.tabs[this.curTab].value\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .widget-detail {\n    box-sizing: border-box;\n    background: #fff;\n    margin-bottom: 10rpx;\n    border: solid 2px #f5f5f5;\n    padding: 30rpx;\n\n    .row-block {\n      padding: 0 20rpx;\n      min-height: 50rpx;\n    }\n\n    .detail-goods {\n      padding: 20rpx;\n\n      .goods-right {\n        padding: 15rpx 0;\n      }\n      .mobile {\n        color: #888888;  \n      }\n\n      .goods-name {\n        margin-bottom: 10rpx;\n        font-size: 34rpx;\n      }\n\n      .goods-props {\n        margin-top: 14rpx;\n        // height: 40rpx;\n        color: #ababab;\n        font-size: 24rpx;\n        overflow: hidden;\n\n        .goods-props-item {\n          display: inline-block;\n          margin-right: 14rpx;\n          padding: 4rpx 16rpx;\n          border-radius: 12rpx;\n          background-color: #F5F5F5;\n          width: auto;\n        }\n      }\n\n    }\n\n    .detail-operate {\n      padding-bottom: 20rpx;\n\n      .detail-btn {\n        border-radius: 4px;\n        border: 1rpx solid #ccc;\n        padding: 8rpx 20rpx;\n        font-size: 28rpx;\n        color: #555;\n        margin-left: 10rpx;\n      }\n    }\n\n    .detail-order {\n      padding: 10rpx 20rpx;\n      font-size: 26rpx;\n      line-height: 50rpx;\n      height: 50rpx;\n\n      .item {\n        margin-bottom: 10rpx;\n\n        &:last-child {\n          margin-bottom: 0;\n        }\n      }\n    }\n  }\n  // 空数据按钮\n  .empty-ipt {\n    width: 220rpx;\n    margin: 10px auto;\n    font-size: 28rpx;\n    height: 64rpx;\n    line-height: 64rpx;\n    text-align: center;\n    color: #fff;\n    border-radius: 5rpx;\n    background: linear-gradient(to right, $fuint-theme, $fuint-theme);\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=3e30f78d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=3e30f78d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420672\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}