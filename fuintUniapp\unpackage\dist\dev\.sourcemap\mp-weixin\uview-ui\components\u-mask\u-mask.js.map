{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-mask/u-mask.vue?ad65", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-mask/u-mask.vue?9c83", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-mask/u-mask.vue?c3c4", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-mask/u-mask.vue?fc89", "uni-app:///uview-ui/components/u-mask/u-mask.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-mask/u-mask.vue?df2a", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-mask/u-mask.vue?fa0a"], "names": ["name", "props", "show", "type", "default", "zIndex", "customStyle", "zoom", "duration", "maskClickAble", "data", "zoomStyle", "transform", "scale", "watch", "computed", "maskStyle", "style", "methods", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxBA;AAAA;AAAA;AAAA;AAA0pB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACU9qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,gBAaA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAZ;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAa;IACAC;MACA;MACAC;MACA,mFACAA;MACAA;MACA;MACA,kFACAA,QACA,iBACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAywC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACA7xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-mask/u-mask.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-mask.vue?vue&type=template&id=4bfa3b00&scoped=true&\"\nvar renderjs\nimport script from \"./u-mask.vue?vue&type=script&lang=js&\"\nexport * from \"./u-mask.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-mask.vue?vue&type=style&index=0&id=4bfa3b00&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4bfa3b00\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-mask/u-mask.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=template&id=4bfa3b00&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.maskStyle, _vm.zoomStyle])\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      $event.stopPropagation()\n      $event.preventDefault()\n      return (function () {})($event)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"u-mask\" hover-stop-propagation :style=\"[maskStyle, zoomStyle]\" @tap=\"click\" @touchmove.stop.prevent=\"() => {}\" :class=\"{\n        'u-mask-zoom': zoom,\n        'u-mask-show': show\n    }\">\n        <slot />\n    </view>\n</template>\n\n<script>\n    /**\n     * mask 遮罩\n     * @description 创建一个遮罩层，用于强调特定的页面元素，并阻止用户对遮罩下层的内容进行操作，一般用于弹窗场景\n     * @tutorial https://www.uviewui.com/components/mask.html\n     * @property {Boolean} show 是否显示遮罩（默认false）\n     * @property {String Number} z-index z-index 层级（默认1070）\n     * @property {Object} custom-style 自定义样式对象，见上方说明\n     * @property {String Number} duration 动画时长，单位毫秒（默认300）\n     * @property {Boolean} zoom 是否使用scale对遮罩进行缩放（默认true）\n     * @property {Boolean} mask-click-able 遮罩是否可点击，为false时点击不会发送click事件（默认true）\n     * @event {Function} click mask-click-able为true时，点击遮罩发送此事件\n     * @example <u-mask :show=\"show\" @click=\"show = false\"></u-mask>\n     */\n    export default {\n        name: \"u-mask\",\n        props: {\n            // 是否显示遮罩\n            show: {\n                type: Boolean,\n                default: false\n            },\n            // 层级z-index\n            zIndex: {\n                type: [Number, String],\n                default: ''\n            },\n            // 用户自定义样式\n            customStyle: {\n                type: Object,\n                default () {\n                    return {}\n                }\n            },\n            // 遮罩的动画样式， 是否使用使用zoom进行scale进行缩放\n            zoom: {\n                type: Boolean,\n                default: true\n            },\n            // 遮罩的过渡时间，单位为ms\n            duration: {\n                type: [Number, String],\n                default: 300\n            },\n            // 是否可以通过点击遮罩进行关闭\n            maskClickAble: {\n                type: Boolean,\n                default: true\n            }\n        },\n        data() {\n            return {\n                zoomStyle: {\n                    transform: ''\n                },\n                scale: 'scale(1.2, 1.2)'\n            }\n        },\n        watch: {\n            show(n) {\n                if(n && this.zoom) {\n                    // 当展示遮罩的时候，设置scale为1，达到缩小(原来为1.2)的效果\n                    this.zoomStyle.transform = 'scale(1, 1)';\n                } else if(!n && this.zoom) {\n                    // 当隐藏遮罩的时候，设置scale为1.2，达到放大(因为显示遮罩时已重置为1)的效果\n                    this.zoomStyle.transform = this.scale;\n                }\n            }\n        },\n        computed: {\n            maskStyle() {\n                let style = {};\n                style.backgroundColor = \"rgba(0, 0, 0, 0.6)\";\n                if(this.show) style.zIndex = this.zIndex ? this.zIndex : this.$u.zIndex.mask;\n                else style.zIndex = -1;\n                style.transition = `all ${this.duration / 1000}s ease-in-out`;\n                // 判断用户传递的对象是否为空，不为空就进行合并\n                if (Object.keys(this.customStyle).length) style = { \n                    ...style,\n                    ...this.customStyle\n                };\n                return style;\n            }\n        },\n        methods: {\n            click() {\n                if (!this.maskClickAble) return;\n                this.$emit('click');\n            }\n        }\n    }\n</script>\n\n<style lang=\"scss\" scoped>\n    @import \"../../libs/css/style.components.scss\";\n    \n    .u-mask {\n        position: fixed;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        opacity: 0;\n        transition: transform 0.3s;\n    }\n\n    .u-mask-show {\n        opacity: 1;\n    }\n    \n    .u-mask-zoom {\n        transform: scale(1.2, 1.2);\n    }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=style&index=0&id=4bfa3b00&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-mask.vue?vue&type=style&index=0&id=4bfa3b00&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425049\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}