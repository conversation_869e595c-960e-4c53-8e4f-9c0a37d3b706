@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.entrance.data-v-0bfe49da {
  position: relative;
  margin-top: -20rpx;
  margin-bottom: 30rpx;
  border-radius: 10rpx;
  background-color: #ffffff;
  box-shadow: #666;
  padding: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: solid 1rpx #ccc;
  margin: 10rpx 10rpx 25rpx 10rpx;
}
.entrance .item.data-v-0bfe49da {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}
.entrance .item.data-v-0bfe49da:nth-child(1):after {
  content: "";
  position: absolute;
  width: 1rpx;
  background-color: #ccc;
  right: 0;
  height: 100%;
  -webkit-transform: scaleX(0.5) scaleY(0.8);
          transform: scaleX(0.5) scaleY(0.8);
}
.entrance .item .icon.data-v-0bfe49da {
  width: 120rpx;
  height: 120rpx;
  margin: 28rpx;
}
.entrance .item .title.data-v-0bfe49da {
  font-size: 36rpx;
  color: #666;
  font-weight: 600;
}
.entrance .item .content.data-v-0bfe49da {
  font-size: 28rpx;
  color: #666;
  font-weight: 400;
}
