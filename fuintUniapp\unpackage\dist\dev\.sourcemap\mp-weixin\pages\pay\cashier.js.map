{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?96d6", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?8ec5", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?a565", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?f487", "uni-app:///pages/pay/cashier.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?cb4d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?7ba2", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?7820", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/pay/cashier.vue?854b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLoading", "userCode", "userInfo", "disabled", "remark", "inputValue", "onLoad", "methods", "onChangeMoney", "getPageData", "app", "Promise", "then", "getUserInfo", "UserApi", "code", "resolve", "onSubmit", "SettlementApi", "type", "cashierPayAmount", "cashierDiscountAmount", "payType", "userId", "catch", "onSubmitCallback", "uni", "title", "content", "showCancel", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACa;AACyB;;;AAG5F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA4oB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgChqB;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;MACAC;MACAC,iCACAC;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QACAC;UAAAC;QAAA,GACAH;UACAF;UACAM;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAP;QACA;MACA;;MAEA;MACAA;;MAEA;MACAQ;QAAAC;QAAAC;QAAAC;QAAAC;QAAAC;QAAAnB;MAAA,GACAQ;QAAA;MAAA,GACAY;QACA;UACA;UACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAf;QACAA;QACA;MACA;MAEAgB;QACAC;QACAC;QACAC;QACAC;UACA;YACApB;YACAA;YACAA;YACAA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1IA;AAAA;AAAA;AAAA;AAA+6B,CAAgB,y4BAAG,EAAC,C;;;;;;;;;;;ACAn8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA+uC,CAAgB,qqCAAG,EAAC,C;;;;;;;;;;;ACAnwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pay/cashier.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pay/cashier.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./cashier.vue?vue&type=template&id=b4d6bb1a&scoped=true&\"\nvar renderjs\nimport script from \"./cashier.vue?vue&type=script&lang=js&\"\nexport * from \"./cashier.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cashier.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./cashier.vue?vue&type=style&index=1&id=b4d6bb1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b4d6bb1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pay/cashier.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cashier.vue?vue&type=template&id=b4d6bb1a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cashier.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cashier.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\" v-if=\"userInfo.id\">\n    <view class=\"account-panel dis-flex flex-y-center\">\n      <view class=\"panel-lable\">\n        <text>会员账户余额</text>\n      </view>\n      <view class=\"panel-balance flex-box\">\n        <text>￥{{ userInfo.balance }}</text>\n      </view>\n    </view>\n    <view class=\"recharge-panel\">\n      <!-- 收款金额输入框 -->\n      <view class=\"recharge-input\">\r\n          <view class=\"label\">收款金额</view>\r\n          <text class=\"uni\">￥</text>\n          <input type=\"number\" class=\"uni-input\" placeholder=\"请输入收款金额（单位：元）\" placeholder-class=\"placeholder\" v-model=\"inputValue\" @input=\"onChangeMoney\" />\n      </view>\n      <view class=\"remark-input\">\r\n        <view class=\"label\">备注信息</view>\n        <input type=\"text\" v-model=\"remark\" placeholder-class=\"placeholder\" placeholder=\"请输入收款备注\"/>\n      </view>\n      <!-- 确认按钮 -->\n      <view class=\"recharge-submit btn-submit\">\n        <form @submit=\"onSubmit\">\n          <button class=\"button\" formType=\"submit\" :disabled=\"disabled\">立即收款</button>\n        </form>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as UserApi from '@/api/user'\n  import * as BalanceApi from '@/api/balance'\n  import * as SettlementApi from '@/api/settlement'\n  import { wxPayment } from '@/utils/app'\n\n  export default {\n    data() {\n      return {\n        // 正在加载\n        isLoading: true,\n        // 会员二维码\n        userCode: \"\",\n        // 会员信息\n        userInfo: {},\n        // 按钮禁用\n        disabled: false,\n        // 收款备注\n        remark: '',\n        // 自定义金额\n        inputValue: '',\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      this.userCode = options.code\n      // 获取页面数据\n      this.getPageData()\n    },\n\n    methods: {\n      // 收款金额输入框\n      onChangeMoney(e) {\n        this.inputValue = e.target.value;\n        this.rechargeAmount = 0;\n      },\n\n      // 获取页面数据\n      getPageData() {\n        const app = this\n        app.isLoading = true\n        Promise.all([app.getUserInfo()])\n          .then(() => app.isLoading = false)\n      },\n\n      // 获取会员信息\n      getUserInfo() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          UserApi.info({ code: app.userCode })\n            .then(result => {\n              app.userInfo = result.data.userInfo;\n              resolve(app.userInfo);\n            })\n        })\n      },\n\n      // 立即收款\n      onSubmit(e) {\n        const app = this\n        if (app.inputValue.length < 1) {\n            app.$error('请输入收款金额！');\n            return false;\n        }\n        \n        // 按钮禁用\n        app.disabled = true\n        \n        // 提交到后端\n        SettlementApi.doCashier({ type: 'payment', cashierPayAmount: app.inputValue, cashierDiscountAmount: 0, payType: 'BALANCE', userId: app.userInfo.id, remark: app.remark })\n          .then(result => app.onSubmitCallback(result))\n          .catch(err => {\n            if (err.result) {\n                const errData = err.result.data;\n                if (errData) {\n                    return false;\n                }\n            }\n        })\n      },\n      // 收款回调\n      onSubmitCallback(result) {\n          const app = this\n          if (result.code != '200') {\n              app.$error(result.message ? result.message : '收款失败');\n              app.disabled = false;\n              return false;\n          }\n          \n          uni.showModal({\n            title: '收款结果',\n            content: '收款成功！',\n              showCancel: false,\n            success(o) {\n              if (o.confirm) {\n                  app.inputValue = '';\n                  app.remark = '';\n                  app.disabled = false;\n                  app.getPageData();\n              }\n            }\n          })\n      }\n    }\n  }\n</script>\r\n\r\n<style>\n    .placeholder {\n        color: #ccc;\r\n        font-size: 28rpx;\r\n        font-weight: normal;\n    }\n</style>\n\n<style lang=\"scss\" scoped>\n  page,\n  .container {\n    background: #fff;\n  }\n\n  .container {\n    padding-bottom: 70rpx;\n  }\n\n  /* 账户面板 */\n  .account-panel {\n    width: 650rpx;\n    height: 180rpx;\n    margin: 50rpx auto;\n    padding: 0 60rpx;\n    box-sizing: border-box;\n    border-radius: 8rpx;\n    color: #fff;\n    background: $fuint-theme;\n    box-shadow: 0 5px 22px 0 rgba(0, 0, 0, 0.26);\n  }\n\n  .panel-lable {\n    font-size: 32rpx;\n  }\n\n  .recharge-label {\n    color: rgb(51, 51, 51);\n    font-size: 32rpx;\n    margin-bottom: 25rpx;\n  }\n\n  .panel-balance {\n    text-align: right;\n    font-size: 46rpx;\n  }\n\n  .recharge-panel {\n    margin-top: 60rpx;\n    padding: 0 60rpx;\n  }\n  .recharge-input {\n    margin-top: 25rpx;\r\n    .label {\r\n        margin-bottom: 10rpx;\r\n    }\r\n    .uni {\r\n        font-weight: bold;\r\n        font-size: 32rpx;\r\n    }\n    input {\n      border: 1rpx solid rgb(228, 228, 228);\n      border-radius: 6rpx;\n      padding: 20rpx;\r\n      font-weight: bold;\n      font-size: 58rpx;\n    }\n  }\n  .remark-input {\r\n      .label {\r\n          margin-bottom: 10rpx;\r\n      }\n      margin-top: 50rpx;\n      input {\n        border: 1rpx solid rgb(228, 228, 228);\n        border-radius: 6rpx;\n        padding: 20rpx;\n        font-size: 26rpx;\n      }\n  }\n\n  /* 立即充值 */\n  .recharge-submit {\n    margin-top: 70rpx;\n  }\n\n  .btn-submit {\n    .button {\n      font-size: 30rpx;\n      background: linear-gradient(to right, #f9211c, #ff6335);\n      border: none;\n      color: white;\n      border-radius: 40rpx;\n      padding: 0 120rpx;\n      line-height: 3;\n    }\n\n    .button[disabled] {\n      background: #ff6335;\n      border-color: #ff6335;\n      color: white;\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cashier.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cashier.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891417986\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cashier.vue?vue&type=style&index=1&id=b4d6bb1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cashier.vue?vue&type=style&index=1&id=b4d6bb1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420721\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}