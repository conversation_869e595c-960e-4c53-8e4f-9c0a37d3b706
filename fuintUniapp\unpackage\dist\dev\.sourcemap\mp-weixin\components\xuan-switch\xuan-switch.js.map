{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/xuan-switch/xuan-switch.vue?f0ed", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/xuan-switch/xuan-switch.vue?014e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/xuan-switch/xuan-switch.vue?8fc4", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/xuan-switch/xuan-switch.vue?a1d3", "uni-app:///components/xuan-switch/xuan-switch.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/xuan-switch/xuan-switch.vue?4596", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/xuan-switch/xuan-switch.vue?9057"], "names": ["props", "switchList", "type", "default", "defaultSwitch", "isShowModal", "disabled", "bj_color", "checked_bj_color", "checked_color", "id", "data", "isSwitch", "initAnimation", "animationData1", "animationData2", "animationData3", "created", "duration", "timingFunction", "methods", "changeSwitch", "uni", "title", "content", "success", "changeAnimation", "callParentEvent"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgpB,CAAgB,8oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBC+BpqB;EACAA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MAAA;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;MACAC;MACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;MACA;QACA;QACA;QACAC;UACAC;UACAC;UACAC;YACA;cACA;cACA;cACA;YACA;UACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACjIA;AAAA;AAAA;AAAA;AAAmvC,CAAgB,yqCAAG,EAAC,C;;;;;;;;;;;ACAvwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/xuan-switch/xuan-switch.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./xuan-switch.vue?vue&type=template&id=6e6e5df4&scoped=true&\"\nvar renderjs\nimport script from \"./xuan-switch.vue?vue&type=script&lang=js&\"\nexport * from \"./xuan-switch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./xuan-switch.vue?vue&type=style&index=0&id=6e6e5df4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6e6e5df4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/xuan-switch/xuan-switch.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xuan-switch.vue?vue&type=template&id=6e6e5df4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xuan-switch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xuan-switch.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"switch-container\" :style=\"[{ background: bj_color}]\">\r\n    <view class=\"switch_view\">\r\n      <view \r\n        class=\"switch-item\" \r\n        :class=\"{'checked_switch':isSwitch}\"\r\n        :style=\"isSwitch?`color:${checked_color}`:''\"\r\n        @click.prevent.stop=\"changeSwitch(true)\"\r\n        :animation=\"animationData2\"\r\n      >\r\n        {{switchList[0]}}\r\n      </view>\r\n      <view \r\n        class=\"switch-item\"\r\n        :class=\"{'checked_switch':!isSwitch}\"\r\n        :style=\"!isSwitch?`color:${checked_color}`:''\"\r\n        @click.prevent.stop=\"changeSwitch(false)\"\r\n        :animation=\"animationData3\"\r\n      >\r\n        {{switchList[1]}}\r\n      </view>\r\n    </view>\r\n    <view class=\"disabled\" v-if=\"disabled\"></view>\r\n    <view \r\n      class=\"position_view\" :animation=\"animationData1\"\r\n      :style=\"[{ background: checked_bj_color}]\"\r\n    ></view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  props: {\r\n    switchList: {\r\n      type: Array,\r\n      default: ()=>{\r\n        return ['开','关'];\r\n      }\r\n    },\r\n    defaultSwitch:{\r\n      type:Boolean,\r\n      default:true\r\n    },\r\n    isShowModal:{//改变开关时，是否弹框提醒\r\n      type:Boolean,\r\n      default:false\r\n    },\r\n    disabled:{\r\n      type:Boolean,\r\n      default:false\r\n    },\r\n    bj_color:{\r\n      type:String,\r\n      default:'#fff'\r\n    },\r\n    checked_bj_color:{\r\n      type:String,\r\n      default:'#1989fa'\r\n    },\r\n    checked_color:{\r\n      type:String,\r\n      default:'#fff'\r\n    },\r\n    id:{\r\n      type:null,\r\n      default:null\r\n    }\r\n  },\r\n  data () {\r\n    return {\r\n      isSwitch:true,\r\n      initAnimation:{},\r\n      animationData1: {},\r\n      animationData2: {},\r\n      animationData3: {}\r\n    };\r\n  },\r\n  created () {\r\n    this.initAnimation = uni.createAnimation({\r\n      duration: 500,\r\n      timingFunction: 'ease'\r\n    });\r\n    this.isSwitch = this.defaultSwitch;\r\n    this.changeAnimation();\r\n  },\r\n  methods: {\r\n    changeSwitch(isSwitch) {\r\n      if(isSwitch == this.isSwitch || this.disabled){\r\n        return;\r\n      }\r\n      if(this.isShowModal){\r\n        let index = isSwitch?0:1;\r\n        let text =  this.switchList[index];\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: `您确定要将其调整为${text}吗？`,\r\n          success: (res) => {\r\n            if(res.confirm){\r\n              this.isSwitch = isSwitch;\r\n              this.changeAnimation();\r\n              this.callParentEvent(isSwitch);\r\n            }\r\n          }\r\n        });\r\n      }else{\r\n        this.isSwitch = isSwitch;\r\n        this.changeAnimation();\r\n        this.callParentEvent(isSwitch);\r\n      }\r\n    },\r\n    changeAnimation(){\r\n      if(this.isSwitch){\r\n        this.animationData1 = this.initAnimation.left(0).width('60%').step().export();\r\n        this.animationData2 = this.initAnimation.width('60%').step().export();\r\n        this.animationData3 = this.initAnimation.width('40%').step().export();\r\n      }else{\r\n        this.animationData1 = this.initAnimation.left('40%').width('60%').step().export();\r\n        this.animationData2 = this.initAnimation.width('40%').step().export();\r\n        this.animationData3 = this.initAnimation.width('60%').step().export();\r\n      }\r\n    },\r\n    callParentEvent(){\r\n      this.$emit('change',this.isSwitch,this.id,()=>{\r\n        // 回调方法应用场景：父级组件请求api接口失败调用\r\n        this.isSwitch = !this.isSwitch;\r\n        this.changeAnimation();\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .switch-container {\r\n    display: flex;\r\n    flex-direction: row;\r\n    width: 398upx;\r\n    height: 76upx;\r\n    border-radius: 80upx;\r\n    border: 3upx solid $fuint-theme;\r\n    font-weight: bold;\r\n    position: relative;\r\n    .switch_view{\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 1;\r\n      display: flex;\r\n      border-radius: 100upx;\r\n      .switch-item {\r\n        color: #666;\r\n        font-size: 24upx;\r\n        height: 100%;\r\n        width: 40%;\r\n        border-radius: 100upx;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n      }\r\n    }\r\n    .position_view{\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 60%;\r\n      height: 100%;\r\n      border-radius: 100upx;\r\n      background: $uni-color-primary;\r\n    }\r\n    .disabled{\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n      z-index: 99;\r\n      background: #fff;\r\n      opacity: 0.6;\r\n      border-radius: 100upx;\r\n    }\r\n  }\r\n</style>\r\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xuan-switch.vue?vue&type=style&index=0&id=6e6e5df4&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./xuan-switch.vue?vue&type=style&index=0&id=6e6e5df4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891423607\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}