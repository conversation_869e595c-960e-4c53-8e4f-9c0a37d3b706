@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-248061c3 {
  padding: 160rpx 60rpx 100rpx 60rpx;
  min-height: 100vh;
  background-color: #fff;
}
.container .fast-icon.data-v-248061c3 {
  margin-bottom: 80rpx;
  font-size: 50rpx;
  cursor: pointer;
}
.header.data-v-248061c3 {
  margin-bottom: 50rpx;
}
.header .title.data-v-248061c3 {
  color: #191919;
  font-size: 33rpx;
  height: 88rpx;
  padding: 10rpx;
  cursor: pointer;
}
.header .title .item.data-v-248061c3 {
  width: 50%;
  height: 88rpx;
  float: left;
  text-align: center;
  font-weight: bold;
}
.header .title .active.data-v-248061c3 {
  border-bottom: #ff3800 10rpx solid;
  padding-bottom: 10rpx;
  text-align: center;
}
.form-item.data-v-248061c3 {
  display: flex;
  padding: 18rpx;
  border-bottom: 2rpx solid #cccccc;
  margin-bottom: 25rpx;
  height: 110rpx;
  align-items: center;
  justify-content: center;
}
.form-item .pre-mobile.data-v-248061c3 {
  line-height: 75rpx;
  color: #888888;
}
.form-item--input.data-v-248061c3 {
  font-size: 26rpx;
  letter-spacing: 1rpx;
  flex: 1;
  height: 100%;
  padding-left: 5rpx;
}
.form-item--parts.data-v-248061c3 {
  min-width: 100rpx;
  height: 100%;
}
.form-item .captcha.data-v-248061c3 {
  height: 100%;
}
.form-item .captcha .image.data-v-248061c3 {
  display: block;
  width: 192rpx;
  height: 80rpx;
}
.form-item .captcha-sms.data-v-248061c3 {
  font-size: 22rpx;
  line-height: 50rpx;
  padding-right: 20rpx;
}
.form-item .captcha-sms .activate.data-v-248061c3 {
  color: #cea26a;
  border: #ccc solid 1px;
  padding: 18rpx;
  border-radius: 8rpx;
}
.form-item .captcha-sms .un-activate.data-v-248061c3 {
  color: #9e9e9e;
}
.login-button.data-v-248061c3 {
  width: 96%;
  height: 86rpx;
  margin: 0 auto;
  margin-top: 60rpx;
  background: #3f51b5;
  text-align: center;
  line-height: 86rpx;
  color: #fff;
  border-radius: 80rpx;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.1);
  letter-spacing: 5rpx;
  cursor: pointer;
}
.cancel-button.data-v-248061c3 {
  width: 96%;
  height: 86rpx;
  margin: 0 auto;
  margin-top: 20rpx;
  background: #dfdfdf;
  color: #fff;
  text-align: center;
  line-height: 86rpx;
  border-radius: 80rpx;
  box-shadow: 0px 10px 20px 0px rgba(0, 0, 0, 0.1);
  letter-spacing: 5rpx;
  cursor: pointer;
}
.register.data-v-248061c3 {
  margin-top: 40rpx;
  text-align: right;
}
.wechat-auth.data-v-248061c3 {
  display: flex;
  justify-content: center;
  margin-top: 40rpx;
}
.wechat-auth .icon.data-v-248061c3 {
  width: 38rpx;
  height: 38rpx;
  margin-right: 15rpx;
}
.wechat-auth .title.data-v-248061c3 {
  font-size: 28rpx;
  color: #666666;
}
