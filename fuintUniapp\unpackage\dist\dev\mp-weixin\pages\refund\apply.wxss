@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.container.data-v-36a440e0 {
  padding-bottom: 100rpx;
}
.row-title.data-v-36a440e0 {
  color: #888;
  margin-bottom: 20rpx;
}
.goods-detail.data-v-36a440e0 {
  padding: 24rpx 20rpx;
}
.goods-detail .left .goods-image.data-v-36a440e0 {
  display: block;
  width: 150rpx;
  height: 150rpx;
}
.goods-detail .right.data-v-36a440e0 {
  padding-left: 20rpx;
}
.goods-detail .goods-props.data-v-36a440e0 {
  margin-top: 14rpx;
  color: #ababab;
  font-size: 24rpx;
  overflow: hidden;
}
.goods-detail .goods-props .goods-props-item.data-v-36a440e0 {
  display: inline-block;
  margin-right: 14rpx;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
  background-color: #F5F5F5;
  width: auto;
}
/* 服务类型 */
.row-service.data-v-36a440e0 {
  padding: 24rpx 20rpx;
}
.service-switch .switch-item.data-v-36a440e0 {
  padding: 6rpx 30rpx;
  margin-right: 25rpx;
  border-radius: 10rpx;
  border: 1px solid #b1b1b1;
  color: #888;
}
.service-switch .switch-item.active.data-v-36a440e0 {
  color: #fc1e56;
  border: 1px solid #fc1e56;
}
/* 申请原因 */
.row-textarea.data-v-36a440e0 {
  padding: 24rpx 20rpx;
}
.row-textarea .textarea.data-v-36a440e0 {
  width: 100%;
  height: 220rpx;
  padding: 12rpx;
  border: 1rpx solid #e8e8e8;
  border-radius: 5rpx;
  box-sizing: border-box;
  font-size: 26rpx;
}
/* 退款金额 */
.row-money.data-v-36a440e0 {
  padding: 24rpx 20rpx;
}
.row-money .row-title.data-v-36a440e0 {
  margin-bottom: 0;
  margin-right: 20rpx;
}
.row-voucher.data-v-36a440e0 {
  padding: 24rpx 20rpx;
}
.row-voucher .image-list.data-v-36a440e0 {
  padding: 0 20rpx;
  margin-top: 20rpx;
  margin-bottom: -20rpx;
}
.row-voucher .image-list.data-v-36a440e0:after {
  clear: both;
  content: " ";
  display: table;
}
.row-voucher .image-list .image.data-v-36a440e0 {
  display: block;
  width: 100%;
  height: 100%;
}
.row-voucher .image-list .image-picker.data-v-36a440e0,
.row-voucher .image-list .image-preview.data-v-36a440e0 {
  width: 184rpx;
  height: 184rpx;
  margin-right: 30rpx;
  margin-bottom: 30rpx;
  float: left;
}
.row-voucher .image-list .image-picker.data-v-36a440e0:nth-child(3n+0),
.row-voucher .image-list .image-preview.data-v-36a440e0:nth-child(3n+0) {
  margin-right: 0;
}
.row-voucher .image-list .image-picker.data-v-36a440e0 {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1rpx dashed #ccc;
  color: #ccc;
}
.row-voucher .image-list .image-picker .choose-icon.data-v-36a440e0 {
  font-size: 48rpx;
  margin-bottom: 6rpx;
}
.row-voucher .image-list .image-picker .choose-text.data-v-36a440e0 {
  font-size: 24rpx;
}
.row-voucher .image-list .image-preview.data-v-36a440e0 {
  position: relative;
}
.row-voucher .image-list .image-preview .image-delete.data-v-36a440e0 {
  position: absolute;
  top: -15rpx;
  right: -15rpx;
  height: 42rpx;
  width: 42rpx;
  line-height: 42rpx;
  background: rgba(0, 0, 0, 0.64);
  border-radius: 50%;
  color: #fff;
  font-weight: bolder;
  font-size: 22rpx;
  z-index: 10;
  text-align: center;
}
.footer-fixed.data-v-36a440e0 {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  height: 180rpx;
  padding-bottom: 30rpx;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);
  background: #fff;
}
.footer-fixed .btn-wrapper.data-v-36a440e0 {
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
}
.footer-fixed .btn-item.data-v-36a440e0 {
  flex: 1;
  font-size: 28rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  color: #fff;
  border-radius: 40rpx;
}
.footer-fixed .btn-item-main.data-v-36a440e0 {
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.footer-fixed .btn-item-main.disabled.data-v-36a440e0 {
  background: #ff9779;
}
