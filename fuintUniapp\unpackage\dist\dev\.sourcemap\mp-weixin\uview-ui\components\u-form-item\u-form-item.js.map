{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form-item/u-form-item.vue?24c8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form-item/u-form-item.vue?5934", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form-item/u-form-item.vue?fdab", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form-item/u-form-item.vue?09a4", "uni-app:///uview-ui/components/u-form-item/u-form-item.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form-item/u-form-item.vue?adc7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-form-item/u-form-item.vue?6311"], "names": ["schema", "name", "mixins", "inject", "uForm", "default", "props", "label", "type", "prop", "borderBottom", "labelPosition", "labelWidth", "labelStyle", "labelAlign", "rightIcon", "leftIcon", "leftIconStyle", "rightIconStyle", "required", "data", "initialValue", "validateState", "validateMessage", "errorType", "fieldValue", "parentData", "watch", "computed", "uLabel<PERSON>idth", "<PERSON><PERSON><PERSON><PERSON>", "showError", "elLabelStyle", "elLabelPosition", "elLabelAlign", "elBorderBottom", "methods", "broadcastInputError", "setRules", "getRules", "rules", "onFieldBlur", "onFieldChange", "getFilteredRule", "validation", "validator", "firstFields", "callback", "reset<PERSON>ield", "mounted", "Object", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAW;AACX;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,8oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC4CnrB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,gBAmBA;EACAC;EACAC;EACAC;IACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAH;IACA;IACA;IACAI;MACAD;MACAH;IACA;IACA;IACAK;MACAF;MACAH;IACA;IACA;IACAM;MACAH;MACAH;IACA;IACA;IACAO;MACAJ;MACAH;IACA;IACA;IACAQ;MACAL;MACAH;QACA;MACA;IACA;IACA;IACAS;MACAN;MACAH;IACA;IACA;IACAU;MACAP;MACAH;IACA;IACA;IACAW;MACAR;MACAH;IACA;IACA;IACAY;MACAT;MACAH;QACA;MACA;IACA;IACA;IACAa;MACAV;MACAH;QACA;MACA;IACA;IACA;IACAc;MACAX;MACAH;IACA;EACA;EACAe;IACA;MACAC;MAAA;MACA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MAAA;MACA;MACAC;QACAhB;QACAE;QACAD;QACAE;QACAC;MACA;IACA;EACA;EACAa;IACAL;MACA;IACA;IACA;IACA;MACA;MACA;IACA;EACA;EACAM;IACA;IACAC;MACA;MACA,mHACAC;IACA;IACAC;MAAA;MACA;QACA;QACA,4DACA,yDACA;MACA;IACA;IACA;IACAD;MACA;MACA,sHACAlB,aACA;IACA;IACA;IACAoB;MACA,yHACA;IACA;IACA;IACAC;MACA,iHACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA,oHACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAC;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;MACA;MACA,8EACA,kBACA;MACAC,qDACA,6BACA;QACAC;MACA;QACA;QACA;QACA;QACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;EAEA;EACAC;IAAA;IACA;IACA;IACA;MACA;MACAC;QACA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;EAEA;EACAC;IAAA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACxVA;AAAA;AAAA;AAAA;AAA8wC,CAAgB,yqCAAG,EAAC,C;;;;;;;;;;;ACAlyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-form-item/u-form-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-form-item.vue?vue&type=template&id=006449ec&scoped=true&\"\nvar renderjs\nimport script from \"./u-form-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-form-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-form-item.vue?vue&type=style&index=0&id=006449ec&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"006449ec\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-form-item/u-form-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form-item.vue?vue&type=template&id=006449ec&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.validateState === \"error\" && _vm.showError(\"border-bottom\")\n  var s0 =\n    _vm.required || _vm.leftIcon || _vm.label\n      ? _vm.__get_style([\n          _vm.elLabelStyle,\n          {\n            \"justify-content\":\n              _vm.elLabelAlign == \"left\"\n                ? \"flex-start\"\n                : _vm.elLabelAlign == \"center\"\n                ? \"center\"\n                : \"flex-end\",\n          },\n        ])\n      : null\n  var m1 = _vm.validateState === \"error\" && _vm.showError(\"message\")\n  var g0 =\n    m1 && _vm.elLabelPosition == \"left\"\n      ? _vm.$u.addUnit(_vm.elLabelWidth)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n        s0: s0,\n        m1: m1,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form-item.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"u-form-item\" :class=\"{'u-border-bottom': elBorderBottom, 'u-form-item__border-bottom--error': validateState === 'error' && showError('border-bottom')}\">\n        <view class=\"u-form-item__body\" :style=\"{\n            flexDirection: elLabelPosition == 'left' ? 'row' : 'column'\n        }\">\n            <!-- 微信小程序中，将一个参数设置空字符串，结果会变成字符串\"true\" -->\n            <view class=\"u-form-item--left\" :style=\"{\n                width: uLabelWidth,\n                flex: `0 0 ${uLabelWidth}`,\n                marginBottom: elLabelPosition == 'left' ? 0 : '10rpx',\n            }\">\n                <!-- 为了块对齐 -->\n                <view class=\"u-form-item--left__content\" v-if=\"required || leftIcon || label\">\n                    <!-- nvue不支持伪元素before -->\n                    <text v-if=\"required\" class=\"u-form-item--left__content--required\">*</text>\n                    <view class=\"u-form-item--left__content__icon\" v-if=\"leftIcon\">\n                        <u-icon :name=\"leftIcon\" :custom-style=\"leftIconStyle\"></u-icon>\n                    </view>\n                    <view class=\"u-form-item--left__content__label\" :style=\"[elLabelStyle, {\n                        'justify-content': elLabelAlign == 'left' ? 'flex-start' : elLabelAlign == 'center' ? 'center' : 'flex-end'\n                    }]\">\n                        {{label}}\n                    </view>\n                </view>\n            </view>\n            <view class=\"u-form-item--right u-flex\">\n                <view class=\"u-form-item--right__content\">\n                    <view class=\"u-form-item--right__content__slot \">\n                        <slot />\n                    </view>\n                    <view class=\"u-form-item--right__content__icon u-flex\" v-if=\"$slots.right || rightIcon\">\n                        <u-icon :custom-style=\"rightIconStyle\" v-if=\"rightIcon\" :name=\"rightIcon\"></u-icon>\n                        <slot name=\"right\" />\n                    </view>\n                </view>\n            </view>\n        </view>\n        <view class=\"u-form-item__message\" v-if=\"validateState === 'error' && showError('message')\" :style=\"{\n            paddingLeft: elLabelPosition == 'left' ? $u.addUnit(elLabelWidth) : '0',\n        }\">{{validateMessage}}</view>\n    </view>\n</template>\n\n<script>\n    import Emitter from '../../libs/util/emitter.js';\n    import schema from '../../libs/util/async-validator';\n    // 去除警告信息\n    schema.warning = function() {};\n\n    /**\n     * form-item 表单item\n     * @description 此组件一般用于表单场景，可以配置Input输入框，Select弹出框，进行表单验证等。\n     * @tutorial http://uviewui.com/components/form.html\n     * @property {String} label 左侧提示文字\n     * @property {Object} prop 表单域model对象的属性名，在使用 validate、resetFields 方法的情况下，该属性是必填的\n     * @property {Boolean} border-bottom 是否显示表单域的下划线边框\n     * @property {String} label-position 表单域提示文字的位置，left-左侧，top-上方\n     * @property {String Number} label-width 提示文字的宽度，单位rpx（默认90）\n     * @property {Object} label-style lable的样式，对象形式\n     * @property {String} label-align lable的对齐方式\n     * @property {String} right-icon 右侧自定义字体图标(限uView内置图标)或图片地址\n     * @property {String} left-icon 左侧自定义字体图标(限uView内置图标)或图片地址\n     * @property {Object} left-icon-style 左侧图标的样式，对象形式\n     * @property {Object} right-icon-style 右侧图标的样式，对象形式\n     * @property {Boolean} required 是否显示左边的\"*\"号，这里仅起展示作用，如需校验必填，请通过rules配置必填规则(默认false)\n     * @example <u-form-item label=\"姓名\"><u-input v-model=\"form.name\" /></u-form-item>\n     */\n\n    export default {\n        name: 'u-form-item',\n        mixins: [Emitter],\n        inject: {\n            uForm: {\n                default () {\n                    return null\n                }\n            }\n        },\n        props: {\n            // input的label提示语\n            label: {\n                type: String,\n                default: ''\n            },\n            // 绑定的值\n            prop: {\n                type: String,\n                default: ''\n            },\n            // 是否显示表单域的下划线边框\n            borderBottom: {\n                type: [String, Boolean],\n                default: ''\n            },\n            // label的位置，left-左边，top-上边\n            labelPosition: {\n                type: String,\n                default: ''\n            },\n            // label的宽度，单位rpx\n            labelWidth: {\n                type: [String, Number],\n                default: ''\n            },\n            // lable的样式，对象形式\n            labelStyle: {\n                type: Object,\n                default () {\n                    return {}\n                }\n            },\n            // lable字体的对齐方式\n            labelAlign: {\n                type: String,\n                default: ''\n            },\n            // 右侧图标\n            rightIcon: {\n                type: String,\n                default: ''\n            },\n            // 左侧图标\n            leftIcon: {\n                type: String,\n                default: ''\n            },\n            // 左侧图标的样式\n            leftIconStyle: {\n                type: Object,\n                default () {\n                    return {}\n                }\n            },\n            // 左侧图标的样式\n            rightIconStyle: {\n                type: Object,\n                default () {\n                    return {}\n                }\n            },\n            // 是否显示左边的必填星号，只作显示用，具体校验必填的逻辑，请在rules中配置\n            required: {\n                type: Boolean,\n                default: false\n            }\n        },\n        data() {\n            return {\n                initialValue: '', // 存储的默认值\n                // isRequired: false, // 是否必填，由于人性化考虑，必填\"*\"号通过props的required配置，不再通过rules的规则自动生成\n                validateState: '', // 是否校验成功\n                validateMessage: '', // 校验失败的提示语\n                // 有错误时的提示方式，message-提示信息，border-如果input设置了边框，变成呈红色，\n                errorType: ['message'],\n                fieldValue: '', // 获取当前子组件input的输入的值\n                // 父组件的参数，在computed计算中，无法得知this.parent发生变化，故将父组件的参数值，放到data中\n                parentData: {\n                    borderBottom: true,\n                    labelWidth: 90,\n                    labelPosition: 'left',\n                    labelStyle: {},\n                    labelAlign: 'left',\n                }\n            };\n        },\n        watch: {\n            validateState(val) {\n                this.broadcastInputError();\n            },\n            // 监听u-form组件的errorType的变化\n            \"uForm.errorType\"(val) {\n                this.errorType = val;\n                this.broadcastInputError();\n            },\n        },\n        computed: {\n            // 计算后的label宽度，由于需要多个判断，故放到computed中\n            uLabelWidth() {\n                // 如果用户设置label为空字符串(微信小程序空字符串最终会变成字符串的'true')，意味着要将label的位置宽度设置为auto\n                return this.elLabelPosition == 'left' ? (this.label === 'true' || this.label === '' ? 'auto' : this.$u.addUnit(this\n                    .elLabelWidth)) : '100%';\n            },\n            showError() {\n                return type => {\n                    // 如果errorType数组中含有none，或者toast提示类型\n                    if (this.errorType.indexOf('none') >= 0) return false;\n                    else if (this.errorType.indexOf(type) >= 0) return true;\n                    else return false;\n                }\n            },\n            // label的宽度\n            elLabelWidth() {\n                // label默认宽度为90，优先使用本组件的值，如果没有(如果设置为0，也算是配置了值，依然起效)，则用u-form的值\n                return (this.labelWidth != 0 || this.labelWidth != '') ? this.labelWidth : (this.parentData.labelWidth ? this.parentData\n                    .labelWidth :\n                    90);\n            },\n            // label的样式\n            elLabelStyle() {\n                return Object.keys(this.labelStyle).length ? this.labelStyle : (this.parentData.labelStyle ? this.parentData.labelStyle :\n                    {});\n            },\n            // label的位置，左侧或者上方\n            elLabelPosition() {\n                return this.labelPosition ? this.labelPosition : (this.parentData.labelPosition ? this.parentData.labelPosition :\n                    'left');\n            },\n            // label的对齐方式\n            elLabelAlign() {\n                return this.labelAlign ? this.labelAlign : (this.parentData.labelAlign ? this.parentData.labelAlign : 'left');\n            },\n            // label的下划线\n            elBorderBottom() {\n                // 子组件的borderBottom默认为空字符串，如果不等于空字符串，意味着子组件设置了值，优先使用子组件的值\n                return this.borderBottom !== '' ? this.borderBottom : this.parentData.borderBottom ? this.parentData.borderBottom :\n                    true;\n            }\n        },\n        methods: {\n            broadcastInputError() {\n                // 子组件发出事件，第三个参数为true或者false，true代表有错误\n                this.broadcast('u-input', 'on-form-item-error', this.validateState === 'error' && this.showError('border'));\n            },\n            // 判断是否需要required校验\n            setRules() {\n                let that = this;\n                // 由于人性化考虑，必填\"*\"号通过props的required配置，不再通过rules的规则自动生成\n                // 从父组件u-form拿到当前u-form-item需要验证 的规则\n                // let rules = this.getRules();\n                // if (rules.length) {\n                //     this.isRequired = rules.some(rule => {\n                //         // 如果有必填项，就返回，没有的话，就是undefined\n                //         return rule.required;\n                //     });\n                // }\n\n                // blur事件\n                this.$on('on-form-blur', that.onFieldBlur);\n                // change事件\n                this.$on('on-form-change', that.onFieldChange);\n            },\n\n            // 从u-form的rules属性中，取出当前u-form-item的校验规则\n            getRules() {\n                // 父组件的所有规则\n                let rules = this.parent.rules;\n                rules = rules ? rules[this.prop] : [];\n                // 保证返回的是一个数组形式\n                return [].concat(rules || []);\n            },\n\n            // blur事件时进行表单校验\n            onFieldBlur() {\n                this.validation('blur');\n            },\n\n            // change事件进行表单校验\n            onFieldChange() {\n                this.validation('change');\n            },\n\n            // 过滤出符合要求的rule规则\n            getFilteredRule(triggerType = '') {\n                let rules = this.getRules();\n                // 整体验证表单时，triggerType为空字符串，此时返回所有规则进行验证\n                if (!triggerType) return rules;\n                // 历遍判断规则是否有对应的事件，比如blur，change触发等的事件\n                // 使用indexOf判断，是因为某些时候设置的验证规则的trigger属性可能为多个，比如['blur','change']\n                // 某些场景可能的判断规则，可能不存在trigger属性，故先判断是否存在此属性\n                return rules.filter(res => res.trigger && res.trigger.indexOf(triggerType) !== -1);\n            },\n\n            // 校验数据\n            validation(trigger, callback = () => {}) {\n                // 检验之间，先获取需要校验的值\n                this.fieldValue = this.parent.model[this.prop];\n                // blur和change是否有当前方式的校验规则\n                let rules = this.getFilteredRule(trigger);\n                // 判断是否有验证规则，如果没有规则，也调用回调方法，否则父组件u-form会因为\n                // 对count变量的统计错误而无法进入上一层的回调\n                if (!rules || rules.length === 0) {\n                    return callback('');\n                }\n                // 设置当前的装填，标识为校验中\n                this.validateState = 'validating';\n                // 调用async-validator的方法\n                let validator = new schema({\n                    [this.prop]: rules\n                });\n                validator.validate({\n                    [this.prop]: this.fieldValue\n                }, {\n                    firstFields: true\n                }, (errors, fields) => {\n                    // 记录状态和报错信息\n                    this.validateState = !errors ? 'success' : 'error';\n                    this.validateMessage = errors ? errors[0].message : '';\n                    // 调用回调方法\n                    callback(this.validateMessage);\n                });\n            },\n\n            // 清空当前的u-form-item\n            resetField() {\n                this.parent.model[this.prop] = this.initialValue;\n                // 设置为`success`状态，只是为了清空错误标记\n                this.validateState = 'success';\n            }\n        },\n\n        // 组件创建完成时，将当前实例保存到u-form中\n        mounted() {\n            // 支付宝、头条小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环应用\n            this.parent = this.$u.$parent.call(this, 'u-form');\n            if (this.parent) {\n                // 历遍parentData中的属性，将parent中的同名属性赋值给parentData\n                Object.keys(this.parentData).map(key => {\n                    this.parentData[key] = this.parent[key];\n                });\n                // 如果没有传入prop，或者uForm为空(如果u-form-input单独使用，就不会有uForm注入)，就不进行校验\n                if (this.prop) {\n                    // 将本实例添加到父组件中\n                    this.parent.fields.push(this);\n                    this.errorType = this.parent.errorType;\n                    // 设置初始值\n                    this.initialValue = this.fieldValue;\n                    // 添加表单校验，这里必须要写在$nextTick中，因为u-form的rules是通过ref手动传入的\n                    // 不在$nextTick中的话，可能会造成执行此处代码时，父组件还没通过ref把规则给u-form，导致规则为空\n                    this.$nextTick(() => {\n                        this.setRules();\n                    })\n                }\n            }\n        },\n\n        // 组件销毁前，将实例从u-form的缓存中移除\n        beforeDestroy() {\n            // 如果当前没有prop的话表示当前不要进行删除（因为没有注入）\n            if (this.parent && this.prop) {\n                this.parent.fields.map((item, index) => {\n                    if (item === this) this.parent.fields.splice(index, 1);\n                })\n            }\n        },\n    };\n</script>\n\n<style lang=\"scss\" scoped>\n    @import \"../../libs/css/style.components.scss\";\n\n    .u-form-item {\n        @include vue-flex;\n        // align-items: flex-start;\n        padding: 20rpx 0;\n        font-size: 28rpx;\n        color: $u-main-color;\n        box-sizing: border-box;\n        line-height: $u-form-item-height;\n        flex-direction: column;\n\n        &__border-bottom--error:after {\n            border-color: $u-type-error;\n        }\n\n        &__body {\n            @include vue-flex;\n        }\n\n        &--left {\n            @include vue-flex;\n            align-items: center;\n\n            &__content {\n                position: relative;\n                @include vue-flex;\n                align-items: center;\n                padding-right: 10rpx;\n                flex: 1;\n\n                &__icon {\n                    margin-right: 8rpx;\n                }\n\n                &--required {\n                    position: absolute;\n                    left: -16rpx;\n                    vertical-align: middle;\n                    color: $u-type-error;\n                    padding-top: 6rpx;\n                }\n\n                &__label {\n                    @include vue-flex;\n                    align-items: center;\n                    flex: 1;\n                }\n            }\n        }\n\n        &--right {\n            flex: 1;\n\n            &__content {\n                @include vue-flex;\n                align-items: center;\n                flex: 1;\n\n                &__slot {\n                    flex: 1;\n                    /* #ifndef MP */\n                    @include vue-flex;\n                    align-items: center;\n                    /* #endif */\n                }\n\n                &__icon {\n                    margin-left: 10rpx;\n                    color: $u-light-color;\n                    font-size: 30rpx;\n                }\n            }\n        }\n\n        &__message {\n            font-size: 24rpx;\n            line-height: 24rpx;\n            color: $u-type-error;\n            margin-top: 12rpx;\n        }\n    }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form-item.vue?vue&type=style&index=0&id=006449ec&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-form-item.vue?vue&type=style&index=0&id=006449ec&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425094\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}