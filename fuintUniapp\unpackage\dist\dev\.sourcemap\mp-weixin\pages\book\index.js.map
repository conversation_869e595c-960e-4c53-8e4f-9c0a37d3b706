{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/index.vue?2150", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/index.vue?77f5", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/index.vue?2992", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/index.vue?a097", "uni-app:///pages/book/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/index.vue?21c7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/index.vue?d4fa"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "MescrollBody", "mixins", "data", "categoryList", "list", "curId", "upOption", "auto", "page", "size", "noMoreSize", "onLoad", "app", "methods", "upCallback", "then", "catch", "getCategoryList", "BookApi", "getBookList", "cateId", "load", "resolve", "onSwitchTab", "onTargetDetail", "bookId", "onShareAppMessage", "title", "path", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACwC9pB;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;MACAC;IACA;IACA;IACAA;EACA;EAEAC;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAF,0BACAG;QACA;QACA;QACAH;MACA,GACAI;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACAC,mBACAH;QACAH;MACA;IACA;IAEA;AACA;AACA;AACA;IACAO;MAAA;MACA;MACA;QACAD;UAAAE;UAAAZ;QAAA;UAAAa;QAAA,GACAN;UACA;UACA;UACAH;UACAU;QACA,GACAN;UAAA;QAAA;MACA;IACA;IAEA;IACAO;MAAA;MACA;MACA;MACAX;MACA;MACAA;MACAA;IACA;IAEA;IACAY;MACA;QAAAC;MAAA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;MACAF;MACAC;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;ACvKA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/book/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/book/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=99224d36&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=99224d36&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"99224d36\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/book/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=99224d36&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ use: false }\" :up=\"upOption\" @up=\"upCallback\">\n\n    <!-- 分类列表tab -->\n    <view class=\"tabs-wrapper\">\n      <scroll-view class=\"scroll-view\" scroll-x>\n        <view class=\"tab-item\" :class=\"{ active: curId ==  0 }\" @click=\"onSwitchTab(0)\">\n          <view class=\"value\"><text>全部</text></view>\n        </view>\n        <!-- 分类列表 -->\n        <view class=\"tab-item\" :class=\"{ active: curId ==  item.id }\" @click=\"onSwitchTab(item.id)\"\n          v-for=\"(item, index) in categoryList\" :key=\"index\">\n          <view class=\"value\"><text>{{ item.name }}</text></view>\n        </view>\n      </scroll-view>\n    </view>\n\n    <!-- 预约列表 -->\n    <view class=\"book-list\">\n      <view class=\"book-item show-type\" v-for=\"(item, index) in list.content\" :key=\"index\" @click=\"onTargetDetail(item.id)\">\n        <block>\r\n          <view class=\"book-item-image\">\r\n            <image class=\"image\" :src=\"item.logo\"></image>\r\n          </view>\n          <view class=\"book-item-left flex-box\">\n            <view class=\"book-item-title twolist-hidden\">\n              <text>{{ item.name }}</text>\n            </view>\n            <view class=\"book-item-footer m-top10\">\n              <text class=\"book-views\">{{ item.description }}</text>\n            </view>\n          </view>\n        </block>\n      </view>\n    </view>\n  </mescroll-body>\n</template>\n\n<script>\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import * as BookApi from '@/api/book'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n\n  const pageSize = 15\n\n  export default {\n    components: {\n      MescrollBody\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        // 分类列表\n        categoryList: [],\n        // 预约列表\n        list: getEmptyPaginateObj(),\n        // 当前选中的分类id (0则代表首页)\n        curId: 0,\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于3条才显示无更多数据\n          noMoreSize: 3,\n        }\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      const app = this;\n      if (options.categoryId) {\n          app.curId = options.categoryId;\n      }\n      // 获取分类数据\n      app.getCategoryList();\n    },\n\n    methods: {\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this;\n        // 设置列表数据\n        app.getBookList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length;\n            const totalSize = list.content.totalElements;\n            app.mescroll.endBySize(curPageLen, totalSize);\n          })\n          .catch(() => app.mescroll.endErr());\n      },\n\n      // 获取预约分类数据\n      getCategoryList() {\n        const app = this;\n        BookApi.cateList()\n          .then(result => {\n              app.categoryList = result.data.cateList;\n          })\n      },\n\n      /**\n       * 获取预约项目列表\n       * @param {Number} pageNo 页码\n       */\n      getBookList(pageNo = 1) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          BookApi.list({ cateId: app.curId, page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data;\n              app.list.content = getMoreListData(newList, app.list, pageNo);\n              resolve(newList);\n            })\n            .catch(result => reject());\n        })\n      },\n\n      // 切换选择的分类\n      onSwitchTab(categoryId = 0) {\n        const app = this;\n        // 切换当前的分类ID\n        app.curId = categoryId;\n        // 刷新列表数据\n        app.list = getEmptyPaginateObj();\n        app.mescroll.resetUpScroll();\n      },\n\n      // 跳转预约详情页\n      onTargetDetail(bookId) {\n        this.$navTo('pages/book/detail', { bookId });\n      }\n    },\n\n    /**\n     * 分享当前页面\n     */\n    onShareAppMessage() {\n      return {\n        title: '预约项目',\n        path: \"/pages/book/index?\" + this.$getShareUrlParams()\n      }\n    },\n\n    /**\n     * 分享到朋友圈\n     * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)\n     * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html\n     */\n    onShareTimeline() {\n      return {\n        title: '预约项目',\n        path: \"/pages/book/index?\" + this.$getShareUrlParams()\n      }\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  /* 顶部选项卡 */\n\n  .container {\n    min-height: 100vh;\n  }\n\n  .tabs-wrapper {\n    position: sticky;\n    top: 1rpx;\n    display: flex;\n    width: 100%;\n    height: 88rpx;\n    color: #333;\n    font-size: 28rpx;\n    background: #fff;\n    border-bottom: 1rpx solid #e4e4e4;\n    z-index: 100;\n    overflow: hidden;\n    white-space: nowrap;\n  }\n\n  .tab-item {\n    display: inline-block;\n    padding: 0 10rpx;\n    text-align: center;\n    min-width: 10%;\n    height: 87rpx;\n    line-height: 88rpx;\n    box-sizing: border-box;\n\n    .value {\n      height: 100%;\n    }\n\n    &.active .value {\n      color: #000;\n      border-bottom: 4rpx solid #000;\r\n      font-weight: bold;\n    }\n  }\n\n  /* 预约列表 */\n  .book-list {\n    padding-top: 0rpx;\n    line-height: 1;\n    background: #f7f7f7;\n  }\n\n  .book-item {\n    padding: 40rpx;\n    background: #fff;\r\n    margin: 20rpx;\r\n    border-radius: 15rpx;\n\n    &:last-child {\n      margin-bottom: 0;\n    }\n\n    .book-item-title {\n      max-height: 80rpx;\n      font-size: 32rpx;\r\n      font-weight: bold;\n      color: #333;\n    }\n\n    .book-item-image .image {\n      display: block;\r\n      border-radius: 16rpx;\r\n      height: 160rpx;\r\n      width: 200rpx;\r\n      border: 2rpx solid #cccccc;\n    }\n  }\n\n  .show-type {\n    display: flex;\n    .book-item-left {\n      padding-left: 20rpx;\n    }\n    .book-item-title {\n      font-size: 32rpx;\n    }\r\n    .book-item-footer {\r\n        line-height: 40rpx;\r\n        max-height: 120rpx;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n    }\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=99224d36&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=99224d36&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420647\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}