{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/order/index.vue?15d9", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/order/index.vue?0272", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/order/index.vue?773b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/order/index.vue?2279", "uni-app:///pages/merchant/order/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/order/index.vue?16d0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/merchant/order/index.vue?4777"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "value", "components", "MescrollBody", "mixins", "data", "DeliveryStatusEnum", "DeliveryTypeEnum", "OrderStatusEnum", "PayStatusEnum", "PayTypeEnum", "ReceiptStatusEnum", "options", "dataType", "tabs", "curTab", "list", "isLoading", "upOption", "auto", "page", "size", "noMoreSize", "empty", "tip", "canReset", "showPayPopup", "statusText", "onLoad", "onShow", "methods", "initCurTab", "console", "app", "upCallback", "then", "catch", "getOrderList", "OrderApi", "load", "resolve", "onTargetIndex", "getTabValue", "onChangeTab", "onRefreshList", "setTimeout", "handleTargetDetail", "orderId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;AC2E7qB;AASA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AACA;;AAEA;AACA;EACAC;EACAC;AACA;EACAD;EACAC;AACA;EACAD;EACAC;AACA;EACAD;EACAC;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;EACAC;IACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEA;MACAC;QAAAC;MAAA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;UAAAC;QAAA;QACA;QACAC;QACA;QACAC;UACAC;QACA;MACA;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACA;QACAC;QACA;UAAA;QAAA;QACAC;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;MACAD,2BACAE;QACA;QACA;QACAF;MACA,GACAG;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACAC;UAAAzB;UAAAO;QAAA;UAAAmB;QAAA,GACAJ;UACA;UACA;UACAF;UACAO;QACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACAV;MACA;MACAA;IACA;IAEA;IACAW;MAAA;MACA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;QAAAC;MAAA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;ACrPA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/merchant/order/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/merchant/order/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3a20e7fa&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3a20e7fa&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3a20e7fa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/merchant/order/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=3a20e7fa&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-tabs/u-tabs\" */ \"@/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <mescroll-body ref=\"mescrollRef\" :sticky=\"true\" @init=\"mescrollInit\" :down=\"{ native: true }\" @down=\"downCallback\"\n      :up=\"upOption\" @up=\"upCallback\">\n\n      <!-- tab栏 -->\n      <u-tabs :list=\"tabs\" :is-scroll=\"false\" :current=\"curTab\" active-color=\"#FA2209\" :duration=\"0.2\" @change=\"onChangeTab\" />\n\n      <!-- 订单列表 -->\n      <view class=\"order-list\">\n        <view class=\"order-item\" v-for=\"(item, index) in list.content\" :key=\"index\">\n          <view class=\"item-top\" @click=\"handleTargetDetail(item.id)\">\n            <view class=\"item-top-left\">\n              <text class=\"order-type\">{{ item.typeName }}</text>\n            </view>\n            <view class=\"item-top-right\">\n              <text :class=\"item.status\">{{ item.statusText }}</text>\n            </view>\n          </view>\n          <!-- 商品列表 -->\n          <view class=\"goods-list\" v-if=\"item.goods\" @click=\"handleTargetDetail(item.id)\">\n            <view class=\"goods-item\" v-for=\"(goods, idx) in item.goods\" :key=\"idx\">\n              <!-- 商品图片 -->\n              <view class=\"goods-image\">\n                <image class=\"image\" :src=\"goods.image\"></image>\n              </view>\n              <!-- 商品信息 -->\n              <view class=\"goods-content\">\n                <view class=\"goods-title twolist-hidden\"><text>{{ goods.name }}</text></view>\n                <view class=\"goods-props clearfix\">\n                  <view class=\"goods-props-item\" v-for=\"(props, idx) in goods.specList\" :key=\"idx\">\n                    <text>{{ props.specValue }}</text>\n                  </view>\n                </view>\n              </view>\n              <!-- 交易信息 -->\n              <view class=\"goods-trade\">\n                <view class=\"goods-price\">\n                  <text class=\"unit\">￥</text>\n                  <text class=\"value\">{{ goods.price }}</text>\n                </view>\n                <view class=\"goods-num\">\n                  <text>×{{ goods.num }}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          <!-- 备注信息 -->\n          <view v-if=\"item.remark\" class=\"remark\" @click=\"handleTargetDetail(item.id)\">\n              <text>备注：</text>\n              <text>{{ item.remark ? item.remark : '--'}}</text>\n          </view>\n          <!-- 订单合计 -->\n          <view class=\"order-total\" @click=\"handleTargetDetail(item.id)\">\n            <text>总金额</text>\n            <text class=\"unit\">￥</text>\n            <text class=\"money\">{{ item.amount }}</text>\n          </view>\n          <!-- 订单操作 -->\n          <view class=\"order-handle\">\n            <view class=\"order-time\">\n                <text class=\"time\">{{ item.createTime }}</text>\n            </view>\n            <view class=\"btn-group\">\n                <view class=\"btn-item\" @click=\"handleTargetDetail(item.id)\">详情</view>\n            </view>\n          </view>\n        </view>\n      </view>\n    </mescroll-body>\n  </view>\n\n</template>\n\n<script>\n  import {\n    DeliveryStatusEnum,\n    DeliveryTypeEnum,\n    OrderStatusEnum,\n    PayStatusEnum,\n    PayTypeEnum,\n    ReceiptStatusEnum\n  } from '@/common/enum/order'\n  import MescrollBody from '@/components/mescroll-uni/mescroll-body.vue'\n  import MescrollMixin from '@/components/mescroll-uni/mescroll-mixins'\n  import { getEmptyPaginateObj, getMoreListData } from '@/utils/app'\n  import * as OrderApi from '@/api/merchant/order'\n  import { wxPayment } from '@/utils/app'\n\n  // 每页记录数量\n  const pageSize = 15\n\n  // tab栏数据\n  const tabs = [{\n    name: `全部`,\n    value: 'all'\n  }, {\n    name: `待支付`,\n    value: 'toPay'\n  }, {\n    name: `已支付`,\n    value: 'paid'\n  }, {\n    name: `已取消`,\n    value: 'cancel'\n  }]\n\n  export default {\n    components: {\n      MescrollBody\n    },\n    mixins: [MescrollMixin],\n    data() {\n      return {\n        // 枚举类\n        DeliveryStatusEnum,\n        DeliveryTypeEnum,\n        OrderStatusEnum,\n        PayStatusEnum,\n        PayTypeEnum,\n        ReceiptStatusEnum,\n\n        // 当前页面参数\n        options: { dataType: 'all' },\n        // tab栏数据\n        tabs,\n        // 当前标签索引\n        curTab: 0,\n        // 订单列表数据\n        list: getEmptyPaginateObj(),\n        // 正在加载\n        isLoading: false,\n        // 上拉加载配置\n        upOption: {\n          // 首次自动执行\n          auto: true,\n          // 每页数据的数量; 默认10\n          page: { size: pageSize },\n          // 数量要大于12条才显示无更多数据\n          noMoreSize: 12,\n          // 空布局\n          empty: {\n            tip: '亲，暂无订单记录'\n          }\n        },\n        // 控制首次触发onShow事件时不刷新列表\n        canReset: false,\n        // 支付方式弹窗\n        showPayPopup: false,\n        statusText: \"payStatus\"\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 初始化当前选中的标签\n      this.initCurTab(options)\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow() {\n      this.canReset && this.onRefreshList()\n      this.canReset = true\n    },\n\n    methods: {\n\n      // 初始化当前选中的标签\n      initCurTab(options) {\n        const app = this\n        if (options.dataType) {\r\n            console.log(\"options == \", options);\n            const index = app.tabs.findIndex(item => item.value == options.dataType)\n            app.curTab = index > -1 ? index : 0\n        }\n      },\n\n      /**\n       * 上拉加载的回调 (页面初始化时也会执行一次)\n       * 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10\n       * @param {Object} page\n       */\n      upCallback(page) {\n        const app = this\n        // 设置列表数据\n        app.getOrderList(page.num)\n          .then(list => {\n            const curPageLen = list.content.length\n            const totalSize = list.totalElements\n            app.mescroll.endBySize(curPageLen, totalSize)\n          })\n          .catch(() => app.mescroll.endErr())\n      },\n\n      // 获取订单列表\n      getOrderList(pageNo = 1) {\n        const app = this\n        return new Promise((resolve, reject) => {\n          OrderApi.list({ dataType: app.getTabValue(), page: pageNo }, { load: false })\n            .then(result => {\n              // 合并新数据\n              const newList = result.data;\n              app.list.content = getMoreListData(newList, app.list, pageNo);\n              resolve(newList)\n            })\n        })\n      },\n      \n      // 点击跳转到首页\n      onTargetIndex() {\n        this.$navTo('pages/index/index')\n      },\n\n      // 获取当前标签项的值\n      getTabValue() {\n        return this.tabs[this.curTab].value\n      },\n\n      // 切换标签项\n      onChangeTab(index) {\n        const app = this\n        // 设置当前选中的标签\n        app.curTab = index\n        // 刷新订单列表\n        app.onRefreshList()\n      },\n\n      // 刷新订单列表\n      onRefreshList() {\n        this.list = getEmptyPaginateObj()\n        setTimeout(() => {\n          this.mescroll.resetUpScroll()\n        }, 120)\n      },\n\n      // 跳转到订单详情页\n      handleTargetDetail(orderId) {\n        this.$navTo('pages/order/detail', { orderId })\n      }\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  // 项目内容\n  .order-item {\n    margin: 10rpx auto 10rpx auto;\n    padding: 20rpx 20rpx;\n    width: 94%;\n    border: 3rpx solid #e8e8e8;\n    box-shadow: 5rpx 5rpx 5rpx 5rpx rgba(0.05, 0.05, 0.05, 0.05);\n    border-radius: 16rpx;\n    background: #fff;\n    .A{\n        color:$uni-text-color-active;\n    }\n    .B{\n        color:$uni-text-color;\n    }\n  }\n\n  // 项目顶部\n  .item-top {\n    display: flex;\n    justify-content: space-between;\n    font-size: 26rpx;\n    margin-bottom: 40rpx;\n\n    .order-type {\n      font-weight: bold;\n      margin-left: 20rpx;\n    }\n\n    .state-text {\n      color: $uni-text-color-active;\n    }\n  }\n\n  // 商品列表\n  .goods-list {\n    // 商品项\n    .goods-item {\n      display: flex;\n      margin-bottom: 10rpx;\n      border-bottom: 3rpx solid #e8e8e8;\n      padding: 20rpx;\n\n      // 商品图片\n      .goods-image {\n        width: 180rpx;\n        height: 143rpx;\n\n        .image {\n          display: block;\n          width: 100%;\n          height: 100%;\n          border-radius: 8rpx;\n        }\n      }\n\n      // 商品内容\n      .goods-content {\n        flex: 1;\n        padding-left: 16rpx;\n        padding-top: 16rpx;\n\n        .goods-title {\n          font-size: 26rpx;\n          max-height: 76rpx;\n        }\n\n        .goods-props {\n          margin-top: 14rpx;\n          // height: 40rpx;\n          color: #ababab;\n          font-size: 24rpx;\n          overflow: hidden;\n\n          .goods-props-item {\n            display: inline-block;\n            margin-right: 14rpx;\n            padding: 4rpx 16rpx;\n            border-radius: 12rpx;\n            background-color: #F5F5F5;\n            width: auto;\n          }\n        }\n      }\n\n      // 交易信息\n      .goods-trade {\n        padding-top: 16rpx;\n        width: 150rpx;\n        text-align: right;\n        color: $uni-text-color-grey;\n        font-size: 26rpx;\n\n        .goods-price {\n          vertical-align: bottom;\n          margin-bottom: 16rpx;\n          .unit {\n            margin-right: -2rpx;\n            font-size: 24rpx;\n          }\n        }\n      }\n    }\n  }\n  // 备注信息\n  .remark {\n      padding: 12rpx 0 12rpx 20rpx;\n      border-radius: 5rpx;\n      height: 60rpx;\n  }\n\n  // 订单合计\n  .order-total {\n    font-size: 26rpx;\n    vertical-align: bottom;\n    text-align: right;\n    height: 40rpx;\n    margin-top: 30rpx;\n    margin-bottom: 30rpx;\n\n    .unit {\n      margin-left: 8rpx;\n      margin-right: -2rpx;\n      font-size: 26rpx;\n    }\n\n    .money {\n      font-size: 28rpx;\n    }\n  }\n\n  // 订单操作\n  .order-handle {\n    height: 50rpx;\n    .order-time {\n        color: #777;\n        float: left;\n        margin-left: 20rpx;\n    }\n    .btn-group {\n      .btn-item {\n        border-radius: 10rpx;\n        padding: 8rpx 24rpx;\n        font-size: 28rpx;\n        float: right;\n        color: #ffffff;\r\n        background: #f9211c;\n        border: 1rpx solid #f9211c;\n        margin-left: 25rpx;\n      }\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=3a20e7fa&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=3a20e7fa&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420782\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}