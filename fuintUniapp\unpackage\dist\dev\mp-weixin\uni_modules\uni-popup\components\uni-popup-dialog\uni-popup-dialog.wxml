<view class="uni-popup-dialog data-v-6f54520a"><view class="uni-dialog-title data-v-6f54520a"><text class="{{['uni-dialog-title-text','data-v-6f54520a','uni-popup__'+dialogType]}}">{{title}}</text></view><block wx:if="{{mode==='base'}}"><view class="uni-dialog-content data-v-6f54520a"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><text class="uni-dialog-content-text data-v-6f54520a">{{content}}</text></block></view></block><block wx:else><view class="uni-dialog-content data-v-6f54520a"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><input class="uni-dialog-input data-v-6f54520a" type="text" placeholder="{{placeholder}}" focus="{{focus}}" data-event-opts="{{[['input',[['__set_model',['','val','$event',[]]]]]]}}" value="{{val}}" bindinput="__e"/></block></view></block><view class="uni-dialog-button-group data-v-6f54520a"><view data-event-opts="{{[['tap',[['closeDialog',['$event']]]]]}}" class="uni-dialog-button data-v-6f54520a" bindtap="__e"><text class="uni-dialog-button-text data-v-6f54520a">取消</text></view><view data-event-opts="{{[['tap',[['onOk',['$event']]]]]}}" class="uni-dialog-button uni-border-left data-v-6f54520a" bindtap="__e"><text class="uni-dialog-button-text uni-button-color data-v-6f54520a">确定</text></view></view></view>