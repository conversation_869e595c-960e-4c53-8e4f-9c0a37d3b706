{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-input/u-input.vue?2d7e", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-input/u-input.vue?3100", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-input/u-input.vue?2ffc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-input/u-input.vue?4370", "uni-app:///uview-ui/components/u-input/u-input.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-input/u-input.vue?c105", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-input/u-input.vue?70a6"], "names": ["name", "mixins", "props", "value", "type", "default", "inputAlign", "placeholder", "disabled", "maxlength", "placeholder<PERSON><PERSON><PERSON>", "confirmType", "customStyle", "fixed", "focus", "passwordIcon", "border", "borderColor", "autoHeight", "selectOpen", "height", "clearable", "cursorSpacing", "selectionStart", "selectionEnd", "trim", "showConfirmbar", "data", "defaultValue", "inputHeight", "textareaHeight", "validateState", "focused", "showPassword", "lastValue", "watch", "detail", "computed", "inputMaxlength", "getStyle", "style", "getCursorSpacing", "uSelectionStart", "uSelectionEnd", "created", "methods", "handleInput", "setTimeout", "handleBlur", "onFormItemError", "onFocus", "onConfirm", "onClear", "inputClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjDA;AAAA;AAAA;AAAA;AAA2pB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0E/qB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,gBA0BA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;QACA;MACA;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;EACA;EACAsB;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAhC;MACA;MACA;MACA;QACAiC;UACAjC;QACA;MACA;IACA;EACA;EACAkC;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC,gFACA;MACAA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;;QAKA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACAD;QACA;MACA;MACA;MACA;MACAA;QACA;;QAKA;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC9UA;AAAA;AAAA;AAAA;AAA0wC,CAAgB,qqCAAG,EAAC,C;;;;;;;;;;;ACA9xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-input/u-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-input.vue?vue&type=template&id=460c1d26&scoped=true&\"\nvar renderjs\nimport script from \"./u-input.vue?vue&type=script&lang=js&\"\nexport * from \"./u-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-input.vue?vue&type=style&index=0&id=460c1d26&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"460c1d26\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-input/u-input.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=template&id=460c1d26&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.type == \"textarea\" ? _vm.__get_style([_vm.getStyle]) : null\n  var s1 = !(_vm.type == \"textarea\") ? _vm.__get_style([_vm.getStyle]) : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showPassword = !_vm.showPassword\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=script&lang=js&\"", "<template>\n    <view\n        class=\"u-input\"\n        :class=\"{\n            'u-input--border': border,\n            'u-input--error': validateState\n        }\"\n        :style=\"{\n            padding: `0 ${border ? 20 : 0}rpx`,\n            borderColor: borderColor,\n            textAlign: inputAlign\n        }\"\n        @tap.stop=\"inputClick\"\n    >\n        <textarea\n            v-if=\"type == 'textarea'\"\n            class=\"u-input__input u-input__textarea\"\n            :style=\"[getStyle]\"\n            :value=\"defaultValue\"\n            :placeholder=\"placeholder\"\n            :placeholderStyle=\"placeholderStyle\"\n            :disabled=\"disabled\"\n            :maxlength=\"inputMaxlength\"\n            :fixed=\"fixed\"\n            :focus=\"focus\"\n            :autoHeight=\"autoHeight\"\n            :selection-end=\"uSelectionEnd\"\n            :selection-start=\"uSelectionStart\"\n            :cursor-spacing=\"getCursorSpacing\"\n            :show-confirm-bar=\"showConfirmbar\"\n            @input=\"handleInput\"\n            @blur=\"handleBlur\"\n            @focus=\"onFocus\"\n            @confirm=\"onConfirm\"\n        />\n        <input\n            v-else\n            class=\"u-input__input\"\n            :type=\"type == 'password' ? 'text' : type\"\n            :style=\"[getStyle]\"\n            :value=\"defaultValue\"\n            :password=\"type == 'password' && !showPassword\"\n            :placeholder=\"placeholder\"\n            :placeholderStyle=\"placeholderStyle\"\n            :disabled=\"disabled || type === 'select'\"\n            :maxlength=\"inputMaxlength\"\n            :focus=\"focus\"\n            :confirmType=\"confirmType\"\n            :cursor-spacing=\"getCursorSpacing\"\n            :selection-end=\"uSelectionEnd\"\n            :selection-start=\"uSelectionStart\"\n            :show-confirm-bar=\"showConfirmbar\"\n            @focus=\"onFocus\"\n            @blur=\"handleBlur\"\n            @input=\"handleInput\"\n            @confirm=\"onConfirm\"\n        />\n        <view class=\"u-input__right-icon u-flex\">\n            <view class=\"u-input__right-icon__clear u-input__right-icon__item\" @tap=\"onClear\" v-if=\"clearable && value != '' && focused\">\n                <u-icon size=\"32\" name=\"close-circle-fill\" color=\"#c0c4cc\"/>\n            </view>\n            <view class=\"u-input__right-icon__clear u-input__right-icon__item\" v-if=\"passwordIcon && type == 'password'\">\n                <u-icon size=\"32\" :name=\"!showPassword ? 'eye' : 'eye-fill'\" color=\"#c0c4cc\" @click=\"showPassword = !showPassword\"/>\n            </view>\n            <view class=\"u-input__right-icon--select u-input__right-icon__item\" v-if=\"type == 'select'\" :class=\"{\n                'u-input__right-icon--select--reverse': selectOpen\n            }\">\n                <u-icon name=\"arrow-down-fill\" size=\"26\" color=\"#c0c4cc\"></u-icon>\n            </view>\n        </view>\n    </view>\n</template>\n\n<script>\nimport Emitter from '../../libs/util/emitter.js';\n\n/**\n * input 输入框\n * @description 此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件u-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。\n * @tutorial http://uviewui.com/components/input.html\n * @property {String} type 模式选择，见官网说明\n * @property {Boolean} clearable 是否显示右侧的清除图标(默认true)\n * @property {} v-model 用于双向绑定输入框的值\n * @property {String} input-align 输入框文字的对齐方式(默认left)\n * @property {String} placeholder placeholder显示值(默认 '请输入内容')\n * @property {Boolean} disabled 是否禁用输入框(默认false)\n * @property {String Number} maxlength 输入框的最大可输入长度(默认140)\n * @property {String Number} selection-start 光标起始位置，自动聚焦时有效，需与selection-end搭配使用（默认-1）\n * @property {String Number} maxlength 光标结束位置，自动聚焦时有效，需与selection-start搭配使用（默认-1）\n * @property {String Number} cursor-spacing 指定光标与键盘的距离，单位px(默认0)\n * @property {String} placeholderStyle placeholder的样式，字符串形式，如\"color: red;\"(默认 \"color: #c0c4cc;\")\n * @property {String} confirm-type 设置键盘右下角按钮的文字，仅在type为text时生效(默认done)\n * @property {Object} custom-style 自定义输入框的样式，对象形式\n * @property {Boolean} focus 是否自动获得焦点(默认false)\n * @property {Boolean} fixed 如果type为textarea，且在一个\"position:fixed\"的区域，需要指明为true(默认false)\n * @property {Boolean} password-icon type为password时，是否显示右侧的密码查看图标(默认true)\n * @property {Boolean} border 是否显示边框(默认false)\n * @property {String} border-color 输入框的边框颜色(默认#dcdfe6)\n * @property {Boolean} auto-height 是否自动增高输入区域，type为textarea时有效(默认true)\n * @property {String Number} height 高度，单位rpx(text类型时为70，textarea时为100)\n * @example <u-input v-model=\"value\" :type=\"type\" :border=\"border\" />\n */\nexport default {\n    name: 'u-input',\n    mixins: [Emitter],\n    props: {\n        value: {\n            type: [String, Number],\n            default: ''\n        },\n        // 输入框的类型，textarea，text，number\n        type: {\n            type: String,\n            default: 'text'\n        },\n        inputAlign: {\n            type: String,\n            default: 'left'\n        },\n        placeholder: {\n            type: String,\n            default: '请输入内容'\n        },\n        disabled: {\n            type: Boolean,\n            default: false\n        },\n        maxlength: {\n            type: [Number, String],\n            default: 140\n        },\n        placeholderStyle: {\n            type: String,\n            default: 'color: #c0c4cc;'\n        },\n        confirmType: {\n            type: String,\n            default: 'done'\n        },\n        // 输入框的自定义样式\n        customStyle: {\n            type: Object,\n            default() {\n                return {};\n            }\n        },\n        // 如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true\n        fixed: {\n            type: Boolean,\n            default: false\n        },\n        // 是否自动获得焦点\n        focus: {\n            type: Boolean,\n            default: false\n        },\n        // 密码类型时，是否显示右侧的密码图标\n        passwordIcon: {\n            type: Boolean,\n            default: true\n        },\n        // input|textarea是否显示边框\n        border: {\n            type: Boolean,\n            default: false\n        },\n        // 输入框的边框颜色\n        borderColor: {\n            type: String,\n            default: '#dcdfe6'\n        },\n        autoHeight: {\n            type: Boolean,\n            default: true\n        },\n        // type=select时，旋转右侧的图标，标识当前处于打开还是关闭select的状态\n        // open-打开，close-关闭\n        selectOpen: {\n            type: Boolean,\n            default: false\n        },\n        // 高度，单位rpx\n        height: {\n            type: [Number, String],\n            default: ''\n        },\n        // 是否可清空\n        clearable: {\n            type: Boolean,\n            default: true\n        },\n        // 指定光标与键盘的距离，单位 px\n        cursorSpacing: {\n            type: [Number, String],\n            default: 0\n        },\n        // 光标起始位置，自动聚焦时有效，需与selection-end搭配使用\n        selectionStart: {\n            type: [Number, String],\n            default: -1\n        },\n        // 光标结束位置，自动聚焦时有效，需与selection-start搭配使用\n        selectionEnd: {\n            type: [Number, String],\n            default: -1\n        },\n        // 是否自动去除两端的空格\n        trim: {\n            type: Boolean,\n            default: true\n        },\n        // 是否显示键盘上方带有”完成“按钮那一栏\n        showConfirmbar:{\n            type:Boolean,\n            default:true\n        }\n    },\n    data() {\n        return {\n            defaultValue: this.value,\n            inputHeight: 70, // input的高度\n            textareaHeight: 100, // textarea的高度\n            validateState: false, // 当前input的验证状态，用于错误时，边框是否改为红色\n            focused: false, // 当前是否处于获得焦点的状态\n            showPassword: false, // 是否预览密码\n            lastValue: '', // 用于头条小程序，判断@input中，前后的值是否发生了变化，因为头条中文下，按下键没有输入内容，也会触发@input时间\n        };\n    },\n    watch: {\n        value(nVal, oVal) {\n            this.defaultValue = nVal;\n            // 当值发生变化，且为select类型时(此时input被设置为disabled，不会触发@input事件)，模拟触发@input事件\n            if(nVal != oVal && this.type == 'select') this.handleInput({\n                detail: {\n                    value: nVal\n                }\n            })\n        },\n    },\n    computed: {\n        // 因为uniapp的input组件的maxlength组件必须要数值，这里转为数值，给用户可以传入字符串数值\n        inputMaxlength() {\n            return Number(this.maxlength);\n        },\n        getStyle() {\n            let style = {};\n            // 如果没有自定义高度，就根据type为input还是textare来分配一个默认的高度\n            style.minHeight = this.height ? this.height + 'rpx' : this.type == 'textarea' ?\n                this.textareaHeight + 'rpx' : this.inputHeight + 'rpx';\n            style = Object.assign(style, this.customStyle);\n            return style;\n        },\n        //\n        getCursorSpacing() {\n            return Number(this.cursorSpacing);\n        },\n        // 光标起始位置\n        uSelectionStart() {\n            return String(this.selectionStart);\n        },\n        // 光标结束位置\n        uSelectionEnd() {\n            return String(this.selectionEnd);\n        }\n    },\n    created() {\n        // 监听u-form-item发出的错误事件，将输入框边框变红色\n        this.$on('on-form-item-error', this.onFormItemError);\n    },\n    methods: {\n        /**\n         * change 事件\n         * @param event\n         */\n        handleInput(event) {\n            let value = event.detail.value;\n            // 判断是否去除空格\n            if(this.trim) value = this.$u.trim(value);\n            // vue 原生的方法 return 出去\n            this.$emit('input', value);\n            // 当前model 赋值\n            this.defaultValue = value;\n            // 过一个生命周期再发送事件给u-form-item，否则this.$emit('input')更新了父组件的值，但是微信小程序上\n            // 尚未更新到u-form-item，导致获取的值为空，从而校验混论\n            // 这里不能延时时间太短，或者使用this.$nextTick，否则在头条上，会造成混乱\n            setTimeout(() => {\n                // 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\n                // #ifdef MP-TOUTIAO\n                if(this.$u.trim(value) == this.lastValue) return ;\n                this.lastValue = value;\n                // #endif\n                // 将当前的值发送到 u-form-item 进行校验\n                this.dispatch('u-form-item', 'on-form-change', value);\n            }, 40)\n        },\n        /**\n         * blur 事件\n         * @param event\n         */\n        handleBlur(event) {\n            // 最开始使用的是监听图标@touchstart事件，自从hx2.8.4后，此方法在微信小程序出错\n            // 这里改为监听点击事件，手点击清除图标时，同时也发生了@blur事件，导致图标消失而无法点击，这里做一个延时\n            setTimeout(() => {\n                this.focused = false;\n            }, 100)\n            // vue 原生的方法 return 出去\n            this.$emit('blur', event.detail.value);\n            setTimeout(() => {\n                // 头条小程序由于自身bug，导致中文下，每按下一个键(尚未完成输入)，都会触发一次@input，导致错误，这里进行判断处理\n                // #ifdef MP-TOUTIAO\n                if(this.$u.trim(value) == this.lastValue) return ;\n                this.lastValue = value;\n                // #endif\n                // 将当前的值发送到 u-form-item 进行校验\n                this.dispatch('u-form-item', 'on-form-blur', event.detail.value);\n            }, 40)\n        },\n        onFormItemError(status) {\n            this.validateState = status;\n        },\n        onFocus(event) {\n            this.focused = true;\n            this.$emit('focus');\n        },\n        onConfirm(e) {\n            this.$emit('confirm', e.detail.value);\n        },\n        onClear(event) {\n            this.$emit('input', '');\n        },\n        inputClick() {\n            this.$emit('click');\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/style.components.scss\";\n\n.u-input {\n    position: relative;\n    flex: 1;\n    @include vue-flex;\n\n    &__input {\n        //height: $u-form-item-height;\n        font-size: 28rpx;\n        color: $u-main-color;\n        flex: 1;\n    }\n\n    &__textarea {\n        width: auto;\n        font-size: 28rpx;\n        color: $u-main-color;\n        padding: 10rpx 0;\n        line-height: normal;\n        flex: 1;\n    }\n\n    &--border {\n        border-radius: 6rpx;\n        border-radius: 4px;\n        border: 1px solid $u-form-item-border-color;\n    }\n\n    &--error {\n        border-color: $u-type-error!important;\n    }\n\n    &__right-icon {\n\n        &__item {\n            margin-left: 10rpx;\n        }\n\n        &--select {\n            transition: transform .4s;\n\n            &--reverse {\n                transform: rotate(-180deg);\n            }\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=460c1d26&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-input.vue?vue&type=style&index=0&id=460c1d26&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425055\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}