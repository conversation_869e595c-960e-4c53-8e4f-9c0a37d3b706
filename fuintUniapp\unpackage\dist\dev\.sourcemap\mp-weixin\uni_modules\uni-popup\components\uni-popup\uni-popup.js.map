{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?4e11", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?ba99", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?a832", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?338b", "uni-app:///uni_modules/uni-popup/components/uni-popup/uni-popup.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?aa23", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uni_modules/uni-popup/components/uni-popup/uni-popup.vue?c6de"], "names": ["name", "components", "props", "animation", "type", "default", "maskClick", "backgroundColor", "safeArea", "watch", "handler", "immediate", "isDesktop", "data", "duration", "ani", "showPopup", "showTrans", "popup<PERSON><PERSON><PERSON>", "popupHeight", "config", "top", "bottom", "center", "left", "right", "message", "dialog", "share", "maskClass", "position", "transClass", "maskShow", "mkclick", "popupstyle", "computed", "bg", "mounted", "windowWidth", "windowHeight", "windowTop", "safeAreaInsets", "fixSize", "created", "methods", "closeMask", "disableMask", "clear", "e", "open", "direction", "console", "show", "close", "clearTimeout", "touchstart", "onTap", "paddingBottom", "display", "flexDirection", "justifyContent", "alignItems"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACiL;AACjL,gBAAgB,kLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA4qB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmBhsB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,eAoBA;EACAA;EACAC,aAIA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EAEAI;IACA;AACA;AACA;IACAL;MACAM;QACA;QACA;MACA;MACAC;IACA;IACAC;MACAF;QACA;QACA;MACA;MACAC;IACA;IACA;AACA;AACA;AACA;IACAL;MACAI;QACA;MACA;MACAC;IACA;EACA;EACAE;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAC;QACAR;QACAD;QACAG;QACAC;QACAlB;MACA;MACAwB;QACAD;QACAN;QACAC;MACA;MACAO;MACAC;MACAC;IACA;EACA;EACAC;IACAvB;MACA;IACA;IACAwB;MACA;QACA;MACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;QAAAC;QAAAC;QAAAC;QAAAC;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;EAOA;EACAC;IACA;IACA;MACA;IACA;MACA;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;IACAC;MAEAC;MAEA;IACA;IAEAC;MACA;MACA;QACAC;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;QACAC;QACAhD;MACA;IACA;IACAiD;MAAA;MACA;MACA;QACAD;QACAhD;MACA;MACAkD;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IAEAC;MACA;QACA;QACA;QACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAnC;MAAA;MACA;MACA;MACA;QACAS;QACAN;QACAC;QACAlB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAe;MACA;MACA;MAEA;QACAQ;QACAN;QACAC;QACAH;QACAmC;QACAlD;MACA;MACA;MACA;MACA;MACA;IACA;IACA;AACA;AACA;IACAgB;MACA;MACA;MACA;QACAO;QAEA4B;QACAC;QAEArC;QACAE;QACAC;QACAJ;QACAuC;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACArC;MACA;MACA;MACA;QACAM;QACAN;QACAF;QACAD;QACAd;QAEAmD;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;IACAlC;MACA;MACA;MACA;QACAK;QACAR;QACAG;QACAJ;QACAd;QAEAmD;QACAC;MAEA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9VA;AAAA;AAAA;AAAA;AAAuyC,CAAgB,uqCAAG,EAAC,C;;;;;;;;;;;ACA3zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-popup/components/uni-popup/uni-popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-popup.vue?vue&type=template&id=7c43d41b&scoped=true&\"\nvar renderjs\nimport script from \"./uni-popup.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-popup.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-popup.vue?vue&type=style&index=0&id=7c43d41b&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7c43d41b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-popup/components/uni-popup/uni-popup.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup.vue?vue&type=template&id=7c43d41b&scoped=true&\"", "var components\ntry {\n  components = {\n    uniTransition: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-transition/uni-transition\" */ \"@/components/uni-transition/uni-transition.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup.vue?vue&type=script&lang=js&\"", "<template>\n    <view v-if=\"showPopup\" class=\"uni-popup\" :class=\"[popupstyle, isDesktop ? 'fixforpc-z-index' : '']\" @touchmove.stop.prevent=\"clear\">\n        <view @touchstart=\"touchstart\" >\n            <uni-transition key=\"1\" v-if=\"maskShow\" name=\"mask\" mode-class=\"fade\" :styles=\"maskClass\" :duration=\"duration\" :show=\"showTrans\" @click=\"onTap\" />\n            <uni-transition key=\"2\" :mode-class=\"ani\" name=\"content\" :styles=\"transClass\" :duration=\"duration\" :show=\"showTrans\" @click=\"onTap\">\n                <view class=\"uni-popup__wrapper\" :style=\"{ backgroundColor: bg }\" :class=\"[popupstyle]\" @click=\"clear\"><slot /></view>\n            </uni-transition>\n        </view>\n        <!-- #ifdef H5 -->\n        <keypress v-if=\"maskShow\" @esc=\"onTap\" />\n        <!-- #endif -->\n    </view>\n</template>\n\n<script>\n// #ifdef H5\nimport keypress from './keypress.js'\n// #endif\n\n/**\n * PopUp 弹出层\n * @description 弹出层组件，为了解决遮罩弹层的问题\n * @tutorial https://ext.dcloud.net.cn/plugin?id=329\n * @property {String} type = [top|center|bottom|left|right|message|dialog|share] 弹出方式\n *     @value top 顶部弹出\n *     @value center 中间弹出\n *     @value bottom 底部弹出\n *     @value left        左侧弹出\n *     @value right  右侧弹出\n *     @value message 消息提示\n *     @value dialog 对话框\n *     @value share 底部分享示例\n * @property {Boolean} animation = [ture|false] 是否开启动画\n * @property {Boolean} maskClick = [ture|false] 蒙版点击是否关闭弹窗\n * @property {String}  backgroundColor                     主窗口背景色\n * @property {Boolean} safeArea                                    是否适配底部安全区\n * @event {Function} change 打开关闭弹窗触发，e={show: false}\n */\n\nexport default {\n    name: 'uniPopup',\n    components: {\n        // #ifdef H5\n        keypress\n        // #endif\n    },\n    props: {\n        // 开启动画\n        animation: {\n            type: Boolean,\n            default: true\n        },\n        // 弹出层类型，可选值，top: 顶部弹出层；bottom：底部弹出层；center：全屏弹出层\n        // message: 消息提示 ; dialog : 对话框\n        type: {\n            type: String,\n            default: 'center'\n        },\n        // maskClick\n        maskClick: {\n            type: Boolean,\n            default: true\n        },\n        backgroundColor: {\n            type: String,\n            default: 'none'\n        },\n        safeArea:{\n            type: Boolean,\n            default: true\n        }\n    },\n\n    watch: {\n        /**\n         * 监听type类型\n         */\n        type: {\n            handler: function(type) {\n                if (!this.config[type]) return\n                this[this.config[type]](true)\n            },\n            immediate: true\n        },\n        isDesktop: {\n            handler: function(newVal) {\n                if (!this.config[newVal]) return\n                this[this.config[this.type]](true)\n            },\n            immediate: true\n        },\n        /**\n         * 监听遮罩是否可点击\n         * @param {Object} val\n         */\n        maskClick: {\n            handler: function(val) {\n                this.mkclick = val\n            },\n            immediate: true\n        }\n    },\n    data() {\n        return {\n            duration: 300,\n            ani: [],\n            showPopup: false,\n            showTrans: false,\n            popupWidth: 0,\n            popupHeight: 0,\n            config: {\n                top: 'top',\n                bottom: 'bottom',\n                center: 'center',\n                left: 'left',\n                right: 'right',\n                message: 'top',\n                dialog: 'center',\n                share: 'bottom'\n            },\n            maskClass: {\n                position: 'fixed',\n                bottom: 0,\n                top: 0,\n                left: 0,\n                right: 0,\n                backgroundColor: 'rgba(0, 0, 0, 0.4)'\n            },\n            transClass: {\n                position: 'fixed',\n                left: 0,\n                right: 0\n            },\n            maskShow: true,\n            mkclick: true,\n            popupstyle: this.isDesktop ? 'fixforpc-top' : 'top'\n        }\n    },\n    computed: {\n        isDesktop() {\n            return this.popupWidth >= 500 && this.popupHeight >= 500\n        },\n        bg() {\n            if (this.backgroundColor === '' || this.backgroundColor === 'none') {\n                return 'transparent'\n            }\n            return this.backgroundColor\n        }\n    },\n    mounted() {\n        const fixSize = () => {\n            const { windowWidth, windowHeight, windowTop, safeAreaInsets } = uni.getSystemInfoSync()\n            this.popupWidth = windowWidth\n            this.popupHeight = windowHeight + windowTop\n            // 是否适配底部安全区\n            if(this.safeArea){\n                this.safeAreaInsets = safeAreaInsets\n            }else{\n                this.safeAreaInsets = 0\n            }\n        }\n        fixSize()\n        // #ifdef H5\n        // window.addEventListener('resize', fixSize)\n        // this.$once('hook:beforeDestroy', () => {\n        //     window.removeEventListener('resize', fixSize)\n        // })\n        // #endif\n    },\n    created() {\n        this.mkclick = this.maskClick\n        if (this.animation) {\n            this.duration = 300\n        } else {\n            this.duration = 0\n        }\n        // TODO 处理 message 组件生命周期异常的问题\n        this.messageChild = null\n        // TODO 解决头条冒泡的问题\n        this.clearPropagation = false\n    },\n    methods: {\n        /**\n         * 公用方法，不显示遮罩层\n         */\n        closeMask() {\n            this.maskShow = false\n        },\n        /**\n         * 公用方法，遮罩层禁止点击\n         */\n        disableMask() {\n            this.mkclick = false\n        },\n        // TODO nvue 取消冒泡\n        clear(e) {\n            // #ifndef APP-NVUE\n            e.stopPropagation()\n            // #endif\n            this.clearPropagation = true\n        },\n\n        open(direction) {\n            let innerType = ['top', 'center', 'bottom', 'left', 'right', 'message', 'dialog', 'share']\n            if (!(direction && innerType.indexOf(direction) !== -1)) {\n                direction = this.type\n            }\n            if (!this.config[direction]) {\n                console.error('缺少类型：', direction)\n                return\n            }\n            this[this.config[direction]]()\n            this.$emit('change', {\n                show: true,\n                type: direction\n            })\n        },\n        close(type) {\n            this.showTrans = false\n            this.$emit('change', {\n                show: false,\n                type: this.type\n            })\n            clearTimeout(this.timer)\n            // // 自定义关闭事件\n            // this.customOpen && this.customClose()\n            this.timer = setTimeout(() => {\n                this.showPopup = false\n            }, 300)\n        },\n        // TODO 处理冒泡事件，头条的冒泡事件有问题 ，先这样兼容\n        touchstart(){\n            this.clearPropagation = false\n        },\n\n        onTap() {\n            if (this.clearPropagation) {\n                // fix by mehaotian 兼容 nvue\n                this.clearPropagation = false\n                return\n            }\n            this.$emit('maskClick')\n            if (!this.mkclick) return\n            this.close()\n        },\n        /**\n         * 顶部弹出样式处理\n         */\n        top(type) {\n            this.popupstyle = this.isDesktop ? 'fixforpc-top' : 'top'\n            this.ani = ['slide-top']\n            this.transClass = {\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                backgroundColor: this.bg\n            }\n            // TODO 兼容 type 属性 ，后续会废弃\n            if (type) return\n            this.showPopup = true\n            this.showTrans = true\n            this.$nextTick(() => {\n                if (this.messageChild && this.type === 'message') {\n                    this.messageChild.timerClose()\n                }\n            })\n        },\n        /**\n         * 底部弹出样式处理\n         */\n        bottom(type) {\n            this.popupstyle = 'bottom'\n            this.ani = ['slide-bottom']\n\n            this.transClass = {\n                position: 'fixed',\n                left: 0,\n                right: 0,\n                bottom: 0,\n                paddingBottom: (this.safeAreaInsets && this.safeAreaInsets.bottom) || 0,\n                backgroundColor: this.bg\n            }\n            // TODO 兼容 type 属性 ，后续会废弃\n            if (type) return\n            this.showPopup = true\n            this.showTrans = true\n        },\n        /**\n         * 中间弹出样式处理\n         */\n        center(type) {\n            this.popupstyle = 'center'\n            this.ani = ['zoom-out', 'fade']\n            this.transClass = {\n                position: 'fixed',\n                /* #ifndef APP-NVUE */\n                display: 'flex',\n                flexDirection: 'column',\n                /* #endif */\n                bottom: 0,\n                left: 0,\n                right: 0,\n                top: 0,\n                justifyContent: 'center',\n                alignItems: 'center'\n            }\n            // TODO 兼容 type 属性 ，后续会废弃\n            if (type) return\n            this.showPopup = true\n            this.showTrans = true\n        },\n        left(type) {\n            this.popupstyle = 'left'\n            this.ani = ['slide-left']\n            this.transClass = {\n                position: 'fixed',\n                left: 0,\n                bottom: 0,\n                top: 0,\n                backgroundColor: this.bg,\n                /* #ifndef APP-NVUE */\n                display: 'flex',\n                flexDirection: 'column'\n                /* #endif */\n            }\n            // TODO 兼容 type 属性 ，后续会废弃\n            if (type) return\n            this.showPopup = true\n            this.showTrans = true\n        },\n        right(type) {\n            this.popupstyle = 'right'\n            this.ani = ['slide-right']\n            this.transClass = {\n                position: 'fixed',\n                bottom: 0,\n                right: 0,\n                top: 0,\n                backgroundColor: this.bg,\n                /* #ifndef APP-NVUE */\n                display: 'flex',\n                flexDirection: 'column'\n                /* #endif */\n            }\n            // TODO 兼容 type 属性 ，后续会废弃\n            if (type) return\n            this.showPopup = true\n            this.showTrans = true\n        }\n    }\n}\n</script>\n<style lang=\"scss\" scoped>\n.uni-popup {\n    position: fixed;\n    /* #ifndef APP-NVUE */\n    z-index: 99;\n    /* #endif */\n    &.top,\n    &.left,\n    &.right {\n        /* #ifdef H5 */\n        top: var(--window-top);\n        /* #endif */\n        /* #ifndef H5 */\n        top: 0;\n        /* #endif */\n    }\n    .uni-popup__wrapper {\n        /* #ifndef APP-NVUE */\n        display: block;\n        /* #endif */\n        position: relative;\n        /* iphonex 等安全区设置，底部安全区适配 */\n        /* #ifndef APP-NVUE */\n        // padding-bottom: constant(safe-area-inset-bottom);\n        // padding-bottom: env(safe-area-inset-bottom);\n        /* #endif */\n        &.left,\n        &.right {\n            /* #ifdef H5 */\n            padding-top: var(--window-top);\n            /* #endif */\n            /* #ifndef H5 */\n            padding-top: 0;\n            /* #endif */\n            flex: 1;\n        }\n    }\n}\n\n.fixforpc-z-index {\n    /* #ifndef APP-NVUE */\n    z-index: 999;\n    /* #endif */\n}\n\n.fixforpc-top {\n    top: 0;\n}\n</style>\n", "import mod from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup.vue?vue&type=style&index=0&id=7c43d41b&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-popup.vue?vue&type=style&index=0&id=7c43d41b&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891421835\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}