{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?891d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?ef81", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?9053", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?4943", "uni-app:///pages/cart/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?4bbf", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?8a24", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?e151", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/cart/index.vue?174b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Empty", "data", "inArray", "isLoading", "mode", "list", "total", "checkedIds", "totalPrice", "gradeInfo", "hafanInfo", "watch", "handler", "uni", "immediate", "onShow", "computed", "hafanLevel", "isMemberPrice", "methods", "getUserInfo", "UserApi", "rev", "onCalcTotalPrice", "checkedList", "tempPrice", "app", "getCartList", "CartApi", "then", "finally", "onClearInvalidId", "handleToggleMode", "onChangeStepper", "item", "onUpdateCartNum", "catch", "setTimeout", "onTargetGoods", "goodsId", "onTargetIndex", "handleCheckItem", "index", "handleCheckAll", "handleOrder", "cartIds", "handleDelete", "title", "content", "showCancel", "success", "confirm", "onClearCart", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACmG9pB;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAJ;MACAK;QACA;QACA;QACA;QACAC;MACA;MACAC;IACA;IACA;IACAR;MACA;MACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAS;IACA;IACA;MACA;MACA;IACA;MACA;IACA;IACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QAAA;MAAA;MACA;MACA;MACAC;QACA;QACA;QACA;QACAC;MACA;MACAC;IACA;IAEA;IACAC;MACA;MACAD;MACAE,eACAC;QACAH;QACAA;QACA;QACAA;MACA,GACAI;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;MACAL;IACA;IAEA;IACAM;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACAC;QACAA;MACA;MACA;MACAA;MACA;MACAA;IACA;IAEA;IACAC;MACA;MACAP,sDACAC;QACA;QACAH;QACA;QACAA;QACA;QACAQ;MACA,GACAE;QACA;QACAF;QACAG;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;QAAAC;MAAA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QAAA;MAAA;MACAC;IACA;IAEA;IACAC;MACA;QAAAtC;MACA;QAAA;MAAA;IACA;IAEA;IACAuC;MACA;MACA;QACA;QACAlB;UAAAmB;QAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;MACAjC;QACAkC;QACAC;QACAC;QACAC;UAAA;UACA;UACAC;QACA;MACA;IACA;IAEA;IACAC;MACA;MACAxB,8BACAC;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAwB;MAAA;MACAhB;QACA;QACAxB;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjUA;AAAA;AAAA;AAAA;AAA66B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACAj8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/cart/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/cart/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=457cfe48&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=457cfe48&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"457cfe48\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/cart/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=457cfe48&scoped=true&\"", "var components\ntry {\n  components = {\n    uNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-number-box/u-number-box\" */ \"@/uview-ui/components/u-number-box/u-number-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var g1 = _vm.list.length\n  var l0 = g1\n    ? _vm.__map(_vm.list, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.inArray(item.id, _vm.checkedIds)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  var g2 = _vm.list.length\n  var g3 = _vm.list.length\n  var g4 = g3\n    ? _vm.checkedIds.length > 0 && _vm.checkedIds.length === _vm.list.length\n    : null\n  var g5 = g3 && _vm.mode == \"normal\" ? _vm.checkedIds.join() : null\n  var g6 = g3 && _vm.mode == \"normal\" ? _vm.checkedIds.length : null\n  var g7 = g3 && _vm.mode == \"normal\" && g6 > 0 ? _vm.checkedIds.length : null\n  var g8 = g3 && _vm.mode == \"edit\" ? _vm.checkedIds.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <!-- 页面顶部 -->\n    <view v-if=\"list.length\" class=\"head-info\">\n      <view class=\"cart-total\">\n        <text>共</text>\n        <text class=\"active\">{{ total }}</text>\n        <text>件商品</text>\n      </view>\n      <view class=\"cart-edit\" @click=\"handleToggleMode\">\n        <view v-if=\"mode == 'normal'\" class=\"normal\">\n          <text class=\"icon iconfont icon-bianji\"></text>\n          <text>编辑</text>\n        </view>\n        <view v-if=\"mode == 'edit'\" class=\"edit\">\n          <text>完成</text>\n        </view>\n      </view>\n    </view>\n    <!-- 购物车商品列表 -->\n    <view v-if=\"list.length\" class=\"cart-list\">\n      <view class=\"cart-item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"onTargetGoods(item.goodsId)\">\n        <label class=\"item-radio\" @click.stop=\"handleCheckItem(item.id)\">\n          <radio class=\"radio\" color=\"#fa2209\" :checked=\"inArray(item.id, checkedIds)\" />\n        </label>\n        <view class=\"goods-image\">\n          <image class=\"image\" :src=\"item.goodsInfo.logo\"></image>\n        </view>\n        <view class=\"item-content\">\n          <view class=\"goods-title twolist-hidden\"><text>{{ item.goodsInfo.name }}</text></view>\n          <!-- 普通商品规格 -->\n          <view class=\"goods-props clearfix\" v-if=\"!item.isPackage\">\n            <view class=\"goods-props-item\" v-for=\"(spec, idx) in item.specList\" :key=\"idx\">\n              <text>{{ spec.specName }}{{ spec.specValue }}</text>\n            </view>\n          </view>\n          <!-- 套餐商品信息 -->\n          <view class=\"package-info\" v-if=\"item.isPackage === 'Y' && item.packageGroups\">\n            <view class=\"package-group\" v-for=\"(group, groupIndex) in item.packageGroups\" :key=\"groupIndex\">\n              <view class=\"group-name\">{{ group.groupName }}</view>\n              <view class=\"group-items\">\n                <view class=\"package-item\" v-for=\"(packageItem, itemIndex) in group.items\" :key=\"itemIndex\">\n                  <text class=\"item-name\">{{ packageItem.itemName }}</text>\n                  <text class=\"item-spec\" v-if=\"packageItem.selectedSkuText\">{{ packageItem.selectedSkuText }}</text>\n                  <text class=\"item-quantity\">x{{ packageItem.quantity }}</text>\n                </view>\n              </view>\n            </view>\n          </view>\n          <view class=\"item-foot\">\n            <view class=\"goods-price\">\n              <text class=\"unit\">￥</text>\n              <text class=\"value\">{{ (isMemberPrice && item.goodsInfo.gradePrice > 0) ? item.goodsInfo.gradePrice  : item.goodsInfo.price }}</text>\n            </view>\n            <view class=\"stepper\">\n              <u-number-box :min=\"1\" :value=\"item.num\" @change=\"onChangeStepper($event, item)\" />\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    <!-- 购物车数据为空 -->\n    <empty v-if=\"!list.length\" :isLoading=\"isLoading\" :custom-style=\"{ padding: '180rpx 50rpx' }\" tips=\"您没有待结算商品, 快去逛逛吧\">\n      <view slot=\"slot\" class=\"empty-ipt\" @click=\"onTargetIndex\">\n        <text>去逛逛</text>\n      </view>\n    </empty>\n    <!-- 底部操作栏 -->\n    <view v-if=\"list.length\" class=\"footer-fixed\">\n      <label class=\"all-radio\" @click=\"handleCheckAll\">\n        <radio class=\"radio\" color=\"#fa2209\" :checked=\"checkedIds.length > 0 && checkedIds.length === list.length\" />\n        <text>全选</text>\n      </label>\n      <view class=\"total-info\">\n        <text>合计：</text>\n        <view class=\"goods-price\">\n          <text class=\"unit\">￥</text>\n          <text class=\"value\">{{ totalPrice }}</text>\n        </view>\n      </view>\n      <view class=\"cart-action\">\n        <view class=\"btn-wrapper\">\n          <!-- dev:下面的disabled条件使用checkedIds.join方式判断 -->\n          <!-- dev:通常情况下vue项目使用checkedIds.length更合理, 但是length属性在微信小程序中不起作用 -->\n          <view v-if=\"mode == 'normal'\" class=\"btn-item btn-main\" :class=\"{ disabled: checkedIds.join() == '' }\"\n            @click=\"handleOrder()\">\n            <text>确认结算 {{ checkedIds.length > 0 ? `(${checkedIds.length})` : '' }}</text>\n          </view>\n          <view v-if=\"mode == 'edit'\" class=\"btn-item btn-main\" :class=\"{ disabled: !checkedIds.length }\"\n            @click=\"handleDelete()\">\n            <text>删除</text>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import { inArray, arrayIntersect, debounce } from '@/utils/util'\n  import { checkLogin, setCartTotalNum, setCartTabBadge } from '@/utils/app'\n  import * as CartApi from '@/api/cart'\n  import * as UserApi from '@/api/user'\n  import Empty from '@/components/empty'\n  const CartIdsIndex = 'CartIds'\n  export default {\n    components: {\n      Empty\n    },\n    data() {\n      return {\n        inArray,\n        // 正在加载\n        isLoading: true,\n        // 当前模式: normal正常 edit编辑\n        mode: 'normal',\n        // 购物车列表\n        list: [],\n        // 购物车商品总数量\n        total: 0,\n        // 选中的商品ID记录\n        checkedIds: [],\n        // 选中的商品总金额\n        totalPrice: '0.00',\n\t\tgradeInfo: {},\n\t\thafanInfo: {}\n      }\n    },\n    watch: {\n      // 监听选中的商品\n      checkedIds: {\n        handler(val) {\n          // 计算合计金额\n          this.onCalcTotalPrice()\n          // 记录到缓存中\n          uni.setStorageSync(CartIdsIndex, val)\n        },\n        immediate: false\n      },\n      // 监听购物车商品总数量\n      total(val) {\n        // 缓存并设置角标\n        setCartTotalNum(val)\n        setCartTabBadge()\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面显示\n     */\n    onShow(options) {\n      // 获取购物车卡券列表\n\t  if(checkLogin()){\n\t\t  this.getCartList()\n\t\t  this.getUserInfo()\n\t  }else{\n\t\t  this.isLoading = false\n\t  } \n      // 获取缓存中的选中记录\n      this.checkedIds = uni.getStorageSync(CartIdsIndex)\n    },\n\n    computed: {\n      // 计算哈帆会员等级\n      hafanLevel() {\n        return this.hafanInfo?.premium?.level || 'free';\n      },\n      // 计算是否为会员价格\n      isMemberPrice() {\n        return (this.gradeInfo && this.gradeInfo.grade > 1) || (this.hafanLevel !== 'free');\n      }\n    },\n\n    methods: {\n\t\tasync getUserInfo(){\n\t\t\tconst rev = await UserApi.info();\n\t\t\tthis.gradeInfo = rev.data.gradeInfo;\n\t\t\tthis.hafanInfo = rev.data.hafanInfo || {};\n\t\t},\n      // 计算合计金额 (根据选中的商品)\n      onCalcTotalPrice() {\n        const app = this\n        // 选中的商品记录\n        const checkedList = app.list.filter(item => inArray(item.id, app.checkedIds))\n        // 计算总金额\n        let tempPrice = 0;\n        checkedList.forEach(item => {\n          // 商品单价, 为了方便计算先转换单位为分 (整数)\n\t\t  const price = (this.isMemberPrice && item.goodsInfo.gradePrice > 0)  ? item.goodsInfo.gradePrice  : item.goodsInfo.price\n          const unitPrice = price * 100\n          tempPrice += unitPrice * item.num\n        })\n        app.totalPrice = (tempPrice / 100).toFixed(2)\n      },\n\n      // 获取购物车商品列表\n      getCartList() {\n        const app = this\n        app.isLoading = true\n        CartApi.list()\n          .then(result => {\n            app.list = result.data.list;\n            app.total = result.data.totalNum;\n            // 清除checkedIds中无效的ID\n            app.onClearInvalidId();\n          })\n          .finally(() => app.isLoading = false)\n      }, \n\n      // 清除checkedIds中无效的ID\n      onClearInvalidId() {\n        const app = this\n        const listIds = app.list.map(item => item.id)\n        app.checkedIds = arrayIntersect(listIds, app.checkedIds)\n      },\n\n      // 切换当前模式\n      handleToggleMode() {\n        this.mode = this.mode == 'normal' ? 'edit' : 'normal'\n      },\n\n      // 监听步进器更改事件\n      onChangeStepper({ value }, item) {\n        // 这里是组织首次启动时的执行\n        if (item.num == value) return\n        // 记录一个节流函数句柄\n        if (!item.debounceHandle) {\n          item.oldValue = item.num\n          item.debounceHandle = debounce(this.onUpdateCartNum, 500)\n        }\n        // 更新商品数量\n        item.num = value\n        // 提交更新购物车数量 (节流)\n        item.debounceHandle(item, item.oldValue, value)\n      },\n\n      // 提交更新购物车数量\n      onUpdateCartNum(item, oldValue, newValue) {\n        const app = this\n        CartApi.save(item.goodsId, \"=\", item.skuId, newValue)\n          .then(result => {\n            // 更新商品数量\n            app.total = result.data.cartTotal\n            // 重新计算合计金额\n            app.onCalcTotalPrice()\n            // 清除节流函数句柄\n            item.debounceHandle = null\n          })\n          .catch(err => {\n            // 还原商品数量\n            item.num = oldValue\n            setTimeout(() => app.$toast(err.errMsg), 10)\n          })\n      },\n\n      // 跳转到商品详情页\n      onTargetGoods(goodsId) {\n        this.$navTo('pages/goods/detail', { goodsId })\n      },\n\n      // 点击去逛逛按钮, 跳转到首页\n      onTargetIndex() {\n        this.$navTo('pages/index/index')\n      },\n\n      // 选中商品\n      handleCheckItem(cartId) {\n        const { checkedIds } = this\n        const index = checkedIds.findIndex(id => id === cartId)\n        index < 0 ? checkedIds.push(cartId) : checkedIds.splice(index, 1)\n      },\n\n      // 全选事件\n      handleCheckAll() {\n        const { checkedIds, list } = this\n        this.checkedIds = checkedIds.length === list.length ? [] : list.map(item => item.id)\n      },\n\n      // 结算选中的商品\n      handleOrder() {\n        const app = this\n        if (app.checkedIds.length) {\n          const cartIds = app.checkedIds.join()\n          app.$navTo('pages/settlement/goods', { cartIds })\n        }\n      },\n\n      // 删除选中的商品弹窗事件\n      handleDelete() {\n        const app = this\n        if (!app.checkedIds.length) {\n          return false\n        }\n        uni.showModal({\n          title: '友情提示',\n          content: '您确定要删除商品吗？',\n          showCancel: true,\n          success({ confirm }) {\n            // 确认删除\n            confirm && app.onClearCart()\n          }\n        })\n      },\n\n      // 确认删除商品\n      onClearCart() {\n        const app = this\n        CartApi.clear(app.checkedIds)\n          .then(result => app.getCartList())\n      },\n      \n      /**\n       * 下拉刷新\n       */\n      onPullDownRefresh() {\n          setTimeout(() => {\n            this.getCartList();\n            uni.stopPullDownRefresh();\n          }, 1000)\n      }\n    }\n  }\n</script>\n\n<style>\n  page {\n    background: #f5f5f5;\n  }\n</style>\n<style lang=\"scss\" scoped>\n  // 页面顶部\n  .head-info {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 4rpx 30rpx;\n    height: 80rpx;\n\n    .cart-total {\n      font-size: 35rpx;\n      color: #333;\n\n      .active {\n        color: #f03c3c;\n        margin: 0 2rpx;\n      }\n    }\n\n    .cart-edit {\n      padding-left: 20rpx;\n\n      .icon {\n        margin-right: 12rpx;\n      }\n\n      .edit {\n        color: #f03c3c;\n        font-weight: bold;\n      }\n    }\n\n  }\n\n  // 购物车列表\n  .cart-list {\n    padding: 0 16rpx 180rpx 16rpx;\n  }\n\n  .cart-item {\n    background: #fff;\n    border-radius: 12rpx;\n    display: flex;\n    align-items: center;\n    padding: 30rpx 16rpx;\n    margin-bottom: 24rpx;\n\n\n    .item-radio {\n      width: 56rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      margin-right: 10rpx;\n      text-align: center;\n\n      .radio {\n        transform: scale(0.76)\n      }\n    }\n\n    .goods-image {\n      width: 180rpx;\n      .image {\n        display: block;\n        width: 100%;\n        height: 160rpx;\n        border-radius: 8rpx;\n      }\n    }\n\n    .item-content {\n      flex: 1;\n      padding-left: 24rpx;\n\n      .goods-title {\n        font-size: 28rpx;\n        max-height: 76rpx;\n      }\n\n      .goods-props {\n        margin-top: 14rpx;\n        // height: 40rpx;\n        color: #ababab;\n        font-size: 24rpx;\n        overflow: hidden;\n\n        .goods-props-item {\n          display: inline-block;\n          margin-right: 14rpx;\n          padding: 4rpx 16rpx;\n          border-radius: 12rpx;\n          background-color: #F5F5F5;\n          width: auto;\n        }\n      }\n\n      .package-info {\n        margin-top: 14rpx;\n        \n        .package-group {\n          margin-bottom: 10rpx;\n          \n          .group-name {\n            font-size: 24rpx;\n            color: #666;\n            margin-bottom: 6rpx;\n          }\n          \n          .group-items {\n            .package-item {\n              display: flex;\n              align-items: center;\n              margin-bottom: 4rpx;\n              \n              .item-name {\n                font-size: 24rpx;\n                color: #333;\n                margin-right: 10rpx;\n              }\n              \n              .item-spec {\n                font-size: 22rpx;\n                color: #999;\n                margin-right: 10rpx;\n              }\n              \n              .item-quantity {\n                font-size: 22rpx;\n                color: #999;\n              }\n            }\n          }\n        }\n      }\n\n\n      .item-foot {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        margin-top: 20rpx;\n\n        .goods-price {\n          vertical-align: bottom;\n          color: $uni-text-color-active;\n\n          .unit {\n            font-size: 24rpx;\n          }\n\n          .value {\n            font-size: 32rpx;\n            color: #f03c3c;\n            font-weight: bold;\n          }\n        }\n      }\n\n    }\n  }\n\n  // 空数据按钮\n  .empty-ipt {\n    width: 150rpx;\n    margin: 0 auto;\n    font-size: 28rpx;\n    height: 68rpx;\n    line-height: 68rpx;\n    text-align: center;\n    color: #fff;\n    border-radius: 5rpx;\n    background: linear-gradient(to right, $fuint-theme, $fuint-theme);\n  }\n\n  // 底部操作栏\n  .footer-fixed {\n    display: flex;\n    align-items: center;\n    height: 180rpx;\n    background: #fff;\n    padding: 0rpx 0rpx 40rpx 30rpx;\n    position: fixed;\n    bottom: var(--window-bottom);\n    left: 0;\n    right: 0;\n    z-index: 11;\n\n    .all-radio {\n      width: 140rpx;\n      display: flex;\n      align-items: center;\n\n      .radio {\n        margin-bottom: -4rpx;\n        transform: scale(0.76)\n      }\n    }\n\n    .total-info {\n      flex: 1;\n      display: flex;\n      align-items: center;\n      justify-content: flex-end;\n      padding-right: 30rpx;\n\n      .goods-price {\n        vertical-align: bottom;\n        color: #fa2209;\n\n        .unit {\n          font-size: 24rpx;\n        }\n\n        .value {\n          font-size: 36rpx;\n          color: #f03c3c;\n          font-weight: bold;\n        }\n      }\n    }\n\n    .cart-action {\n      width: 200rpx;\n\n      .btn-wrapper {\n        height: 100%;\n        display: flex;\n        align-items: center;\n      }\n\n      .btn-item {\n        flex: 1;\n        font-size: 28rpx;\n        height: 92rpx;\n        line-height: 92rpx;\n        text-align: center;\n        color: #fff;\n      }\n\n      // 立即购买按钮\n      .btn-main {\n        background: linear-gradient(to right, $fuint-theme, $fuint-theme);\n        color: #fff;\n        padding-top: 5rpx;\n        border-radius: 5rpx;\n        margin-right: 20rpx;\n        // 禁用按钮\n        &.disabled {\n          background: #cccccc;\n        }\n      }\n\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891417989\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=457cfe48&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=457cfe48&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420867\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}