<block wx:if="{{!isLoading}}"><view class="container b-f p-b data-v-c764ac9e"><view class="base data-v-c764ac9e"><view class="coupon-main data-v-c764ac9e"><view class="left data-v-c764ac9e"><image class="image data-v-c764ac9e" src="{{detail.image}}"></image></view><view class="right data-v-c764ac9e"><view class="item data-v-c764ac9e"><view class="name data-v-c764ac9e">{{detail.name?detail.name:''}}</view></view><block wx:if="{{detail.amount>0}}"><view class="item data-v-c764ac9e"><view class="amount data-v-c764ac9e">￥<text class="num data-v-c764ac9e">{{detail.amount}}</text></view></view></block></view></view><block wx:if="{{detail.point>0}}"><view class="item data-v-c764ac9e"><view class="label data-v-c764ac9e">兑换积分：</view><view class="amount data-v-c764ac9e">{{detail.point}}</view></view></block><view class="item data-v-c764ac9e"><view class="label data-v-c764ac9e">有效期至：</view><view class="time data-v-c764ac9e">{{detail.effectiveDate}}</view></view><view class="item data-v-c764ac9e"><view class="label data-v-c764ac9e">适用门店：</view><view class="data-v-c764ac9e">{{detail.storeNames?detail.storeNames:'全部'}}</view></view><block wx:if="{{detail.code&&detail.status=='A'&&detail.isGive}}"><view data-event-opts="{{[['tap',[['give']]]]}}" class="gift data-v-c764ac9e" bindtap="__e"><text class="data-v-c764ac9e">无偿赠送</text></view></block><block wx:if="{{detail.code&&detail.status=='A'&&detail.isFission}}"><view data-event-opts="{{[['tap',[['fission']]]]}}" class="gift data-v-c764ac9e" bindtap="__e"><text class="data-v-c764ac9e">分享有礼</text></view></block></view><view class="coupon-content m-top20 data-v-c764ac9e"><view class="title data-v-c764ac9e">使用须知</view><view class="content data-v-c764ac9e"><jyf-parser vue-id="252fe516-1" html="{{detail.description?detail.description:'暂无...'}}" class="data-v-c764ac9e" bind:__l="__l"></jyf-parser></view></view><shortcut vue-id="252fe516-2" class="data-v-c764ac9e" bind:__l="__l"></shortcut><block wx:if="{{detail.qrCode}}"><view class="fission-popup data-v-c764ac9e"><uni-popup vue-id="252fe516-3" type="dialog" data-ref="fissionPopup" class="data-v-c764ac9e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('252fe516-4')+','+('252fe516-3')}}" mode="input" focus="false" title="分享给好友,对方使用后自己可再得一张" type="info" placeholder="输入好友手机号码" before-close="{{true}}" data-event-opts="{{[['^close',[['cancelFission']]],['^confirm',[['doFission']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-c764ac9e" bind:__l="__l"></uni-popup-dialog></uni-popup></view></block><block wx:if="{{detail.qrCode}}"><view class="give-popup data-v-c764ac9e"><uni-popup vue-id="252fe516-5" type="dialog" data-ref="givePopup" class="data-v-c764ac9e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('252fe516-6')+','+('252fe516-5')}}" mode="input" focus="false" title="转赠给好友" type="info" placeholder="输入好友手机号码" before-close="{{true}}" data-event-opts="{{[['^close',[['cancelGive']]],['^confirm',[['doGive']]]]}}" bind:close="__e" bind:confirm="__e" class="data-v-c764ac9e" bind:__l="__l"></uni-popup-dialog></uni-popup></view></block><block wx:if="{{!detail.code&&!detail.isReceive}}"><view class="footer-fixed data-v-c764ac9e"><view class="footer-container data-v-c764ac9e"><view class="foo-item-btn data-v-c764ac9e"><view class="btn-wrapper data-v-c764ac9e"><view data-event-opts="{{[['tap',[['receive',['$0'],['detail.id']]]]]}}" class="btn-item btn-item-main data-v-c764ac9e" bindtap="__e"><block wx:if="{{!detail.point||detail.point<1}}"><text class="data-v-c764ac9e">立即领取</text></block><block wx:if="{{detail.point&&detail.point>0}}"><text class="data-v-c764ac9e">立即兑换</text></block></view></view></view></view></view></block><block wx:if="{{detail.isReceive}}"><view class="footer-fixed data-v-c764ac9e"><view class="footer-container data-v-c764ac9e"><view class="foo-item-btn data-v-c764ac9e"><view class="btn-wrapper data-v-c764ac9e"><view class="btn-item btn-item-main state data-v-c764ac9e"><block wx:if="{{!detail.point||detail.point<1}}"><text class="data-v-c764ac9e">您已领取</text></block><block wx:if="{{detail.point&&detail.point>0}}"><text class="data-v-c764ac9e">您已兑换</text></block></view></view></view></view></view></block><view class="receive-pop data-v-c764ac9e"><uni-popup vue-id="252fe516-7" type="dialog" data-ref="receiveCodePopup" class="data-v-c764ac9e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-popup-dialog vue-id="{{('252fe516-8')+','+('252fe516-7')}}" mode="input" focus="false" title="领取码" type="info" placeholder="请输入领取码" before-close="{{true}}" value="{{receiveCode}}" data-event-opts="{{[['^close',[['cancelReceive']]],['^confirm',[['doReceive']]],['^input',[['__set_model',['','receiveCode','$event',[]]]]]]}}" bind:close="__e" bind:confirm="__e" bind:input="__e" class="data-v-c764ac9e" bind:__l="__l"></uni-popup-dialog></uni-popup></view></view></block>