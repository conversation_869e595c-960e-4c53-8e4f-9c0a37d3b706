{"pages": ["pages/index/index", "pages/category/index", "pages/cart/index", "pages/user/index", "pages/user/setting", "pages/user/code", "pages/user/card", "pages/user/password", "pages/user/level", "pages/custom/index", "pages/search/index", "pages/location/index", "pages/login/index", "pages/login/auth", "pages/help/index", "pages/coupon/list", "pages/coupon/detail", "pages/timer/detail", "pages/prestore/buy", "pages/prestore/detail", "pages/confirm/doConfirm", "pages/confirm/result", "pages/goods/list", "pages/goods/detail", "pages/my-coupon/index", "pages/address/index", "pages/address/create", "pages/address/update", "pages/points/detail", "pages/points/gift", "pages/give/index", "pages/wallet/index", "pages/wallet/recharge/index", "pages/wallet/recharge/order", "pages/wallet/balance/log", "pages/settlement/index", "pages/settlement/goods", "pages/order/index", "pages/order/detail", "pages/order/result", "pages/refund/index", "pages/refund/detail", "pages/refund/apply", "pages/pay/index", "pages/pay/result", "pages/merchant/index", "pages/pay/cashier", "pages/merchant/member/index", "pages/merchant/order/index", "pages/article/index", "pages/article/detail", "pages/coupon/receive", "pages/share/index", "pages/book/index", "pages/book/detail", "pages/book/submit", "pages/book/my", "pages/book/bookDetail"], "subPackages": [], "window": {"navigationBarBackgroundColor": "#ffffff", "navigationBarTitleText": "", "navigationBarTextStyle": "black", "backgroundTextStyle": "dark"}, "tabBar": {"color": "#333333", "selectedColor": "#333333", "borderStyle": "white", "backgroundColor": "#ffffff", "fontSize": "20rpx", "list": [{"pagePath": "pages/index/index", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-active.png", "text": "首页"}, {"pagePath": "pages/category/index", "iconPath": "static/tabbar/cart.png", "selectedIconPath": "static/tabbar/cart-active.png", "text": "点单"}, {"pagePath": "pages/order/index", "iconPath": "static/tabbar/shop.png", "selectedIconPath": "static/tabbar/shop-active.png", "text": "订单"}, {"pagePath": "pages/user/index", "iconPath": "static/tabbar/user.png", "selectedIconPath": "static/tabbar/user-active.png", "text": "我的"}]}, "requiredPrivateInfos": ["getLocation"], "permission": {"scope.userLocation": {"desc": "获取距离您最近的店铺位置"}}, "lazyCodeLoading": "requiredComponents", "usingComponents": {}}