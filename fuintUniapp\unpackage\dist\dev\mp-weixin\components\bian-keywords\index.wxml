<view class="{{['mark',show_key?'':'hidden']}}"><view class="kong"></view><view class="msg"><view data-event-opts="{{[['tap',[['closeFuc',['$event']]]]]}}" class="img iconfont icon-guanbi" bindtap="__e"></view><view class="title">{{''+(title?title:"请输入您的密码")}}</view><view hidden="{{!(show_subTitle&&price>0)}}" class="subTitle">{{'付款金额：'+price+''}}</view><view class="pswBox"><block wx:for="{{6}}" wx:for-item="item" wx:for-index="__i0__" wx:key="*this"><view class="content_item">{{password[item]?'●':''}}</view></block></view><block wx:if="{{false}}"><view data-event-opts="{{[['tap',[['forgetFuc',['$event']]]]]}}" class="forget" bindtap="__e">忘记密码？</view></block></view><view class="numeric"><block wx:for="{{num1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><block wx:if="{{mix}}"><view data-event-opts="{{[['tap',[['press',[['o',['num',item]]]]]]]}}" class="{{['num',item==10?'amend1':item==12?'amend3 iconfont icon-backspace':'']}}" bindtap="__e">{{''+(item==10?'':item==11?'0':item==12?'':item)+''}}</view></block><block wx:else><block wx:for="{{num}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['press',[['o',['num',item]]]]]]]}}" class="{{['num',item==10?'amend1':item==12?'amend3 iconfont icon-backspace':'']}}" bindtap="__e">{{''+(item==10?'':item==11?'0':item==12?'':item)+''}}</view></block></block></block></view></view>