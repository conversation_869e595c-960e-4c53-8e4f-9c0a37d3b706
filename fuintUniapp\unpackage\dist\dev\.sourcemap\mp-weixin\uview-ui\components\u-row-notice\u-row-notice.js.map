{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-row-notice/u-row-notice.vue?0c53", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-row-notice/u-row-notice.vue?08b1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-row-notice/u-row-notice.vue?83c7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-row-notice/u-row-notice.vue?cf5a", "uni-app:///uview-ui/components/u-row-notice/u-row-notice.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-row-notice/u-row-notice.vue?3a44", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/uview-ui/components/u-row-notice/u-row-notice.vue?33ca"], "names": ["props", "list", "type", "default", "volumeIcon", "moreIcon", "closeIcon", "autoplay", "color", "bgColor", "show", "fontSize", "volumeSize", "speed", "playState", "padding", "data", "textWidth", "boxWidth", "animationDuration", "animationPlayState", "showText", "watch", "immediate", "handler", "computed", "computeColor", "textStyle", "style", "computeBgColor", "mounted", "methods", "initSize", "uni", "in", "select", "boundingClientRect", "exec", "resolve", "query", "Promise", "setTimeout", "click", "close", "getMore"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,+oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBCqCprB;EACAA;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACArB;MACAsB;MACAC;QAAA;QACA;QACA;UACA;QACA;MACA;IACA;IACAV;MACA,4DACA;IACA;IACAD;MACA;IACA;EACA;EACAY;IACA;IACAC;MACA;MACA;MAAA,KACA,+CACA;IACA;IACA;IACAC;MACA;MACA,8CACA;MACAC;MACA;IACA;IACA;IACAC;MACA,2CACA;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACAd;QACAD;MACA;QACAgB,0BACAC,WACAC,4BACAC,qBACAC;UACA;UACAC;QACA;MACA;MACAC;MACAC;QACA;QACA;QACA;QACA;QACA;QACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC/MA;AAAA;AAAA;AAAA;AAA+wC,CAAgB,0qCAAG,EAAC,C;;;;;;;;;;;ACAnyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uview-ui/components/u-row-notice/u-row-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-row-notice.vue?vue&type=template&id=d36ba0c0&scoped=true&\"\nvar renderjs\nimport script from \"./u-row-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./u-row-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-row-notice.vue?vue&type=style&index=0&id=d36ba0c0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d36ba0c0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uview-ui/components/u-row-notice/u-row-notice.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-row-notice.vue?vue&type=template&id=d36ba0c0&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show ? _vm.__get_style([_vm.textStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-row-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-row-notice.vue?vue&type=script&lang=js&\"", "<template>\n    <view\n        v-if=\"show\"\n        class=\"u-notice-bar\"\n        :style=\"{\n            background: computeBgColor,\n            padding: padding\n        }\"\n        :class=\"[\n            type ? `u-type-${type}-light-bg` : ''\n        ]\"\n    >\n        <view class=\"u-direction-row\">\n            <view class=\"u-icon-wrap\">\n                <u-icon class=\"u-left-icon\" v-if=\"volumeIcon\" name=\"volume-fill\" :size=\"volumeSize\" :color=\"computeColor\"></u-icon>\n            </view>\n            <view class=\"u-notice-box\" id=\"u-notice-box\">\n                <view\n                    class=\"u-notice-content\"\n                    id=\"u-notice-content\"\n                    :style=\"{\n                        animationDuration: animationDuration,\n                        animationPlayState: animationPlayState,\n                    }\"\n                >\n                    <text class=\"u-notice-text\" @tap=\"click\" :style=\"[textStyle]\"\n                    :class=\"['u-type-' + type]\">{{showText}}</text>\n                </view>\n            </view>\n            <view class=\"u-icon-wrap\">\n                <u-icon @click=\"getMore\" class=\"u-right-icon\" v-if=\"moreIcon\" name=\"arrow-right\" :size=\"26\" :color=\"computeColor\"></u-icon>\n                <u-icon @click=\"close\" class=\"u-right-icon\" v-if=\"closeIcon\" name=\"close\" :size=\"24\" :color=\"computeColor\"></u-icon>\n            </view>\n        </view>\n    </view>\n</template>\n<script>\nexport default {\n    props: {\n        // 显示的内容，数组\n        list: {\n            type: Array,\n            default() {\n                return [];\n            }\n        },\n        // 显示的主题，success|error|primary|info|warning|none\n        // none主题默认为透明背景，黑色(contentColor)字体\n        type: {\n            type: String,\n            default: 'warning'\n        },\n        // 是否显示左侧的音量图标\n        volumeIcon: {\n            type: Boolean,\n            default: true\n        },\n        // 是否显示右侧的右箭头图标\n        moreIcon: {\n            type: Boolean,\n            default: false\n        },\n        // 是否显示右侧的关闭图标\n        closeIcon: {\n            type: Boolean,\n            default: false\n        },\n        // 是否自动播放\n        autoplay: {\n            type: Boolean,\n            default: true\n        },\n        // 文字颜色，各图标也会使用文字颜色\n        color: {\n            type: String,\n            default: ''\n        },\n        // 背景颜色\n        bgColor: {\n            type: String,\n            default: ''\n        },\n        // 是否显示\n        show: {\n            type: Boolean,\n            default: true\n        },\n        // 字体大小，单位rpx\n        fontSize: {\n            type: [Number, String],\n            default: 26\n        },\n        // 音量喇叭的大小\n        volumeSize: {\n            type: [Number, String],\n            default: 34\n        },\n        // 水平滚动时的滚动速度，即每秒滚动多少rpx，这有利于控制文字无论多少时，都能有一个恒定的速度\n        speed: {\n            type: [Number, String],\n            default: 160\n        },\n        // 播放状态，play-播放，paused-暂停\n        playState: {\n            type: String,\n            default: 'play'\n        },\n        // 通知的边距\n        padding: {\n            type: [Number, String],\n            default: '18rpx 24rpx'\n        }\n    },\n    data() {\n        return {\n            textWidth: 0, // 滚动的文字宽度\n            boxWidth: 0, // 供文字滚动的父盒子的宽度，和前者一起为了计算滚动速度\n            animationDuration: '10s', // 动画执行时间\n            animationPlayState: 'paused', // 动画的开始和结束执行\n            showText: '' // 显示的文本\n        };\n    },\n    watch: {\n        list: {\n            immediate: true,\n            handler(val) {\n                this.showText = val.join('，');\n                this.$nextTick(() => {\n                    this.initSize();\n                });\n            }\n        },\n        playState(val) {\n            if(val == 'play') this.animationPlayState = 'running';\n            else this.animationPlayState = 'paused';\n        },\n        speed(val) {\n            this.initSize();\n        }\n    },\n    computed: {\n        // 计算字体颜色，如果没有自定义的，就用uview主题颜色\n        computeColor() {\n            if (this.color) return this.color;\n            // 如果是无主题，就默认使用content-color\n            else if(this.type == 'none') return '#606266';\n            else return this.type;\n        },\n        // 文字内容的样式\n        textStyle() {\n            let style = {};\n            if (this.color) style.color = this.color;\n            else if(this.type == 'none') style.color = '#606266';\n            style.fontSize = this.fontSize + 'rpx';\n            return style;\n        },\n        // 计算背景颜色\n        computeBgColor() {\n            if (this.bgColor) return this.bgColor;\n            else if(this.type == 'none') return 'transparent';\n        }\n    },\n    mounted() {\n        this.$nextTick(() => {\n            this.initSize();\n        });\n    },\n    methods: {\n        initSize() {\n            let query = [],\n                boxWidth = 0,\n                textWidth = 0;\n            let textQuery = new Promise((resolve, reject) => {\n                uni.createSelectorQuery()\n                    .in(this)\n                    .select(`#u-notice-content`)\n                    .boundingClientRect()\n                    .exec(ret => {\n                        this.textWidth = ret[0].width;\n                        resolve();\n                    });\n            });\n            query.push(textQuery);\n            Promise.all(query).then(() => {\n                // 根据t=s/v(时间=路程/速度)，这里为何不需要加上#u-notice-box的宽度，因为中设置了.u-notice-content样式中设置了padding-left: 100%\n                // 恰巧计算出来的结果中已经包含了#u-notice-box的宽度\n                this.animationDuration = `${this.textWidth / uni.upx2px(this.speed)}s`;\n                // 这里必须这样开始动画，否则在APP上动画速度不会改变(HX版本2.4.6，IOS13)\n                this.animationPlayState = 'paused';\n                setTimeout(() => {\n                    if(this.playState == 'play' && this.autoplay) this.animationPlayState = 'running';\n                }, 10);\n            });\n        },\n        // 点击通告栏\n        click(index) {\n            this.$emit('click');\n        },\n        // 点击关闭按钮\n        close() {\n            this.$emit('close');\n        },\n        // 点击更多箭头按钮\n        getMore() {\n            this.$emit('getMore');\n        }\n    }\n};\n</script>\n\n<style lang=\"scss\" scoped>\n@import \"../../libs/css/style.components.scss\";\n    \n.u-notice-bar {\n    padding: 18rpx 24rpx;\n    overflow: hidden;\n}\n\n.u-direction-row {\n    @include vue-flex;\n    align-items: center;\n    justify-content: space-between;\n}\n\n.u-left-icon {\n    /* #ifndef APP-NVUE */\n    display: inline-flex;\n    /* #endif */\n    align-items: center;\n}\n\n.u-notice-box {\n    flex: 1;\n    @include vue-flex;\n    overflow: hidden;\n    margin-left: 12rpx;\n}\n\n.u-right-icon {\n    margin-left: 12rpx;\n    display: inline-flex;\n    align-items: center;\n}\n\n.u-notice-content {\n    animation: u-loop-animation 10s linear infinite both;\n    text-align: right;\n    // 这一句很重要，为了能让滚动左右连接起来\n    padding-left: 100%;\n    @include vue-flex;\n    flex-wrap: nowrap;\n}\n\n.u-notice-text {\n    font-size: 26rpx;\n    word-break: keep-all;\n    white-space: nowrap\n}\n\n@keyframes u-loop-animation {\n    0% {\n        transform: translate3d(0, 0, 0);\n    }\n\n    100% {\n        transform: translate3d(-100%, 0, 0);\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-row-notice.vue?vue&type=style&index=0&id=d36ba0c0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-row-notice.vue?vue&type=style&index=0&id=d36ba0c0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426964\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}