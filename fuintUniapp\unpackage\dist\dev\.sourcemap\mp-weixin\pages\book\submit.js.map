{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/submit.vue?6019", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/submit.vue?eeaa", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/submit.vue?0d61", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/submit.vue?a37a", "uni-app:///pages/book/submit.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/submit.vue?47a3", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/book/submit.vue?fedf"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "isLoading", "bookId", "bookInfo", "disabled", "form", "contact", "mobile", "remark", "bookData", "onLoad", "onShow", "methods", "getBookDetail", "app", "BookApi", "then", "handleSubmit", "date", "time", "setTimeout", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACgD/pB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QAAAC;QAAAC;QAAAC;MAAA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA;IACA;EACA;EACAC;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC;MACAC,2BACAC;QACAF;QACAA;MACA;IACA;IAEA;IACAG;MACA;MACA;QACAH;QACA;MACA;MACA;MACA;QAAAZ;QACAM;QACAD;QACAD;QACAY;QACAC;MAAA;MACAJ,sBACAC;QACA;UACAF;UACAM;YACAN;YACAO;UACA;QACA;UACA;YACAP;UACA;YACAA;UACA;UACAA;UACA;QACA;MACA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAA8uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAlwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/book/submit.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/book/submit.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./submit.vue?vue&type=template&id=14ba4f15&scoped=true&\"\nvar renderjs\nimport script from \"./submit.vue?vue&type=script&lang=js&\"\nexport * from \"./submit.vue?vue&type=script&lang=js&\"\nimport style0 from \"./submit.vue?vue&type=style&index=0&id=14ba4f15&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"14ba4f15\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/book/submit.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./submit.vue?vue&type=template&id=14ba4f15&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./submit.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./submit.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"!isLoading\" class=\"container\">\n\n    <!-- 预约时间 -->\n    <view class=\"row-service b-f m-top20\">\n      <view class=\"row-title\">预约时间</view>\n      <view class=\"service-switch dis-flex\">\n        <view class=\"switch-item active\">{{ bookData.date }} {{ bookData.week }} {{ bookData.time }}\r\n        </view>\n      </view>\n    </view>\r\n    \r\n    <!-- 称呼 -->\r\n    <view class=\"row-input b-f m-top20 dis-flex\">\r\n      <view class=\"row-title\">联系人名：</view>\r\n      <view class=\"mo ney col-m\">\r\n          <input class=\"weui-input value\" type=\"text\" v-model=\"form.contact\" placeholder=\"请输入联系人姓名\"/>\r\n      </view>\r\n    </view>\r\n    \r\n    <!-- 电话 -->\r\n    <view class=\"row-input b-f m-top20 dis-flex\">\r\n      <view class=\"row-title\">联系电话：</view>\r\n      <view class=\"money col-m\">\r\n          <input class=\"weui-input value\" type=\"text\" v-model=\"form.mobile\" placeholder=\"请输入联系人电话\"/>\r\n      </view>\r\n    </view>\n\n    <!-- 备注信息 -->\n    <view class=\"row-textarea b-f m-top20\">\n      <view class=\"row-title\">备注信息：</view>\n      <view class=\"content\">\n        <textarea class=\"textarea\" v-model=\"form.remark\" maxlength=\"2000\" placeholder=\"请填写备注信息\"\n          placeholderStyle=\"color:#ccc\"></textarea>\n      </view>\n    </view>\n\n    <!-- 底部操作按钮 -->\n    <view class=\"footer-fixed\">\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" :class=\"{ disabled }\" @click=\"handleSubmit()\">确认提交</view>\n      </view>\n    </view>\n\n  </view>\n</template>\n\n<script>\n  import * as BookApi from '@/api/book'\n  export default {\n    data() {\n      return {\n        // 正在加载\n        isLoading: true,\n        // 预约项目id\n        bookId: null,\n        // 预约详情\n        bookInfo: {},\n        // 按钮禁用\n        disabled: false,\r\n        // 表单数据\r\n        form: { contact: '', mobile: '', remark: '' },\r\n        bookData: null\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad({ bookId }) {\n      this.bookId = bookId\n      // 获取预约项目详情\n      this.getBookDetail()\n    },\r\n    onShow() {\r\n       this.bookData = uni.getStorageSync('bookData');\r\n    },\n\n    methods: {\n\n      // 获取预约详情\n      getBookDetail() {\n        const app = this\n        app.isLoading = true\n        BookApi.detail(app.bookId)\n          .then(result => {\n            app.bookInfo = result.bookInfo;\n            app.isLoading = false;\n          })\n      },\n\n      // 提交预约信息\n      handleSubmit() {\n        const app = this;\r\n        if (app.form.mobile.length < 6 || app.form.contact.length < 1) {\r\n            app.$toast(\"请先提交联系人和联系电话！\");\r\n            return false;\r\n        }\r\n        if (app.disabled === true) return false;\r\n        const param = { bookId: app.bookData.bookId,\r\n                        remark: app.form.remark,\r\n                        mobile: app.form.mobile,\r\n                        contact: app.form.contact,\r\n                        date: app.bookData.date,\r\n                        time: app.bookData.time };\n        BookApi.submit(param)\n           .then(result => {\r\n                if (result.code == '200') {\r\n                    app.$toast('提交预约成功，请等待确认！')\r\n                    setTimeout(() => {\r\n                      app.disabled = false;\r\n                      uni.navigateBack();\r\n                    }, 3000)\r\n                } else {\r\n                    if (result.message) {\r\n                        app.$error(result.message);\r\n                    } else {\r\n                        app.$error('预约提交失败');\r\n                    }\r\n                    app.disabled = false;\r\n                    return false;\r\n                }\n            }).catch(err => app.disabled = false)\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    padding: 20rpx 20rpx 100rpx 20rpx;\n  }\n\n  .row-title {\n    color: #333;\r\n    font-weight: bold;\r\n    line-height: 60rpx;\n  }\n\n  /* 服务类型 */\n  .row-service {\n    padding: 24rpx 20rpx;\r\n    border-radius: 20rpx;\n  }\n\n  .service-switch {\n    .switch-item {\n      padding: 6rpx 30rpx;\n      margin-right: 25rpx;\n      border-radius: 10rpx;\n      color: #f9211c;\n      border: 1px solid #f9211c;\n    }\n  }\n\n  /* 备注信息 */\n  .row-textarea {\r\n    margin: 20rpx auto;\n    border-radius: 20rpx;\r\n    border: solid 1rpx #f5f5f5;\r\n    height: 100%;\r\n    display: block;\r\n    padding: 10rpx 10rpx 80rpx 10rpx;\n    .textarea {\n      min-height: 260rpx;\r\n      width: 100%;\n      padding: 12rpx;\n      border: 1rpx solid #ccc;\n      border-radius: 12rpx;\n      box-sizing: border-box;\n      font-size: 26rpx;\r\n      margin: 0 auto;\n    }\n  }\n\n  /* 表单项 */\n  .row-input {\n    padding: 24rpx 20rpx;\r\n    border-radius: 20rpx;\r\n    border: solid 1rpx #f5f5f5;\n    .row-title {\n      margin-bottom: 0;\n      margin-right: 20rpx;\n    }\r\n    .value {\r\n        color: #333;\r\n        padding-top: 10rpx;\r\n    }\n  }\n\n  // 底部操作栏\n  .footer-fixed {\n    position: fixed;\n    bottom: var(--window-bottom);\n    left: 0;\n    right: 0;\n    height: 180rpx;\r\n    padding-bottom: 30rpx;\n    z-index: 11;\n    box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);\n    background: #fff;\n\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      padding: 0 20rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n      // 禁用按钮\n      &.disabled {\n        background: #ff9779;\n      }\n    }\n\n  }\n</style>", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./submit.vue?vue&type=style&index=0&id=14ba4f15&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./submit.vue?vue&type=style&index=0&id=14ba4f15&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420751\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}