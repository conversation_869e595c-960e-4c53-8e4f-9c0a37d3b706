{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/components/Popup.vue?d124", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/components/Popup.vue?d33c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/components/Popup.vue?ff24", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/prestore/components/Popup.vue?3eab", "uni-app:///pages/prestore/components/Popup.vue"], "names": ["components", "CouponPopup", "props", "value", "Type", "default", "couponInfo", "type", "storeRule", "data", "created", "methods", "onChangeValue", "findCouponInfo", "resolve", "openPopup", "closePopup"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;;;AAGpD;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACM7qB;AACA;AAAA;AAAA;;;;;;;;;;;eAGA;EACAA;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAC;MACAF;IACA;IACA;IACAG;MACAD;MACAF;IACA;EACA;EAEAI;IACA;EACA;EAEAC;IACA;EAAA,CACA;EAEAC;IAEA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;QACAC;MACA;IACA;IAEA;IACAC;MACA;IAAA,CACA;IAEAC;MACA;IAAA;EAEA;AACA;AAAA,2B", "file": "pages/prestore/components/Popup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./Popup.vue?vue&type=template&id=aeaf9c0a&scoped=true&\"\nvar renderjs\nimport script from \"./Popup.vue?vue&type=script&lang=js&\"\nexport * from \"./Popup.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"aeaf9c0a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/prestore/components/Popup.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Popup.vue?vue&type=template&id=aeaf9c0a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Popup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./Popup.vue?vue&type=script&lang=js&\"", "<template>\n  <coupon-popup :value=\"value\" @input=\"onChangeValue\" :couponInfo=\"couponInfo\" :storeRule=\"storeRule\" border-radius=\"20\" :maskCloseAble=\"true\"\n    @open=\"openPopup\" @close=\"closePopup\"/>\n</template>\n\n<script>\n  import { setCartTotalNum } from '@/utils/app'\n  import * as CartApi from '@/api/cart'\n  import CouponPopup from '@/components/prestore-popup'\n\n  export default {\n    components: {\n      CouponPopup\n    },\n    props: {\n      // true 组件显示 false 组件隐藏\n      value: {\n        Type: Boolean,\n        default: false\n      },\n      // 卡券详情信息\n      couponInfo: {\n        type: Object,\n        default: {}\n      },\n      // 预存规则\n      storeRule: {\n        type: Array,\n        default: []\n      }\n    },\n\n    data() {\n      return {}\n    },\n\n    created() {\n      // empty\n    },\n\n    methods: {\n\n      // 监听组件显示隐藏\n      onChangeValue(val) {\n        this.$emit('input', val)\n      },\n\n      /**\n       * 获取卡券信息\n       */\n      findCouponInfo() {\n        return new Promise((resolve, reject) => {\n          resolve(couponInfo)\n        })\n      },\n\n      // sku组件 开始-----------------------------------------------------------\n      openPopup() {\n        // console.log(\"监听 - 打开sku组件\")\n      },\n\n      closePopup() {\n        // console.log(\"监听 - 关闭sku组件\")\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n\n</style>\n"], "sourceRoot": ""}