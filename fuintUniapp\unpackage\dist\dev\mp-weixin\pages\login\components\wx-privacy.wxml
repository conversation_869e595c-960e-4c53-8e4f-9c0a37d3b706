<view class="privacy data-v-ee2dccb4"><u-popup bind:input="__e" vue-id="d78ac7c0-1" id="privacy" mode="bottom" border-radius="{{26}}" closeable="{{true}}" value="{{showPrivacy}}" data-event-opts="{{[['^input',[['__set_model',['','showPrivacy','$event',[]]]]]]}}" class="data-v-ee2dccb4" bind:__l="__l" vue-slots="{{['default']}}"><view class="ws-privacy-popup data-v-ee2dccb4"><view class="ws-privacy-popup__header data-v-ee2dccb4"><view class="ws-picker__title data-v-ee2dccb4">{{title}}</view></view><scroll-view class="content-scroll data-v-ee2dccb4" scroll-y="{{true}}"><view class="ws-privacy-popup__container data-v-ee2dccb4"><text class="data-v-ee2dccb4">{{desc}}</text><text class="data-v-ee2dccb4">{{subDesc}}</text></view></scroll-view><view class="ws-privacy-popup__footer data-v-ee2dccb4"><button class="is-agree data-v-ee2dccb4" id="agree-btn" open-type="agreePrivacyAuthorization" data-event-opts="{{[['agreeprivacyauthorization',[['handleAgree',['$event']]]]]}}" bindagreeprivacyauthorization="__e">{{''+agreeText+''}}</button><button class="is-disagree data-v-ee2dccb4" id="disagree-btn" data-event-opts="{{[['tap',[['handleDisagree',['$event']]]]]}}" bindtap="__e">{{''+disagreeText+''}}</button></view></view></u-popup></view>