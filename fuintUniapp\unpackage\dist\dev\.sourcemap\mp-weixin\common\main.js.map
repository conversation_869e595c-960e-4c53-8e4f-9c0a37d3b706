{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/App.vue?ad7e", "uni-app:///App.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/App.vue?b5d2", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/App.vue?9fc9", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/App.vue?81fa", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/App.vue?5273"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "config", "productionTip", "App", "mpType", "prototype", "$platform", "getPlatform", "use", "uView", "$toast", "showToast", "$success", "showSuccess", "$error", "showError", "$navTo", "navTo", "$getShareUrlParams", "getShareUrlParams", "app", "store", "created", "bootstrap", "$mount", "globalData", "onLaunch", "methods", "updateManager", "uni", "title", "content", "showCancel", "success"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AACA;AACA;AACA;AAQA;AAA0B;AAAA;AAd1B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAe1DC,YAAG,CAACC,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCC,YAAG,CAACC,MAAM,GAAG,KAAK;;AAElB;AACAJ,YAAG,CAACK,SAAS,CAACC,SAAS,GAAG,IAAAC,gBAAW,GAAE;;AAEvC;AACAP,YAAG,CAACQ,GAAG,CAACC,gBAAK,CAAC;;AAEd;AACAT,YAAG,CAACK,SAAS,CAACK,MAAM,GAAGC,cAAS;AAChCX,YAAG,CAACK,SAAS,CAACO,QAAQ,GAAGC,gBAAW;AACpCb,YAAG,CAACK,SAAS,CAACS,MAAM,GAAGC,cAAS;AAChCf,YAAG,CAACK,SAAS,CAACW,MAAM,GAAGC,UAAK;AAC5BjB,YAAG,CAACK,SAAS,CAACa,kBAAkB,GAAGC,sBAAiB;;AAEpD;AACA,IAAMC,GAAG,GAAG,IAAIpB,YAAG,iCACdG,YAAG;EACNkB,KAAK,EAALA,cAAK;EACLC,OAAO,EAAEC;AAAS,GAClB;AACF,UAAAH,GAAG,EAACI,MAAM,EAAE,C;;;;;;;;;;;;;ACxCZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;AACD;;;AAG/D;AACqK;AACrK,gBAAgB,kLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAA0mB,CAAgB,soBAAG,EAAC,C;;;;;;;;;;;;;;;;;;eCC9nB;EAEA;AACA;AACA;EACAC,aAEA;EAEA;AACA;AACA;EACAC;IACA;IACA;EACA;EAEAC;IAEA;AACA;AACA;IACAC;MACA;MACAA;QACA;QACA;MAAA,CACA;MACAA;QACAC;UACAC;UACAC;UACAC;UACAC;YACA;cACA;cACAL;YACA;UACA;QACA;MACA;MACAA;QACA;QACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAA6pC,CAAgB,yoCAAG,EAAC,C;;;;;;;;;;;ACAjrC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAi4B,CAAgB,q4BAAG,EAAC,C;;;;;;;;;;;ACAr5B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\nimport App from './App'\nimport store from './store'\nimport uView from 'uview-ui'\nimport bootstrap from './core/bootstrap'\nimport {\n  getPlatform,\n  navTo,\n  showToast,\n  showSuccess,\n  showError,\n  getShareUrlParams\n} from './utils/app'\nimport './core/ican-H5Api'\n\nVue.config.productionTip = false\n\nApp.mpType = 'app'\n\n// 当前运行的终端\nVue.prototype.$platform = getPlatform()\n\n// 载入uView库\nVue.use(uView)\n\n// 挂载全局函数\nVue.prototype.$toast = showToast\nVue.prototype.$success = showSuccess\nVue.prototype.$error = showError\nVue.prototype.$navTo = navTo\nVue.prototype.$getShareUrlParams = getShareUrlParams\n\n// 实例化应用\nconst app = new Vue({\n  ...App,\n  store,\n  created: bootstrap\n})\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./App.vue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n  export default {\r\n\r\n    /**\r\n     * 全局变量\r\n     */\r\n    globalData: {\r\n\r\n    },\r\n\r\n    /**\r\n     * 初始化完成时触发\r\n     */\r\n    onLaunch() {\r\n      // 小程序主动更新\r\n      this.updateManager()\r\n    },\r\n\r\n    methods: {\r\n\r\n      /**\r\n       * 小程序主动更新\r\n       */\r\n      updateManager() {\r\n        const updateManager = uni.getUpdateManager();\r\n        updateManager.onCheckForUpdate(res => {\r\n          // 请求完新版本信息的回调\r\n          // console.log(res.hasUpdate)\r\n        })\r\n        updateManager.onUpdateReady(() => {\r\n          uni.showModal({\r\n            title: '更新提示',\r\n            content: '新版本已经准备好，即将重启应用',\r\n            showCancel: false,\r\n            success(res) {\r\n              if (res.confirm) {\r\n                // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启\r\n                updateManager.applyUpdate()\r\n              }\r\n            }\r\n          })\r\n        })\r\n        updateManager.onUpdateFailed(() => {\r\n          // 新的版本下载失败\r\n          uni.showModal({\r\n            title: '更新提示',\r\n            content: '新版本下载失败',\r\n            showCancel: false\r\n          })\r\n        })\r\n      }\r\n    }\r\n\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  /* 引入uView库样式 */\r\n  @import \"uview-ui/index.scss\";\r\n</style>\r\n\r\n<style>\r\n  /* 项目基础样式 */\r\n  @import \"./app.scss\";\r\n</style>\r\n", "import mod from \"-!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424968\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./App.vue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891421069\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}