{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/banner/index.vue?83f2", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/banner/index.vue?9de7", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/banner/index.vue?4fc0", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/banner/index.vue?d36e", "uni-app:///components/page/banner/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/banner/index.vue?d075", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/components/page/banner/index.vue?90ea"], "names": ["name", "props", "itemIndex", "itemStyle", "params", "dataList", "mixins", "data", "width", "indicatorDots", "autoplay", "duration", "imgHeights", "imgCurrent", "methods", "onLink", "_imagesHeight", "imgheight", "ratio", "_bindChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmB7qB;;;;;;;;;;;;;;;;;;;eAEA;EACAA;EAEA;AACA;AACA;AACA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EAEAC;EAEA;AACA;AACA;AACA;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EAEA;AACA;AACA;AACA;EACAC;IACAC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;MACA;QACAC;QACA;QACAC;MACA;MACA;MACA;MACA;MACAN;MACA;IACA;IAEA;AACA;AACA;IACAO;MACA;IACA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;ACtFA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/page/banner/index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2c2596f8&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2c2596f8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2c2596f8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/page/banner/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2c2596f8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"diy-banner\" :style=\"{ height: `${imgHeights[imgCurrent]}rpx` }\">\n    <!-- 图片轮播 -->\n    <swiper :autoplay=\"autoplay\" class=\"swiper-box\" :duration=\"duration\" :circular=\"true\" :interval=\"itemStyle.interval * 1000\"\n      @change=\"_bindChange\">\n      <swiper-item v-for=\"(dataItem, index) in dataList\" :key=\"index\">\n        <image lazy-load :lazy-load-margin=\"0\" class=\"slide-image\" :src=\"dataItem.image\" @click=\"onLink(dataItem.url)\"\n          @load=\"_imagesHeight\" />\n      </swiper-item>\n    </swiper>\n    <!-- 指示点 -->\n    <view class=\"indicator-dots\" :class=\"itemStyle.btnShape\">\n      <view class=\"dots-item\" :class=\"{ active: imgCurrent == index }\" :style=\"{ backgroundColor: itemStyle.btnColor }\"\n        v-for=\"(dataItem, index) in dataList\" :key=\"index\"></view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import mixin from '../mixin'\n\n  export default {\n    name: \"Banner\",\n\n    /**\n     * 组件的属性列表\n     * 用于组件自定义设置\n     */\n    props: {\n      itemIndex: String,\n      itemStyle: Object,\n      params: Object,\n      dataList: Array\n    },\n\n    mixins: [mixin],\n\n    /**\n     * 私有数据,组件的初始数据\n     * 可用于模版渲染\n     */\n    data() {\n      return {\r\n        width: 730,\n        indicatorDots: false, // 是否显示面板指示点\n        autoplay: true, // 是否自动切换\n        duration: 800, // 滑动动画时长\n        imgHeights: [], // 图片的高度\n        imgCurrent: 0, // 当前banne所在滑块指针\n      }\n    },\n\n    /**\n     * 组件的方法列表\n     * 更新属性和数据的方法与更新页面数据的方法类似\n     */\n    methods: {\n        onLink(linkObj) {\n            this.$navTo(linkObj)\n        },\n      /**\n       * 计算图片高度\n       */\n      _imagesHeight(e) {\n        // 获取图片真实宽度\n        const imgwidth = e.detail.width,\n          imgheight = e.detail.height,\n          // 宽高比\n          ratio = imgwidth / imgheight;\n        // 计算的高度值\n        const viewHeight = this.width / ratio;\n        const imgHeights = this.imgHeights;\n        // 把每一张图片的高度记录到数组里\n        imgHeights.push(viewHeight);\n        this.imgHeights = imgHeights;\n      },\n\n      /**\n       * 记录当前指针\n       */\n      _bindChange(e) {\n        this.imgCurrent = e.detail.current\n      }\n\n    }\n\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .diy-banner {\n    position: relative;\r\n    max-height: 400rpx;\r\n    margin-top: 100rpx;\r\n    /* #ifdef H5 */\r\n    margin-top: 120rpx;\r\n    /* #endif */\n    // swiper组件\n    .swiper-box {\n      height: 100%;\n      padding: 0rpx 10rpx 0rpx 10rpx;\r\n      max-width: 750rpx;\r\n      max-height: 450rpx;\n      .slide-image {\n        width: 100%;\n        height: 100%;\n        margin: 0 auto;\n        display: block;\r\n        border-radius: 10rpx;\n      }\n    }\n    \n    /* 指示点 */\n    .indicator-dots {\n      width: 100%;\n      height: 28rpx;\n      padding: 0 20rpx;\n      position: absolute;\n      left: 0;\n      right: 0;\n      bottom: 20rpx;\n      opacity: 0.8;\n      display: flex;\n      justify-content: center;\n\n      .dots-item {\n        width: 16rpx;\n        height: 16rpx;\n        margin-right: 8rpx;\n        background-color: #fff;\n\n        &:last-child {\n          margin-right: 0;\n        }\n\n        &.active {\n          background-color: #313131 !important;\n        }\n      }\n\n      // 圆形\n      &.round .dots-item {\n        width: 16rpx;\n        height: 16rpx;\n        border-radius: 20rpx;\n      }\n\n      // 正方形\n      &.square .dots-item {\n        width: 16rpx;\n        height: 16rpx;\n      }\n\n      // 长方形\n      &.rectangle .dots-item {\n        width: 22rpx;\n        height: 14rpx;\n      }\n\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2c2596f8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=2c2596f8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891426768\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}