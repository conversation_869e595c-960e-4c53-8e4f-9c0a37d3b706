<block wx:if="{{isShow}}"><view class="u-notice-bar-wrap data-v-087a7280" style="{{'border-radius:'+(borderRadius+'rpx')+';'}}"><block wx:if="{{mode=='horizontal'&&isCircular}}"><block class="data-v-087a7280"><u-row-notice vue-id="06d45e86-1" type="{{type}}" color="{{color}}" bgColor="{{bgColor}}" list="{{list}}" volumeIcon="{{volumeIcon}}" moreIcon="{{moreIcon}}" volumeSize="{{volumeSize}}" closeIcon="{{closeIcon}}" mode="{{mode}}" fontSize="{{fontSize}}" speed="{{speed}}" playState="{{playState}}" padding="{{padding}}" data-event-opts="{{[['^getMore',[['getMore']]],['^close',[['close']]],['^click',[['click']]]]}}" bind:getMore="__e" bind:close="__e" bind:click="__e" class="data-v-087a7280" bind:__l="__l"></u-row-notice></block></block><block wx:if="{{mode=='vertical'||mode=='horizontal'&&!isCircular}}"><block class="data-v-087a7280"><u-column-notice vue-id="06d45e86-2" type="{{type}}" color="{{color}}" bgColor="{{bgColor}}" list="{{list}}" volumeIcon="{{volumeIcon}}" moreIcon="{{moreIcon}}" closeIcon="{{closeIcon}}" mode="{{mode}}" volumeSize="{{volumeSize}}" disable-touch="{{disableTouch}}" fontSize="{{fontSize}}" duration="{{duration}}" playState="{{playState}}" padding="{{padding}}" data-event-opts="{{[['^getMore',[['getMore']]],['^close',[['close']]],['^click',[['click']]],['^end',[['end']]]]}}" bind:getMore="__e" bind:close="__e" bind:click="__e" bind:end="__e" class="data-v-087a7280" bind:__l="__l"></u-column-notice></block></block></view></block>