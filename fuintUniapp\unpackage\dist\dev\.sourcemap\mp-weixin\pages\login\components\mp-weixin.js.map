{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/mp-weixin.vue?c766", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/mp-weixin.vue?7533", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/mp-weixin.vue?61c8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/mp-weixin.vue?53cb", "uni-app:///pages/login/components/mp-weixin.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/mp-weixin.vue?d8b1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/login/components/mp-weixin.vue?9010"], "names": ["components", "WxPrivacy", "data", "code", "needPhone", "isProfile", "isOpenPrivacy", "agreePrivacy", "protocolTitle", "protocolSubDesc", "ProtocolEnum", "SettingKeyEnum", "created", "methods", "privacyChange", "openPrivacy", "handleDisagree", "handleAgree", "getCode", "uni", "provider", "success", "console", "resolve", "fail", "getUserSetting", "UserApi", "then", "app", "catch", "getUserProfile", "wx", "lang", "desc", "userInfo", "getPhoneNumber", "onAuthSuccess", "store", "shareId", "setTimeout", "onEmitSuccess", "o<PERSON>h", "$emit", "cancelLogin", "onNavigateBack", "delta"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,4oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACkCjrB;AACA;AAEA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAA;IACAC;EACA;EACAC;IACA;MACA;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;;IAEA;IACA;EACA;EAEAC;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IAEA;IACA;IACAC;MACA;QACAC;UACAC;UACAC;YACAC;YACAC;UACA;UACAC;QACA;MACA;IACA;IAEAC;MACA;MACA;QACAC,kBACAC;UACAC;QACA,GACAC;UACA;QAAA,CACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAF;QACA;MACA;MACAG;QACAC;QACAC;QACAZ;UAAA;UACAC;UACA;YACAM;UACA;UACA;UACAM;UACAN;QACA;QACAJ;UACAF;UACAM;QACA;MACA;IACA;IAEA;IACAO;MACA;QACA;UAAA;UAAA;UAAA;UAAA;QAAA;MACA;IACA;IAEA;IACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR,aACA;gBAAA,cACAS;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAAA;gBAAA;kBAAAlC;kBAAAmC;kBAAAJ;gBAAA;gBAAA,iEACAP;kBACA;oBACA;oBACAC;oBACA;oBACAW;sBACAX;oBACA;kBACA;oBACAS;kBACA;gBACA,GACAR;kBACA;kBACAD;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAZ;gBAAA,eACAA;gBAAA;gBAAA,OAEAA;cAAA;gBAAA;gBAAA,eACAM;gBAAA;kBAFAO;kBACAtC;kBACA+B;gBAAA;gBAAA,aAHAQ;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAKA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACAzB;QACA0B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7MA;AAAA;AAAA;AAAA;AAA4wC,CAAgB,uqCAAG,EAAC,C;;;;;;;;;;;ACAhyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/components/mp-weixin.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mp-weixin.vue?vue&type=template&id=3aaf109c&scoped=true&\"\nvar renderjs\nimport script from \"./mp-weixin.vue?vue&type=script&lang=js&\"\nexport * from \"./mp-weixin.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mp-weixin.vue?vue&type=style&index=0&id=3aaf109c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3aaf109c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/components/mp-weixin.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-weixin.vue?vue&type=template&id=3aaf109c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-weixin.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-weixin.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"wechatapp\">\n      <view class=\"header\">\n        <open-data class=\"avatar\" type=\"userAvatarUrl\"></open-data>\n      </view>\n    </view>\n    <view class=\"auth-title\">申请获取以下权限</view>\n    <view v-if=\"isProfile\">\n        <view class=\"auth-subtitle\">获得您微信绑定的手机号码</view>\n        <view class=\"login-btn\">\n          <button class=\"button-mobile btn-primary\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">授权手机号</button>\n        </view>\n    </view>\n    <view v-if=\"!isProfile\">\n        <view class=\"auth-subtitle\">获得你的公开信息（昵称、头像等）</view>\n        <view class=\"login-btn\">\n          <button class=\"button btn-normal\" @click.stop=\"getUserProfile\">授权登录</button>\n        </view>\n    </view>\n    <view class=\"no-login-btn\">\n      <button class=\"button btn-normal\" @click=\"cancelLogin\">暂不登录</button>\n    </view>\r\n    <view class=\"privacy\">\r\n        <label @click=\"privacyChange\"><checkbox :checked=\"agreePrivacy\" value=\"agree\" color=\"#fa2209\" style=\"transform:scale(0.7)\"/>已阅读并同意</label>\r\n        <text class=\"privacy-ptl\" @click=\"openPrivacy(1)\">《隐私协议》</text>\r\n        <text class=\"member-ptl\" @click=\"openPrivacy(2)\">《用户协议》</text>\r\n        <wx-privacy ref=\"popPrivacy\" @agree=\"handleAgree\" :title=\"protocolTitle\" :subDesc=\"protocolSubDesc\" @disagree=\"handleDisagree\"></wx-privacy>\r\n    </view>\r\n    \n  </view>\n</template>\n\n<script>\n  import store from '@/store'\n  import * as UserApi from '@/api/user'\r\n  import WxPrivacy from './wx-privacy'\r\n  import ProtocolEnum from '@/common/enum/protocol/Protocol'\r\n  import SettingKeyEnum from '@/common/enum/setting/Key'\n  export default {\r\n    components: {\r\n      WxPrivacy\r\n    },\n    data() {\n      return {\n        // 微信小程序登录凭证 (code)\n        // 提交到后端，用于换取openid\n        code: '',\n        needPhone: false,\n        isProfile: false,\r\n        isOpenPrivacy: false,\r\n        agreePrivacy: false,\r\n        protocolTitle: '',\r\n        protocolSubDesc: '',\r\n        ProtocolEnum,\r\n        SettingKeyEnum\n      }\n    },\n\n    created() {\n      // 获取code\n      this.getCode()\n      \n      // 获取配置\n      this.getUserSetting()\n    },\n\n    methods: {\r\n      privacyChange() {\r\n         this.agreePrivacy = !this.agreePrivacy;\r\n      },\r\n      openPrivacy(type) {\r\n         if (type == 1) {\r\n             this.protocolTitle = ProtocolEnum.data[0].name;\r\n             this.protocolSubDesc = ProtocolEnum.data[0].value;\r\n         } else {\r\n             this.protocolTitle = ProtocolEnum.data[1].name;\r\n             this.protocolSubDesc = ProtocolEnum.data[1].value;\r\n         }\r\n         this.$refs.popPrivacy.openPrivacy();\r\n      },\r\n      handleDisagree() {\r\n         this.agreePrivacy = false;\r\n         this.$refs.popPrivacy.closePrivacy();\r\n      },\r\n      handleAgree() {\r\n         this.agreePrivacy = true;\r\n         this.$refs.popPrivacy.closePrivacy();\r\n      },\n\n      // 获取code\n      // https://developers.weixin.qq.com/miniprogram/dev/api/open-api/login/wx.login.html\n      getCode() {\n        return new Promise((resolve, reject) => {\n          uni.login({\n            provider: 'weixin',\n            success: res => {\n              console.log('getCode res = ', res)\n              resolve(res.code)\n            },\n            fail: reject\n          })\n        })\n      },\n      \n      getUserSetting() {\n        const app = this\n        return new Promise((resolve, reject) => {\n            UserApi.setting()\n            .then(result => {\n                app.needPhone = result.data.loginNeedPhone === 'true' ? true : false\n            })\n            .catch(err => {\n              // empty\n            })\n        })\n      },\n\n      // 获取微信用户信息\n      getUserProfile() {\n        const app = this;\r\n        if (!app.agreePrivacy) {\r\n            app.$error(\"请仔细阅读《用户协议》和《隐私协议》，点击同意代表您已认上述协议内容！\");\r\n            return false;\r\n        }\n        wx.canIUse('getUserProfile') && wx.getUserProfile({\n          lang: 'zh_CN',\n          desc: '获取用户相关信息',\n          success({ userInfo }) {\n            console.log('userInfo == ', userInfo)\n            if (app.needPhone) {\n                app.isProfile = true;\n            }\n            // 授权成功事件\n            userInfo.type = \"profile\";\n            app.onAuthSuccess(userInfo);\n          },\n          fail: function(res) {\n            console.log('登录授权失败，请设置小程序隐私保护协议，返回信息： ', res);\r\n            app.$error(\"抱歉，登录授权失败.\");\n          }\n        })\n      },\n      \n      // 获取微信绑定的手机号\n      getPhoneNumber(e) {\n          if (e.detail.errMsg == \"getPhoneNumber:ok\") {\n             this.onAuthSuccess({\"type\": \"phone\", \"encryptedData\": e.detail.encryptedData, \"iv\": e.detail.iv, \"sessionKey\": e.detail.iv})\n          }\n      },\n      \n      // 授权成功事件\n      // 这里分为两个逻辑:\n      // 1.将code和userInfo提交到后端，如果存在该用户 则实现自动登录，无需再填写手机号\n      // 2.如果不存在该用户, 则显示注册页面, 需填写手机号\n      async onAuthSuccess(userInfo) {\n        const app = this\n        // 提交到后端\n        store.dispatch('MpWxLogin', { code: await app.getCode(), shareId: (uni.getStorageSync('shareId') ? uni.getStorageSync('shareId') : 0), userInfo })\n          .then(result => {\n            if (!app.needPhone || userInfo.type == \"phone\") {\n                // 显示登录成功\n                app.$toast(result.message)\n                // 跳转回原页面\n                setTimeout(() => {\n                  app.onNavigateBack()\n                }, 1000)\n            } else {\n                store.dispatch('Logout')\n            }\n          })\n          .catch(() => {\n            // 将oauth提交给父级\n            app.onEmitSuccess(userInfo)\n          })\n      },\n\n      // 将oauth提交给父级\n      // 这里要重新获取code, 因为上一次获取的code不能复用(会报错)\n      async onEmitSuccess(userInfo) {\n        const app = this\n        app.$emit('success', {\n          oauth: 'MP-WEIXIN', // 第三方登录类型: MP-WEIXIN\n          code: await app.getCode(), // 微信登录的code, 用于换取openid\n          userInfo // 微信用户信息\n        })\n      },\n\n      /**\n       * 暂不登录\n       */\n      cancelLogin() {\r\n        // 跳转回原页面\r\n        this.onNavigateBack();\n      },\n\n      /**\n       * 授权成功 跳转回原页面\n       */\n      onNavigateBack(delta = 1) {\n        uni.navigateBack({\n          delta: Number(delta)\n        })\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    padding: 0 60rpx;\n    font-size: 32rpx;\n    background: #fff;\n    min-height: 100vh;\n  }\n\n  .wechatapp {\n    padding: 80rpx 0 48rpx;\n    border-bottom: 1rpx solid #e3e3e3;\n    margin-bottom: 72rpx;\n    text-align: center;\n\n    .header {\n      width: 190rpx;\n      height: 190rpx;\n      border: 4rpx solid #fff;\n      margin: 0 auto 0;\n      border-radius: 50%;\n      overflow: hidden;\n      box-shadow: 2rpx 0 10rpx rgba(50, 50, 50, 0.3);\n    }\n  }\n\n  .auth-title {\n    color: #585858;\n    font-size: 34rpx;\n    margin-bottom: 40rpx;\n  }\n\n  .auth-subtitle {\n    color: #888;\n    margin-bottom: 88rpx;\n    font-size: 28rpx;\n  }\n\n  .login-btn {\n    padding: 0 20rpx;\n\n    .button {\n      height: 88rpx;\n      line-height: 88rpx;\n      background: $fuint-theme;\n      color: #fff;\n      font-size: 30rpx;\n      border-radius: 12rpx;\n      text-align: center;\n    }\r\n    .button-mobile {\r\n        height: 88rpx;\r\n        line-height: 88rpx;\r\n        background: $fuint-theme;\r\n        color: #fff;\r\n        font-size: 30rpx;\r\n        border-radius: 12rpx;\r\n        text-align: center;\r\n    }\n  }\n\n\n  .no-login-btn {\n    margin-top: 24rpx;\n    padding: 0 20rpx;\n\n    .button {\n      height: 88rpx;\n      line-height: 88rpx;\n      background: #dfdfdf;\n      color: #fff;\n      font-size: 30rpx;\n      border-radius: 12rpx;\n      text-align: center;\n    }\n  }\r\n  \r\n  .privacy {\r\n      margin-top: 50rpx;\r\n      padding-left: 5rpx;\r\n      font-size: 24rpx;\r\n      text-align: center;\r\n      .member-ptl {\r\n          color: $fuint-theme;\r\n      }\r\n      .privacy-ptl {\r\n          color: $fuint-theme;\r\n      }\r\n  }\n</style>\n", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-weixin.vue?vue&type=style&index=0&id=3aaf109c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./mp-weixin.vue?vue&type=style&index=0&id=3aaf109c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891425482\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}