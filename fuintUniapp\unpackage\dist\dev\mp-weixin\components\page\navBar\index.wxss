@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.diy-navBar .data-list.data-v-6e663448::after {
  clear: both;
  content: " ";
  display: table;
}
.item-nav.data-v-6e663448 {
  float: left;
  margin: 10rpx 0px 5rpx 0px;
  text-align: center;
  background: #ffffff;
  padding: 2rpx;
  color: #666666;
}
.item-nav .nav-to.data-v-6e663448 {
  border: 2rpx solid #3f51b5;
  margin: 0rpx 2px 0px 2px;
  padding: 38rpx 10rpx 10rpx 10rpx;
  border-radius: 8rpx;
  background: #ffffff;
  height: 150rpx;
}
.item-nav .item-text.data-v-6e663448 {
  text-align: left;
  padding-left: 20rpx;
}
.item-nav .item-text .text.data-v-6e663448 {
  font-size: 32rpx;
}
.item-nav .item-text .tip.data-v-6e663448 {
  font-size: 22rpx;
  margin-top: 8rpx;
  color: #999;
}
.item-nav .item-image.data-v-6e663448 {
  margin-bottom: 4px;
  font-size: 0;
  margin-left: 30rpx;
  width: 88rpx;
  height: 88rpx;
  float: left;
}
.item-nav .item-image .image.data-v-6e663448 {
  width: 80rpx;
  height: 80rpx;
}
/* 分列布局 */
.diy-navBar .avg-sm-3 > .item-nav.data-v-6e663448 {
  width: 33.33333333%;
}
.diy-navBar .avg-sm-4 > .item-nav.data-v-6e663448 {
  width: 25%;
}
.diy-navBar .avg-sm-2 > .item-nav.data-v-6e663448 {
  width: 50%;
}
