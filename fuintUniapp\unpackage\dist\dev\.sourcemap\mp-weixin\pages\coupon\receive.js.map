{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/coupon/receive.vue?b649", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/coupon/receive.vue?369d", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/coupon/receive.vue?7ff3", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/coupon/receive.vue?beac", "uni-app:///pages/coupon/receive.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/coupon/receive.vue?c8d1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/coupon/receive.vue?b77a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "code", "disabled", "onLoad", "methods", "doScan", "uni", "success", "app", "doSubmit", "couponApi", "then"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uMAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA4oB,CAAgB,0oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2BhqB;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;eACA;EACAC;IACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;EACA;EAEAC;IAEA;AACA;AACA;IACAC;MACA;MACAC;QACAC;UACAC;QACA;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACAD;MACAE;QAAA;QAAA;MAAA,GACAC;QACAH;QACAA;QACA;QACA;UACAA;QACA;UACAA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAA+uC,CAAgB,qqCAAG,EAAC,C;;;;;;;;;;;ACAnwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/coupon/receive.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/coupon/receive.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./receive.vue?vue&type=template&id=264d149a&scoped=true&\"\nvar renderjs\nimport script from \"./receive.vue?vue&type=script&lang=js&\"\nexport * from \"./receive.vue?vue&type=script&lang=js&\"\nimport style0 from \"./receive.vue?vue&type=style&index=0&id=264d149a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"264d149a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/coupon/receive.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=template&id=264d149a&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uview-ui/components/u-icon/u-icon\" */ \"@/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"search-wrapper\">\n      <view class=\"search-input\">\n        <view class=\"search-input-wrapper\">\n          <view class=\"right\">\n            <input v-model=\"code\" class=\"input\" placeholder=\"请输入卡券核销码\" type=\"text\"></input>\n          </view>\r\n          <view class=\"scan u-icon-wrap\">\r\n             <view class=\"icon\" @click=\"doScan\">\r\n               <u-icon name=\"scan\"></u-icon>\r\n             </view>\r\n          </view>\n        </view>\n      </view>\n    </view>\n    <view class=\"main-form\">\n      <view class=\"footer\">\r\n        <view class=\"btn-wrapper\">\r\n          <view class=\"btn-item btn-item-main\" :class=\"{ disabled }\" @click=\"doSubmit()\">确定兑换</view>\r\n        </view>\r\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\n  import * as couponApi from '@/api/coupon'\n  export default {\n    data() {\n      return {\n        code: '',\r\n        // 按钮禁用\r\n        disabled: false\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n       this.code = options.code ? options.code : '';\n    },\n\n    methods: {\n      \r\n      /**\n       * 扫码\n       */\r\n      doScan() {\r\n          const app = this;\r\n          uni.scanCode({\r\n              success: function(res) {\r\n                app.code = res.result;\r\n              }\r\n          });\r\n      },\r\n      \r\n      /**\r\n       * 提交兑换\r\n       */\r\n      doSubmit() {\r\n        const app = this;\r\n        app.disabled = true;\r\n        couponApi.receive({ 'couponId': 0, 'receiveCode': app.code })\r\n          .then(result => {\r\n                app.code = '';\r\n                app.disabled = false;\r\n                // 显示提示\r\n                if (parseInt(result.code) === 200) {\r\n                    app.$success(\"兑换成功！\");\r\n                } else {\r\n                    app.$error(result.message);\r\n                }\r\n          })\r\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    padding: 20rpx;\n    min-height: 100vh;\n    background: #f7f7f7;\n  }\n\n  .search-wrapper {\n    display: flex;\n    height: 100rpx;\r\n    margin-top: 80rpx;\r\n    padding: 0 5rpx;\n  }\n\n  // 搜索输入框\n  .search-input {\n    width: 100%;\n    background: #fff;\n    border-radius: 10rpx 0 0 10rpx;\n    box-sizing: border-box;\n    overflow: hidden;\n    .search-input-wrapper {\n      display: flex;\n      .right {\n        flex: 1;\n        input {\n          font-size: 30rpx;\n          height: 100rpx;\n          line-height: 100rpx;\n          padding-left: 30rpx;\n          .input-placeholder {\n            color: #aba9a9;\n          }\n        }\n      }\r\n      \r\n      .scan {\r\n        display: flex;\r\n        width: 60rpx;\r\n        justify-content: center;\r\n        align-items: center;\r\n        .icon {\r\n          display: block;\r\n          color: #b4b4b4;\r\n          font-size: 48rpx;\r\n        }\r\n      \r\n      }\n    }\n  }\n  /* 底部操作栏 */\r\n  .footer {\r\n    margin-top: 100rpx;\r\n    .btn-wrapper {\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 5rpx;\r\n    }\r\n    .btn-item {\r\n      flex: 1;\r\n      font-size: 28rpx;\r\n      height: 80rpx;\r\n      line-height: 80rpx;\r\n      text-align: center;\r\n      color: #fff;\r\n      border-radius: 40rpx;\r\n    }\r\n  \r\n    .btn-item-main {\r\n      background: linear-gradient(to right, #f9211c, #ff6335);\r\n      // 禁用按钮\r\n      &.disabled {\r\n        background: #ff9779;\r\n      }\r\n    }\r\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=style&index=0&id=264d149a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./receive.vue?vue&type=style&index=0&id=264d149a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891420631\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}