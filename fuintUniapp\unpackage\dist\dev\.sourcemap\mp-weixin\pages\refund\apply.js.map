{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/apply.vue?c708", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/apply.vue?3df3", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/apply.vue?81df", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/apply.vue?422a", "uni-app:///pages/refund/apply.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/apply.vue?9503", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/refund/apply.vue?2f3a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "RefundTypeEnum", "isLoading", "orderId", "orderInfo", "formData", "images", "type", "content", "imageList", "maxImage<PERSON>ength", "disabled", "onLoad", "methods", "getOrderDetail", "app", "OrderApi", "then", "onSwitchService", "chooseImage", "uni", "count", "sizeType", "sourceType", "success", "deleteImage", "handleSubmit", "catch", "console", "onSubmit", "RefundApi", "setTimeout", "uploadFile", "UploadApi", "resolve"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmF9pB;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AAAA,eAEA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;QACA;QACAC;QACA;QACAC;QACA;QACAC;MACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IAAA;IACA;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC;MACAC,6BACAC;QACAF;QACAA;MACA;IACA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACAC;QACAC;QACAC;QAAA;QACAC;QAAA;QACAC;UAAA;UACAT;QACA;MACA;IACA;IAEA;IACAU;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACAX;MACA;MACA;QACAA,iBACAE;UAAA;QAAA,GACAU;UACAZ;UACA;YACAA;UACA;UACAa;QACA;MACA;QACAb;MACA;IACA;IAEA;IACAc;MACA;MACAC,2CACAb;QACAF;QACAgB;UACAhB;UACAK;QACA;MACA,GACAO;QAAA;MAAA;IACA;IAEA;IACAK;MACA;MACA;MACA;MACA;QACA;UACAC,2BACAhB;YACAF;YACAmB;UACA,GACAP;YAAA;UAAA;QACA;UACAO;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AChOA;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/refund/apply.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/refund/apply.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./apply.vue?vue&type=template&id=36a440e0&scoped=true&\"\nvar renderjs\nimport script from \"./apply.vue?vue&type=script&lang=js&\"\nexport * from \"./apply.vue?vue&type=script&lang=js&\"\nimport style0 from \"./apply.vue?vue&type=style&index=0&id=36a440e0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"36a440e0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/refund/apply.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./apply.vue?vue&type=template&id=36a440e0&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.isLoading && false ? _vm.imageList.length : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./apply.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./apply.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-if=\"!isLoading\" class=\"container\">\n\n    <!-- 商品详情 -->\n    <view class=\"goods-detail b-f dis-flex flex-dir-row\" v-for=\"(goods, idx) in orderInfo.goods\" :key=\"idx\">\n      <view class=\"left\">\n        <image class=\"goods-image\" :src=\"goods.image\"></image>\n      </view>\n      <view class=\"right dis-flex flex-box flex-dir-column flex-x-around\">\n        <view class=\"goods-name\">\n          <text class=\"twolist-hidden\">{{ goods.name }}</text>\n        </view>\n        <view class=\"dis-flex col-9 f-24\">\n          <view class=\"flex-box\">\n            <view class=\"goods-props clearfix\">\n              <view class=\"goods-props-item\" v-for=\"(props, idx) in goods.specList\" :key=\"idx\">\n                <text>{{ props.specValue }}</text>\n              </view>\n            </view>\n          </view>\n          <text class=\"t-r\">×{{ goods.num }}</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 服务类型 -->\n    <view class=\"row-service b-f m-top20\">\n      <view class=\"row-title\">服务类型</view>\n      <view class=\"service-switch dis-flex\">\n        <view class=\"switch-item\" v-for=\"(item, index) in RefundTypeEnum.data\" :key=\"index\" :class=\"{ active: formData.type == item.value }\"\n          @click=\"onSwitchService(item.value)\">{{ item.name }}</view>\n      </view>\n    </view>\r\n    \r\n    <!-- 订单总金额 -->\r\n    <view class=\"row-money b-f m-top20 dis-flex\">\r\n      <view class=\"row-title\">订单总金额</view>\r\n      <view class=\"money col-m\">￥{{ orderInfo.amount }}</view>\r\n    </view>\r\n    \r\n    <!-- 实付款 -->\r\n    <view class=\"row-money b-f m-top20 dis-flex\">\r\n      <view class=\"row-title\">实际付款</view>\r\n      <view class=\"money col-m\">￥{{ orderInfo.payAmount }}</view>\r\n    </view>\n\n    <!-- 申请原因 -->\n    <view class=\"row-textarea b-f m-top20\">\n      <view class=\"row-title\">申请原因</view>\n      <view class=\"content\">\n        <textarea class=\"textarea\" v-model=\"formData.content\" maxlength=\"2000\" placeholder=\"请详细填写申请原因，建议您先与卖家沟通!\"\n          placeholderStyle=\"color:#ccc\"></textarea>\n      </view>\n    </view>\n\n    <!-- 上传凭证 -->\n    <view class=\"row-voucher b-f m-top20\" v-if=\"false\">\n      <view class=\"row-title\">上传凭证 (最多6张)</view>\n      <view class=\"image-list\">\n        <!-- 图片列表 -->\n        <view class=\"image-preview\" v-for=\"(image, imageIndex) in imageList\" :key=\"imageIndex\">\n          <text class=\"image-delete iconfont icon-shanchu\" @click=\"deleteImage(imageIndex)\"></text>\n          <image class=\"image\" mode=\"aspectFill\" :src=\"image.path\"></image>\n        </view>\n        <!-- 上传图片 -->\n        <view v-if=\"imageList.length < maxImageLength\" class=\"image-picker\" @click=\"chooseImage()\">\n          <text class=\"choose-icon iconfont icon-tubiao_xiangji\"></text>\n          <text class=\"choose-text\">上传图片</text>\n        </view>\n      </view>\n    </view>\n\n    <!-- 底部操作按钮 -->\n    <view class=\"footer-fixed\">\n      <view class=\"btn-wrapper\">\n        <view class=\"btn-item btn-item-main\" :class=\"{ disabled }\" @click=\"handleSubmit()\">确认提交</view>\n      </view>\n    </view>\n\n  </view>\n</template>\n\n<script>\n  import { RefundTypeEnum } from '@/common/enum/order/refund'\n  import * as UploadApi from '@/api/upload'\n  import * as RefundApi from '@/api/refund'\r\n  import * as OrderApi from '@/api/order'\n\n  const maxImageLength = 6\n\n  export default {\n    data() {\n      return {\n        // 枚举类\n        RefundTypeEnum,\n        // 正在加载\n        isLoading: true,\n        // 订单id\n        orderId: null,\n        // 订单详情\n        orderInfo: {},\n        // 表单数据\n        formData: {\n          // 图片上传成功的文件ID集\n          images: [],\n          // 服务类型\n          type: 10,\n          // 申请原因\n          content: ''\n        },\n        // 用户选择的图片列表\n        imageList: [],\n        // 最大图片数量\n        maxImageLength,\n        // 按钮禁用\n        disabled: false\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad({ orderId }) {\n      this.orderId = orderId\n      // 获取订单详情\n      this.getOrderDetail()\n    },\n\n    methods: {\n\n      // 获取订单详情\n      getOrderDetail() {\n        const app = this\n        app.isLoading = true\n        OrderApi.detail(app.orderId)\n          .then(result => {\n            app.orderInfo = result.data\n            app.isLoading = false\n          })\n      },\n\n      // 切换类型\n      onSwitchService(value) {\n        this.formData.type = value\n      },\n\n      // 选择图片\n      chooseImage() {\n        const app = this\n        const oldImageList = app.imageList\n        // 选择图片\n        uni.chooseImage({\n          count: maxImageLength - oldImageList.length,\n          sizeType: ['original', 'compressed'], // 可以指定是原图还是压缩图，默认二者都有\n          sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机，默认二者都有\n          success({ tempFiles }) {\n            app.imageList = oldImageList.concat(tempFiles)\n          }\n        });\n      },\n\n      // 删除图片\n      deleteImage(imageIndex) {\n        this.imageList.splice(imageIndex, 1)\n      },\n\n      // 表单提交\n      handleSubmit() {\n        const app = this\n        const { imageList } = app\n        // 判断是否重复提交\n        if (app.disabled === true) return false\n        // 按钮禁用\n        app.disabled = true\n        // 判断是否需要上传图片\n        if (imageList.length > 0) {\n          app.uploadFile()\n            .then(() => app.onSubmit())\n            .catch(err => {\n              app.disabled = false\n              if (err.statusCode !== 0) {\n                app.$toast(err.errMsg)\n              }\n              console.log('err', err)\n            })\n        } else {\n          app.onSubmit()\n        }\n      },\n\n      // 提交到后端\n      onSubmit() {\n        const app = this\n        RefundApi.apply(app.orderId, app.formData)\n          .then(result => {\n            app.$toast('已提交申请，请耐心等待！')\n            setTimeout(() => {\n              app.disabled = false\n              uni.navigateBack()\n            }, 3000)\n          })\n          .catch(err => app.disabled = false)\n      },\n\n      // 上传图片\n      uploadFile() {\n        const app = this\n        const { imageList } = app\n        // 批量上传\n        return new Promise((resolve, reject) => {\n          if (imageList.length > 0) {\n            UploadApi.image(imageList)\n              .then(fileIds => {\n                app.formData.images = fileIds\n                resolve(fileIds)\n              })\n              .catch(err => reject(err))\n          } else {\n            resolve()\n          }\n        })\n      }\n\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n  .container {\n    padding-bottom: 100rpx;\n  }\n\n  .row-title {\n    color: #888;\n    margin-bottom: 20rpx;\n  }\n\n  // 商品信息\n  .goods-detail {\n    padding: 24rpx 20rpx;\n\n    .left {\n      .goods-image {\n        display: block;\n        width: 150rpx;\n        height: 150rpx;\n      }\n    }\n\n    .right {\n      padding-left: 20rpx;\n    }\n\n    .goods-props {\n      margin-top: 14rpx;\n      // height: 40rpx;\n      color: #ababab;\n      font-size: 24rpx;\n      overflow: hidden;\n\n      .goods-props-item {\n        display: inline-block;\n        margin-right: 14rpx;\n        padding: 4rpx 16rpx;\n        border-radius: 12rpx;\n        background-color: #F5F5F5;\n        width: auto;\n      }\n    }\n  }\n\n  /* 服务类型 */\n  .row-service {\n    padding: 24rpx 20rpx;\n  }\n\n  .service-switch {\n    .switch-item {\n      padding: 6rpx 30rpx;\n      margin-right: 25rpx;\n      border-radius: 10rpx;\n      border: 1px solid rgb(177, 177, 177);\n      color: #888;\n\n      &.active {\n        color: #fc1e56;\n        border: 1px solid #fc1e56;\n      }\n    }\n  }\n\n  /* 申请原因 */\n  .row-textarea {\n    padding: 24rpx 20rpx;\n\n    .textarea {\n      width: 100%;\n      height: 220rpx;\n      padding: 12rpx;\n      border: 1rpx solid #e8e8e8;\n      border-radius: 5rpx;\n      box-sizing: border-box;\n      font-size: 26rpx;\n    }\n  }\n\n  /* 退款金额 */\n  .row-money {\n    padding: 24rpx 20rpx;\n    .row-title {\n      margin-bottom: 0;\n      margin-right: 20rpx;\n    }\n  }\n\n  // 上传凭证\n  .row-voucher {\n    padding: 24rpx 20rpx;\n\n    .image-list {\n      padding: 0 20rpx;\n      margin-top: 20rpx;\n      margin-bottom: -20rpx;\n\n      &:after {\n        clear: both;\n        content: \" \";\n        display: table;\n      }\n\n      .image {\n        display: block;\n        width: 100%;\n        height: 100%;\n      }\n\n      .image-picker,\n      .image-preview {\n        width: 184rpx;\n        height: 184rpx;\n        margin-right: 30rpx;\n        margin-bottom: 30rpx;\n        float: left;\n\n        &:nth-child(3n+0) {\n          margin-right: 0;\n        }\n      }\n\n      .image-picker {\n        display: flex;\n        flex-direction: column;\n        justify-content: center;\n        align-items: center;\n        border: 1rpx dashed #ccc;\n        color: #ccc;\n\n        .choose-icon {\n          font-size: 48rpx;\n          margin-bottom: 6rpx;\n        }\n\n        .choose-text {\n          font-size: 24rpx;\n        }\n      }\n\n      .image-preview {\n        position: relative;\n\n        .image-delete {\n          position: absolute;\n          top: -15rpx;\n          right: -15rpx;\n          height: 42rpx;\n          width: 42rpx;\n          line-height: 42rpx;\n          background: rgba(0, 0, 0, 0.64);\n          border-radius: 50%;\n          color: #fff;\n          font-weight: bolder;\n          font-size: 22rpx;\n          z-index: 10;\n          text-align: center;\n        }\n      }\n    }\n\n\n  }\n\n  // 底部操作栏\n  .footer-fixed {\n    position: fixed;\n    bottom: var(--window-bottom);\n    left: 0;\n    right: 0;\n    height: 180rpx;\r\n    padding-bottom: 30rpx;\n    z-index: 11;\n    box-shadow: 0 -4rpx 40rpx 0 rgba(144, 52, 52, 0.1);\n    background: #fff;\n\n    .btn-wrapper {\n      height: 100%;\n      display: flex;\n      align-items: center;\n      padding: 0 20rpx;\n    }\n\n    .btn-item {\n      flex: 1;\n      font-size: 28rpx;\n      height: 80rpx;\n      line-height: 80rpx;\n      text-align: center;\n      color: #fff;\n      border-radius: 40rpx;\n    }\n\n    .btn-item-main {\n      background: linear-gradient(to right, #f9211c, #ff6335);\n\n      // 禁用按钮\n      &.disabled {\n        background: #ff9779;\n      }\n    }\n\n  }\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./apply.vue?vue&type=style&index=0&id=36a440e0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./apply.vue?vue&type=style&index=0&id=36a440e0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891422955\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}