
page {
  background: #f2f2f2;
}

@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/*$fuint-theme: #113a28;*/
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 引入uView全局scss变量文件 */
.flex-row.data-v-57d42baa {
  display: flex;
  flex-direction: row;
}
.flex-col.data-v-57d42baa {
  display: flex;
  flex-direction: column;
}
.items-center.data-v-57d42baa {
  align-items: center;
}
.justify-between.data-v-57d42baa {
  justify-content: space-between;
}
.justify-center.data-v-57d42baa {
  justify-content: center;
}
.self-stretch.data-v-57d42baa {
  align-self: stretch;
}
.self-center.data-v-57d42baa {
  align-self: center;
}
.page.data-v-57d42baa {
  padding-bottom: 204rpx;
  background-color: #f2f2f2;
  min-height: 100vh;
}
.header-wrapper.data-v-57d42baa {
  padding: 16rpx 0 24rpx;
  background-color: #fff;
  position: relative;
}
.header-title.data-v-57d42baa {
  color: #000;
  font-size: 40rpx;
  font-weight: 600;
  line-height: 38rpx;
  letter-spacing: 4rpx;
  text-align: center;
}
.header-icon.data-v-57d42baa {
  position: absolute;
  right: 204rpx;
  bottom: 23rpx;
  border-radius: 50%;
  width: 70rpx;
  height: 72rpx;
}
.status-card.data-v-57d42baa {
  margin: 6rpx 26rpx 0 34rpx;
  padding: 56rpx 0 42rpx;
  background-color: #fff;
  border-radius: 18rpx;
  box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  align-items: center;
}
.status-title.data-v-57d42baa {
  color: #000;
  font-size: 36rpx;
  font-weight: 300;
  line-height: 33rpx;
  letter-spacing: 4rpx;
  text-shadow: 0 0 #000;
}
.status-content.data-v-57d42baa {
  margin-top: 42rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.status-message.data-v-57d42baa {
  color: #404040;
  font-size: 24rpx;
  font-weight: 300;
  line-height: 25rpx;
}
.rate-button.data-v-57d42baa {
  margin-top: 42rpx;
  padding: 24rpx 0 28rpx;
  background-color: #fff;
  border-radius: 39rpx;
  width: 348rpx;
  border: 1rpx solid #333;
  display: flex;
  justify-content: center;
  align-items: center;
}
.rate-text.data-v-57d42baa {
  color: #404040;
  font-size: 24rpx;
  font-weight: 600;
  line-height: 23rpx;
  letter-spacing: 4rpx;
}
.pickup-code.data-v-57d42baa {
  margin-top: 30rpx;
  color: #404040;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}
.pickup-code .pickup-label.data-v-57d42baa {
  margin-right: 10rpx;
}
.pickup-code .pickup-number.data-v-57d42baa {
  font-size: 32rpx;
  font-weight: 600;
  color: #2b387e;
}
.points-card.data-v-57d42baa {
  margin: 24rpx 26rpx 0 34rpx;
  padding: 28rpx 0;
  background-color: #fff;
  border-radius: 18rpx;
  box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.points-text.data-v-57d42baa {
  margin-left: 32rpx;
  color: #404040;
  font-size: 24rpx;
  font-weight: 300;
  line-height: 22rpx;
}
.goods-detail-card.data-v-57d42baa {
  margin: 24rpx 26rpx 0 34rpx;
  padding: 0 32rpx 116rpx;
  background-color: #fff;
  border-radius: 18rpx;
  box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
}
.goods-header.data-v-57d42baa {
  padding-top: 40rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.2);
}
.goods-header-title.data-v-57d42baa {
  color: #000;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 26rpx;
  letter-spacing: 3rpx;
}
.goods-list.data-v-57d42baa {
  padding: 48rpx 0;
  border-top: 2rpx solid rgba(0, 0, 0, 0.2);
}
.goods-item.data-v-57d42baa {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.goods-item.data-v-57d42baa:last-child {
  margin-bottom: 0;
}
.goods-image.data-v-57d42baa {
  border-radius: 8rpx;
  width: 126rpx;
  height: 124rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}
.goods-info.data-v-57d42baa {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.goods-name.data-v-57d42baa {
  margin-left: 12rpx;
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 26rpx;
  letter-spacing: 3rpx;
  margin-bottom: 24rpx;
}
.goods-specs.data-v-57d42baa {
  margin-left: 12rpx;
  color: #666;
  font-size: 20rpx;
  font-weight: 300;
  line-height: 19rpx;
  text-shadow: 0 0 #666;
  margin-bottom: 24rpx;
}
.goods-quantity.data-v-57d42baa {
  color: #666;
  font-size: 26rpx;
  line-height: 20rpx;
}
.fee-details.data-v-57d42baa {
  padding-bottom: 36rpx;
  border-bottom: 2rpx solid rgba(0, 0, 0, 0.2);
}
.fee-item.data-v-57d42baa {
  margin-bottom: 48rpx;
}
.fee-item.data-v-57d42baa:last-child {
  margin-bottom: 0;
}
.fee-label.data-v-57d42baa {
  color: #333;
  font-size: 24rpx;
  font-weight: 300;
  line-height: 22rpx;
  letter-spacing: 2rpx;
}
.address-card.data-v-57d42baa {
  margin: 24rpx 26rpx 0 34rpx;
  padding: 40rpx 32rpx;
  background-color: #fff;
  border-radius: 18rpx;
  box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
}
.address-header.data-v-57d42baa {
  margin-bottom: 20rpx;
}
.address-type.data-v-57d42baa {
  color: #333;
  font-size: 28rpx;
  font-weight: 600;
}
.address-info.data-v-57d42baa {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}
.contact-name.data-v-57d42baa {
  color: #999;
  font-size: 26rpx;
  margin-right: 20rpx;
}
.contact-phone.data-v-57d42baa {
  color: #999;
  font-size: 26rpx;
}
.address-detail.data-v-57d42baa {
  color: #999;
  font-size: 24rpx;
}
.address-region.data-v-57d42baa {
  margin-right: 12rpx;
}
.address-full.data-v-57d42baa {
  margin-left: 12rpx;
}
.order-info-card.data-v-57d42baa {
  margin: 24rpx 24rpx 0 32rpx;
  padding: 48rpx 24rpx 48rpx 32rpx;
  background-color: #fff;
  border-radius: 18rpx;
  box-shadow: 0 8rpx 8rpx rgba(0, 0, 0, 0.25);
}
.info-card-title.data-v-57d42baa {
  color: #000;
  font-size: 28rpx;
  font-weight: 600;
  line-height: 26rpx;
  letter-spacing: 3rpx;
}
.info-divider.data-v-57d42baa {
  margin-right: 20rpx;
  margin-top: 28rpx;
  background-color: rgba(0, 0, 0, 0.2);
  height: 2rpx;
}
.info-list.data-v-57d42baa {
  margin-top: 32rpx;
}
.info-row.data-v-57d42baa {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}
.info-row.data-v-57d42baa:last-child {
  margin-bottom: 0;
}
.info-label.data-v-57d42baa {
  color: rgba(51, 51, 51, 0.5);
  font-size: 24rpx;
  font-weight: 300;
  line-height: 22rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 0 rgba(51, 51, 51, 0.5);
}
.info-value.data-v-57d42baa {
  color: #333;
  font-size: 24rpx;
  font-weight: 300;
  line-height: 19rpx;
  letter-spacing: 2rpx;
  text-shadow: 0 0 #333;
}
.info-value-wrapper.data-v-57d42baa {
  display: flex;
  align-items: center;
}
.copy-button.data-v-57d42baa {
  margin-left: 16rpx;
  padding: 4rpx 16rpx;
  border: 1rpx solid #c1c1c1;
  border-radius: 12rpx;
}
.copy-text.data-v-57d42baa {
  color: #666;
  font-size: 20rpx;
}
.payment-info.data-v-57d42baa {
  margin-right: 16rpx;
  line-height: 22rpx;
  height: 22rpx;
}
.payment-method.data-v-57d42baa {
  color: #333;
  font-size: 24rpx;
  font-weight: 300;
  line-height: 22rpx;
  letter-spacing: 2rpx;
}
.payment-symbol.data-v-57d42baa {
  color: #333;
  font-size: 20rpx;
  font-weight: 300;
  line-height: 15rpx;
}
.payment-amount.data-v-57d42baa {
  color: #333;
  font-size: 24rpx;
  font-weight: 300;
  line-height: 19rpx;
  letter-spacing: 2rpx;
}
.bottom-notice.data-v-57d42baa {
  margin-top: 26rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.notice-text.data-v-57d42baa {
  color: #999;
  font-size: 22rpx;
  line-height: 21rpx;
  letter-spacing: 2rpx;
}
/* 底部操作栏 */
.footer-fixed.data-v-57d42baa {
  position: fixed;
  bottom: 0px;
  left: 0;
  right: 0;
  height: 180rpx;
  padding-bottom: 30rpx;
  z-index: 11;
  box-shadow: 0 -4rpx 40rpx 0 rgba(97, 97, 97, 0.1);
  background: #fff;
}
.footer-fixed .btn-wrapper.data-v-57d42baa {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 30rpx;
}
.footer-fixed .btn-item.data-v-57d42baa {
  min-width: 164rpx;
  border-radius: 8rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #383838;
  text-align: center;
  border: 1rpx solid #a8a8a8;
  margin-left: 10rpx;
}
.footer-fixed .btn-item.common.data-v-57d42baa {
  color: #fff;
  border: none;
  background: linear-gradient(to right, #2b387e, #2b387e);
}
.footer-fixed .btn-item.active.data-v-57d42baa {
  color: #fff;
  border: none;
  background: linear-gradient(to right, #f9211c, #ff6335);
}
.pay-popup.data-v-57d42baa {
  padding: 25rpx 25rpx 70rpx 25rpx;
}
.pay-popup .title.data-v-57d42baa {
  font-size: 30rpx;
  margin-bottom: 50rpx;
  font-weight: bold;
  text-align: center;
}
.pay-popup .pop-content.data-v-57d42baa {
  min-height: 120rpx;
  padding: 0 20rpx;
}
.pay-popup .pop-content .pay-item.data-v-57d42baa {
  padding: 30rpx;
  font-size: 30rpx;
  background: #fff;
  border: 1rpx solid #2b387e;
  border-radius: 8rpx;
  color: #888;
  margin-bottom: 12rpx;
  text-align: center;
}
.pay-popup .pop-content .pay-item .item-left_icon.data-v-57d42baa {
  margin-right: 20rpx;
  font-size: 48rpx;
}
.pay-popup .pop-content .pay-item .item-left_icon.wechat.data-v-57d42baa {
  color: #00c800;
}
.pay-popup .pop-content .pay-item .item-left_icon.balance.data-v-57d42baa {
  color: #2b387e;
}
.reservation-info.data-v-57d42baa {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}
.reservation-info .reservation-time.data-v-57d42baa {
  color: #ff6600;
  font-weight: bold;
  font-size: 28rpx;
  margin-bottom: 8rpx;
}
.reservation-info .reservation-status.data-v-57d42baa {
  font-size: 24rpx;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}
.reservation-info .reservation-status.status-pending.data-v-57d42baa {
  background-color: #fff3cd;
  color: #856404;
}
.reservation-info .reservation-status.status-processed.data-v-57d42baa {
  background-color: #d4edda;
  color: #155724;
}
