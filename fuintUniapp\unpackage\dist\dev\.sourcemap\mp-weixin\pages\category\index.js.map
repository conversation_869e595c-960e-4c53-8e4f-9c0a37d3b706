{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?9524", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?cc46", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?3290", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?f951", "uni-app:///pages/category/index.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?c8a1", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?e3aa", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?0b1c", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/index.vue?d7b6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "Search", "Sku<PERSON><PERSON><PERSON>", "PackagePopup", "Empty", "Location", "data", "old", "scrollTop", "orderId", "goodsCart", "totalNum", "totalPrice", "scrollHeight", "curIndex", "list", "isLoading", "showSkuPopup", "showPackagePopup", "skuMode", "goods", "storeInfo", "gradeInfo", "hafanInfo", "currentDeliveryMode", "categoryPositions", "isProgrammaticScroll", "scrollTimer", "onLoad", "tableId", "uni", "app", "console", "onShow", "type", "success", "fail", "onHide", "clearTimeout", "onUnload", "computed", "hafanLevel", "isMemberPrice", "methods", "scroll", "onScroll", "updateActiveCategory", "query", "activeIndex", "getPageData", "Promise", "GoodsApi", "CartApi", "UserApi", "then", "finally", "item", "total", "totalBuyNum", "onGetStoreInfo", "<PERSON><PERSON><PERSON>", "initDeliveryMode", "title", "icon", "duration", "onDeliveryModeChange", "onTargetGoods", "goodsId", "setListHeight", "handleSelectNav", "scrollToCategoryByIndex", "setTimeout", "onVipPrice", "onSaveCart", "resolve", "catch", "onAddCart", "goCart", "doSubmit", "cartIds", "onUpdateSku", "group", "onShowSkuPopup", "goodsData", "onShowPackagePopup", "PackageApi", "reject", "onShareAppMessage", "path", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAA0oB,CAAgB,woBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACyI9pB;AAIA;AACA;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAOA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAN;MACA;MACAO;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC,8BAGA;IAAA,IAFAC;MACApB;IAEA;IACAoB;IACA;MACAC;IACA;IACAC;IACA;IACAA;IACAC;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACAF;IACAA;IAEAD;MACAI;MACAC;QACAL;QACAA;QACAC;MACA;MACAK;QACA;MAAA;IAEA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;MACAC;MACAP;IACA;EACA;EAEA;AACA;AACA;EACAQ;IACA;IACA;IACA;MACAD;MACAP;IACA;EACA;EAEAS;IACA;IACAC;MAAA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACAd;;MAEA;MACA;QACA;MACA;;MAEA;MACA;QACAO;MACA;MAEAP;QACAA;MACA;IACA;IAEA;IACAe;MACA;;MAEA;MACA;;MAEA;MACAC;;MAEA;MACAhB;QACAgB;MACA;MAEAA;QACA;UACA;UACA;UAEA;;UAEA;UACA;YACA;YACA;cACAC;YACA;UACA;;UAEA;UACA;YACAjB;UACA;QACA;MACA;IACA;IAGA;AACA;AACA;IACAkB;MACA;MACAlB;MACAmB;MACA;MACAC;MACA;MACAC,gBACAC,eACA,EACAC;QACA;QACAvB;QACAA;QACAA;QACAA;QACAA;QACA;QACA;MACA,GACAwB;QACAxB;QACAA;QACAA;UACA;UACAyB;YACA;YACAzB;cACA;gBACA0B;gBACAC;gBAEA3B;cACA;YACA;YACAA;UACA;UACAA;QACA;MAGA;IACA;IAEA;AACA;AACA;IACA4B;MACA;MACAC,0BACAN;QACAvB;QACA;QACAA;MACA;IACA;IAEA;AACA;AACA;IACA;AACA;AACA;IACA8B;MACA;MACA;MACA;MACA7B;MAEA;QACAF;UACAgC;UACAC;UACAC;QACA;QACA;QACAjC;QACAD;MACA;QACA;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAkC;MACA;MACAjC;;MAEA;MACAD;;MAEA;MACA;MACA;QACAD;UACAgC;UACAC;UACAC;QACA;MACA;QACAlC;UACAgC;UACAC;UACAC;QACA;MACA;;MAEA;MACAlC;IACA;IAEA;AACA;AACA;IACAoC;MACA;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACAtC;QACAK;UACAJ;QACA;MACA;IACA;IAEA;IACAsC;MACA;MACAtC;;MAEA;MACAA;;MAEA;MACAA;IACA;IAEA;IACAuC;MACA;;MAEA;MACA;MACAvB;MACAA;MACAA;MAEAA;QACA;UACA;UACA;UACA;UAEA;YACA;YACA;YACA;YACA;YACA;YAEAhB;YACAA;cACAA;;cAEA;cACAwC;gBACAxC;cACA;YACA;UACA;QACA;MACA;IACA;IAEAyC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACArB,8BACAE;UACAvB;UACA2C;QACA,GACAC;UACA3C;QACA;MACA;IACA;IACA;IACA4C;MACA;MACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QAEA;QACA1B,eACAE;UACA;UACA;YAAA;UAAA;UACA;YAAAyB;UAAA;QACA,GACAxB;UAAA;QAAA;MAEA;QACA;MACA;IACA;IACA;IACAyB;MACA;MACA;QACA;QACA;UACAC;YACA;cACA;cACAzB;cACAA;cACAA;cACAA;YACA;UACA;QACA;MACA;IACA;IAEA0B;MACA;MACAnD;MACA;QACAoB,yBACAG;UACA;UAEA;YACA6B;cACAA;cACAA;YACA;UACA;UAEApD;UACAA;;UAEA;UACAA;;UAEA;UACAA;UAEA2C;QACA,GACAC;UAAA;QAAA;MACA;IACA;IACA;IACAS;MACA;MACArD;MACAC;MACA;QACA;QACAmB,yBACAG;UACA;UACAvB;;UAEA;UACAsD,sCACA/B;YAEA;YACAvB;;YAEA;YACAA;YACA;YACAA;YAEA2C;UACA,GACAC;YACA3C;YACAD;YACAuD;UACA;QACA,GACAX;UACA3C;UACAD;UACAuD;QACA;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;MACAzB;MACA0B;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;IACA;MACA3B;MACA0B;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3qBA;AAAA;AAAA;AAAA;AAA66B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;ACAj8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA6uC,CAAgB,mqCAAG,EAAC,C;;;;;;;;;;;ACAjwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/category/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/category/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2f754cba&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=2f754cba&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2f754cba\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=2f754cba&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var l1 =\n    g0 > 0\n      ? _vm.__map(_vm.list, function (category, categoryIndex) {\n          var $orig = _vm.__get_orig(category)\n          var g1 = category.goodsList.length\n          var l0 = g1\n            ? _vm.__map(category.goodsList, function (item, idx) {\n                var $orig = _vm.__get_orig(item)\n                var g2 = !_vm.isMemberPrice ? Math.floor(item.price || 0) : null\n                var g3 = !_vm.isMemberPrice\n                  ? ((item.price || 0) - Math.floor(item.price || 0))\n                      .toFixed(2)\n                      .substring(1)\n                  : null\n                var g4 = _vm.isMemberPrice\n                  ? Math.floor(\n                      item.gradePrice > 0 ? item.gradePrice : item.price\n                    )\n                  : null\n                var g5 = _vm.isMemberPrice\n                  ? (\n                      (item.gradePrice > 0 ? item.gradePrice : item.price) -\n                      Math.floor(\n                        item.gradePrice > 0 ? item.gradePrice : item.price\n                      )\n                    )\n                      .toFixed(2)\n                      .substring(1)\n                  : null\n                return {\n                  $orig: $orig,\n                  g2: g2,\n                  g3: g3,\n                  g4: g4,\n                  g5: g5,\n                }\n              })\n            : null\n          var g6 = category.goodsList.length\n          return {\n            $orig: $orig,\n            g1: g1,\n            l0: l0,\n            g6: g6,\n          }\n        })\n      : null\n  var g7 = g0 > 0 ? _vm.list.length : null\n  var g8 = Math.floor(_vm.totalPrice)\n  var g9 = (_vm.totalPrice - Math.floor(_vm.totalPrice)).toFixed(2).substring(1)\n  var g10 = _vm.list.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function (val) {\n      return (_vm.showPackagePopup = val)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l1: l1,\n        g7: g7,\n        g8: g8,\n        g9: g9,\n        g10: g10,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"container\">\n\t\t<Location v-if=\"storeInfo\" :storeInfo=\"storeInfo\" :currentDeliveryMode=\"currentDeliveryMode\" @deliveryModeChange=\"onDeliveryModeChange\" />\n\n\t\t<!-- 搜索框 -->\n\t\t<Search tips=\"请输入搜索关键字...\" @event=\"$navTo('pages/search/index')\" />\n\n\t\t<view class=\"main-content\" v-if=\"list.length > 0\">\n\t\t\t<!-- 左侧分类导航 -->\n\t\t\t<scroll-view class=\"category-nav\" :scroll-y=\"true\" :style=\"{ height: `${scrollHeight}px` }\">\n\t\t\t\t<view v-for=\"(item, index) in list\" :key=\"index\" class=\"nav-item-wrapper\">\n\t\t\t\t\t<text class=\"cart-badge\" v-if=\"item.total\">{{ item.total }}</text>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 人气推荐特殊样式 -->\n\t\t\t\t\t<view v-if=\"index === 0\" class=\"popular-category\" :class=\"{ selected: curIndex == index }\" @click=\"handleSelectNav(index)\">\n\t\t\t\t\t\t<view class=\"popular-badge\">人气TOP</view>\n\t\t\t\t\t\t<text class=\"category-title\">人气推荐</text>\n\t\t\t\t\t\t<view class=\"category-subtitle\">\n\t\t\t\t\t\t\t<text class=\"en-title\">POPULAR</text>\n\t\t\t\t\t\t\t<text class=\"en-subtitle\">Recommendations</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<!-- 普通分类 -->\n\t\t\t\t\t<view v-else class=\"normal-category\" :class=\"{ selected: curIndex == index }\" @click=\"handleSelectNav(index)\">\n\t\t\t\t\t\t<image class=\"category-logo\" lazy-load :lazy-load-margin=\"0\" v-if=\"item.logo\" :src=\"item.logo\"></image>\n\t\t\t\t\t\t<text class=\"category-name\">{{ item.name }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\n\t\t\t<!-- 右侧商品列表 -->\n\t\t\t<scroll-view class=\"goods-content\" :scroll-top=\"scrollTop\" @scroll=\"onScroll\" :scroll-y=\"true\"\n\t\t\t\t:style=\"{ height: `${scrollHeight}px` }\" :scroll-with-animation=\"true\">\n\t\t\t\t<view class=\"goods-wrapper\">\n\t\t\t\t\t<!-- 按分类显示所有商品 -->\n\t\t\t\t\t<view v-for=\"(category, categoryIndex) in list\" :key=\"categoryIndex\"\n\t\t\t\t\t\t:id=\"`category-${categoryIndex}`\" class=\"category-section\">\n\t\t\t\t\t\t<!-- 分类标题 -->\n\t\t\t\t\t\t<view class=\"category-title\" :id=\"`category-title-${categoryIndex}`\">\n\t\t\t\t\t\t\t<text class=\"title-text\">{{ category.name }}</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 该分类下的商品列表 -->\n\t\t\t\t\t\t<view v-if=\"category.goodsList.length\" class=\"goods-list\">\n\t\t\t\t\t\t\t<view class=\"goods-item\" v-for=\"(item, idx) in category.goodsList\" :key=\"idx\">\n\t\t\t\t\t\t\t\t<image class=\"goods-image\" v-if=\"item.logo\" lazy-load :lazy-load-margin=\"0\" :src=\"item.logo\"\n\t\t\t\t\t\t\t\t\t@click=\"onTargetGoods(item.id)\"></image>\n\n\t\t\t\t\t\t\t\t<view class=\"goods-info\">\n\t\t\t\t\t\t\t\t\t<text class=\"goods-name\">{{ item.name }}</text>\n\t\t\t\t\t\t\t\t\t<view class=\"goods-desc\">\n\t\t\t\t\t\t\t\t\t\t<text class=\"desc-text\">{{ item.salePoint || '精选优质食材，新鲜制作，香甜多汁，搭配优质原料，口感细腻顺滑，每一口都充...' }}</text>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t<view class=\"goods-action\">\n\t\t\t\t\t\t\t\t\t\t<view class=\"price-section\">\n\t\t\t\t\t\t\t\t\t\t\t<template v-if=\"!isMemberPrice\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-symbol\">￥</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-main\">{{ Math.floor(item.price || 0) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-decimal\">{{ ((item.price || 0) - Math.floor(item.price || 0)).toFixed(2).substring(1) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-suffix\">起</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"vip-price\" v-if=\"item.gradePrice > 0\" @tap=\"onVipPrice()\">￥{{ item.gradePrice }}</text>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t<template v-if=\"isMemberPrice\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-symbol\">￥</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-main\">{{ Math.floor(item.gradePrice > 0 ? item.gradePrice : item.price) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-decimal\">{{ ((item.gradePrice > 0 ? item.gradePrice : item.price) - Math.floor(item.gradePrice > 0 ? item.gradePrice : item.price)).toFixed(2).substring(1) }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"price-suffix\">起</text>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t<view class=\"cart-controls\">\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.isSingleSpec === 'Y' && item.type !== 'package'\" class=\"single-spec\">\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"control-minus\" v-if=\"item.buyNum\" @click=\"onSaveCart(item.id, '-')\"></view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"control-num\" v-if=\"item.buyNum\">{{ item.buyNum || 0 }}</view>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"control-add\" v-if=\"item.stock > 0\" @click=\"onSaveCart(item.id, '+')\"></view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.isSingleSpec === 'N'\" class=\"multi-spec\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"spec-badge\" v-if=\"item.buyNum\">{{ item.buyNum }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"spec-button\" @click=\"onShowSkuPopup(2, item.id)\">选规格</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t\t<view v-if=\"item.type === 'package'\" class=\"multi-spec\">\n\t\t\t\t\t\t\t\t\t\t\t\t<text class=\"spec-badge\" v-if=\"item.buyNum\">{{ item.buyNum }}</text>\n\t\t\t\t\t\t\t\t\t\t\t\t<view class=\"spec-button\" @click=\"onShowPackagePopup(item.id)\">选套餐</view>\n\t\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 该分类无商品时的提示 -->\n\t\t\t\t\t\t<view v-if=\"!category.goodsList.length\" class=\"empty-category\">\n\t\t\t\t\t\t\t<text class=\"empty-text\">该分类暂无商品</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 全部分类都无商品时的提示 -->\n\t\t\t\t\t<empty v-if=\"!list.length\" :isLoading=\"isLoading\" tips=\"暂无商品~\"></empty>\n\t\t\t\t</view>\n\t\t\t</scroll-view>\n\t\t</view>\n\n\t\t<!-- 商品SKU弹窗 -->\n\t\t<SkuPopup v-if=\"!isLoading && showSkuPopup && skuMode !== 3\" v-model=\"showSkuPopup\" :gradeInfo=\"gradeInfo\" :hafanInfo=\"hafanInfo\" :skuMode=\"skuMode\" :goods=\"goods\"\n\t\t\t@addCart=\"onAddCart\" />\n\t\t\t\n\t\t<!-- 套餐选择弹窗 -->\n\t\t<PackagePopup v-if=\"!isLoading && showPackagePopup\" :value=\"showPackagePopup\" @input=\"val => showPackagePopup = val\" :gradeInfo=\"gradeInfo\" :hafanInfo=\"hafanInfo\" :goods=\"goods\"\n\t\t\t@addCart=\"onAddCart\" />\n\n\t\t<!-- 底部购物车 -->\n\t\t<view class=\"cart-footer\">\n\t\t\t<view class=\"cart-container\">\n\t\t\t\t<image class=\"cart-bg\" src=\"@/static/cart-bg.png\"></image>\n\t\t\t\t<view class=\"cart-info\">\n\t\t\t\t\t<image class=\"cart-icon\" @click=\"goCart()\" src=\"@/static/cart-icon.png\"></image>\n\t\t\t\t\t<text class=\"cart-count\">共计{{ totalNum }}件</text>\n\t\t\t\t\t<view class=\"total-amount\">\n\t\t\t\t\t\t<text class=\"total-label\">总金额</text>\n\t\t\t\t\t\t<view class=\"amount-display\">\n\t\t\t\t\t\t\t<text class=\"amount-symbol\">￥</text>\n\t\t\t\t\t\t\t<text class=\"amount-main\">{{ Math.floor(totalPrice) }}</text>\n\t\t\t\t\t\t\t<text class=\"amount-decimal\">{{ (totalPrice - Math.floor(totalPrice)).toFixed(2).substring(1) }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"checkout-button\" @click=\"doSubmit()\">\n\t\t\t\t\t<text class=\"checkout-text\">去结算</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<empty v-if=\"!list.length\" :isLoading=\"isLoading\" />\n\t</view>\n</template>\n\n<script>\n\timport {\n\t\tsetCartTabBadge,\n\t\tsetCartTotalNum\n\t} from '@/utils/app'\n\timport * as CartApi from '@/api/cart'\n\timport * as UserApi from '@/api/user'\n\timport * as GoodsApi from '@/api/goods'\n\timport * as PackageApi from '@/api/package'\n\timport * as settingApi from '@/api/setting'\n\timport Search from '@/components/search'\n\timport Empty from '@/components/empty'\n\timport SkuPopup from './components/SkuPopup'\n\timport PackagePopup from './components/PackagePopup'\n\timport Location from '@/components/page/location'\n\n\texport default {\n\t\tcomponents: {\n\t\tSearch,\n\t\tSkuPopup,\n\t\tPackagePopup,\n\t\tEmpty,\n\t\tLocation\n\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\told: {\n\t\t\t\t\tscrollTop: 0\n\t\t\t\t},\n\t\t\t\torderId: 0,\n\t\t\t\tgoodsCart: [],\n\t\t\t\ttotalNum: 0,\n\t\t\t\ttotalPrice: 0.00,\n\t\t\t\t// 列表高度\n\t\t\t\tscrollHeight: 500,\n\t\t\t\t// 一级分类：指针\n\t\t\t\tcurIndex: 0,\n\t\t\t\t// 内容区竖向滚动条位置\n\t\t\t\tscrollTop: 0,\n\t\t\t\t// 分类列表\n\t\t\t\tlist: [],\n\t\t\t\t// 正在加载中\n\t\t\t\tisLoading: true,\n\t\t\t\tshowSkuPopup: false,\n\t\t\t\tshowPackagePopup: false,\n\t\t\t\tskuMode: 1,\n\t\t\t\tgoods: {},\n\t\t\t\tstoreInfo: null,\n\t\t\t\tgradeInfo: {},\n\t\t\t\thafanInfo: {},\n\t\t\t\tcurrentDeliveryMode: 'oneself', // 当前配送模式，默认为自取\n\t\t\t\t// 新增：分类区域位置信息\n\t\t\t\tcategoryPositions: [],\n\t\t\t\t// 新增：是否正在程序化滚动（避免滚动监听冲突）\n\t\t\t\tisProgrammaticScroll: false,\n\t\t\t\t// 新增：滚动节流定时器\n\t\t\t\tscrollTimer: null\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * 生命周期函数--监听页面加载\n\t\t */\n\t\tonLoad({\n\t\t\ttableId,\n\t\t\torderId\n\t\t}) {\n\t\t\tconst app = this;\n\t\t\ttableId = tableId ? parseInt(tableId) : 0;\n\t\t\tif (tableId > 0) {\n\t\t\t\tuni.setStorageSync('tableId', tableId);\n\t\t\t}\n\t\t\tapp.orderId = orderId;\n\t\t\t// 设置分类列表高度\n\t\t\tapp.setListHeight();\n\t\t\tconsole.log('orderId = ', app.orderId);\n\t\t},\n\n\t\t/**\n\t\t * 生命周期函数--监听页面显示\n\t\t */\n\t\tonShow() {\n\t\t\tconst app = this;\n\t\t\t// 获取页面数据\n\t\t\tapp.getPageData();\n\t\t\tapp.onGetStoreInfo();\n\n\t\t\tuni.getLocation({\n\t\t\t\ttype: 'gcj02',\n\t\t\t\tsuccess(res) {\n\t\t\t\t\tuni.setStorageSync('latitude', res.latitude);\n\t\t\t\t\tuni.setStorageSync('longitude', res.longitude);\n\t\t\t\t\tapp.onGetStoreInfo();\n\t\t\t\t},\n\t\t\t\tfail(e) {\n\t\t\t\t\t// empty\n\t\t\t\t}\n\t\t\t})\n\t\t},\n\n\t\t/**\n\t\t * 生命周期函数--监听页面隐藏\n\t\t */\n\t\tonHide() {\n\t\t\tconst app = this;\n\t\t\t// 清除滚动定时器\n\t\t\tif (app.scrollTimer) {\n\t\t\t\tclearTimeout(app.scrollTimer);\n\t\t\t\tapp.scrollTimer = null;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * 生命周期函数--监听页面卸载\n\t\t */\n\t\tonUnload() {\n\t\t\tconst app = this;\n\t\t\t// 清除滚动定时器\n\t\t\tif (app.scrollTimer) {\n\t\t\t\tclearTimeout(app.scrollTimer);\n\t\t\t\tapp.scrollTimer = null;\n\t\t\t}\n\t\t},\n\n\t\tcomputed: {\n\t\t\t// 计算哈帆会员等级\n\t\t\thafanLevel() {\n\t\t\t\treturn this.hafanInfo?.premium?.level || 'free';\n\t\t\t},\n\t\t\t// 计算是否为会员价格\n\t\t\tisMemberPrice() {\n\t\t\t\treturn (this.gradeInfo && this.gradeInfo.grade > 1) || (this.hafanLevel !== 'free');\n\t\t\t}\n\t\t},\n\n\t\tmethods: {\n\t\t\t// 原有的scroll方法保留兼容性\n\t\t\tscroll: function(e) {\n\t\t\t\tthis.old.scrollTop = e.detail.scrollTop\n\t\t\t},\n\n\t\t\t// 新的滚动处理方法\n\t\t\tonScroll: function(e) {\n\t\t\t\tconst app = this;\n\t\t\t\tapp.old.scrollTop = e.detail.scrollTop;\n\n\t\t\t\t// 如果是程序化滚动，不处理分类高亮\n\t\t\t\tif (app.isProgrammaticScroll) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 节流处理，避免频繁计算\n\t\t\t\tif (app.scrollTimer) {\n\t\t\t\t\tclearTimeout(app.scrollTimer);\n\t\t\t\t}\n\n\t\t\t\tapp.scrollTimer = setTimeout(() => {\n\t\t\t\t\tapp.updateActiveCategory(e.detail.scrollTop);\n\t\t\t\t}, 100); // 增加节流时间\n\t\t\t},\n\n\t\t\t// 更新当前激活的分类 - 实时查询版本\n\t\t\tupdateActiveCategory(scrollTop) {\n\t\t\t\tconst app = this;\n\n\t\t\t\t// 实时查询每个分类标题的位置\n\t\t\t\tconst query = uni.createSelectorQuery().in(app);\n\n\t\t\t\t// 查询滚动容器位置\n\t\t\t\tquery.select('.goods-content').boundingClientRect();\n\n\t\t\t\t// 查询所有分类标题位置\n\t\t\t\tapp.list.forEach((item, index) => {\n\t\t\t\t\tquery.select(`#category-title-${index}`).boundingClientRect();\n\t\t\t\t});\n\n\t\t\t\tquery.exec((res) => {\n\t\t\t\t\tif (res && res.length > 1) {\n\t\t\t\t\t\tconst containerRect = res[0];\n\t\t\t\t\t\tconst titleRects = res.slice(1);\n\n\t\t\t\t\t\tlet activeIndex = 0;\n\n\t\t\t\t\t\t// 找到当前应该高亮的分类\n\t\t\t\t\t\tfor (let i = 0; i < titleRects.length; i++) {\n\t\t\t\t\t\t\tconst titleRect = titleRects[i];\n\t\t\t\t\t\t\tif (titleRect && titleRect.top <= containerRect.top + 50) {\n\t\t\t\t\t\t\t\tactiveIndex = i;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 更新当前分类索引\n\t\t\t\t\t\tif (app.curIndex !== activeIndex) {\n\t\t\t\t\t\t\tapp.curIndex = activeIndex;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\n\t\t\t/**\n\t\t\t * 获取页面数据\n\t\t\t */\n\t\t\tgetPageData() {\n\t\t\t\tconst app = this\n\t\t\t\tapp.isLoading = true\n\t\t\t\tPromise.all([\n\t\t\t\t\t\t// 获取分类列表\n\t\t\t\t\t\tGoodsApi.cateList(),\n\t\t\t\t\t\t// 获取购物车列表\n\t\t\t\t\t\tCartApi.list(),\n\t\t\t\t\t\tUserApi.info()\n\t\t\t\t\t])\n\t\t\t\t\t.then(result => {\n\t\t\t\t\t\t// 初始化分类列表数据\n\t\t\t\t\t\tapp.list = result[0].data;\n\t\t\t\t\t\tapp.totalNum = result[1].data.totalNum;\n\t\t\t\t\t\tapp.goodsCart = result[1].data.list;\n\t\t\t\t\t\tapp.gradeInfo = result[2].data.gradeInfo || {};\n\t\t\t\t\t\tapp.hafanInfo = result[2].data.hafanInfo || {};\n\t\t\t\t\t\tsetCartTotalNum(app.totalNum);\n\t\t\t\t\t\tsetCartTabBadge();\n\t\t\t\t\t})\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tapp.isLoading = false\n\t\t\t\t\t\tapp.totalPrice = 0\n\t\t\t\t\t\tapp.list.forEach(function(item, index) {\n\t\t\t\t\t\t\tlet total = 0\n\t\t\t\t\t\t\titem.goodsList.forEach(function(goods, key) {\n\t\t\t\t\t\t\t\tlet totalBuyNum = 0\n\t\t\t\t\t\t\t\tapp.goodsCart.forEach(function(cart) {\n\t\t\t\t\t\t\t\t\tif (goods.id == cart.goodsId) {\n\t\t\t\t\t\t\t\t\t\ttotal = total + cart.num;\n\t\t\t\t\t\t\t\t\t\ttotalBuyNum = totalBuyNum + cart.num;\n\n\t\t\t\t\t\t\t\t\t\tapp.totalPrice = app.totalPrice + ((app.isMemberPrice ? cart.goodsInfo.gradePrice : cart.goodsInfo.price  )* cart.num);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\tapp.$set(app.list[index].goodsList[key], 'buyNum', totalBuyNum)\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tapp.$set(app.list[index], 'total', total)\n\t\t\t\t\t\t})\n\n\n\t\t\t\t\t})\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取默认店铺\n\t\t\t * */\n\t\t\tonGetStoreInfo() {\n\t\t\t\tconst app = this\n\t\t\t\tsettingApi.systemConfig()\n\t\t\t\t\t.then(result => {\n\t\t\t\t\t\tapp.storeInfo = result.data.storeInfo\n\t\t\t\t\t\t// 店铺信息加载完成后初始化配送模式\n\t\t\t\t\t\tapp.initDeliveryMode()\n\t\t\t\t\t})\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理配送模式切换\n\t\t\t */\n\t\t\t/**\n\t\t\t * 初始化配送模式\n\t\t\t */\n\t\t\tinitDeliveryMode() {\n\t\t\t\tconst app = this;\n\t\t\t\t// 从本地存储读取配送模式\n\t\t\t\tconst savedMode = uni.getStorageSync('current_order_mode');\n\t\t\t\tconsole.log('savedMode :>> ', savedMode);\n\t\t\t\t\n\t\t\t\tif (app.storeInfo && app.storeInfo.deliverySupported !== 'Y' && savedMode === 'express') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '该店铺暂不支持外送服务',\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\tduration: 2000\n\t\t\t\t\t});\n\t\t\t\t\t// 如果店铺不支持外送且之前选择了外送，重置为自取\n\t\t\t\t\tapp.currentDeliveryMode = 'oneself';\n\t\t\t\t\tuni.setStorageSync('current_order_mode', 'oneself');\n\t\t\t\t} else if (savedMode) {\n\t\t\t\t\t// 如果有保存的配送模式，使用它\n\t\t\t\t\tapp.currentDeliveryMode = savedMode;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理配送模式切换\n\t\t\t */\n\t\t\tonDeliveryModeChange(mode) {\n\t\t\t\tconst app = this\n\t\t\t\tconsole.log('配送模式切换:', mode)\n\t\t\t\t\n\t\t\t\t// 更新当前配送模式\n\t\t\t\tapp.currentDeliveryMode = mode;\n\t\t\t\t\n\t\t\t\t// 这里可以添加配送模式切换的业务逻辑\n\t\t\t\t// mode: 'oneself' 自取, 'express' 外送\n\t\t\t\tif (mode === 'oneself') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已切换到自取模式',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t} else if (mode === 'express') {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '已切换到外送模式',\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\tduration: 1500\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// 可以在这里保存配送模式到本地存储\n\t\t\t\tuni.setStorageSync('current_order_mode', mode)\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 跳转商品详情\n\t\t\t */\n\t\t\tonTargetGoods(goodsId) {\n\t\t\t\tthis.$navTo(`pages/goods/detail`, {\n\t\t\t\t\tgoodsId\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 设置分类列表高度\n\t\t\t */\n\t\t\tsetListHeight() {\n\t\t\t\tconst app = this\n\t\t\t\tuni.getSystemInfo({\n\t\t\t\t\tsuccess(res) {\n\t\t\t\t\t\tapp.scrollHeight = res.windowHeight - 120\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 一级分类：选中分类（点击跳转）\n\t\t\thandleSelectNav(index) {\n\t\t\t\tconst app = this;\n\t\t\t\tapp.curIndex = index;\n\n\t\t\t\t// 标记为程序化滚动，避免滚动监听冲突\n\t\t\t\tapp.isProgrammaticScroll = true;\n\n\t\t\t\t// 获取目标分类的位置并滚动到该位置\n\t\t\t\tapp.scrollToCategoryByIndex(index);\n\t\t\t},\n\n\t\t\t// 滚动到指定分类 - 修复版本\n\t\t\tscrollToCategoryByIndex(index) {\n\t\t\t\tconst app = this;\n\n\t\t\t\t// 查询目标分类标题和滚动容器的位置\n\t\t\t\tconst query = uni.createSelectorQuery().in(app);\n\t\t\t\tquery.select(`#category-title-${index}`).boundingClientRect();\n\t\t\t\tquery.select('.goods-content').boundingClientRect();\n\t\t\t\tquery.select('.goods-content').scrollOffset();\n\n\t\t\t\tquery.exec((res) => {\n\t\t\t\t\tif (res && res.length === 3) {\n\t\t\t\t\t\tconst titleRect = res[0];\n\t\t\t\t\t\tconst containerRect = res[1];\n\t\t\t\t\t\tconst scrollOffset = res[2];\n\n\t\t\t\t\t\tif (titleRect && containerRect && scrollOffset) {\n\t\t\t\t\t\t\t// 计算分类标题相对于滚动容器内容顶部的绝对位置\n\t\t\t\t\t\t\t// titleRect.top 是相对于视口的位置\n\t\t\t\t\t\t\t// containerRect.top 是滚动容器相对于视口的位置\n\t\t\t\t\t\t\t// scrollOffset.scrollTop 是当前滚动距离\n\t\t\t\t\t\t\tconst titleAbsoluteTop = titleRect.top - containerRect.top + scrollOffset.scrollTop;\n\n\t\t\t\t\t\t\tapp.scrollTop = app.old.scrollTop;\n\t\t\t\t\t\t\tapp.$nextTick(() => {\n\t\t\t\t\t\t\t\tapp.scrollTop = Math.max(0, titleAbsoluteTop);\n\n\t\t\t\t\t\t\t\t// 延迟重置程序化滚动标记\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tapp.isProgrammaticScroll = false;\n\t\t\t\t\t\t\t\t}, 800);\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\tonVipPrice() {\n\t\t\t\tthis.$toast(\"年度累计消费满200，即可使用会员价购买\");\n\t\t\t},\n\n\t\t\t// 更新购物车\n\t\t\tonSaveCart(goodsId, action) {\n\t\t\t\tconst app = this\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tCartApi.save(goodsId, action)\n\t\t\t\t\t\t.then(result => {\n\t\t\t\t\t\t\tapp.getPageData();\n\t\t\t\t\t\t\tresolve(result);\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\tconsole.log(err);\n\t\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 更新购物车数量\n\t\t\tonAddCart(total) {\n\t\t\t\tthis.getPageData();\n\t\t\t\tthis.$toast(\"添加购物车成功\");\n\t\t\t},\n\t\t\tgoCart(){\n\t\t\t\tif (this.totalPrice > 0) {\n\t\t\t\t\tthis.$navTo('pages/cart/index')\n\t\t\t\t} else {\n\t\t\t\t\tthis.$error(\"请先选择商品\")\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 结算\n\t\t\tdoSubmit() {\n\t\t\t\tif (this.totalPrice > 0) {\n\t\t\t\t\t\n\t\t\t\t\tthis.isLoading = true\n\t\t\t\t\tCartApi.list()\n          .then(result => {\n            const list = result.data.list;  \t\t\t\t\t\t\n\t\t\t\t\t\tconst cartIds = list.map(item => item.id).join(',');\n\t\t\t\t\t\tthis.$navTo('pages/settlement/goods', { cartIds })\n          })\n          .finally(() => this.isLoading = false)\n\n\t\t\t\t} else {\n\t\t\t\t\tthis.$error(\"请先选择商品\")\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 处理规格更新\n\t\t\tonUpdateSku(skuData) {\n\t\t\t  // 用于套餐商品更新规格信息\n\t\t\t  if (this.currentGoods && this.currentGoods.packageGroups) {\n\t\t\t    // 遍历套餐组查找商品并更新\n\t\t\t    this.currentGoods.packageGroups.forEach(group => {\n\t\t\t      group.items.forEach(item => {\n\t\t\t        if (item.itemGoods.id === skuData.id) {\n\t\t\t          // 更新商品规格信息\n\t\t\t          item.selectedSkuText = skuData.selectedSkuText;\n\t\t\t          item.selectedSkuId = skuData.selectedSkuId;\n\t\t\t          item.selectedSkuPrice = skuData.selectedSkuPrice;\n\t\t\t          item.buyNum = skuData.buyNum;\n\t\t\t        }\n\t\t\t      });\n\t\t\t    });\n\t\t\t  }\n\t\t\t},\n\n\t\t\tonShowSkuPopup(skuMode, goodsId) {\n\t\t\t\tconst app = this\n\t\t\t\tapp.isLoading = true\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tGoodsApi.detail(goodsId)\n\t\t\t\t\t\t.then(result => {\n\t\t\t\t\t\t\tconst goodsData = result.data\n\n\t\t\t\t\t\t\tif (goodsData.skuList) {\n\t\t\t\t\t\t\t\tgoodsData.skuList.forEach(function(sku, index) {\n\t\t\t\t\t\t\t\t\tgoodsData.skuList[index].specIds = sku.specIds.split('-')\n\t\t\t\t\t\t\t\t\tgoodsData.skuList[index].skuId = sku.id\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tapp.goods = goodsData\n\t\t\t\t\t\t\tapp.skuMode = skuMode\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 确保数据加载完成后再打开弹窗\n\t\t\t\t\t\t\tapp.isLoading = false\n\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t// 最后打开弹窗\n\t\t\t\t\t\t\tapp.showSkuPopup = true\n\n\t\t\t\t\t\t\tresolve(result)\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(err => reject(err))\n\t\t\t\t})\n\t\t\t},\n\t\t\t// 显示套餐选择弹窗\n\t\t\tonShowPackagePopup(goodsId) {\n\t\t\t\tconst app = this\n\t\t\t\tapp.isLoading = true\n\t\t\t\tconsole.log('Opening package popup for goodsId:', goodsId)\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\t// 先获取商品基本信息\n\t\t\t\t\tGoodsApi.detail(goodsId)\n\t\t\t\t\t\t.then(goodsResult => {\n\t\t\t\t\t\t\tconst goodsData = goodsResult.data\n\t\t\t\t\t\t\tapp.goods = goodsData\n\n\t\t\t\t\t\t\t// 再获取套餐分组信息\n\t\t\t\t\t\t\tPackageApi.getPackageDetails(goodsId)\n\t\t\t\t\t\t\t\t.then(packageResult => {\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 然后把套餐数据赋值给商品对象\n\t\t\t\t\t\t\t\t\tapp.goods.packageGroups = packageResult.data\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\t// 确保数据加载完成后再打开弹窗\n\t\t\t\t\t\t\t\t\tapp.isLoading = false \n\t\t\t\t\t\t\t\t\t// 最后打开弹窗\n\t\t\t\t\t\t\t\t\tapp.showPackagePopup = true\n\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t\tresolve(goodsResult)\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\t\t\tconsole.error(err)\n\t\t\t\t\t\t\t\t\tapp.isLoading = false\n\t\t\t\t\t\t\t\t\treject(err)\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.catch(err => {\n\t\t\t\t\t\t\tconsole.error(err)\n\t\t\t\t\t\t\tapp.isLoading = false\n\t\t\t\t\t\t\treject(err)\n\t\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t},\n\n\t\t/**\n\t\t\t* 设置分享内容\n\t\t\t*/\n\t\tonShareAppMessage() {\n\t\t\tconst app = this\n\t\t\treturn {\n\t\t\t\ttitle: _this.templet.shareTitle,\n\t\t\t\tpath: '/pages/category/index?' + app.$getShareUrlParams()\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * 分享到朋友圈\n\t\t * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)\n\t\t * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html\n\t\t */\n\t\tonShareTimeline() {\n\t\t\tconst app = this\n\t\t\treturn {\n\t\t\t\ttitle: _this.templet.shareTitle,\n\t\t\t\tpath: '/pages/category/index?' + app.$getShareUrlParams()\n\t\t\t}\n\t\t}\n\n\t}\n</script>\n\n<style>\n\tpage {\n\t\tbackground: #fff;\n\t}\n</style>\n<style lang=\"scss\" scoped>\n\t.container {\n\t\tbackground: #fff;\n\t\twidth: 100%;\n\t\toverflow-y: auto;\n\t\toverflow-x: hidden;\n\t\theight: 100vh;\n\t}\n\n\t/* 店铺信息头部 */\n\t.shop-header {\n\t\tposition: relative;\n\t\tpadding: 20rpx 32rpx 20rpx 32rpx;\n\t\tbackground: #fff;\n\t\tbox-shadow: 0px 2rpx 4rpx rgba(0, 0, 0, 0.25);\n\n\t\t.shop-info {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: space-between;\n\t\t\talign-items: flex-end;\n\n\t\t\t.shop-details {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\n\t\t\t\t.shop-name-row {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 8rpx;\n\n\t\t\t\t\t.location-icon {\n\t\t\t\t\t\twidth: 36rpx;\n\t\t\t\t\t\theight: 32rpx;\n\t\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.shop-name {\n\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\tfont-size: 42rpx;\n\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\tline-height: 40rpx;\n\t\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.arrow-icon {\n\t\t\t\t\t\twidth: 14rpx;\n\t\t\t\t\t\theight: 22rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.shop-distance {\n\t\t\t\t\tmargin-left: 44rpx;\n\t\t\t\t\tcolor: rgba(51, 51, 51, 0.6);\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tfont-family: FZLanTingHei-L-GBK;\n\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\tletter-spacing: 2rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.delivery-switch {\n\t\t\t\tmargin-top: 16rpx;\n\t\t\t\tbackground: #f2f2f2;\n\t\t\t\tborder-radius: 999rpx;\n\t\t\t\twidth: 162rpx;\n\t\t\t\theight: 50rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\n\t\t\t\t.switch-item {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\tline-height: 23rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tcolor: #8c8d8f;\n\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tbackground: #232e5d;\n\t\t\t\t\t\tborder-radius: 999rpx;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t\theight: 50rpx;\n\t\t\t\t\t\tline-height: 50rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* 主内容区域 */\n\t.main-content {\n\t\tdisplay: flex;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t/* 左侧分类导航 */\n\t.category-nav {\n\t\twidth: 206rpx;\n\t\tbackground: #f2f2f2;\n\t\tmargin-bottom: 160rpx;\n\n\t\t.nav-item-wrapper {\n\t\t\tposition: relative;\n\n\t\t\t.cart-badge {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 2rpx;\n\t\t\t\ttop: 20rpx;\n\t\t\t\tmargin-right: 10rpx;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tbackground: #fa5151;\n\t\t\t\tz-index: 999;\n\t\t\t\ttext-align: center;\n\t\t\t\tline-height: 30rpx;\n\t\t\t\tcolor: #ffffff;\n\t\t\t\tborder-radius: 50%;\n\t\t\t\tmin-width: 34rpx;\n\t\t\t\tpadding: 6rpx 14rpx;\n\t\t\t}\n\t\t}\n\n\t\t.popular-category {\n\t\t\tposition: relative;\n\t\t\tpadding: 52rpx 20rpx 36rpx;\n\t\t\tbackground: #f2f2f2;\n\t\t\tborder-radius: 0 0 32rpx 0;\n\t\t\ttext-align: center;\n\n\t\t\t&.selected {\n\t\t\t\tbackground: #fff;\n\t\t\t\tborder-left: 20rpx solid #f03c3c;\n\t\t\t}\n\n\t\t\t.popular-badge {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 0;\n\t\t\t\ttop: 0;\n\t\t\t\tpadding: 8rpx 0;\n\t\t\t\tbackground: #de2529;\n\t\t\t\tborder-radius: 0 10rpx 10rpx 0;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 20rpx;\n\t\t\t\tfont-family: FZLanTingHei-L-GBK;\n\t\t\t\tline-height: 18rpx;\n\t\t\t\twidth: 40rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\n\t\t\t.category-title {\n\t\t\t\tcolor: #212f6e;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\tline-height: 26rpx;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t}\n\n\t\t\t.category-subtitle {\n\t\t\t\ttext-align: center;\n\t\t\t\tline-height: 26rpx;\n\n\t\t\t\t.en-title {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-family: Sarabun;\n\t\t\t\t\tletter-spacing: 2rpx;\n\t\t\t\t\tline-height: 36rpx;\n\t\t\t\t\tcolor: rgba(153, 153, 153, 0.6);\n\t\t\t\t\tdisplay: block;\n\t\t\t\t}\n\n\t\t\t\t.en-subtitle {\n\t\t\t\t\tfont-size: 18rpx;\n\t\t\t\t\tfont-family: Sarabun;\n\t\t\t\t\tline-height: 26rpx;\n\t\t\t\t\tcolor: rgba(153, 153, 153, 0.6);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.normal-category {\n\t\t\tpadding: 72rpx 8rpx 12rpx;\n\t\t\ttext-align: center;\n\n\t\t\t&.selected {\n\t\t\t\tbackground: #fff;\n\t\t\t\tborder-left: 20rpx solid #f03c3c;\n\t\t\t\tcolor: #212f6e;\n\t\t\t\tfont-weight: bold;\n\t\t\t}\n\n\t\t\t.category-logo {\n\t\t\t\twidth: 60rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tborder-radius: 60rpx;\n\t\t\t\tmargin-bottom: 4rpx;\n\t\t\t}\n\n\t\t\t.category-name {\n\t\t\t\tcolor: #8b9298;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\tline-height: 26rpx;\n\t\t\t\tdisplay: block;\n\n\t\t\t\t.selected & {\n\t\t\t\t\tcolor: #212f6e;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* 右侧商品内容 */\n\t.goods-content {\n\t\tflex: 1;\n\t\tbackground: #fff;\n\t\tmargin-bottom: 160rpx;\n\n\t\t.goods-wrapper {\n\t\t\tpadding: 8rpx 16rpx 0;\n\t\t}\n\n\t\t.category-section {\n\t\t\tmargin-bottom: 40rpx;\n\n\t\t\t.category-title {\n\t\t\t\tposition: sticky;\n\t\t\t\ttop: 0;\n\t\t\t\tbackground: #fff;\n\t\t\t\tz-index: 10;\n\t\t\t\tpadding: 20rpx 0 16rpx;\n\t\t\t\tborder-bottom: 1rpx solid #f5f5f5;\n\n\t\t\t\t.title-text {\n\t\t\t\t\tfont-size: 32rpx;\n\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\tcolor: #212f6e;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tline-height: 30rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.empty-category {\n\t\t\t\tpadding: 60rpx 0;\n\t\t\t\ttext-align: center;\n\n\t\t\t\t.empty-text {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\tline-height: 26rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.goods-list {\n\t\t\twidth: 100%;\n\t\t\tpadding-top: 16rpx;\n\t\t}\n\n\t\t.goods-item {\n\t\t\tdisplay: flex;\n\t\t\tpadding: 8rpx 0;\n\t\t\tmargin-bottom: 72rpx;\n\n\t\t\t.goods-image {\n\t\t\t\twidth: 194rpx;\n\t\t\t\theight: 194rpx;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t\tmargin-right: 30rpx;\n\t\t\t\tflex-shrink: 0;\n\t\t\t}\n\n\t\t\t.goods-info {\n\t\t\t\tflex: 1;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\tpadding-right: 30rpx;\n\n\t\t\t\t.goods-name {\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\tline-height: 28rpx;\n\t\t\t\t\tcolor: #313132;\n\t\t\t\t\tmargin-bottom: 12rpx;\n\t\t\t\t}\n\n\t\t\t\t.goods-desc {\n\t\t\t\t\tmargin-bottom: 24rpx;\n\t\t\t\t\tline-height: 36rpx;\n\n\t\t\t\t\t.desc-text {\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-family: FZLanTingHei-L-GBK;\n\t\t\t\t\t\tline-height: 36rpx;\n\t\t\t\t\t\tcolor: rgba(51, 51, 51, 0.6);\n\t\t\t\t\t\tdisplay: -webkit-box;\n\t\t\t\t\t\t-webkit-box-orient: vertical;\n\t\t\t\t\t\t-webkit-line-clamp: 2;\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.goods-action {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.price-section {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: baseline;\n\t\t\t\t\t\tline-height: 25rpx;\n\n\t\t\t\t\t\t.price-symbol {\n\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.price-main {\n\t\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\t\tline-height: 25rpx;\n\t\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\t\tmargin: 0 2rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.price-decimal {\n\t\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\t\tletter-spacing: 5rpx;\n\t\t\t\t\t\t\tline-height: 25rpx;\n\t\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.price-suffix {\n\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\t\tletter-spacing: 3rpx;\n\t\t\t\t\t\t\tline-height: 19rpx;\n\t\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\t\tmargin-left: 4rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.vip-price {\n\t\t\t\t\t\t\tcolor: #999;\n\t\t\t\t\t\t\tmargin-left: 6rpx;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\t.cart-controls {\n\t\t\t\t\t\t.single-spec {\n\t\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t\t\t.control-minus {\n\t\t\t\t\t\t\t\tbackground: url('~@/static/icon/minus.png') no-repeat;\n\t\t\t\t\t\t\t\tbackground-size: 100% 100%;\n\t\t\t\t\t\t\t\twidth: 45rpx;\n\t\t\t\t\t\t\t\theight: 45rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.control-num {\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\twidth: 60rpx;\n\t\t\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.control-add {\n\t\t\t\t\t\t\t\tbackground: url('~@/static/icon/add.png') no-repeat;\n\t\t\t\t\t\t\t\tbackground-size: 100% 100%;\n\t\t\t\t\t\t\t\twidth: 45rpx;\n\t\t\t\t\t\t\t\theight: 45rpx;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.multi-spec {\n\t\t\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t\t\t.spec-badge {\n\t\t\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\t\t\ttop: -10rpx;\n\t\t\t\t\t\t\t\tright: -10rpx;\n\t\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\t\tbackground: #f03c3c;\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t\tline-height: 34rpx;\n\t\t\t\t\t\t\t\tcolor: #ffffff;\n\t\t\t\t\t\t\t\tborder-radius: 50%;\n\t\t\t\t\t\t\t\tmin-width: 34rpx;\n\t\t\t\t\t\t\t\tpadding: 4rpx;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t.spec-button {\n\t\t\t\t\t\t\t\tborder: 1rpx solid #20296e;\n\t\t\t\t\t\t\t\tpadding: 4rpx 0 8rpx;\n\t\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\t\tborder-radius: 6rpx;\n\t\t\t\t\t\t\t\tcolor: #20296e;\n\t\t\t\t\t\t\t\tbackground: #fff;\n\t\t\t\t\t\t\t\twidth: 86rpx;\n\t\t\t\t\t\t\t\ttext-align: center;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/* 底部购物车 */\n\t.cart-footer {\n\t\tposition: fixed;\n\t\tbottom: var(--window-bottom);\n\t\tleft: 0;\n\t\tright: 0;\n\t\theight: 116rpx;\n\t\tz-index: 11;\n\n\t\t.cart-container {\n\t\t\tposition: relative;\n\t\t\theight: 100%;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.cart-bg {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 114rpx;\n\t\t\t\ttop: 28rpx;\n\t\t\t\twidth: 472rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tz-index: 1;\n\t\t\t}\n\n\t\t\t.cart-info {\n\t\t\t\tposition: absolute;\n\t\t\t\tleft: 114rpx;\n\t\t\t\ttop: 28rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tz-index: 2;\n\t\t\t\tpadding: 12rpx 40rpx;\n\n\t\t\t\t.cart-icon {\n\t\t\t\t\twidth: 67rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tmargin-right: 40rpx;\n\t\t\t\t}\n\n\t\t\t\t.cart-count {\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tfont-family: FZLanTingHei-L-GBK;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tline-height: 18rpx;\n\t\t\t\t\tmargin-right: 16rpx;\n\t\t\t\t}\n\n\t\t\t\t.total-amount {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: flex-start;\n\n\t\t\t\t\t.total-label {\n\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\tfont-family: FZLanTingHei-L-GBK;\n\t\t\t\t\t\tcolor: #333;\n\t\t\t\t\t\tline-height: 19rpx;\n\t\t\t\t\t\tmargin-bottom: 4rpx;\n\t\t\t\t\t}\n\n\t\t\t\t\t.amount-display {\n\t\t\t\t\t\tdisplay: flex;\n\t\t\t\t\t\talign-items: baseline;\n\n\t\t\t\t\t\t.amount-symbol {\n\t\t\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.amount-main {\n\t\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\t\tline-height: 25rpx;\n\t\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\t\tmargin: 0 2rpx;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.amount-decimal {\n\t\t\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\t\t\tline-height: 25rpx;\n\t\t\t\t\t\t\tcolor: #000;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.checkout-button {\n\t\t\t\tposition: absolute;\n\t\t\t\tright: 90rpx;\n\t\t\t\ttop: 30rpx;\n\t\t\t\twidth: 214rpx;\n\t\t\t\theight: 56rpx;\n\t\t\t\tbackground-image: url('~@/static/checkout-btn-bg.png');\n\t\t\t\tbackground-size: 100% 100%;\n\t\t\t\tbackground-repeat: no-repeat;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tz-index: 3;\n\n\t\t\t\t.checkout-text {\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 30rpx;\n\t\t\t\t\tfont-family: FZLanTingHei-DB-GBK;\n\t\t\t\t\tline-height: 28rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891417991\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=2f754cba&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&id=2f754cba&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891423225\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}