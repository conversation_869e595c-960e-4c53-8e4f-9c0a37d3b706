{"version": 3, "sources": ["webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/PackagePopup.vue?e663", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/PackagePopup.vue?274b", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/PackagePopup.vue?76fc", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/category/components/PackagePopup.vue?8dd2", "uni-app:///pages/category/components/PackagePopup.vue"], "names": ["components", "GoodsPackagePopup", "Sku<PERSON><PERSON><PERSON>", "model", "prop", "event", "props", "value", "Type", "default", "skuMode", "type", "goods", "gradeInfo", "data", "isShowSkuPopup", "currentGoods", "isLoading", "selected<PERSON><PERSON>", "created", "methods", "onChangeValue", "openPackagePopup", "closePackagePopup", "addCart", "console", "package_items", "buy_num", "CartApi", "then", "app", "buyNow", "mode", "goodsId", "packageItems", "buyNum", "showSkuPopup", "GoodsApi", "goodsData", "logo", "name", "catch", "onConfirmSku", "onSkuPopupInput"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;;;AAG3D;AAC8K;AAC9K,gBAAgB,kLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,+oBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACiCprB;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAIA;EACAA;IACAC;IACAC;EACA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAC;MACAF;IACA;IACA;IACAG;MACAD;MACAF;IACA;IACAI;MACAF;MACAF;IACA;EACA;EAEAK;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EAEAC,6BACA;EAEAC;IAEA;IACAC;MACA;IACA;IAGA;IACAC;MACA;IAAA,CACA;IAEAC;MACA;IAAA,CACA;IAEA;IACAC;MACA;MAEAC;MAEA;QAAAC;QAAAC;MACAC,sDACAC;QACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MAEAC,0BACAR;QACA;;QAEA;QACA;UACAS;YACAA;YACAA;UACA;QACA;;QAEA;QACAR,mDACAQ;UACAC;UACAC;QAAA,EACA;;QAEA;QACAV;;QAEA;QACAA;UACAA;QACA;MACA,GACAW;QACAhB;QACAK;QACA;QACAA;MACA;IACA;IAEA;IACAY;MAAA;MACA;QAEA;QACA;MACA;MAEA;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "pages/category/components/PackagePopup.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./PackagePopup.vue?vue&type=template&id=5fc0cfe7&scoped=true&\"\nvar renderjs\nimport script from \"./PackagePopup.vue?vue&type=script&lang=js&\"\nexport * from \"./PackagePopup.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5fc0cfe7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/components/PackagePopup.vue\"\nexport default component.exports", "export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PackagePopup.vue?vue&type=template&id=5fc0cfe7&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PackagePopup.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./PackagePopup.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <GoodsPackagePopup \n      :value=\"value\"\n      :selectedSkus=\"selectedSkus\"\n      @input=\"onChangeValue\"\n      border-radius=\"20\"\n      :goods=\"goods\"\n      :mode=\"2\"\n      :defaultPrice=\"(gradeInfo.grade > 1 && goods.gradePrice > 0) ? goods.gradePrice : goods.price\"\n      :gradeInfo=\"gradeInfo\"\n      :defaultStock=\"goods.stock\"\n      :maskCloseAble=\"true\"\n      @open=\"openPackagePopup\"\n      @close=\"closePackagePopup\"\n      @add-cart=\"addCart\"\n      @buy-now=\"buyNow\"\n      @show-sku-popup=\"showSkuPopup\" />\n    \n    <!-- 商品规格弹出框 -->\n    <SkuPopup\n      v-if=\"isShowSkuPopup\"\n      v-model=\"isShowSkuPopup\"\n      :skuMode=\"5\"\n      :goodsId=\"currentGoods ? currentGoods.id : ''\"\n      :goods=\"currentGoods\"\n      :gradeInfo=\"gradeInfo\"\n      @confirm=\"onConfirmSku\"\n      @input=\"onSkuPopupInput\" />\n  </view>\n</template>\n\n<script>\n  import { setCartTotalNum } from '@/utils/app'\n  import * as CartApi from '@/api/cart'\n  import * as GoodsApi from '@/api/goods'\n  import GoodsPackagePopup from '@/components/goods-package-popup'\n  import SkuPopup from './SkuPopup'\n \n  export default {\n    components: {\n      GoodsPackagePopup,\n      SkuPopup\n    },\n    model: {\n      prop: 'value',\n      event: 'input'\n    },\n    props: {\n      // true 组件显示 false 组件隐藏\n      value: {\n        Type: Boolean,\n        default: false\n      },\n      // 模式 1:都显示 2:只显示购物车 3:只显示立即购买\n      skuMode: {\n        type: Number,\n        default: 1\n      },\n      // 商品详情信息\n      goods: {\n        type: Object,\n        default: {}\n      },\n\t  gradeInfo: {\n\t    type: Object,\n\t    default: {}\n\t  }\n    },\n\n    data() {\n      return {\n        isShowSkuPopup: false, // 规格弹窗显示状态\n        currentGoods: null, // 当前选择的商品\n        isLoading: false, // 加载状态\n        selectedSkus: {}, // 存储选中的多个规格\n      }\n    },\n\n    created() {   \n    },\n\n    methods: { \n\n      // 监听组件显示隐藏\n      onChangeValue(val) { \n        this.$emit('input', val);\n      },\n \n\n      // package组件 开始-----------------------------------------------------------\n      openPackagePopup() { \n        // console.log(\"监听 - 打开package组件\")\n      },\n\n      closePackagePopup() {\n        // console.log(\"监听 - 关闭package组件\")\n      },\n\n      // 加入购物车按钮\n      addCart(selectPackage) {\n        const app = this\n        \n        console.log('selectPackage',selectPackage) \n        \n        const { goods_id, package_items, buy_num } = selectPackage\n        CartApi.savePackage(goods_id, package_items, buy_num)\n          .then(result => {\n            // 隐藏当前弹窗\n            app.onChangeValue(false);\n            // 购物车商品总数量\n            const cartTotal = result.data ? result.data.cartTotal : 0;\n            // 缓存购物车数量\n            setCartTotalNum(cartTotal);\n            // 传递给父级\n            app.$emit('addCart', cartTotal);\n          })\n      },\n\n      // 立即购买\n      buyNow(selectPackage) {\n        // 跳转到订单结算页\n        this.$navTo('pages/settlement/goods', {\n          mode: 'buyNow',\n          goodsId: selectPackage.goods_id,\n          packageItems: JSON.stringify(selectPackage.package_items),\n          buyNum: selectPackage.buy_num\n        })\n        // 隐藏当前弹窗\n        this.onChangeValue(false)\n      },\n\n      // 显示商品规格弹窗\n      showSkuPopup(goods) {\n        const app = this;\n        // 先隐藏套餐弹窗\n        this.value = false;\n        this.isLoading = true;\n        \n        GoodsApi.detail(goods.id)\n          .then(result => {\n            const goodsData = result.data;\n            \n            // 处理规格数据\n            if (goodsData.skuList) {\n              goodsData.skuList.forEach(function(sku, index) {\n                goodsData.skuList[index].specIds = sku.specIds.split('-');\n                goodsData.skuList[index].skuId = sku.id;\n              });\n            }\n            \n            // 更新商品数据，保留套餐中的商品信息\n            app.currentGoods = {\n              ...goodsData,\n              logo: goods.logo,\n              name: goods.name\n            };\n            \n            // 确保数据加载完成后再打开弹窗\n            app.isLoading = false;\n            \n            // 显示规格弹窗\n            app.$nextTick(() => {\n              app.isShowSkuPopup = true;\n            });\n          })\n          .catch(err => {\n            console.error(err);\n            app.isLoading = false;\n            // 出错时恢复套餐弹窗显示\n            app.value = true;\n          });\n      },\n\n      // 确认选择规格\n      onConfirmSku(skuData) {\n        if (this.currentGoods && skuData) { \n\n          this.selectedSkus[skuData.goods_id] = skuData;  \n          this.selectedSkus = {...this.selectedSkus}; \n        }\n        \n        this.isShowSkuPopup = false;\n        // 重新显示套餐弹窗\n        this.$nextTick(() => {\n          this.value = true;\n        });\n      },\n\n      // 监听规格弹窗关闭\n      onSkuPopupInput(val) {\n        this.isShowSkuPopup = val;\n        if (!val) {\n          // 重新显示套餐弹窗\n          this.$nextTick(() => {\n            this.value = true;\n          });\n        }\n      }\n    }\n  }\n</script>\n\n<style lang=\"scss\" scoped>\n\n</style>"], "sourceRoot": ""}