{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?c178", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?7070", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?b35a", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?93c6", "uni-app:///pages/goods/detail.vue", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?56a8", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?96b4", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?3082", "webpack:///D:/workspace/fuintFoodSystem/fuintUniapp/pages/goods/detail.vue?0004"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Shortcut", "SlideImage", "Sku<PERSON><PERSON><PERSON>", "data", "isLoading", "goodsId", "goods", "cartTotal", "showSkuPopup", "skuMode", "gradeInfo", "hafanInfo", "onLoad", "methods", "onRefreshPage", "app", "Promise", "finally", "loadUserInfo", "getGoodsDetail", "GoodsApi", "then", "goodsData", "resolve", "catch", "getCartTotal", "CartApi", "onAddCart", "onShowSkuPopup", "onTargetHome", "onTargetCart", "onShareAppMessage", "title", "imageUrl", "path", "onShareTimeline"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACa;AACyB;;;AAG3F;AAC2K;AAC3K,gBAAgB,kLAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0PAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2oB,CAAgB,yoBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC4H/pB;AACA;AACA;AACA;AAAA;AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAMA;EACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;IACA;EACA;EAEAC;IAEA;IACAC;MACA;MACAC;MACAC,4EACAC;QAAA;MAAA;IACA;IAEAC;MAAA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC,6BACAC;UACA;UACA;YACAC;cACAA;cACAA;YACA;UACA;UACAP;UACAA;UACAQ;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAC,eACAL;UACAN;UACA;UACA;UACAQ;QACA,GACAC;UAAA;QAAA;MACA;IACA;IAEA;IACAG;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;IACA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;EACA;EAEA;AACA;AACA;EACAC;IACA;IACA;IACA;MACA1B;IACA;IACA;MACA2B;MACAC;MACAC;IACA;EACA;EAEA;AACA;AACA;AACA;AACA;EACAC;IACA;IACA;IACA;MACA9B;IACA;IACA;MACA2B;MACAC;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACzRA;AAAA;AAAA;AAAA;AAA86B,CAAgB,w4BAAG,EAAC,C;;;;;;;;;;;ACAl8B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA8uC,CAAgB,oqCAAG,EAAC,C;;;;;;;;;;;ACAlwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=f0a9f5ba&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./detail.vue?vue&type=style&index=1&id=f0a9f5ba&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f0a9f5ba\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods/detail.vue\"\nexport default component.exports", "export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=template&id=f0a9f5ba&scoped=true&\"", "var components\ntry {\n  components = {\n    jyfParser: function () {\n      return import(\n        /* webpackChunkName: \"components/jyf-parser/jyf-parser\" */ \"@/components/jyf-parser/jyf-parser.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\n  <view v-show=\"!isLoading\" class=\"container\">\n    <!-- 商品图片轮播 -->\n    <SlideImage v-if=\"!isLoading\" :images=\"goods.images\" />\n\n    <!-- 商品信息 -->\n    <view v-if=\"!isLoading\" class=\"goods-info m-top20\">\n      <!-- 价格、销量 -->\n      <view class=\"info-item info-item__top dis-flex flex-x-between flex-y-end\">\n        <view class=\"block-left dis-flex flex-y-end\">\n          <!-- 商品售价 -->\n          <text class=\"floor-price__samll\">￥</text>\n          <text class=\"floor-price\">{{ goods.price }}</text>\n\t\t  <!-- 会员价 -->\n\t\t  <template v-if=\"goods.gradePrice > 0\" >\n\t\t\t  <text class=\"floor-price__samll\">专属会员 ￥</text>\n\t\t\t  <text class=\"floor-price\">{{ goods.gradePrice }}</text>\n\t\t  </template>\n          <!-- 划线价 -->\n          <!-- <text class=\"original-price\">￥{{ goods.linePrice }}</text> -->\n        </view>\n        <view class=\"block-right dis-flex\">\n          <!-- 销量 -->\n          <view class=\"goods-sales\">\n            <text>已销售{{ goods.initSale }}</text>\n          </view>\n        </view>\n      </view>\n      <!-- 标题、分享 -->\n      <view class=\"info-item info-item__name dis-flex flex-y-center\">\n        <view class=\"goods-name flex-box\">\n          <text class=\"twolist-hidden\">{{ goods.name }}</text>\n        </view>\n        <!-- #ifdef MP-WEIXIN -->\n        <view class=\"goods-share__line\"></view>\n        <view class=\"goods-share\">\n          <button class=\"share-btn dis-flex flex-dir-column\" open-type=\"share\">\n            <text class=\"share__icon iconfont icon-fenxiang-post\"></text>\n            <text class=\"f-24\">分享</text>\n          </button>\n        </view>\n        <!-- #endif -->\n      </view>\n      <!-- 商品卖点 -->\n      <view v-if=\"goods.salePoint\" class=\"info-item info-item_selling-point\">\n        <text>{{ goods.salePoint }}</text>\n      </view>\n    </view>\n\n    <!-- 选择商品规格 -->\n    <!-- <view v-if=\"goods.isSingleSpec == 'N'\" class=\"goods-choice m-top20 b-f\" @click=\"onShowSkuPopup(3)\">\n      <view class=\"spec-list\">\n        <view class=\"flex-box\">\n          <text class=\"col-8\">选择：</text>\n          <text class=\"spec-name\" v-for=\"(item, index) in goods.specList\" :key=\"index\">{{ item.name }}</text>\n        </view>\n        <view class=\"f-26 col-9 t-r\">\n          <text class=\"iconfont icon-xiangyoujiantou\"></text>\n        </view>\n      </view>\n    </view> -->\n\n    <!-- 商品SKU弹窗 -->\n    <SkuPopup v-if=\"!isLoading\" v-model=\"showSkuPopup\"  :gradeInfo=\"gradeInfo\" :hafanInfo=\"hafanInfo\" :skuMode=\"skuMode\" :goods=\"goods\" @addCart=\"onAddCart\"/>\n\n    <!-- 商品描述 -->\n    <view v-if=\"!isLoading\" class=\"goods-content m-top20\">\n      <view class=\"item-title b-f\">\n        <text>商品详情</text>\n      </view>\n      <block v-if=\"goods.description != ''\">\n        <view class=\"goods-content-detail b-f\">\n          <jyf-parser :html=\"goods.description\"></jyf-parser>\n        </view>\n      </block>\n      <empty v-else tips=\"亲，暂无商品描述\" />\n    </view>\n\n    <!-- 底部选项卡 -->\n    <view class=\"footer-fixed\">\n      <view class=\"footer-container\">\n        <!-- 导航图标 -->\n        <view class=\"foo-item-fast\">\n          <!-- 首页 -->\n          <view class=\"fast-item fast-item--home\" @click=\"onTargetHome\">\n            <view class=\"fast-icon\">\n              <text class=\"iconfont icon-shouye\"></text>\n            </view>\n            <view class=\"fast-text\">\n              <text>首页</text>\n            </view>\n          </view>\n          <!-- 购物车-->\n          <view class=\"fast-item fast-item--cart\" @click=\"onTargetCart\">\n            <view v-if=\"cartTotal > 0\" class=\"fast-badge fast-badge--fixed\">{{ cartTotal > 99 ? '99+' : cartTotal }}</view>\n            <view class=\"fast-icon\">\n              <text class=\"iconfont icon-gouwuche\"></text>\n            </view>\n            <view class=\"fast-text\">\n              <text>购物车</text>\n            </view>\n          </view>\n        </view>\n        <!-- 操作按钮 -->\n        <!-- <view class=\"foo-item-btn\">\n          <view class=\"btn-wrapper\">\n            <view class=\"btn-item btn-item-deputy\" @click=\"onShowSkuPopup(2)\">\n              <text>加入购物车</text>\n            </view>\n            <view class=\"btn-item btn-item-main\" @click=\"onShowSkuPopup(3)\">\n              <text>立即购买</text>\n            </view>\n          </view>\n        </view> -->\n      </view>\n    </view>\n\n    <!-- 快捷导航 -->\n    <shortcut bottom=\"200rpx\" />\n\n  </view>\n</template>\n\n<script>\n  import { setCartTabBadge, setCartTotalNum } from '@/utils/app'\n  import * as GoodsApi from '@/api/goods'\n  import * as CartApi from '@/api/cart'\n  import * as UserApi from '@/api/user'\n  import jyfParser from '@/components/jyf-parser/jyf-parser'\n  import Shortcut from '@/components/shortcut'\n  import SlideImage from './components/SlideImage'\n  import SkuPopup from './components/SkuPopup'\n\n  export default {\n    components: {\n      jyfParser,\n      Shortcut,\n      SlideImage,\n      SkuPopup,\n    },\n    data() {\n      return {\n        // 正在加载\n        isLoading: true,\n        // 当前商品ID\n        goodsId: null,\n        // 商品详情\n        goods: {},\n        // 购物车总数量\n        cartTotal: 0,\n        // 显示/隐藏SKU弹窗\n        showSkuPopup: false,\n        // 模式 1:都显示 2:只显示购物车 3:只显示立即购买\n        skuMode: 1,\t\t\n\t\tgradeInfo: {},\n\t\thafanInfo: {},\n      }\n    },\n\n    /**\n     * 生命周期函数--监听页面加载\n     */\n    onLoad(options) {\n      // 商品ID\n      this.goodsId = parseInt(options.goodsId)\n      // 加载页面数据\n      this.onRefreshPage()\n    },\n\n    methods: {\n\n      // 刷新页面数据\n      onRefreshPage() {\n        const app = this\n        app.isLoading = true\n        Promise.all([app.getGoodsDetail(), app.getCartTotal(),app.loadUserInfo()])\n          .finally(() => app.isLoading = false)\n      },\n\t  \n\t  loadUserInfo(){\n\t\t  return UserApi.info().then(result => {\n\t\t\t  this.gradeInfo = result.data.gradeInfo;\n\t\t\t  this.hafanInfo = result.data.hafanInfo || {};\n\t\t  });\n\t  },\n\n      // 获取商品信息\n      getGoodsDetail() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          GoodsApi.detail(app.goodsId)\n            .then(result => {\n                const goodsData = result.data;\n                if (goodsData.skuList) {\n                    goodsData.skuList.forEach(function(sku, index) {\n                       goodsData.skuList[index].specIds = sku.specIds.split('-');\n                       goodsData.skuList[index].skuId = sku.id;\n                    })\n                }\n                app.goods = goodsData;\n                app.skuMode = 3;\n                resolve(result);\n            })\n            .catch(err => reject(err))\n        })\n      },\n\n      // 获取购物车总数量\n      getCartTotal() {\n        const app = this\n        return new Promise((resolve, reject) => {\n          CartApi.list()\n            .then(result => {\n              app.cartTotal = result.data.totalNum;\n              setCartTotalNum(app.cartTotal);\n              setCartTabBadge();\n              resolve(result);\n            })\n            .catch(err => reject(err));\n        })\n      },\n\n      // 更新购物车数量\n      onAddCart() {\n         this.$toast(\"添加购物车成功\");\n         this.getCartTotal();\n      },\n\n      /**\n       * 显示/隐藏SKU弹窗\n       * @param {skuMode} 模式 1:都显示 2:只显示购物车 3:只显示立即购买\n       */\n      onShowSkuPopup(skuMode = 1) {\n        this.skuMode = skuMode;\n        this.showSkuPopup = !this.showSkuPopup;\n      },\n\n      // 跳转到首页\n      onTargetHome(e) {\n        this.$navTo('pages/index/index');\n      },\n\n      // 跳转到购物车页\n      onTargetCart() {\n        this.$navTo('pages/cart/index')\n      }\n    },\n\n    /**\n     * 分享当前页面\n     */\n    onShareAppMessage() {\n      const app = this\n      // 构建页面参数\n      const params = app.$getShareUrlParams({\n          goodsId: app.goodsId,\n      })\n      return {\n        title: app.goods.name,\n        imageUrl: app.goods.images[0],\n        path: `/pages/goods/detail?${params}`\n      }\n    },\n\n    /**\n     * 分享到朋友圈\n     * 本接口为 Beta 版本，暂只在 Android 平台支持，详见分享到朋友圈 (Beta)\n     * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share-timeline.html\n     */\n    onShareTimeline() {\n      const app = this\n      // 构建页面参数\n      const params = app.$getShareUrlParams({\n        goodsId: app.goodsId,\n      })\n      return {\n        title: app.goods.name,\n        imageUrl: app.goods.images[0],\n        path: `/pages/goods/detail?${params}`\n      }\n    }\n  }\n</script>\n\n<style>\n  page {\n    background: #fafafa;\n  }\n</style>\n<style lang=\"scss\" scoped>\n  @import \"./detail.scss\";\n</style>\n", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891418446\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=1&id=f0a9f5ba&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../soft/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./detail.vue?vue&type=style&index=1&id=f0a9f5ba&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1751891424979\n      var cssReload = require(\"D:/soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}